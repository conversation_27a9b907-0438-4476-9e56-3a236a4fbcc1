#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python客户端 - 生成符合ccserver标准的TCP协议数据包
完全兼容ccserver的协议格式，支持SM4-GCM加密和明文传输
"""

import os
import json
import time
import struct
import secrets
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

# 协议常量定义（与ccserver保持一致）
MSG_START_CHAR1 = ord('@')
MSG_START_CHAR2 = ord('<')
MSG_RESP_START_CHAR2 = ord('>')

# 协议版本
PROTOCOL_VERSION_V1 = 0x01  # 明文协议
PROTOCOL_VERSION_V2 = 0x02  # 加密协议

# 加密方式
ENCRYPTION_NONE = 0x00      # 未加密
ENCRYPTION_SM4_GCM = 0x01   # SM4-GCM加密

# 消息体格式
MSG_FORMAT_JSON = 0x0000    # JSON格式
MSG_FORMAT_BYTE = 0x0001    # 字节流格式

# GCM参数
GCM_NONCE_SIZE = 12         # GCM Nonce长度
GCM_TAG_SIZE = 16           # GCM认证标签长度

def calculate_crc16(data):
    """计算CRC16校验码 - 与ccserver的Crc16CheckSum函数完全一致"""
    # 使用ccserver相同的CRC16查找表算法
    CRC_HI = [
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40
    ]

    CRC_LO = [
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
        0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
        0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
        0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
        0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
        0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
        0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
        0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
        0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
        0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
        0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
        0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
        0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
        0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
        0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
        0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
        0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
        0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
        0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
        0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
        0x43, 0x83, 0x41, 0x81, 0x80, 0x40
    ]

    uc_crc_hi = 0xFF
    uc_crc_lo = 0xFF

    for byte in data:
        idx = uc_crc_lo ^ byte
        uc_crc_lo = uc_crc_hi ^ CRC_HI[idx]
        uc_crc_hi = CRC_LO[idx]

    return (uc_crc_hi << 8) + uc_crc_lo

class CCServerTCPClient:
    """符合ccserver标准的TCP协议客户端"""

    def __init__(self, key=None):
        """初始化TCP客户端"""
        if key is not None and len(key) != 16:
            raise ValueError(f"SM4密钥长度必须为16字节，当前长度: {len(key)}")
        self.key = key
        self.bill_no = 1
        
    def generate_nonce(self):
        """生成12字节随机Nonce"""
        return secrets.token_bytes(12)
    
    def sm4_gcm_encrypt(self, plaintext, nonce):
        """真正的SM4-GCM加密"""
        # 创建SM4-GCM密码器
        cipher = Cipher(algorithms.SM4(self.key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        
        # 加密并获取认证标签
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        auth_tag = encryptor.tag
        
        return ciphertext, auth_tag
    
    def build_tcp_packet(self, message, is_encrypted=True, is_response=False):
        """构造符合ccserver标准的TCP协议包"""
        print(f"📦 构造TCP协议包: 加密={is_encrypted}, 响应={is_response}")
        print(f"📝 原始消息: {message}")

        # 1. 准备消息体
        msg_body = message.encode('utf-8')
        nonce = None
        auth_tag = None

        # 2. 如果需要加密，进行SM4-GCM加密
        if is_encrypted:
            if self.key is None:
                raise ValueError("加密模式需要提供SM4密钥")

            nonce = self.generate_nonce()
            print(f"🔑 生成Nonce: {nonce.hex()}")

            ciphertext, auth_tag = self.sm4_gcm_encrypt(msg_body, nonce)
            msg_body = ciphertext  # 使用密文作为消息体

            print(f"🔐 加密结果: 密文长度={len(ciphertext)}, 认证标签长度={len(auth_tag)}")
            print(f"🔐 密文: {ciphertext.hex()}")
            print(f"🏷️  认证标签: {auth_tag.hex()}")

        # 3. 构造协议包 - 严格按照ccserver的MakeCmd格式
        packet = bytearray()

        # 3.1 消息头标识 (2字节)
        packet.append(MSG_START_CHAR1)  # '@'
        if is_response:
            packet.append(MSG_RESP_START_CHAR2)  # '>'
        else:
            packet.append(MSG_START_CHAR2)  # '<'

        # 3.2 长度占位符 (2字节) - 稍后填充
        length_pos = len(packet)
        packet.extend(b'\x00\x00')

        # 3.3 流水号 (4字节大端序)
        packet.extend(struct.pack('>I', self.bill_no))
        current_bill_no = self.bill_no
        self.bill_no += 1

        # 3.4 消息体格式 (2字节) - JSON格式
        packet.extend(struct.pack('>H', MSG_FORMAT_JSON))

        # 3.5 协议版本号 (1字节)
        if is_encrypted:
            packet.append(PROTOCOL_VERSION_V2)  # 加密协议
        else:
            packet.append(PROTOCOL_VERSION_V1)  # 明文协议

        # 3.6 加密方式 (1字节)
        if is_encrypted:
            packet.append(ENCRYPTION_SM4_GCM)  # SM4-GCM加密
        else:
            packet.append(ENCRYPTION_NONE)  # 未加密

        # 3.7 随机数字段 (4字节)
        if is_encrypted and nonce:
            # V2协议: 复用为Nonce前4字节
            packet.extend(nonce[:4])
        else:
            # V1协议: 填充0
            packet.extend(struct.pack('>I', 0))

        # 3.8 时间戳 (8字节)
        packet.extend(struct.pack('>Q', int(time.time())))

        # 3.9 预留字段 (24字节)
        if is_encrypted and nonce and auth_tag:
            # V2协议: 复用为Nonce后8字节 + 认证标签16字节
            packet.extend(nonce[4:12])  # Nonce后8字节
            packet.extend(auth_tag)     # 认证标签16字节
        else:
            # V1协议: 全部填0
            packet.extend(bytes(24))

        # 3.10 消息体 (N字节)
        packet.extend(msg_body)

        # 3.11 计算并填充长度字段
        # 按照ccserver的MakeCmd逻辑：长度 = 从流水号到CRC16的总长度
        # 即：流水号(4) + 消息体格式(2) + 协议版本(1) + 加密方式(1) + 随机数(4) + 时间戳(8) + 预留(24) + 消息体(N) + CRC16(2)
        # 总计：46 + N + 2
        current_len = len(packet) - 4  # 当前长度减去@<和长度字段本身
        length_value = current_len + 2  # 加上即将添加的CRC16
        struct.pack_into('>H', packet, length_pos, length_value)

        # 3.12 CRC16校验码 (2字节)
        # 按照ccserver的MakeCmd逻辑：从长度字段开始计算CRC16
        crc_data = packet[2:]  # 从长度字段开始到当前结束
        crc16 = calculate_crc16(crc_data)
        packet.extend(struct.pack('>H', crc16))

        print(f"📊 协议包信息:")
        print(f"   消息头: {'@>' if is_response else '@<'}")
        print(f"   长度: {length_value}")
        print(f"   流水号: {current_bill_no}")
        print(f"   协议版本: 0x{PROTOCOL_VERSION_V2 if is_encrypted else PROTOCOL_VERSION_V1:02X}")
        print(f"   加密方式: 0x{ENCRYPTION_SM4_GCM if is_encrypted else ENCRYPTION_NONE:02X}")
        if is_encrypted and nonce:
            print(f"   Nonce: {nonce.hex()}")
        print(f"   CRC16: 0x{crc16:04X}")

        return bytes(packet)
    
    def save_tcp_packet_to_file(self, packet_data, filename):
        """保存TCP协议包到文件"""
        with open(filename, 'wb') as f:
            f.write(packet_data)
        print(f"📁 TCP协议包已保存到: {filename}")
        print(f"📦 数据包长度: {len(packet_data)} 字节")
        print(f"📦 数据包(hex): {packet_data.hex()}")

        # 分析协议包结构
        print(f"📊 协议包结构分析:")
        print(f"   消息头标识: {packet_data[0:2].decode('ascii', errors='ignore')}")
        print(f"   长度: {struct.unpack('>H', packet_data[2:4])[0]}")
        print(f"   流水号: {struct.unpack('>I', packet_data[4:8])[0]}")
        print(f"   消息体格式: 0x{struct.unpack('>H', packet_data[8:10])[0]:04X}")
        print(f"   协议版本: 0x{packet_data[10]:02X}")
        print(f"   加密方式: 0x{packet_data[11]:02X}")

        # 如果是加密协议，显示Nonce信息
        if packet_data[10] == PROTOCOL_VERSION_V2 and packet_data[11] == ENCRYPTION_SM4_GCM:
            nonce_part1 = packet_data[12:16]  # 随机数字段
            nonce_part2 = packet_data[20:28]  # 预留字段前8字节
            full_nonce = nonce_part1 + nonce_part2
            auth_tag = packet_data[28:44]     # 预留字段后16字节
            print(f"   Nonce: {full_nonce.hex()}")
            print(f"   认证标签: {auth_tag.hex()}")

def main():
    print("=== Python客户端 - 生成符合ccserver标准的TCP协议包 ===\n")

    # 1. 初始化客户端
    key = b"1234567890123456"  # 16字节密钥，与ccserver保持一致
    client = CCServerTCPClient(key)
    print(f"🔑 使用密钥: {key.hex()}")

    # 2. 准备测试消息 - 模拟真实设备消息
    test_messages = [
        # 注册消息
        '{"cmd": 1, "imsi": "460110123456789", "imei": "123456789012345", "version": "1.0.0"}',
        # 心跳消息
        '{"cmd": 2, "imsi": "460110123456789", "lat": 39.9042, "lng": 116.4074, "speed": 0, "direction": 0, "altitude": 50, "satellites": 8, "timestamp": 1640995200}',
        # 任务执行报告
        '{"cmd": 24, "imsi": "460110123456789", "taskId": "task_001", "status": 2, "step": 1, "mileage": 1.5, "executionId": 12345}'
    ]

    # 3. 生成加密和明文两种协议包
    for i, message in enumerate(test_messages, 1):
        print(f"\n{'='*60}")
        print(f"处理消息 {i}: {['注册', '心跳', '任务报告'][i-1]}")
        print(f"{'='*60}")

        # 3.1 生成加密协议包 (V2)
        print(f"\n--- 生成加密协议包 (V2) ---")
        encrypted_packet = client.build_tcp_packet(message, is_encrypted=True, is_response=False)
        encrypted_filename = f"ccserver_encrypted_packet_{i}.bin"
        client.save_tcp_packet_to_file(encrypted_packet, encrypted_filename)

        # 3.2 生成明文协议包 (V1) - 用于对比测试
        print(f"\n--- 生成明文协议包 (V1) ---")
        plaintext_packet = client.build_tcp_packet(message, is_encrypted=False, is_response=False)
        plaintext_filename = f"ccserver_plaintext_packet_{i}.bin"
        client.save_tcp_packet_to_file(plaintext_packet, plaintext_filename)

    # 4. 生成响应包示例
    print(f"\n{'='*60}")
    print(f"生成响应包示例")
    print(f"{'='*60}")

    response_message = '{"cmd": 1, "result": 0, "billNo": 1, "message": "注册成功"}'

    # 4.1 加密响应包
    print(f"\n--- 生成加密响应包 ---")
    encrypted_response = client.build_tcp_packet(response_message, is_encrypted=True, is_response=True)
    client.save_tcp_packet_to_file(encrypted_response, "ccserver_encrypted_response.bin")

    # 4.2 明文响应包
    print(f"\n--- 生成明文响应包 ---")
    plaintext_response = client.build_tcp_packet(response_message, is_encrypted=False, is_response=True)
    client.save_tcp_packet_to_file(plaintext_response, "ccserver_plaintext_response.bin")

    # 5. 创建处理完成标志文件
    with open("ccserver_tcp_client_done.flag", "w") as f:
        f.write("ccserver TCP协议客户端处理完成\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"生成消息数量: {len(test_messages)} 个请求 + 1 个响应\n")
        f.write(f"协议版本: V1(明文) + V2(加密)\n")
        f.write(f"使用密钥: {key.hex()}\n")
        f.write(f"CRC16算法: 与ccserver完全一致\n")

    print(f"\n🎉 ccserver TCP协议包生成完成！")
    print(f"📁 生成了 {len(test_messages)*2 + 2} 个协议包文件")
    print(f"📋 包含: {len(test_messages)} 个加密包 + {len(test_messages)} 个明文包 + 2 个响应包")
    print(f"🚀 这些数据包完全符合ccserver协议标准，可直接用于TCP连接测试")

if __name__ == "__main__":
    main()

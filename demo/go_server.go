package main

import (
	"bytes"
	"crypto/cipher"
	"encoding/binary"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/tjfoc/gmsm/sm4"
)

// 协议常量
const (
	// 消息头标识
	MSG_START_CHAR1      = '@'
	MSG_START_CHAR2      = '<'
	MSG_RESP_START_CHAR2 = '>'

	// 协议版本
	PROTOCOL_VERSION_V1 = 0x01 // 明文协议
	PROTOCOL_VERSION_V2 = 0x02 // 加密协议

	// 加密方式
	ENCRYPTION_NONE    = 0x00
	ENCRYPTION_SM4_GCM = 0x01

	// 消息体格式
	MSG_FORMAT_JSON = 0x0000
	MSG_FORMAT_BYTE = 0x0001

	// GCM参数
	GCM_NONCE_SIZE = 12 // GCM Nonce长度
	GCM_TAG_SIZE   = 16 // GCM认证标签长度
)

// SM4GCMCrypto SM4-GCM加密解密器
type SM4GCMCrypto struct {
	key []byte // 16字节SM4密钥
}

// NewSM4GCMCrypto 创建SM4-GCM加密解密器
func NewSM4GCMCrypto(key []byte) (*SM4GCMCrypto, error) {
	if len(key) != 16 {
		return nil, fmt.Errorf("SM4密钥长度必须为16字节，当前长度: %d", len(key))
	}
	return &SM4GCMCrypto{key: key}, nil
}

// Decrypt SM4-GCM解密
func (c *SM4GCMCrypto) Decrypt(ciphertext, authTag, nonce []byte) ([]byte, error) {
	if len(nonce) != GCM_NONCE_SIZE {
		return nil, fmt.Errorf("Nonce长度必须为%d字节", GCM_NONCE_SIZE)
	}

	if len(authTag) != GCM_TAG_SIZE {
		return nil, fmt.Errorf("认证标签长度必须为%d字节", GCM_TAG_SIZE)
	}

	// 创建SM4-GCM解密器
	block, err := sm4.NewCipher(c.key)
	if err != nil {
		return nil, fmt.Errorf("创建SM4密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 重新组合密文和认证标签
	fullCiphertext := append(ciphertext, authTag...)

	// 解密并验证认证标签
	plaintext, err := gcm.Open(nil, nonce, fullCiphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密或认证失败: %v", err)
	}

	return plaintext, nil
}

// ProtocolPacket 协议包结构
type ProtocolPacket struct {
	// 消息头标识 (2字节)
	StartChar1 byte
	StartChar2 byte

	// 长度 (2字节)
	Length uint16

	// 流水号 (4字节)
	BillNo uint32

	// 消息体格式 (2字节)
	MsgFormat uint16

	// 协议版本号 (1字节)
	ProtocolVer byte

	// 加密方式 (1字节)
	EncryptType byte

	// 随机数 (4字节) - 历史兼容字段
	RandomNum uint32

	// Nonce/IV (12字节) - 新增字段，仅加密协议使用
	Nonce []byte

	// 时间戳 (8字节)
	Timestamp uint64

	// 预留 (24字节)
	Reserved [24]byte

	// 消息体 (N字节)
	MsgBody []byte

	// 认证标签 (16字节) - 仅加密协议使用
	AuthTag []byte

	// CRC16校验码 (2字节)
	CRC16 uint16
}

// PacketParser 协议包解析器
type PacketParser struct {
	crypto *SM4GCMCrypto
}

// NewPacketParser 创建协议包解析器
func NewPacketParser(crypto *SM4GCMCrypto) *PacketParser {
	return &PacketParser{crypto: crypto}
}

// ParsePacket 解析协议包
func (pp *PacketParser) ParsePacket(data []byte) (*ProtocolPacket, error) {
	if len(data) < 48 { // 最小包长度
		return nil, fmt.Errorf("数据包长度不足")
	}

	fmt.Printf("🔍 解析数据包，总长度: %d\n", len(data))
	reader := bytes.NewReader(data)
	packet := &ProtocolPacket{}

	// 1. 消息头标识
	binary.Read(reader, binary.BigEndian, &packet.StartChar1)
	binary.Read(reader, binary.BigEndian, &packet.StartChar2)

	// 2. 长度
	binary.Read(reader, binary.BigEndian, &packet.Length)

	// 3. 流水号
	binary.Read(reader, binary.BigEndian, &packet.BillNo)

	// 4. 消息体格式
	binary.Read(reader, binary.BigEndian, &packet.MsgFormat)

	// 5. 协议版本号
	binary.Read(reader, binary.BigEndian, &packet.ProtocolVer)

	// 6. 加密方式
	binary.Read(reader, binary.BigEndian, &packet.EncryptType)

	// 7. 随机数字段 - V2协议复用为Nonce前4字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		packet.Nonce = make([]byte, GCM_NONCE_SIZE)
		reader.Read(packet.Nonce[:4]) // 读取Nonce前4字节
	} else {
		binary.Read(reader, binary.BigEndian, &packet.RandomNum) // V1协议读取随机数
	}

	// 8. 时间戳
	binary.Read(reader, binary.BigEndian, &packet.Timestamp)

	// 9. 预留字段 - V2协议复用为Nonce后8字节+认证标签16字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		reader.Read(packet.Nonce[4:12]) // 读取Nonce后8字节
		packet.AuthTag = make([]byte, GCM_TAG_SIZE)
		reader.Read(packet.AuthTag) // 读取认证标签16字节
		fmt.Printf("🔑 读取Nonce: %x\n", packet.Nonce)
		fmt.Printf("🏷️  读取认证标签: %x\n", packet.AuthTag)
	} else {
		reader.Read(packet.Reserved[:]) // V1协议读取预留字段
	}

	// 10. 计算消息体长度 - V1和V2协议头部长度相同
	headerSize := 4 + 2 + 1 + 1 + 4 + 8 + 24 // 固定头部长度
	remainingLen := int(packet.Length) - headerSize - 2 // -2 for CRC16

	fmt.Printf("📏 长度计算: packet.Length=%d, headerSize=%d, remainingLen=%d\n",
		packet.Length, headerSize, remainingLen)

	if remainingLen < 0 {
		return nil, fmt.Errorf("消息体长度计算错误: remainingLen=%d", remainingLen)
	}

	// 12. 消息体
	packet.MsgBody = make([]byte, remainingLen)
	n, err := reader.Read(packet.MsgBody)
	if err != nil || n != remainingLen {
		return nil, fmt.Errorf("读取消息体失败: expected=%d, actual=%d, err=%v", remainingLen, n, err)
	}
	fmt.Printf("📦 读取消息体: 长度=%d\n", len(packet.MsgBody))

	// 11. CRC16校验码
	err = binary.Read(reader, binary.BigEndian, &packet.CRC16)
	if err != nil {
		return nil, fmt.Errorf("读取CRC16失败: %v", err)
	}

	return packet, nil
}

// DecryptPacket 解密协议包
func (pp *PacketParser) DecryptPacket(packet *ProtocolPacket) ([]byte, error) {
	if packet.ProtocolVer != PROTOCOL_VERSION_V2 {
		return packet.MsgBody, nil // 明文协议直接返回
	}

	if packet.EncryptType != ENCRYPTION_SM4_GCM {
		return nil, fmt.Errorf("不支持的加密方式: %d", packet.EncryptType)
	}

	return pp.crypto.Decrypt(packet.MsgBody, packet.AuthTag, packet.Nonce)
}

// calculateCRC16 计算CRC16校验码 (简化实现)
func calculateCRC16(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 主函数
func main() {
	fmt.Println("=== Go服务端 - SM4-GCM解密演示 ===\n")

	// 1. 初始化解密器
	key := []byte("1234567890123456") // 16字节密钥，与Python客户端保持一致
	crypto, err := NewSM4GCMCrypto(key)
	if err != nil {
		log.Fatalf("创建解密器失败: %v", err)
	}
	fmt.Printf("🔑 使用密钥: %x\n", key)

	// 2. 创建解析器
	parser := NewPacketParser(crypto)

	// 3. 检查Python客户端是否已完成
	for i := 0; i < 10; i++ {
		if _, err := os.Stat("optimized_python_client_done.flag"); err == nil {
			break
		}
		fmt.Println("等待优化后Python客户端处理完成...")
		time.Sleep(1 * time.Second)
	}

	// 4. 查找并处理所有加密数据包文件
	files, err := filepath.Glob("optimized_encrypted_packet_*.bin")
	if err != nil {
		log.Fatalf("查找加密数据包文件失败: %v", err)
	}

	if len(files) == 0 {
		fmt.Println("⚠️ 未找到加密数据包文件，请先运行Python客户端")
		return
	}

	fmt.Printf("📁 找到 %d 个加密数据包文件\n", len(files))

	// 5. 处理每个加密数据包
	for i, file := range files {
		fmt.Printf("\n--- 处理文件 %d: %s ---\n", i+1, file)

		// 读取加密数据包
		encryptedData, err := ioutil.ReadFile(file)
		if err != nil {
			fmt.Printf("❌ 读取文件失败: %v\n", err)
			continue
		}
		fmt.Printf("📦 数据包长度: %d 字节\n", len(encryptedData))

		// 解析协议包
		packet, err := parser.ParsePacket(encryptedData)
		if err != nil {
			fmt.Printf("❌ 解析数据包失败: %v\n", err)
			continue
		}

		fmt.Printf("✅ 解析成功 - 协议版本: 0x%02X, 加密方式: 0x%02X\n", 
			packet.ProtocolVer, packet.EncryptType)

		// 解密消息体
		decryptedMessage, err := parser.DecryptPacket(packet)
		if err != nil {
			fmt.Printf("❌ 解密失败: %v\n", err)
			continue
		}

		fmt.Printf("🔓 解密成功: %s\n", string(decryptedMessage))

		// 保存解密结果
		outputFile := strings.Replace(file, "optimized_encrypted", "optimized_decrypted", 1)
		err = ioutil.WriteFile(outputFile, decryptedMessage, 0644)
		if err != nil {
			fmt.Printf("❌ 保存解密结果失败: %v\n", err)
			continue
		}
		fmt.Printf("📁 解密结果已保存到: %s\n", outputFile)
	}

	fmt.Println("\n✅ Go服务端处理完成！")
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SM4-GCM加解密完整演示
展示V2加密协议的完整处理流程
"""

import os
import json
import time
import struct
import secrets
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

# ==================== 协议常量定义 ====================

# 消息头标识
MSG_START_CHAR1 = 0x40  # '@'
MSG_START_CHAR2_REQUEST = 0x3C  # '<'
MSG_START_CHAR2_RESPONSE = 0x3E  # '>'

# 协议版本号
PROTOCOL_VERSION_V1 = 0x01  # 明文协议
PROTOCOL_VERSION_V2 = 0x02  # 加密协议

# 加密方式
ENCRYPTION_NONE = 0x00      # 未加密
ENCRYPTION_SM4_GCM = 0x01   # SM4-GCM加密

# 消息体格式
MSG_FORMAT_JSON = 0x0000    # JSON格式
MSG_FORMAT_BYTES = 0x0001   # 字节流格式

# 协议包结构常量
YHL_MSG_HEADER_LEN = 46     # 固定头部长度（不含消息头标识和长度字段）
GCM_NONCE_SIZE = 12         # GCM Nonce长度
GCM_TAG_SIZE = 16           # GCM认证标签长度
CRC16_SIZE = 2              # CRC16校验码长度

def calculate_crc16(data):
    """
    CRC16计算
    使用CCITT标准的CRC-16校验码计算方法
    """
    CRC_HI = [
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40
    ]
    
    CRC_LO = [
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
        0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
        0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
        0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
        0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
        0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
        0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
        0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
        0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
        0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
        0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
        0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
        0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
        0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
        0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
        0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
        0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
        0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
        0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
        0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
        0x43, 0x83, 0x41, 0x81, 0x80, 0x40
    ]
    
    uc_crc_hi = 0xFF
    uc_crc_lo = 0xFF
    
    for byte in data:
        idx = uc_crc_lo ^ byte
        uc_crc_lo = uc_crc_hi ^ CRC_HI[idx]
        uc_crc_hi = CRC_LO[idx]
    
    return (uc_crc_hi << 8) + uc_crc_lo

class ProtocolPacket:
    """
    协议包结构
    """
    def __init__(self):
        # 消息头标识 (2字节) - 总是为@<或@>
        self.start_char1 = None  # '@'
        self.start_char2 = None  # '<' 或 '>'
        
        # 长度 (2字节) - 从流水号到校验码的长度
        self.length = None
        
        # 流水号 (4字节) - 用于接收方检测是否有信息的丢失
        self.bill_no = None
        
        # 消息体格式 (2字节) - 0x0000:JSON格式, 0x0001:字节流格式
        self.msg_format = None
        
        # 协议版本号 (1字节) - 0x01:V1明文, 0x02:V2加密
        self.protocol_ver = None
        
        # 加密方式 (1字节) - 0x00:未加密, 0x01:SM4-GCM加密
        self.encrypt_type = None
        
        # 随机数字段 (4字节) - V2协议复用为Nonce前4字节
        self.random_num = None
        
        # 时间戳 (8字节) - UNIX时间戳
        self.timestamp = None
        
        # 预留字段 (24字节) - V2协议复用为Nonce后8字节+认证标签16字节
        self.reserved = None
        
        # V2协议专用字段（从字段复用中提取）
        self.nonce = None      # 12字节Nonce
        self.auth_tag = None   # 16字节认证标签
        
        # 消息体 (N字节) - 实际的业务数据
        self.msg_body = None
        
        # CRC16校验码 (2字节) - 从长度字段到消息体的校验
        self.crc16 = None
        
    def is_encrypted(self):
        """判断是否为加密协议包"""
        return self.encrypt_type == ENCRYPTION_SM4_GCM
        
    def is_response(self):
        """判断是否为响应包"""
        return self.start_char2 == chr(MSG_START_CHAR2_RESPONSE)
        
    def get_protocol_name(self):
        """获取协议版本名称"""
        if self.protocol_ver == PROTOCOL_VERSION_V1:
            return "V1(明文协议)"
        elif self.protocol_ver == PROTOCOL_VERSION_V2:
            return "V2(加密协议)"
        else:
            return f"未知协议(0x{self.protocol_ver:02X})"

class SM4GCMDemo:
    """
    SM4-GCM协议演示类
    完整实现TCP协议标准的加解密功能
    """
    
    def __init__(self, sm4_key):
        """
        初始化演示类
        Args:
            sm4_key: 16字节SM4密钥
        """
        if len(sm4_key) != 16:
            raise ValueError(f"SM4密钥必须为16字节，当前长度: {len(sm4_key)}")
        
        self.sm4_key = sm4_key
        self.bill_no_counter = 1
        
    def generate_secure_nonce(self):
        """生成安全的12字节随机Nonce"""
        return secrets.token_bytes(GCM_NONCE_SIZE)
    
    def sm4_gcm_encrypt(self, plaintext, nonce):
        """
        真正的SM4-GCM加密
        Args:
            plaintext: 明文数据
            nonce: 12字节随机数
        Returns:
            (ciphertext, auth_tag): 密文和16字节认证标签
        """
        if len(nonce) != GCM_NONCE_SIZE:
            raise ValueError(f"Nonce长度必须为{GCM_NONCE_SIZE}字节")
        
        # 使用cryptography库的真正SM4-GCM实现
        cipher = Cipher(algorithms.SM4(self.sm4_key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        
        # 执行加密
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        auth_tag = encryptor.tag
        
        if len(auth_tag) != GCM_TAG_SIZE:
            raise ValueError(f"认证标签长度异常: {len(auth_tag)}")
            
        return ciphertext, auth_tag
    
    def sm4_gcm_decrypt(self, ciphertext, auth_tag, nonce):
        """
        真正的SM4-GCM解密
        Args:
            ciphertext: 密文数据
            auth_tag: 16字节认证标签
            nonce: 12字节随机数
        Returns:
            plaintext: 解密后的明文
        """
        if len(nonce) != GCM_NONCE_SIZE:
            raise ValueError(f"Nonce长度必须为{GCM_NONCE_SIZE}字节")
        if len(auth_tag) != GCM_TAG_SIZE:
            raise ValueError(f"认证标签长度必须为{GCM_TAG_SIZE}字节")
        
        # 使用cryptography库的真正SM4-GCM实现
        cipher = Cipher(algorithms.SM4(self.sm4_key), modes.GCM(nonce, auth_tag))
        decryptor = cipher.decryptor()
        
        try:
            # 执行解密并验证认证标签
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            return plaintext
        except Exception as e:
            raise ValueError(f"SM4-GCM解密失败或认证标签验证失败: {e}")

    def build_protocol_packet(self, json_message, use_encryption=True, is_response=False):
        """
        构造完整的 TCP协议包
        Args:
            json_message: JSON格式的消息内容
            use_encryption: 是否使用V2加密协议
            is_response: 是否为响应包
        Returns:
            (packet_bytes, packet_info): 协议包字节数据和详细信息
        """
        # 1. 准备消息体数据
        msg_body_bytes = json_message.encode('utf-8')

        # 2. 初始化加密相关变量
        nonce = None
        auth_tag = None
        final_msg_body = msg_body_bytes

        # 3. 如果使用加密，执行SM4-GCM加密
        if use_encryption:
            nonce = self.generate_secure_nonce()
            ciphertext, auth_tag = self.sm4_gcm_encrypt(msg_body_bytes, nonce)
            final_msg_body = ciphertext

        # 4. 构造协议包
        packet = bytearray()

        # 4.1 消息头标识 (2字节)
        packet.append(MSG_START_CHAR1)  # '@'
        if is_response:
            packet.append(MSG_START_CHAR2_RESPONSE)  # '>'
        else:
            packet.append(MSG_START_CHAR2_REQUEST)   # '<'

        # 4.2 长度字段占位符 (2字节) - 稍后计算填充
        length_pos = len(packet)
        packet.extend(b'\x00\x00')

        # 4.3 流水号 (4字节，大端序)
        current_bill_no = self.bill_no_counter
        packet.extend(struct.pack('>I', current_bill_no))
        self.bill_no_counter += 1

        # 4.4 消息体格式 (2字节) - JSON格式
        packet.extend(struct.pack('>H', MSG_FORMAT_JSON))

        # 4.5 协议版本号 (1字节)
        if use_encryption:
            packet.append(PROTOCOL_VERSION_V2)
        else:
            packet.append(PROTOCOL_VERSION_V1)

        # 4.6 加密方式 (1字节)
        if use_encryption:
            packet.append(ENCRYPTION_SM4_GCM)
        else:
            packet.append(ENCRYPTION_NONE)

        # 4.7 随机数字段 (4字节) - V2协议复用为Nonce前4字节
        if use_encryption and nonce:
            packet.extend(nonce[:4])  # Nonce前4字节
        else:
            packet.extend(struct.pack('>I', 0))  # V1协议填充0

        # 4.8 时间戳 (8字节，大端序)
        current_timestamp = int(time.time())
        packet.extend(struct.pack('>Q', current_timestamp))

        # 4.9 预留字段 (24字节) - V2协议复用策略
        if use_encryption and nonce and auth_tag:
            # V2协议: Nonce后8字节 + 认证标签16字节
            packet.extend(nonce[4:12])  # Nonce后8字节
            packet.extend(auth_tag)     # 认证标签16字节
        else:
            # V1协议: 全部填充0
            packet.extend(bytes(24))

        # 4.10 消息体 (N字节)
        packet.extend(final_msg_body)

        # 4.11 计算并填充长度字段
        # 长度 = 从流水号到CRC16的总长度（包含即将添加的CRC16）
        content_length = len(packet) - 4 + CRC16_SIZE  # 减去@<和长度字段，加上CRC16
        struct.pack_into('>H', packet, length_pos, content_length)

        # 4.12 计算CRC16校验码 (2字节)
        # 从长度字段开始计算CRC16
        crc_data = packet[2:]  # 从长度字段开始到当前结束
        crc16_value = calculate_crc16(crc_data)
        packet.extend(struct.pack('>H', crc16_value))

        # 5. 构造返回信息
        packet_info = {
            'total_length': len(packet),
            'bill_no': current_bill_no,
            'protocol_version': 'V2(加密)' if use_encryption else 'V1(明文)',
            'encryption_type': 'SM4-GCM' if use_encryption else '明文',
            'msg_format': 'JSON',
            'timestamp': current_timestamp,
            'timestamp_readable': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_timestamp)),
            'crc16': f"0x{crc16_value:04X}",
            'msg_body_length': len(final_msg_body),
            'nonce': nonce.hex() if nonce else None,
            'auth_tag': auth_tag.hex() if auth_tag else None,
            'ciphertext': final_msg_body.hex() if use_encryption else None
        }

        return bytes(packet), packet_info

    def parse_protocol_packet(self, packet_bytes):
        """
        Args:
            packet_bytes: 完整的协议包字节数据
        Returns:
            ProtocolPacket: 解析后的协议包对象
        """
        if len(packet_bytes) < 50:  # 最小包长度检查
            raise ValueError(f"协议包长度不足，最小需要50字节，当前: {len(packet_bytes)}")

        packet = ProtocolPacket()

        # 1. 验证并解析消息头标识 (2字节)
        if packet_bytes[0] != MSG_START_CHAR1:
            raise ValueError(f"无效的消息头标识: 0x{packet_bytes[0]:02X}")
        if packet_bytes[1] not in [MSG_START_CHAR2_REQUEST, MSG_START_CHAR2_RESPONSE]:
            raise ValueError(f"无效的消息头标识: @{chr(packet_bytes[1])}")

        packet.start_char1 = chr(packet_bytes[0])
        packet.start_char2 = chr(packet_bytes[1])

        # 2. 从长度字段开始解析（去除消息头标识）
        data_buf = packet_bytes[2:]

        # 3. 长度字段 (2字节) - 从流水号到CRC16的总长度
        packet.length = struct.unpack('>H', data_buf[0:2])[0]

        # 4. 流水号 (4字节)
        packet.bill_no = struct.unpack('>I', data_buf[2:6])[0]

        # 5. 消息体格式 (2字节)
        packet.msg_format = struct.unpack('>H', data_buf[6:8])[0]

        # 6. 协议版本号 (1字节)
        packet.protocol_ver = data_buf[8]

        # 7. 加密方式 (1字节) - 智能加密检测的关键字段
        packet.encrypt_type = data_buf[9]

        # 8. 随机数字段 (4字节)
        packet.random_num = struct.unpack('>I', data_buf[10:14])[0]

        # 9. 时间戳 (8字节)
        packet.timestamp = struct.unpack('>Q', data_buf[14:22])[0]

        # 10. 预留字段 (24字节) - 关键的字段复用逻辑
        reserved_data = data_buf[22:46]

        # 智能加密检测：以encryptedType字段为主要判断依据
        if packet.encrypt_type == ENCRYPTION_SM4_GCM:
            # 提取12字节Nonce（字段复用策略）
            nonce_bytes = bytearray(GCM_NONCE_SIZE)
            # Nonce前4字节来自随机数字段
            struct.pack_into('>I', nonce_bytes, 0, packet.random_num)
            # Nonce后8字节来自预留字段前8字节
            nonce_bytes[4:12] = reserved_data[0:8]
            packet.nonce = bytes(nonce_bytes)

            # 提取16字节认证标签（来自预留字段后16字节）
            packet.auth_tag = reserved_data[8:24]
        else:
            # 明文协议：保存预留字段原始数据
            packet.reserved = reserved_data

        # 11. 计算消息体长度
        # 消息体长度 = 总长度 - 消息头(2) - 固定头部(46) - CRC16(2)
        msg_body_length = len(packet_bytes) - 2 - YHL_MSG_HEADER_LEN - CRC16_SIZE
        if msg_body_length < 0:
            raise ValueError(f"消息体长度计算错误: {msg_body_length}")

        # 12. 提取消息体 (N字节)
        packet.msg_body = data_buf[46:46 + msg_body_length]

        # 13. 提取CRC16校验码 (2字节) - 总是在数据包最后
        packet.crc16 = struct.unpack('>H', packet_bytes[-2:])[0]

        return packet

    def verify_crc16(self, packet_bytes, expected_crc):
        """
        验证CRC16校验码
        """
        # CRC16计算范围：从长度字段开始到CRC16之前的所有数据
        crc_data = packet_bytes[2:-2]  # 去除@<和最后的CRC16
        calculated_crc = calculate_crc16(crc_data)

        if calculated_crc != expected_crc:
            raise ValueError(f"CRC16校验失败: 期望=0x{expected_crc:04X}, 计算=0x{calculated_crc:04X}")

        return True

    def demonstrate_complete_flow(self, json_message, message_name, use_encryption=True):
        """
        演示完整的SM4-GCM协议流程
        简化输出，只显示关键的加密前后数据

        Args:
            json_message: JSON格式的测试消息
            message_name: 消息名称（用于显示）
            use_encryption: 是否使用加密协议
        """
        print(f"\n{'='*80}")
        print(f"{message_name} - {'V2加密协议' if use_encryption else 'V1明文协议'}")
        print(f"{'='*80}")

        try:
            # 1. 显示原始明文
            print(f"📝 加密前文本（明文）:")
            print(f"   {json_message}")

            # 2. 构造协议包（包含加密过程）
            packet_bytes, packet_info = self.build_protocol_packet(json_message, use_encryption)

            # 3. 显示加密后的密文（仅加密协议）
            if use_encryption:
                print(f"\n🔐 加密后文本（密文）:")
                print(f"   {packet_info['ciphertext']}")

            # 4. 解析协议包
            parsed_packet = self.parse_protocol_packet(packet_bytes)

            # 5. 验证CRC16
            self.verify_crc16(packet_bytes, parsed_packet.crc16)

            # 6. 处理消息体（解密或直接使用）
            final_message = None

            if parsed_packet.is_encrypted():
                # 执行解密
                decrypted_bytes = self.sm4_gcm_decrypt(
                    parsed_packet.msg_body,
                    parsed_packet.auth_tag,
                    parsed_packet.nonce
                )
                final_message = decrypted_bytes.decode('utf-8')
            else:
                # 明文协议，直接使用消息体
                final_message = parsed_packet.msg_body.decode('utf-8')

            # 7. 显示解密后的明文
            print(f"\n📝 解密后文本（明文）:")
            print(f"   {final_message}")

            # 8. 验证消息完整性
            if final_message == json_message:
                print(f"\n✅ 验证通过：原始消息与解密消息完全一致")
            else:
                print(f"\n❌ 验证失败：消息不一致")
                return False

            return True

        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False

def main():
    """主演示函数"""
    print("=" * 80)

    # 1. 初始化演示类
    sm4_key = b"1234567890123456"  # 16字节标准SM4密钥
    demo = SM4GCMDemo(sm4_key)

    print(f"🔑 SM4密钥: {sm4_key.hex()}")

    # 2. 定义测试消息（设备注册命令）
    test_message = {
        'name': '设备注册命令',
        'message': '{"cmd": 1, "imsi": "460110123456789", "imei": "123456789012345", "version": "1.0.0"}'
    }

    # 3. 演示V2加密协议
    print(f"\n🔐 SM4-GCM加密协议演示")
    print(f"=" * 80)

    demo.demonstrate_complete_flow(
        test_message['message'],
        test_message['name'],
        use_encryption=True
    )

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python服务端 - 解析符合ccserver标准的TCP协议包
完全兼容ccserver的协议格式，支持V1明文和V2加密协议的解析和解密
"""

import os
import json
import struct
import glob
import time
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

# 协议常量定义（与ccserver保持一致）
MSG_START_CHAR1 = ord('@')
MSG_START_CHAR2 = ord('<')
MSG_RESP_START_CHAR2 = ord('>')

# 协议版本
PROTOCOL_VERSION_V1 = 0x01  # 明文协议
PROTOCOL_VERSION_V2 = 0x02  # 加密协议

# 加密方式
ENCRYPTION_NONE = 0x00      # 未加密
ENCRYPTION_SM4_GCM = 0x01   # SM4-GCM加密

# 消息体格式
MSG_FORMAT_JSON = 0x0000    # JSON格式
MSG_FORMAT_BYTE = 0x0001    # 字节流格式

# GCM参数
GCM_NONCE_SIZE = 12         # GCM Nonce长度
GCM_TAG_SIZE = 16           # GCM认证标签长度

def calculate_crc16(data):
    """计算CRC16校验码 - 与ccserver的Crc16CheckSum函数完全一致"""
    # 使用ccserver相同的CRC16查找表算法
    CRC_HI = [
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40
    ]

    CRC_LO = [
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
        0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
        0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
        0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
        0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
        0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
        0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
        0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
        0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
        0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
        0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
        0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
        0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
        0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
        0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
        0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
        0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
        0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
        0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
        0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
        0x43, 0x83, 0x41, 0x81, 0x80, 0x40
    ]

    uc_crc_hi = 0xFF
    uc_crc_lo = 0xFF

    for byte in data:
        idx = uc_crc_lo ^ byte
        uc_crc_lo = uc_crc_hi ^ CRC_HI[idx]
        uc_crc_hi = CRC_LO[idx]

    return (uc_crc_hi << 8) + uc_crc_lo

class CCServerProtocolPacket:
    """ccserver协议包结构"""
    def __init__(self):
        # 消息头标识 (2字节)
        self.start_char1 = None
        self.start_char2 = None

        # 长度 (2字节)
        self.length = None

        # 流水号 (4字节)
        self.bill_no = None

        # 消息体格式 (2字节)
        self.msg_format = None

        # 协议版本号 (1字节)
        self.protocol_ver = None

        # 加密方式 (1字节)
        self.encrypt_type = None

        # 随机数字段 (4字节) - V2协议复用为Nonce前4字节
        self.random_num = None

        # 时间戳 (8字节)
        self.timestamp = None

        # 预留字段 (24字节) - V2协议复用为Nonce后8字节+认证标签16字节
        self.reserved = None

        # V2协议专用字段
        self.nonce = None      # 12字节Nonce (仅V2协议)
        self.auth_tag = None   # 16字节认证标签 (仅V2协议)

        # 消息体 (N字节)
        self.msg_body = None

        # CRC16校验码 (2字节)
        self.crc16 = None

    def is_encrypted(self):
        """判断是否为加密协议包"""
        return self.encrypt_type == ENCRYPTION_SM4_GCM

    def is_response(self):
        """判断是否为响应包"""
        return self.start_char2 == chr(MSG_RESP_START_CHAR2)

    def get_protocol_name(self):
        """获取协议版本名称"""
        if self.protocol_ver == PROTOCOL_VERSION_V1:
            return "V1(明文)"
        elif self.protocol_ver == PROTOCOL_VERSION_V2:
            return "V2(加密)"
        else:
            return f"未知(0x{self.protocol_ver:02X})"

class CCServerTCPServer:
    """符合ccserver标准的TCP协议服务端"""

    def __init__(self, key=None):
        """初始化TCP服务端"""
        if key is not None and len(key) != 16:
            raise ValueError(f"SM4密钥长度必须为16字节，当前长度: {len(key)}")
        self.key = key
        
    def sm4_gcm_decrypt(self, ciphertext, auth_tag, nonce):
        """真正的SM4-GCM解密"""
        if len(nonce) != 12:
            raise ValueError(f"Nonce长度必须为12字节，当前长度: {len(nonce)}")
        
        if len(auth_tag) != 16:
            raise ValueError(f"认证标签长度必须为16字节，当前长度: {len(auth_tag)}")
        
        # 创建SM4-GCM解密器
        cipher = Cipher(algorithms.SM4(self.key), modes.GCM(nonce, auth_tag))
        decryptor = cipher.decryptor()
        
        # 解密并验证认证标签
        try:
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            return plaintext
        except Exception as e:
            raise ValueError(f"解密或认证失败: {e}")
    
    def parse_tcp_packet(self, data):
        """解析ccserver标准的TCP协议包 - 完全按照boot/yhl_protocol.go的HandleRecv逻辑"""
        if len(data) < 50:  # 最小包长度: @< + 长度 + 46字节头部 + CRC16
            raise ValueError(f"数据包长度不足，最小需要50字节，当前: {len(data)}")

        packet = CCServerProtocolPacket()

        # 按照ccserver的HandleRecv方法解析
        # 1. 验证消息头标识 '@<' 或 '@>'
        if data[0] != ord('@'):
            raise ValueError(f"无效的消息头标识: {chr(data[0])}")
        if data[1] not in [ord('<'), ord('>')]:
            raise ValueError(f"无效的消息头标识: @{chr(data[1])}")

        packet.start_char1 = chr(data[0])
        packet.start_char2 = chr(data[1])

        # 2. 去除消息头标识，从长度字段开始解析（与ccserver逻辑一致）
        data_buf = data[2:]  # 去除@<或@>

        # 3. 长度字段 (2字节) - 从流水号到CRC16的总长度
        packet.length = struct.unpack('>H', data_buf[0:2])[0]

        # 4. 流水号 (4字节)
        packet.bill_no = struct.unpack('>I', data_buf[2:6])[0]

        # 5. 消息体格式 (2字节)
        packet.msg_format = struct.unpack('>H', data_buf[6:8])[0]

        # 6. 协议版本号 (1字节)
        packet.protocol_ver = data_buf[8]

        # 7. 加密方式 (1字节) - encryptedType
        packet.encrypt_type = data_buf[9]

        # 8. 随机数字段 (4字节) - encryptedRand
        packet.random_num = struct.unpack('>I', data_buf[10:14])[0]

        # 9. 时间戳 (8字节)
        packet.timestamp = struct.unpack('>Q', data_buf[14:22])[0]

        # 10. 预留字段 (24字节) - 按照ccserver的智能加密检测逻辑
        reserved_data = data_buf[22:46]

        # 🚀 智能加密检测：以encryptedType字段为主要判断依据（与ccserver完全一致）
        if packet.encrypt_type == ENCRYPTION_SM4_GCM:
            # 检测到加密消息：解析Nonce和认证标签（支持任意协议版本）
            packet.nonce = bytearray(GCM_NONCE_SIZE)
            packet.auth_tag = bytearray(GCM_TAG_SIZE)

            # Nonce前4字节来自随机数字段（encryptedRand）
            struct.pack_into('>I', packet.nonce, 0, packet.random_num)

            # Nonce后8字节来自预留字段前8字节
            packet.nonce[4:12] = reserved_data[0:8]

            # 认证标签来自预留字段后16字节
            packet.auth_tag[:] = reserved_data[8:24]

            packet.nonce = bytes(packet.nonce)
            packet.auth_tag = bytes(packet.auth_tag)
        else:
            # 明文消息：保存预留字段
            packet.reserved = reserved_data

        # 11. 计算消息体长度 - 使用实际数据包长度
        # 消息体长度 = 总数据包长度 - 消息头(@<, 2字节) - 固定头部长度(46字节) - CRC16长度(2字节)
        msg_body_len = len(data) - 2 - 46 - 2

        if msg_body_len < 0:
            raise ValueError(f"消息体长度计算错误: {msg_body_len}")

        # 12. 消息体
        packet.msg_body = data_buf[46:46 + msg_body_len]

        # 13. CRC16校验码 (2字节) - 总是在整个数据包的最后2字节
        packet.crc16 = struct.unpack('>H', data[-2:])[0]

        return packet
    
    def verify_crc16(self, data, expected_crc):
        """验证CRC16校验码 - 按照ccserver的Crc16CheckSum逻辑"""
        # 按照ccserver的MakeCmd逻辑：CRC16覆盖从长度字段开始到CRC16之前的所有数据
        # CRC16计算的数据：从长度字段开始，到数据包倒数第3字节结束（排除最后2字节的CRC16）
        crc_data = data[2:-2]  # 从长度字段开始，排除最后2字节CRC16
        calculated_crc = calculate_crc16(crc_data)

        if calculated_crc != expected_crc:
            raise ValueError(f"CRC16校验失败: 期望={expected_crc:04X}, 计算={calculated_crc:04X}")

        return True
    
    def process_tcp_packet(self, packet_data):
        """处理ccserver标准的TCP协议包"""
        # 1. 解析协议包
        packet = self.parse_tcp_packet(packet_data)

        # 2. 验证CRC16
        self.verify_crc16(packet_data, packet.crc16)

        # 3. 处理消息体
        final_message = None
        if packet.is_encrypted():
            # 加密消息：需要解密
            if self.key is None:
                raise ValueError("处理加密消息需要提供SM4密钥")

            plaintext = self.sm4_gcm_decrypt(packet.msg_body, packet.auth_tag, packet.nonce)
            final_message = plaintext.decode('utf-8')
        else:
            # 明文消息：直接使用
            final_message = packet.msg_body.decode('utf-8')

        # 4. 构造返回结果
        result = {
            'packet_info': {
                'start_chars': f"{packet.start_char1}{packet.start_char2}",
                'packet_type': '响应包' if packet.is_response() else '请求包',
                'protocol_version': packet.get_protocol_name(),
                'encryption': '加密' if packet.is_encrypted() else '明文',
                'length': packet.length,
                'bill_no': packet.bill_no,
                'msg_format': f"0x{packet.msg_format:04X}",
                'protocol_ver': f"0x{packet.protocol_ver:02X}",
                'encrypt_type': f"0x{packet.encrypt_type:02X}",
                'timestamp': packet.timestamp,
                'timestamp_readable': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(packet.timestamp)),
                'crc16': f"0x{packet.crc16:04X}"
            },
            'message': final_message
        }

        # 5. 添加加密相关信息
        if packet.is_encrypted():
            result['encryption_info'] = {
                'nonce': packet.nonce.hex(),
                'auth_tag': packet.auth_tag.hex(),
                'ciphertext_length': len(packet.msg_body),
                'plaintext_length': len(final_message.encode('utf-8'))
            }

        return result

def main():
    print("=== Python服务端 - 解析ccserver标准TCP协议包 ===\n")

    # 1. 初始化服务端
    key = b"1234567890123456"  # 16字节密钥，与客户端保持一致
    server = CCServerTCPServer(key)
    print(f"🔑 使用密钥: {key.hex()}")

    # 2. 查找客户端生成的协议包文件
    packet_files = glob.glob("ccserver_*.bin")
    if not packet_files:
        print("❌ 未找到协议包文件")
        print("💡 请先运行 python_sm4_gcm_client.py 生成协议包")
        return

    packet_files.sort()  # 按文件名排序
    print(f"📁 找到 {len(packet_files)} 个协议包文件")

    # 3. 处理所有协议包
    success_count = 0
    encrypted_count = 0
    plaintext_count = 0

    for i, filename in enumerate(packet_files, 1):
        print(f"\n{'='*80}")
        print(f"处理协议包 {i}: {filename}")
        print(f"{'='*80}")

        try:
            # 读取协议包
            with open(filename, 'rb') as f:
                packet_data = f.read()

            print(f"📦 数据包长度: {len(packet_data)} 字节")
            print(f"📦 数据包(hex): {packet_data.hex()}")

            # 处理协议包
            result = server.process_tcp_packet(packet_data)

            # 显示处理结果
            print(f"\n✅ 协议包解析成功!")
            print(f"📊 协议包信息:")
            for key, value in result['packet_info'].items():
                print(f"   {key}: {value}")

            # 显示加密信息（如果有）
            if 'encryption_info' in result:
                print(f"🔐 加密信息:")
                for key, value in result['encryption_info'].items():
                    print(f"   {key}: {value}")
                encrypted_count += 1
            else:
                plaintext_count += 1

            print(f"\n📝 消息内容: {result['message']}")

            # 验证JSON格式
            try:
                json_data = json.loads(result['message'])
                print(f"✅ JSON格式验证通过")
                print(f"📋 解析后的JSON:")
                print(json.dumps(json_data, ensure_ascii=False, indent=2))

                # 分析消息类型
                if 'cmd' in json_data:
                    cmd_names = {
                        1: '注册',
                        2: '心跳',
                        24: '任务执行报告'
                    }
                    cmd_name = cmd_names.get(json_data['cmd'], f"未知命令({json_data['cmd']})")
                    print(f"🎯 消息类型: {cmd_name}")

            except json.JSONDecodeError as e:
                print(f"⚠️  JSON格式验证失败: {e}")

            success_count += 1

        except Exception as e:
            print(f"❌ 协议包处理失败: {e}")
            import traceback
            traceback.print_exc()

    # 4. 创建处理完成标志文件
    with open("ccserver_tcp_server_done.flag", "w") as f:
        f.write("ccserver TCP协议服务端处理完成\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"成功处理: {success_count}/{len(packet_files)}\n")
        f.write(f"加密协议包: {encrypted_count}\n")
        f.write(f"明文协议包: {plaintext_count}\n")
        f.write(f"使用密钥: {server.key.hex()}\n")
        f.write(f"CRC16算法: 与ccserver完全一致\n")

    print(f"\n🎉 协议包处理完成!")
    print(f"📊 处理统计:")
    print(f"   总计: {len(packet_files)} 个文件")
    print(f"   成功: {success_count} 个")
    print(f"   加密协议包: {encrypted_count} 个")
    print(f"   明文协议包: {plaintext_count} 个")

    if success_count == len(packet_files):
        print("✅ 所有协议包处理成功，与ccserver协议完全兼容!")
    else:
        print("⚠️  部分协议包处理失败，请检查兼容性问题")

if __name__ == "__main__":
    import time
    main()

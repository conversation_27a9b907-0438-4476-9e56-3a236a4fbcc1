package main

import (
	"bytes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"log"
	"time"

	"github.com/tjfoc/gmsm/sm4"
)

// 协议常量
const (
	// 消息头标识
	MSG_START_CHAR1      = '@'
	MSG_START_CHAR2      = '<'
	MSG_RESP_START_CHAR2 = '>'

	// 协议版本
	PROTOCOL_VERSION_V1 = 0x01 // 明文协议
	PROTOCOL_VERSION_V2 = 0x02 // 加密协议

	// 加密方式
	ENCRYPTION_NONE    = 0x00
	ENCRYPTION_SM4_GCM = 0x01

	// 消息体格式
	MSG_FORMAT_JSON = 0x0000
	MSG_FORMAT_BYTE = 0x0001

	// GCM参数
	GCM_NONCE_SIZE = 12 // GCM Nonce长度
	GCM_TAG_SIZE   = 16 // GCM认证标签长度
)

// SM4GCMCrypto SM4-GCM加密解密器
type SM4GCMCrypto struct {
	key []byte // 16字节SM4密钥
}

// NewSM4GCMCrypto 创建SM4-GCM加密解密器
func NewSM4GCMCrypto(key []byte) (*SM4GCMCrypto, error) {
	if len(key) != 16 {
		return nil, fmt.Errorf("SM4密钥长度必须为16字节，当前长度: %d", len(key))
	}
	return &SM4GCMCrypto{key: key}, nil
}

// GenerateNonce 生成12字节随机Nonce
func (c *SM4GCMCrypto) GenerateNonce() ([]byte, error) {
	nonce := make([]byte, GCM_NONCE_SIZE)
	_, err := rand.Read(nonce)
	if err != nil {
		return nil, fmt.Errorf("生成Nonce失败: %v", err)
	}
	return nonce, nil
}

// Encrypt SM4-GCM加密
func (c *SM4GCMCrypto) Encrypt(plaintext, nonce []byte) ([]byte, []byte, error) {
	if len(nonce) != GCM_NONCE_SIZE {
		return nil, nil, fmt.Errorf("Nonce长度必须为%d字节", GCM_NONCE_SIZE)
	}

	// 创建SM4-GCM加密器
	block, err := sm4.NewCipher(c.key)
	if err != nil {
		return nil, nil, fmt.Errorf("创建SM4密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 加密并生成认证标签
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// 分离密文和认证标签
	if len(ciphertext) < GCM_TAG_SIZE {
		return nil, nil, fmt.Errorf("加密结果长度异常")
	}

	actualCiphertext := ciphertext[:len(ciphertext)-GCM_TAG_SIZE]
	authTag := ciphertext[len(ciphertext)-GCM_TAG_SIZE:]

	return actualCiphertext, authTag, nil
}

// Decrypt SM4-GCM解密
func (c *SM4GCMCrypto) Decrypt(ciphertext, authTag, nonce []byte) ([]byte, error) {
	if len(nonce) != GCM_NONCE_SIZE {
		return nil, fmt.Errorf("Nonce长度必须为%d字节", GCM_NONCE_SIZE)
	}

	if len(authTag) != GCM_TAG_SIZE {
		return nil, fmt.Errorf("认证标签长度必须为%d字节", GCM_TAG_SIZE)
	}

	// 创建SM4-GCM解密器
	block, err := sm4.NewCipher(c.key)
	if err != nil {
		return nil, fmt.Errorf("创建SM4密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 重新组合密文和认证标签
	fullCiphertext := append(ciphertext, authTag...)

	// 解密并验证认证标签
	plaintext, err := gcm.Open(nil, nonce, fullCiphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密或认证失败: %v", err)
	}

	return plaintext, nil
}

// ProtocolPacket 协议包结构
type ProtocolPacket struct {
	// 消息头标识 (2字节)
	StartChar1 byte
	StartChar2 byte

	// 长度 (2字节)
	Length uint16

	// 流水号 (4字节)
	BillNo uint32

	// 消息体格式 (2字节)
	MsgFormat uint16

	// 协议版本号 (1字节)
	ProtocolVer byte

	// 加密方式 (1字节)
	EncryptType byte

	// 随机数 (4字节) - 历史兼容字段
	RandomNum uint32

	// Nonce/IV (12字节) - 新增字段，仅加密协议使用
	Nonce []byte

	// 时间戳 (8字节)
	Timestamp uint64

	// 预留 (24字节)
	Reserved [24]byte

	// 消息体 (N字节)
	MsgBody []byte

	// 认证标签 (16字节) - 仅加密协议使用
	AuthTag []byte

	// CRC16校验码 (2字节)
	CRC16 uint16
}

// PacketBuilder 协议包构造器
type PacketBuilder struct {
	crypto *SM4GCMCrypto
	billNo uint32
}

// NewPacketBuilder 创建协议包构造器
func NewPacketBuilder(crypto *SM4GCMCrypto) *PacketBuilder {
	return &PacketBuilder{
		crypto: crypto,
		billNo: 1,
	}
}

// BuildEncryptedPacket 构造加密协议包
func (pb *PacketBuilder) BuildEncryptedPacket(msgBody []byte, isResponse bool) ([]byte, error) {
	// 生成Nonce
	nonce, err := pb.crypto.GenerateNonce()
	if err != nil {
		return nil, fmt.Errorf("生成Nonce失败: %v", err)
	}

	// 加密消息体
	ciphertext, authTag, err := pb.crypto.Encrypt(msgBody, nonce)
	if err != nil {
		return nil, fmt.Errorf("加密失败: %v", err)
	}

	// 构造协议包
	packet := ProtocolPacket{
		StartChar1:  MSG_START_CHAR1,
		StartChar2:  MSG_START_CHAR2,
		BillNo:      pb.billNo,
		MsgFormat:   MSG_FORMAT_JSON,
		ProtocolVer: PROTOCOL_VERSION_V2,
		EncryptType: ENCRYPTION_SM4_GCM,
		RandomNum:   0, // 历史兼容字段
		Nonce:       nonce,
		Timestamp:   uint64(time.Now().Unix()),
		MsgBody:     ciphertext,
		AuthTag:     authTag,
	}

	if isResponse {
		packet.StartChar2 = MSG_RESP_START_CHAR2
	}

	pb.billNo++

	return pb.serializePacket(&packet)
}

// BuildPlaintextPacket 构造明文协议包
func (pb *PacketBuilder) BuildPlaintextPacket(msgBody []byte, isResponse bool) ([]byte, error) {
	packet := ProtocolPacket{
		StartChar1:  MSG_START_CHAR1,
		StartChar2:  MSG_START_CHAR2,
		BillNo:      pb.billNo,
		MsgFormat:   MSG_FORMAT_JSON,
		ProtocolVer: PROTOCOL_VERSION_V1,
		EncryptType: ENCRYPTION_NONE,
		RandomNum:   0,
		Timestamp:   uint64(time.Now().Unix()),
		MsgBody:     msgBody,
	}

	if isResponse {
		packet.StartChar2 = MSG_RESP_START_CHAR2
	}

	pb.billNo++

	return pb.serializePacket(&packet)
}

// serializePacket 序列化协议包
func (pb *PacketBuilder) serializePacket(packet *ProtocolPacket) ([]byte, error) {
	var buffer bytes.Buffer

	// 1. 消息头标识
	buffer.WriteByte(packet.StartChar1)
	buffer.WriteByte(packet.StartChar2)

	// 2. 长度占位符(稍后填充)
	lengthPos := buffer.Len()
	binary.Write(&buffer, binary.BigEndian, uint16(0))

	// 3. 流水号
	binary.Write(&buffer, binary.BigEndian, packet.BillNo)

	// 4. 消息体格式
	binary.Write(&buffer, binary.BigEndian, packet.MsgFormat)

	// 5. 协议版本号
	buffer.WriteByte(packet.ProtocolVer)

	// 6. 加密方式
	buffer.WriteByte(packet.EncryptType)

	// 7. 随机数字段 - V2协议复用为Nonce前4字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		if len(packet.Nonce) != GCM_NONCE_SIZE {
			return nil, fmt.Errorf("Nonce长度错误")
		}
		buffer.Write(packet.Nonce[:4]) // Nonce前4字节
	} else {
		binary.Write(&buffer, binary.BigEndian, packet.RandomNum) // V1协议填0
	}

	// 8. 时间戳
	binary.Write(&buffer, binary.BigEndian, packet.Timestamp)

	// 9. 预留字段 - V2协议复用为Nonce后8字节+认证标签16字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		if len(packet.AuthTag) != GCM_TAG_SIZE {
			return nil, fmt.Errorf("认证标签长度错误")
		}
		buffer.Write(packet.Nonce[4:12]) // Nonce后8字节
		buffer.Write(packet.AuthTag)     // 认证标签16字节
	} else {
		buffer.Write(packet.Reserved[:]) // V1协议填0
	}

	// 10. 消息体
	buffer.Write(packet.MsgBody)

	// 13. 计算并填充长度字段
	totalLen := buffer.Len()
	lengthValue := uint16(totalLen - 2 - 2 + 2) // 减去头部2字节，加上CRC16的2字节
	lengthBytes := buffer.Bytes()
	binary.BigEndian.PutUint16(lengthBytes[lengthPos:lengthPos+2], lengthValue)

	// 14. 计算CRC16校验码
	crcData := lengthBytes[2:] // 从长度字段开始
	crc16 := calculateCRC16(crcData)
	binary.Write(&buffer, binary.BigEndian, crc16)

	return buffer.Bytes(), nil
}

// PacketParser 协议包解析器
type PacketParser struct {
	crypto *SM4GCMCrypto
}

// NewPacketParser 创建协议包解析器
func NewPacketParser(crypto *SM4GCMCrypto) *PacketParser {
	return &PacketParser{crypto: crypto}
}

// ParsePacket 解析协议包
func (pp *PacketParser) ParsePacket(data []byte) (*ProtocolPacket, error) {
	if len(data) < 48 { // 最小包长度
		return nil, fmt.Errorf("数据包长度不足")
	}

	reader := bytes.NewReader(data)
	packet := &ProtocolPacket{}

	// 1. 消息头标识
	binary.Read(reader, binary.BigEndian, &packet.StartChar1)
	binary.Read(reader, binary.BigEndian, &packet.StartChar2)

	// 2. 长度
	binary.Read(reader, binary.BigEndian, &packet.Length)

	// 3. 流水号
	binary.Read(reader, binary.BigEndian, &packet.BillNo)

	// 4. 消息体格式
	binary.Read(reader, binary.BigEndian, &packet.MsgFormat)

	// 5. 协议版本号
	binary.Read(reader, binary.BigEndian, &packet.ProtocolVer)

	// 6. 加密方式
	binary.Read(reader, binary.BigEndian, &packet.EncryptType)

	// 7. 随机数字段 - V2协议复用为Nonce前4字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		packet.Nonce = make([]byte, GCM_NONCE_SIZE)
		reader.Read(packet.Nonce[:4]) // 读取Nonce前4字节
	} else {
		binary.Read(reader, binary.BigEndian, &packet.RandomNum) // V1协议读取随机数
	}

	// 8. 时间戳
	binary.Read(reader, binary.BigEndian, &packet.Timestamp)

	// 9. 预留字段 - V2协议复用为Nonce后8字节+认证标签16字节
	if packet.ProtocolVer == PROTOCOL_VERSION_V2 {
		reader.Read(packet.Nonce[4:12]) // 读取Nonce后8字节
		packet.AuthTag = make([]byte, GCM_TAG_SIZE)
		reader.Read(packet.AuthTag) // 读取认证标签16字节
	} else {
		reader.Read(packet.Reserved[:]) // V1协议读取预留字段
	}

	// 10. 计算消息体长度 - V1和V2协议头部长度相同
	headerSize := 4 + 2 + 1 + 1 + 4 + 8 + 24 // 固定头部长度
	remainingLen := int(packet.Length) - headerSize - 2 // -2 for CRC16



	if remainingLen < 0 {
		return nil, fmt.Errorf("消息体长度计算错误: remainingLen=%d", remainingLen)
	}

	// 12. 消息体
	packet.MsgBody = make([]byte, remainingLen)
	n, err := reader.Read(packet.MsgBody)
	if err != nil || n != remainingLen {
		return nil, fmt.Errorf("读取消息体失败: expected=%d, actual=%d, err=%v", remainingLen, n, err)
	}

	// 11. CRC16校验码
	err = binary.Read(reader, binary.BigEndian, &packet.CRC16)
	if err != nil {
		return nil, fmt.Errorf("读取CRC16失败: %v", err)
	}

	return packet, nil
}

// DecryptPacket 解密协议包
func (pp *PacketParser) DecryptPacket(packet *ProtocolPacket) ([]byte, error) {
	if packet.ProtocolVer != PROTOCOL_VERSION_V2 {
		return packet.MsgBody, nil // 明文协议直接返回
	}

	if packet.EncryptType != ENCRYPTION_SM4_GCM {
		return nil, fmt.Errorf("不支持的加密方式: %d", packet.EncryptType)
	}

	return pp.crypto.Decrypt(packet.MsgBody, packet.AuthTag, packet.Nonce)
}

// calculateCRC16 计算CRC16校验码 (简化实现)
func calculateCRC16(data []byte) uint16 {
	// 这里使用简化的CRC16实现，实际项目中应使用与ccserver相同的算法
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 测试用例
func runTests() {
	fmt.Println("=== SM4-GCM加密解密Demo测试 ===")

	// 1. 初始化密钥
	key := []byte("1234567890123456") // 16字节测试密钥
	crypto, err := NewSM4GCMCrypto(key)
	if err != nil {
		log.Fatalf("创建加密器失败: %v", err)
	}

	// 2. 创建构造器和解析器
	builder := NewPacketBuilder(crypto)
	parser := NewPacketParser(crypto)

	// 3. 测试消息
	testMessage := `{"cmd": 2, "imsi": "460110123456789", "data": {"lat": 39.9042, "lng": 116.4074}}`
	fmt.Printf("原始消息: %s\n", testMessage)

	// 4. 测试加密协议包
	fmt.Println("\n--- 测试加密协议包 ---")
	encryptedPacket, err := builder.BuildEncryptedPacket([]byte(testMessage), false)
	if err != nil {
		log.Fatalf("构造加密包失败: %v", err)
	}
	fmt.Printf("加密包长度: %d 字节\n", len(encryptedPacket))
	fmt.Printf("加密包(hex): %s\n", hex.EncodeToString(encryptedPacket))

	// 5. 解析加密协议包
	parsedPacket, err := parser.ParsePacket(encryptedPacket)
	if err != nil {
		log.Fatalf("解析加密包失败: %v", err)
	}
	fmt.Printf("解析成功 - 协议版本: 0x%02X, 加密方式: 0x%02X\n",
		parsedPacket.ProtocolVer, parsedPacket.EncryptType)

	// 6. 解密消息体
	decryptedMessage, err := parser.DecryptPacket(parsedPacket)
	if err != nil {
		log.Fatalf("解密失败: %v", err)
	}
	fmt.Printf("解密消息: %s\n", string(decryptedMessage))

	// 7. 验证解密结果
	if string(decryptedMessage) == testMessage {
		fmt.Println("✅ 加密解密测试通过!")
	} else {
		fmt.Println("❌ 加密解密测试失败!")
	}

	// 8. 测试明文协议包
	fmt.Println("\n--- 测试明文协议包 ---")
	plaintextPacket, err := builder.BuildPlaintextPacket([]byte(testMessage), true)
	if err != nil {
		log.Fatalf("构造明文包失败: %v", err)
	}
	fmt.Printf("明文包长度: %d 字节\n", len(plaintextPacket))

	// 9. 解析明文协议包
	parsedPlainPacket, err := parser.ParsePacket(plaintextPacket)
	if err != nil {
		log.Fatalf("解析明文包失败: %v", err)
	}
	fmt.Printf("解析成功 - 协议版本: 0x%02X, 加密方式: 0x%02X\n",
		parsedPlainPacket.ProtocolVer, parsedPlainPacket.EncryptType)

	plainMessage, err := parser.DecryptPacket(parsedPlainPacket)
	if err != nil {
		log.Fatalf("处理明文包失败: %v", err)
	}
	fmt.Printf("明文消息: %s\n", string(plainMessage))

	// 10. 性能测试
	fmt.Println("\n--- 性能测试 ---")
	testCount := 1000
	start := time.Now()
	for i := 0; i < testCount; i++ {
		encPkt, _ := builder.BuildEncryptedPacket([]byte(testMessage), false)
		parsedPkt, _ := parser.ParsePacket(encPkt)
		parser.DecryptPacket(parsedPkt)
	}
	elapsed := time.Since(start)
	fmt.Printf("完成 %d 次加密解密操作，耗时: %v\n", testCount, elapsed)
	fmt.Printf("平均每次操作耗时: %v\n", elapsed/time.Duration(testCount))

	fmt.Println("\n=== 测试完成 ===")
}

func main() {
	runTests()
}

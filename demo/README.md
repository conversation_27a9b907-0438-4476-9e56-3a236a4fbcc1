# TCP通信加密协议 - 核心实现

本目录包含TCP通信加密协议的核心参考实现，基于优化后的字段复用方案，实现SM4-GCM认证加密。

## 核心文件

### 1. `sm4_gcm_demo.go`
**Go客户端完整加解密实现**
- 完整的SM4-GCM加密解密流程
- 协议包构造和解析
- 字段复用方案实现（随机数字段+预留字段 → Nonce+认证标签）
- 性能测试和验证

### 2. `python_sm4_gcm_client.py`
**Python客户端加密实现**
- Python版本的SM4-GCM加密实现
- 使用cryptography库的标准SM4-GCM模式
- 与Go服务端完全兼容的协议包构造
- 字段复用方案实现

### 3. `go_server.go`
**Go服务端解密实现**
- 服务端协议包解析和解密
- 支持跨语言兼容性验证
- 字段复用方案的解析逻辑
- 完整的错误处理和日志输出

## 字段复用方案

优化后的V2加密协议使用字段复用，避免新增字段：
- **随机数字段(4字节)** → Nonce前4字节
- **预留字段前8字节** → Nonce后8字节
- **预留字段后16字节** → 认证标签16字节
- **协议包长度**: 与V1明文协议完全相同

## 运行测试

### Go客户端完整流程测试
```bash
go run sm4_gcm_demo.go
```

### 跨语言兼容性测试
```bash
# 1. Python客户端加密
python3 python_sm4_gcm_client.py

# 2. Go服务端解密
go run go_server.go
```

## 技术特性

- ✅ **SM4-GCM认证加密**: 符合GM/T 0002-2012标准
- ✅ **字段复用优化**: 零新增字段，完全向后兼容
- ✅ **跨语言兼容**: Python客户端 ↔ Go服务端
- ✅ **协议包长度**: V1和V2完全相同
- ✅ **安全特性**: 防篡改、防重放、认证加密

## 依赖库

**Python环境**:
```bash
pip install cryptography>=35.0.0
```

**Go环境**:
```bash
go get github.com/tjfoc/gmsm
```

---

这些文件作为TCP通信加密协议的标准参考实现，可直接用于生产环境集成。

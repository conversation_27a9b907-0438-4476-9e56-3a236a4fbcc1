ccserver项目是tcp服务端项目，其中作为接口的功能已弃用
# 概述
* app         ------公共函数（接口相关的功能已弃用）
* boot        ------启动程序和协议解析
* config      ------配置文件
* logs        ------执行日志
* scripts     ------数据备份脚本
* upload      ------文件上传
# 编译和安装
## 运行软件环境
1. 操作系统：
    * windows、linux、macOS均可执行。
    * 支持安装go语言的系统版本均可。
    * x86、x64、arm架构均可。
2. 运行时环境：
    * 在有安装go语言的机器上，执行`go run server.go`即可运行。
    * 在没有安装go语言的机器上，可将代码编译成对应机器系统的可执行文件，然后运行该文件。
3. 库依赖：
    * 无需额外库支持。
4. 中间件：
    * 数据库中间件：mysql连接池，使用gorm框架。
    * 缓存系统：redis。
    * 消息队列：kafka，用于异步通信。
    * 消息服务：emqx，用于消息发送和接收。
5. Web服务器和架构：
    * 使用nginx，用于托管Web应用程序。
6. 开发工具和插件：
    * 集成开发环境（IDE）：GoLand2024.2。
7. 语言和环境管理器：
    * 使用go 1.18版本。
8. 安全性和身份验证：
    * 防火墙：保护系统免受未经授权的访问。
    * 身份验证和授权机制：使用CRC16校验码加密tcp连接。
9. 日志和监控：
    * 使用自开发日志系统用于收集和分析日志。
    * 使用夜莺监控系统，用于监控系统的运行状态和性能。
## 运行硬件环境
1. 处理器（CPU）：
    * 类型、架构等没有硬性要求，建议2核及以上即可。
2. 内存（RAM）：
    * 类型、容量、速度等没有硬性要求，建议4G及以上即可。
3. 存储：
    * 类型、速度等没有硬性要求，建议安装完系统后剩余可用空间在10G及以上。
4. 图形处理器（GPU）：
    * 无要求。
5. 网络：
    * 无要求。
6. 电源：
    * 无要求。
7. 其他硬件：
    * 无要求。
## 服务
1. 安装和运行mysql。
2. 安装和运行redis。
3. 安装和运行kafka。
4. 安装和运行emqx。
# 架构/框架图
![img_1.png](img_1.png)
# 版本说明
############ V1.4.0 2024.12.20 #################

新增SM4-GCM加密通信协议支持，提升TCP通信安全性。

############ V4.5.0 2024.9.2 #################

修改了心跳参数。
# 使用说明
* 需修改config下的config.toml文件，包括服务运行端口、数据库配置、redis配置、kafka配置、心跳清理间隔等基础信息。
* 配置完成后执行`go run server.go`运行，出现服务运行的端口号则代表成功。
![img.png](img.png)
# 使用建议
* 需先检查所需的服务是否全部安装完成并成功运行。
* 根据实际需求修改配置文件和参数。
* 可修改pix_log/log.go文件配置日志输出位置，默认为tcp.log。
# 其他建议
* 开发过程中需注意代码规范。
* 开发过程中尽量保持原有的文件分类格式。
# 常见问题解答
Q: 程序运行后没有输出端口？\
A: 说明所属软件未全部安装或配置错误，可查看tcp.log寻找具体报错原因进行修改。

Q: 连接不上tcp服务？\
A: 可查看最新的通信协议文档进行修改。
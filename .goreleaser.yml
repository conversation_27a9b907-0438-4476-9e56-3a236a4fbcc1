# This is an example goreleaser.yaml file with some sane defaults.
# goreleaser --snapshot --rm-dist
# Make sure to check the documentation at http://goreleaser.com
env:
  - GO111MODULE=on
project_name: server
before:
  hooks:
    # You may remove this if you don't use go modules.
    - go mod download
    # you may remove this if you don't need go generate
    #- go generate ./...
builds:
  - id: "routerserver"
    binary: routerserver
    env:
      - CGO_ENABLED=0
    goos:
      - linux
    goarch:
      - amd64
    # Custom flags templates.
    # Default is empty.
    flags:
      - -tags=dev
      - -v

archives:
  # Archive name template.
  # Defaults:
  # - if format is `tar.gz`, `tar.xz`, `gz` or `zip`:
  #   - `{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ if .Arm }}v{{ .Arm }}{{ end }}{{ if .Mips }}_{{ .Mips }}{{ end }}`
  # - if format is `binary`:
  #   - `{{ .Binary }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ if .Arm }}v{{ .Arm }}{{ end }}{{ if .Mips }}_{{ .Mips }}{{ end }}`
  - name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    replacements:
      linux: linux
      amd64: x86_64
    files:
      - config/config.toml
      - data/router.sql
      - data/init_data.sql
      - data/1.2.4_1.2.5.sql
      - data/1.2.5_1.2.6.sql
      - data/1.2.6_1.3.0.sql
      - data/1.3.0_1.3.5.sql
      - data/changelog.sql
      - upload/docs/终端列表导入模板.xlsx
      - upload/docs/广播IP列表导入模板.xlsx
      - router-server.service
      - scripts/*
      - docs/*

checksum:
  name_template: 'checksums.txt'
snapshot:
#  name_template: "{{ .Tag }}-next"
  name_template: "snapshot-{{.ShortCommit}}"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
      -

// 设备状态追踪系统 JavaScript

// 模拟数据生成器
class DeviceDataGenerator {
    constructor() {
        this.devicePrefixes = ['RoboBusMS', 'RoboBusRS', 'RoboBusRR', 'RoboBus'];
        this.locations = [
            { name: '深圳南山科技园', lat: 22.5431, lng: 113.9344 },
            { name: '深圳福田中心区', lat: 22.5329, lng: 114.0577 },
            { name: '深圳宝安机场', lat: 22.6390, lng: 113.8110 },
            { name: '深圳龙华新区', lat: 22.6569, lng: 114.0298 },
            { name: '深圳坪山新区', lat: 22.6947, lng: 114.3373 },
            { name: '深圳龙岗中心城', lat: 22.7167, lng: 114.2500 }
        ];
        this.taskStations = [
            '起始站A', '中转站B', '目标站C', '服务站D', '充电站E', '维修站F'
        ];
        this.workModes = {
            0: '停止',
            1: '手动',
            2: '遥控',
            3: '自动'
        };
    }

    // 生成随机IMSI
    generateIMSI() {
        const prefix = this.devicePrefixes[Math.floor(Math.random() * this.devicePrefixes.length)];
        const number = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
        return `${prefix}${number}`;
    }

    // 生成随机位置
    generateLocation() {
        const location = this.locations[Math.floor(Math.random() * this.locations.length)];
        return {
            name: location.name,
            lat: location.lat + (Math.random() - 0.5) * 0.01, // 添加小范围随机偏移
            lng: location.lng + (Math.random() - 0.5) * 0.01
        };
    }

    // 生成任务信息
    generateTaskInfo() {
        const taskId = Math.floor(Math.random() * 1000);
        const step = Math.floor(Math.random() * 3); // 0=空闲, 1=执行中, 2=完成
        const currentStation = this.taskStations[Math.floor(Math.random() * this.taskStations.length)];
        const targetStation = this.taskStations[Math.floor(Math.random() * this.taskStations.length)];
        
        return {
            taskId: taskId,
            step: step,
            progress: step === 0 ? 0 : Math.floor(Math.random() * 100),
            leftTime: step === 1 ? Math.floor(Math.random() * 3600) : 0, // 剩余时间（秒）
            currentStation: currentStation,
            targetStation: targetStation,
            taskDistance: Math.floor(Math.random() * 50000), // 任务距离（米）
            startTime: step > 0 ? Date.now() - Math.floor(Math.random() * 7200000) : 0 // 开始时间
        };
    }

    // 生成单个设备数据
    generateDevice(index) {
        const imsi = this.generateIMSI();
        const location = this.generateLocation();
        const taskInfo = this.generateTaskInfo();
        const isOnline = Math.random() > 0.2; // 80%概率在线
        const lastHeartbeat = isOnline 
            ? Date.now() - Math.floor(Math.random() * 300000) // 5分钟内
            : Date.now() - Math.floor(Math.random() * 1800000 + 300000); // 5-35分钟前

        return {
            id: index + 1,
            imsi: imsi,
            deviceName: `设备-${imsi}`,
            currentStatus: isOnline ? 1 : 0,
            lastHeartbeatTime: new Date(lastHeartbeat),
            onlineDuration: isOnline ? Math.floor(Math.random() * 86400) : 0, // 在线时长（秒）
            position: {
                latitude: location.lat,
                longitude: location.lng,
                locationName: location.name
            },
            speed: isOnline ? Math.floor(Math.random() * 60) : 0, // 速度 km/h
            mileage: Math.floor(Math.random() * 50000), // 里程 km
            workMode: Math.floor(Math.random() * 4), // 工作模式
            taskStatus: taskInfo,
            heartbeatDelay: isOnline ? Math.floor(Math.random() * 10) : -1, // 心跳延迟（秒）
            deviceType: Math.floor(Math.random() * 3), // 设备类型
            batteryLevel: Math.floor(Math.random() * 100), // 电池电量
            signalStrength: isOnline ? Math.floor(Math.random() * 5) + 1 : 0, // 信号强度
            temperature: Math.floor(Math.random() * 40) + 10, // 温度
            errorCount: Math.floor(Math.random() * 5), // 错误计数
            lastMaintenanceTime: new Date(Date.now() - Math.floor(Math.random() * 2592000000)), // 最后维护时间
            firmwareVersion: `v${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`
        };
    }

    // 生成设备列表
    generateDeviceList(count = 50) {
        const devices = [];
        for (let i = 0; i < count; i++) {
            devices.push(this.generateDevice(i));
        }
        return devices;
    }
}

// 全局变量
let deviceData = [];
let filteredData = [];
let currentPage = 1;
let pageSize = 20;
let autoRefreshInterval = null;
const dataGenerator = new DeviceDataGenerator();

// WebSocket相关变量
let websocket = null;
let reconnectAttempts = 0;
let maxReconnectAttempts = 5;
let reconnectDelay = 1000; // 初始重连延迟1秒

// API配置
const API_BASE_URL = '/api/v1';
const API_ENDPOINTS = {
    devices: `${API_BASE_URL}/devices`,
    deviceDetail: (imsi) => `${API_BASE_URL}/devices/${imsi}`,
    deviceHistory: (imsi) => `${API_BASE_URL}/devices/${imsi}/history`,
    health: `${API_BASE_URL}/health`
};

// WebSocket配置
const WS_URL = `ws://${window.location.host}/ws/devices`;

// 页面状态管理
const pageState = {
    searchTerm: '',
    statusFilter: '',
    startTime: '',
    endTime: '',
    autoRefresh: true,
    sortColumn: '',
    sortDirection: 'asc'
};

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadDeviceData();
    setupEventListeners();
    startAutoRefresh();
});

// 初始化页面
function initializePage() {
    // 设置默认时间范围（最近24小时）
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    document.getElementById('startTime').value = formatDateTimeLocal(yesterday);
    document.getElementById('endTime').value = formatDateTimeLocal(now);
    
    // 更新最后更新时间
    updateLastUpdateTime();
}

// 设置事件监听器
function setupEventListeners() {
    // 搜索框
    document.getElementById('imsiSearch').addEventListener('input', handleSearch);
    
    // 状态过滤
    document.getElementById('statusFilter').addEventListener('change', handleSearch);
    
    // 时间范围
    document.getElementById('startTime').addEventListener('change', handleSearch);
    document.getElementById('endTime').addEventListener('change', handleSearch);
    
    // 自动刷新开关
    document.getElementById('autoRefresh').addEventListener('change', handleAutoRefreshToggle);
    
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', refreshData);
    
    // 导出按钮
    document.getElementById('exportBtn').addEventListener('click', exportData);
    
    // 分页
    document.getElementById('prevPage').addEventListener('click', () => changePage(currentPage - 1));
    document.getElementById('nextPage').addEventListener('click', () => changePage(currentPage + 1));
    document.getElementById('pageSize').addEventListener('change', handlePageSizeChange);
}

// 加载设备数据
async function loadDeviceData() {
    showLoading(true);

    try {
        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            page_size: pageSize
        });

        // 添加过滤参数
        if (pageState.searchTerm) {
            params.append('imsi', pageState.searchTerm);
        }
        if (pageState.statusFilter) {
            params.append('status', pageState.statusFilter);
        }
        if (pageState.startTime) {
            params.append('start_time', pageState.startTime);
        }
        if (pageState.endTime) {
            params.append('end_time', pageState.endTime);
        }

        // 调用API
        const response = await fetch(`${API_ENDPOINTS.devices}?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        if (result.code !== 200) {
            throw new Error(result.message || '获取设备数据失败');
        }

        // 更新数据
        deviceData = result.data.devices || [];
        filteredData = deviceData; // API已经过滤了数据

        // 更新统计信息
        updateStatisticsFromAPI(result.data.stats);

        // 渲染界面
        renderDeviceTable();
        renderPagination(result.data.total);
        showLoading(false);
        updateLastUpdateTime();

        console.log(`[API] 成功加载 ${deviceData.length} 个设备`);

    } catch (error) {
        console.error('[API] 加载设备数据失败:', error);
        showError('加载设备数据失败: ' + error.message);
        showLoading(false);

        // 显示错误状态
        document.getElementById('errorState').classList.remove('hidden');
        document.getElementById('emptyState').classList.add('hidden');
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const tableBody = document.getElementById('deviceTableBody');
    
    if (show) {
        loadingState.classList.remove('hidden');
        emptyState.classList.add('hidden');
        tableBody.innerHTML = '';
    } else {
        loadingState.classList.add('hidden');
        if (filteredData.length === 0) {
            emptyState.classList.remove('hidden');
        } else {
            emptyState.classList.add('hidden');
        }
    }
}

// 应用过滤器（现在由API处理，这里主要用于更新页面状态）
function applyFilters() {
    // 更新页面状态
    pageState.searchTerm = document.getElementById('imsiSearch').value;
    pageState.statusFilter = document.getElementById('statusFilter').value;
    pageState.startTime = document.getElementById('startTime').value;
    pageState.endTime = document.getElementById('endTime').value;

    // 重置到第一页
    currentPage = 1;

    // 重新加载数据
    loadDeviceData();
}

// 从API响应更新统计信息
function updateStatisticsFromAPI(stats) {
    if (!stats) return;

    // 显示加载状态
    showStatLoading(true);

    // 模拟加载延迟
    setTimeout(() => {
        // 更新数值
        document.getElementById('totalDevicesValue').textContent = stats.total_devices || 0;
        document.getElementById('onlineDevicesValue').textContent = stats.online_devices || 0;
        document.getElementById('offlineDevicesValue').textContent = stats.offline_devices || 0;
        document.getElementById('errorDevicesValue').textContent = stats.error_devices || 0;
        document.getElementById('onlineRate').textContent = `${stats.online_rate || 0}%`;
        document.getElementById('offlineRate').textContent = `${stats.offline_rate || 0}%`;

        // 隐藏加载状态
        showStatLoading(false);
    }, 300);
}

// 更新统计信息（兼容旧版本）
function updateStatistics() {
    const total = filteredData.length;
    const online = filteredData.filter(d => d.current_status === 1).length;
    const offline = filteredData.filter(d => d.current_status === 0).length;
    const error = filteredData.filter(d => d.error_count > 0).length;

    const onlineRate = total > 0 ? Math.round((online / total) * 100) : 0;
    const offlineRate = total > 0 ? Math.round((offline / total) * 100) : 0;

    const stats = {
        total_devices: total,
        online_devices: online,
        offline_devices: offline,
        error_devices: error,
        online_rate: onlineRate,
        offline_rate: offlineRate
    };

    updateStatisticsFromAPI(stats);
}

// 显示/隐藏统计加载状态
function showStatLoading(show) {
    const loadingElements = ['totalDevicesLoading', 'onlineDevicesLoading', 'offlineDevicesLoading', 'errorDevicesLoading'];
    const valueElements = ['totalDevicesValue', 'onlineDevicesValue', 'offlineDevicesValue', 'errorDevicesValue'];

    loadingElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.toggle('hidden', !show);
        }
    });

    valueElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.toggle('hidden', show);
        }
    });
}

// 渲染设备表格
function renderDeviceTable() {
    const tableBody = document.getElementById('deviceTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-8 text-base-content/50">暂无数据</td></tr>';
        return;
    }
    
    tableBody.innerHTML = pageData.map(device => `
        <tr class="hover">
            <td class="font-mono text-sm" data-label="IMSI">${device.imsi}</td>
            <td data-label="状态">
                <div class="badge ${device.current_status === 1 ? 'badge-success' : 'badge-error'} badge-sm status-indicator ${device.current_status === 1 ? 'online' : 'offline'}">
                    ${device.current_status === 1 ? '在线' : '离线'}
                </div>
            </td>
            <td class="text-sm" data-label="最后心跳" title="${formatDateTime(device.last_heartbeat_time)}">${formatRelativeTime(new Date(device.last_heartbeat_time))}</td>
            <td class="text-sm" data-label="位置" title="${device.position.latitude.toFixed(6)}, ${device.position.longitude.toFixed(6)}">
                <div class="flex items-center gap-1">
                    <i class="fas fa-map-marker-alt text-xs text-primary"></i>
                    ${device.position.location_name}
                </div>
            </td>
            <td class="text-sm" data-label="速度">
                <div class="flex items-center gap-1">
                    <i class="fas fa-tachometer-alt text-xs ${device.speed > 0 ? 'text-success' : 'text-base-content/50'}"></i>
                    ${device.speed} km/h
                </div>
            </td>
            <td class="text-sm" data-label="里程">
                <div class="flex items-center gap-1">
                    <i class="fas fa-road text-xs text-info"></i>
                    ${device.mileage} km
                </div>
            </td>
            <td data-label="任务状态">
                <div class="badge ${getTaskStatusBadgeClass(device.task_status.step)} badge-sm">
                    ${getTaskStatusText(device.task_status.step)}
                </div>
            </td>
            <td class="text-sm" data-label="工作模式">
                <div class="flex items-center gap-1">
                    <i class="fas ${getWorkModeIcon(device.work_mode)} text-xs"></i>
                    ${getWorkModeText(device.work_mode)}
                </div>
            </td>
            <td data-label="操作">
                <div class="flex gap-1">
                    <button class="btn btn-ghost btn-xs" onclick="showDeviceDetail('${device.imsi}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-ghost btn-xs" onclick="refreshDeviceData('${device.imsi}')" title="刷新数据">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 工具函数
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

function formatRelativeTime(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}小时前`;
    
    const days = Math.floor(hours / 24);
    return `${days}天前`;
}

function getTaskStatusText(step) {
    const statusMap = { 0: '空闲', 1: '执行中', 2: '已完成' };
    return statusMap[step] || '未知';
}

function getTaskStatusBadgeClass(step) {
    const classMap = { 0: 'badge-ghost', 1: 'badge-warning', 2: 'badge-success' };
    return classMap[step] || 'badge-ghost';
}

function updateLastUpdateTime() {
    document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString('zh-CN');
}

function getWorkModeIcon(mode) {
    const iconMap = {
        0: 'fa-stop-circle',
        1: 'fa-hand-paper',
        2: 'fa-gamepad',
        3: 'fa-robot'
    };
    return iconMap[mode] || 'fa-question-circle';
}

function getWorkModeText(mode) {
    const modeMap = {
        0: '停止',
        1: '手动',
        2: '遥控',
        3: '自动'
    };
    return modeMap[mode] || '未知';
}

function formatDateTime(dateStr) {
    if (!dateStr) return '--';
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
}

// 显示错误信息
function showError(message) {
    showToast(message, 'error');
}

// API请求封装
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        if (result.code !== 200) {
            throw new Error(result.message || '请求失败');
        }

        return result.data;
    } catch (error) {
        console.error('[API] 请求失败:', error);
        throw error;
    }
}

// 排序功能
function sortTable(column) {
    if (pageState.sortColumn === column) {
        pageState.sortDirection = pageState.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        pageState.sortColumn = column;
        pageState.sortDirection = 'asc';
    }

    filteredData.sort((a, b) => {
        let valueA, valueB;

        switch (column) {
            case 'imsi':
                valueA = a.imsi;
                valueB = b.imsi;
                break;
            case 'status':
                valueA = a.currentStatus;
                valueB = b.currentStatus;
                break;
            case 'heartbeat':
                valueA = a.lastHeartbeatTime;
                valueB = b.lastHeartbeatTime;
                break;
            case 'speed':
                valueA = a.speed;
                valueB = b.speed;
                break;
            case 'mileage':
                valueA = a.mileage;
                valueB = b.mileage;
                break;
            default:
                return 0;
        }

        if (valueA < valueB) return pageState.sortDirection === 'asc' ? -1 : 1;
        if (valueA > valueB) return pageState.sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    renderDeviceTable();
    updateSortIcons();
}

// 更新排序图标
function updateSortIcons() {
    // 重置所有排序图标
    document.querySelectorAll('.fa-sort, .fa-sort-up, .fa-sort-down').forEach(icon => {
        icon.className = 'fas fa-sort text-xs opacity-50 cursor-pointer';
    });

    // 更新当前排序列的图标
    if (pageState.sortColumn) {
        const sortIcon = document.querySelector(`[onclick="sortTable('${pageState.sortColumn}')"] i`);
        if (sortIcon) {
            sortIcon.className = `fas fa-sort-${pageState.sortDirection === 'asc' ? 'up' : 'down'} text-xs text-primary cursor-pointer`;
        }
    }
}

// 刷新单个设备数据
async function refreshDeviceData(imsi) {
    try {
        // 重新加载所有数据（简化版本）
        await loadDeviceData();

        // 显示成功提示
        showToast(`设备 ${imsi} 数据已刷新`, 'success');
    } catch (error) {
        console.error('[API] 刷新设备数据失败:', error);
        showError('刷新设备数据失败: ' + error.message);
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} fixed top-4 right-4 w-auto z-50 shadow-lg`;
    toast.innerHTML = `
        <div class="flex items-center gap-2">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 事件处理函数
function handleSearch() {
    applyFilters(); // 这会更新页面状态并重新加载数据
}

function handleAutoRefreshToggle() {
    pageState.autoRefresh = document.getElementById('autoRefresh').checked;
    if (pageState.autoRefresh) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
}

function refreshData() {
    currentPage = 1; // 重置到第一页
    loadDeviceData();
}

function exportData() {
    // 模拟导出功能
    alert('导出功能开发中...');
}

function startAutoRefresh() {
    if (autoRefreshInterval) clearInterval(autoRefreshInterval);
    if (pageState.autoRefresh) {
        autoRefreshInterval = setInterval(() => {
            // 重新加载数据
            loadDeviceData();
        }, 10000); // 10秒刷新
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// 分页相关函数
function changePage(page) {
    if (page < 1) return;

    currentPage = page;
    loadDeviceData(); // 重新加载数据
}

function handlePageSizeChange() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    currentPage = 1;
    loadDeviceData(); // 重新加载数据
}

function renderPagination(totalCount = filteredData.length) {
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, totalCount);

    // 更新页面信息
    document.getElementById('pageInfo').textContent = `${startIndex}-${endIndex}`;
    document.getElementById('totalCount').textContent = totalCount;
    
    // 更新分页按钮状态
    document.getElementById('prevPage').disabled = currentPage === 1;
    document.getElementById('nextPage').disabled = currentPage === totalPages;
    
    // 生成页码按钮
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';
    
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const button = document.createElement('button');
        button.className = `join-item btn btn-sm ${i === currentPage ? 'btn-active' : ''}`;
        button.textContent = i;
        button.onclick = () => changePage(i);
        pageNumbers.appendChild(button);
    }
}

// 显示设备详情
async function showDeviceDetail(imsi) {
    try {
        // 从API获取设备详情
        const device = await apiRequest(API_ENDPOINTS.deviceDetail(imsi));

        const modal = document.getElementById('deviceDetailModal');
        const content = document.getElementById('deviceDetailContent');

        content.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="card bg-base-200">
                <div class="card-body">
                    <h4 class="card-title text-lg">基本信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">IMSI:</span>
                            <span class="font-mono">${device.imsi}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">设备名称:</span>
                            <span>${device.device_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">固件版本:</span>
                            <span>${device.firmware_version}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">最后维护:</span>
                            <span>${formatDateTime(device.last_maintenance_time)}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实时状态 -->
            <div class="card bg-base-200">
                <div class="card-body">
                    <h4 class="card-title text-lg">实时状态</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-base-content/70">当前状态:</span>
                            <div class="badge ${device.current_status === 1 ? 'badge-success' : 'badge-error'}">
                                ${device.current_status === 1 ? '在线' : '离线'}
                            </div>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">最后心跳:</span>
                            <span>${formatDateTime(device.last_heartbeat_time)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">电池电量:</span>
                            <span>${device.battery_level}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">信号强度:</span>
                            <span>${device.signal_strength}/5</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 位置信息 -->
            <div class="card bg-base-200">
                <div class="card-body">
                    <h4 class="card-title text-lg">位置信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">当前位置:</span>
                            <span>${device.position.location_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">GPS坐标:</span>
                            <span class="font-mono text-sm">${device.position.latitude.toFixed(6)}, ${device.position.longitude.toFixed(6)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">当前速度:</span>
                            <span>${device.speed} km/h</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">总里程:</span>
                            <span>${device.mileage} km</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 任务信息 -->
            <div class="card bg-base-200">
                <div class="card-body">
                    <h4 class="card-title text-lg">任务信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-base-content/70">任务状态:</span>
                            <div class="badge ${getTaskStatusBadgeClass(device.task_status.step)}">
                                ${getTaskStatusText(device.task_status.step)}
                            </div>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务ID:</span>
                            <span>${device.task_status.task_id || '无'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">当前站点:</span>
                            <span>${device.task_status.current_station || '无'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">目标站点:</span>
                            <span>${device.task_status.target_station || '无'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">任务进度:</span>
                            <span>${device.task_status.progress}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 设备状态历史时间线 -->
        <div class="mt-6">
            <h4 class="text-lg font-bold mb-4">设备状态历史</h4>
            <ul class="timeline timeline-vertical timeline-compact">
                <!-- 设备在线状态 -->
                <li>
                    <div class="timeline-start text-sm text-base-content/70">
                        ${formatDateTime(device.last_heartbeat_time)}
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 ${device.current_status === 1 ? 'text-success' : 'text-error'}">
                            ${device.current_status === 1 ?
                                '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />' :
                                '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />'
                            }
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">${device.current_status === 1 ? '设备在线' : '设备离线'}</div>
                        <div class="text-sm text-base-content/70">
                            最后心跳: ${formatRelativeTime(new Date(device.last_heartbeat_time))}
                        </div>
                    </div>
                    <hr class="${device.current_status === 1 ? 'bg-success' : 'bg-error'}" />
                </li>

                <!-- 位置更新 -->
                ${device.position.latitude !== 0 || device.position.longitude !== 0 ? `
                <li>
                    <hr class="bg-info" />
                    <div class="timeline-start text-sm text-base-content/70">
                        刚刚
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-info">
                            <path fill-rule="evenodd" d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.757.433 5.741 5.741 0 00.281.14l.018.008.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">位置更新</div>
                        <div class="text-sm text-base-content/70">
                            ${device.position.location_name}
                        </div>
                        <div class="text-xs text-base-content/50">
                            速度: ${device.speed >= 0 ? device.speed + ' km/h' : '未知'}
                        </div>
                    </div>
                    <hr class="bg-info" />
                </li>
                ` : ''}

                <!-- 任务状态 -->
                ${device.task_status.task_id > 0 ? `
                <li>
                    <hr class="${device.task_status.step === 2 ? 'bg-success' : device.task_status.step === 1 ? 'bg-warning' : 'bg-base-content/30'}" />
                    <div class="timeline-start text-sm text-base-content/70">
                        任务进行中
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="w-5 h-5 ${device.task_status.step === 2 ? 'text-success' : device.task_status.step === 1 ? 'text-warning' : 'text-base-content/30'}">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">任务 #${device.task_status.task_id}</div>
                        <div class="text-sm text-base-content/70">
                            ${getTaskStatusText(device.task_status.step)} - ${device.task_status.progress}%
                        </div>
                        ${device.task_status.current_station ? `
                        <div class="text-xs text-base-content/50">
                            ${device.task_status.current_station} → ${device.task_status.target_station}
                        </div>
                        ` : ''}
                    </div>
                    <hr class="${device.task_status.step === 2 ? 'bg-success' : device.task_status.step === 1 ? 'bg-warning' : 'bg-base-content/30'}" />
                </li>
                ` : `
                <li>
                    <hr class="bg-base-content/30" />
                    <div class="timeline-start text-sm text-base-content/70">
                        当前状态
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-base-content/30">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 000-1.5h-3.25V5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">空闲状态</div>
                        <div class="text-sm text-base-content/70">
                            工作模式: ${getWorkModeText(device.work_mode)}
                        </div>
                    </div>
                    <hr class="bg-base-content/30" />
                </li>
                `}

                <!-- 设备信息 -->
                <li>
                    <hr class="bg-primary" />
                    <div class="timeline-start text-sm text-base-content/70">
                        设备信息
                    </div>
                    <div class="timeline-middle">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-primary">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="timeline-end timeline-box">
                        <div class="font-medium">设备状态</div>
                        <div class="text-sm text-base-content/70 space-y-1">
                            <div>电池: ${device.battery_level}%</div>
                            <div>信号: ${device.signal_strength}/5</div>
                            <div>温度: ${device.temperature}°C</div>
                            <div>版本: ${device.firmware_version}</div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        `;

        modal.showModal();

    } catch (error) {
        console.error('[API] 获取设备详情失败:', error);
        showError('获取设备详情失败: ' + error.message);
    }
}

// ==================== WebSocket 实时更新功能 ====================

// 初始化WebSocket连接
function initWebSocket() {
    if (websocket && websocket.readyState === WebSocket.OPEN) {
        return; // 已经连接
    }

    console.log('[WebSocket] 正在连接到:', WS_URL);
    websocket = new WebSocket(WS_URL);

    websocket.onopen = function(event) {
        console.log('[WebSocket] 连接已建立');
        reconnectAttempts = 0;
        reconnectDelay = 1000;

        // 显示连接状态
        showConnectionStatus(true);
    };

    websocket.onmessage = function(event) {
        try {
            const message = JSON.parse(event.data);
            handleWebSocketMessage(message);
        } catch (error) {
            console.error('[WebSocket] 解析消息失败:', error, event.data);
        }
    };

    websocket.onclose = function(event) {
        console.log('[WebSocket] 连接已关闭:', event.code, event.reason);
        websocket = null;

        // 显示断开状态
        showConnectionStatus(false);

        // 尝试重连
        if (reconnectAttempts < maxReconnectAttempts) {
            setTimeout(() => {
                reconnectAttempts++;
                reconnectDelay = Math.min(reconnectDelay * 2, 30000); // 最大30秒
                console.log(`[WebSocket] 尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                initWebSocket();
            }, reconnectDelay);
        } else {
            console.error('[WebSocket] 重连次数已达上限，停止重连');
            showError('实时连接已断开，请刷新页面重试');
        }
    };

    websocket.onerror = function(error) {
        console.error('[WebSocket] 连接错误:', error);
    };
}

// 处理WebSocket消息
function handleWebSocketMessage(message) {
    console.log('[WebSocket] 收到消息:', message.type);

    switch (message.type) {
        case 'device_list_update':
            handleDeviceListUpdate(message.data);
            break;
        case 'device_detail_update':
            handleDeviceDetailUpdate(message.data);
            break;
        case 'device_history_update':
            handleDeviceHistoryUpdate(message.data);
            break;
        case 'system_status_update':
            handleSystemStatusUpdate(message.data);
            break;
        default:
            console.warn('[WebSocket] 未知消息类型:', message.type);
    }
}

// 处理设备列表更新
function handleDeviceListUpdate(data) {
    deviceData = data.devices || [];
    filteredData = deviceData;

    // 更新统计信息
    updateStatisticsFromAPI(data.stats);

    // 重新渲染界面
    renderDeviceTable();
    renderPagination(data.total);
    updateLastUpdateTime();

    console.log(`[WebSocket] 设备列表已更新: ${deviceData.length} 个设备`);
}

// 处理设备详情更新
function handleDeviceDetailUpdate(data) {
    // 如果当前打开的详情弹窗是这个设备，更新弹窗内容
    const modal = document.getElementById('deviceDetailModal');
    if (modal && modal.open) {
        const modalTitle = modal.querySelector('h3');
        if (modalTitle && modalTitle.textContent.includes(data.imsi)) {
            // 重新显示设备详情
            showDeviceDetail(data.device);
        }
    }

    console.log(`[WebSocket] 设备详情已更新: ${data.imsi}`);
}

// 处理设备历史更新
function handleDeviceHistoryUpdate(data) {
    console.log(`[WebSocket] 设备历史已更新: ${data.imsi}`);
    // 这里可以添加历史数据更新的逻辑
}

// 处理系统状态更新
function handleSystemStatusUpdate(data) {
    console.log('[WebSocket] 系统状态已更新:', data);
    // 这里可以添加系统状态更新的逻辑
}

// 显示连接状态
function showConnectionStatus(connected) {
    // 可以在页面上添加一个连接状态指示器
    const statusIndicator = document.getElementById('connectionStatus');
    if (statusIndicator) {
        statusIndicator.className = connected ? 'badge badge-success' : 'badge badge-error';
        statusIndicator.textContent = connected ? '实时连接' : '连接断开';
    }
}

// 关闭WebSocket连接
function closeWebSocket() {
    if (websocket) {
        websocket.close();
        websocket = null;
    }
}

// 修改初始化函数，启用WebSocket而不是定时刷新
function initializePage() {
    // 初始加载数据
    loadDeviceData();

    // 启动WebSocket连接
    initWebSocket();

    // 页面卸载时关闭WebSocket
    window.addEventListener('beforeunload', function() {
        closeWebSocket();
    });
}

/**
 * 工具函数模块
 * 
 * 职责: 提供通用工具函数，包括时间处理、状态转换、数据验证等
 * 依赖: config.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { DEVICE_CONFIG, THEME_CONFIG } from './config.js';

// ==================== 时间工具函数 ====================
export const DateUtils = {
    /**
     * 格式化日期时间
     * @param {Date|string} date 日期对象或字符串
     * @param {string} format 格式化模式 ('datetime', 'date', 'time', 'relative')
     * @returns {string} 格式化后的字符串
     */
    format(date, format = 'datetime') {
        if (!date || date === '0001-01-01T00:00:00Z') {
            return '未知';
        }

        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime())) {
            return '无效日期';
        }

        switch (format) {
            case 'datetime':
                return dateObj.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            case 'date':
                return dateObj.toLocaleDateString('zh-CN');
            case 'time':
                return dateObj.toLocaleTimeString('zh-CN');
            case 'relative':
                return this.getRelativeTime(dateObj);
            default:
                return dateObj.toString();
        }
    },

    /**
     * 获取相对时间描述
     * @param {Date} date 日期对象
     * @returns {string} 相对时间描述
     */
    getRelativeTime(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        return this.format(date, 'date');
    },

    /**
     * 检查日期是否在指定范围内
     * @param {Date} date 要检查的日期
     * @param {Date} startDate 开始日期
     * @param {Date} endDate 结束日期
     * @returns {boolean} 是否在范围内
     */
    isInRange(date, startDate, endDate) {
        const checkDate = new Date(date);
        return checkDate >= startDate && checkDate <= endDate;
    }
};

// ==================== 状态工具函数 ====================
export const StatusUtils = {
    /**
     * 获取设备状态文本
     * @param {number} status 状态码
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        switch (status) {
            case DEVICE_CONFIG.STATUS.ONLINE:
                return '在线';
            case DEVICE_CONFIG.STATUS.OFFLINE:
                return '离线';
            default:
                return '未知';
        }
    },

    /**
     * 获取设备状态图标
     * @param {number} status 状态码
     * @returns {string} 图标类名
     */
    getStatusIcon(status) {
        switch (status) {
            case DEVICE_CONFIG.STATUS.ONLINE:
                return 'fas fa-circle text-success';
            case DEVICE_CONFIG.STATUS.OFFLINE:
                return 'fas fa-circle text-error';
            default:
                return 'fas fa-question-circle text-warning';
        }
    },

    /**
     * 获取设备状态颜色
     * @param {number} status 状态码
     * @returns {string} 颜色值
     */
    getStatusColor(status) {
        switch (status) {
            case DEVICE_CONFIG.STATUS.ONLINE:
                return THEME_CONFIG.COLORS.STATUS.ONLINE;
            case DEVICE_CONFIG.STATUS.OFFLINE:
                return THEME_CONFIG.COLORS.STATUS.OFFLINE;
            default:
                return THEME_CONFIG.COLORS.STATUS.WARNING;
        }
    },

    /**
     * 获取工作模式文本
     * @param {number} mode 工作模式码
     * @returns {string} 工作模式文本
     */
    getWorkModeText(mode) {
        switch (mode) {
            case DEVICE_CONFIG.WORK_MODE.STANDBY:
                return '待机';
            case DEVICE_CONFIG.WORK_MODE.RUNNING:
                return '运行';
            case DEVICE_CONFIG.WORK_MODE.MAINTENANCE:
                return '维护';
            case DEVICE_CONFIG.WORK_MODE.ERROR:
                return '故障';
            default:
                return '未知';
        }
    },

    /**
     * 获取工作模式图标
     * @param {number} mode 工作模式码
     * @returns {string} 图标类名
     */
    getWorkModeIcon(mode) {
        switch (mode) {
            case DEVICE_CONFIG.WORK_MODE.STANDBY:
                return 'fas fa-pause';
            case DEVICE_CONFIG.WORK_MODE.RUNNING:
                return 'fas fa-play';
            case DEVICE_CONFIG.WORK_MODE.MAINTENANCE:
                return 'fas fa-wrench';
            case DEVICE_CONFIG.WORK_MODE.ERROR:
                return 'fas fa-exclamation-triangle';
            default:
                return 'fas fa-question';
        }
    },

    /**
     * 获取任务状态文本
     * @param {number} status 任务状态码
     * @returns {string} 任务状态文本
     */
    getTaskStatusText(status) {
        switch (status) {
            case DEVICE_CONFIG.TASK_STATUS.IDLE:
                return '空闲';
            case DEVICE_CONFIG.TASK_STATUS.RUNNING:
                return '执行中';
            case DEVICE_CONFIG.TASK_STATUS.COMPLETED:
                return '已完成';
            case DEVICE_CONFIG.TASK_STATUS.FAILED:
                return '失败';
            default:
                return '未知';
        }
    }
};

// ==================== DOM工具函数 ====================
export const DOMUtils = {
    /**
     * 显示/隐藏加载状态
     * @param {boolean} show 是否显示
     * @param {string} containerId 容器ID
     */
    showLoading(show, containerId = 'loadingState') {
        const element = document.getElementById(containerId);
        if (element) {
            element.classList.toggle('hidden', !show);
        }
    },

    /**
     * 显示错误信息
     * @param {string} message 错误消息
     * @param {number} duration 显示时长(ms)
     */
    showError(message, duration = 5000) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-error fixed top-4 right-4 z-50 max-w-md';
        errorDiv.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>${message}</span>
        `;

        document.body.appendChild(errorDiv);

        // 自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, duration);
    },

    /**
     * 显示成功信息
     * @param {string} message 成功消息
     * @param {number} duration 显示时长(ms)
     */
    showSuccess(message, duration = 3000) {
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success fixed top-4 right-4 z-50 max-w-md';
        successDiv.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>${message}</span>
        `;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, duration);
    },

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const element = document.getElementById('lastUpdateTime');
        if (element) {
            element.textContent = DateUtils.format(new Date(), 'datetime');
        }
    },

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} delay 延迟时间(ms)
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} delay 延迟时间(ms)
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
};

// ==================== 数据工具函数 ====================
export const DataUtils = {
    /**
     * 验证IMSI格式
     * @param {string} imsi IMSI字符串
     * @returns {boolean} 是否有效
     */
    validateIMSI(imsi) {
        return typeof imsi === 'string' && imsi.length > 0 && /^[a-zA-Z0-9_-]+$/.test(imsi);
    },

    /**
     * 格式化数字
     * @param {number} num 数字
     * @param {number} decimals 小数位数
     * @returns {string} 格式化后的字符串
     */
    formatNumber(num, decimals = 0) {
        if (typeof num !== 'number' || isNaN(num)) {
            return '0';
        }
        return num.toFixed(decimals);
    },

    /**
     * 格式化百分比
     * @param {number} value 值
     * @param {number} total 总数
     * @returns {string} 百分比字符串
     */
    formatPercentage(value, total) {
        if (total === 0) return '0%';
        return `${Math.round((value / total) * 100)}%`;
    },

    /**
     * 深拷贝对象
     * @param {*} obj 要拷贝的对象
     * @returns {*} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
        return obj;
    },

    /**
     * 安全的JSON解析
     * @param {string} jsonString JSON字符串
     * @param {*} defaultValue 默认值
     * @returns {*} 解析结果
     */
    safeJSONParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            console.warn('[DataUtils] JSON解析失败:', error);
            return defaultValue;
        }
    }
};

// ==================== 默认导出 ====================
export default {
    DateUtils,
    StatusUtils,
    DOMUtils,
    DataUtils
};

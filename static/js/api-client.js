/**
 * API客户端模块
 * 
 * 职责: 封装所有API调用逻辑，提供统一的数据获取接口
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { API_CONFIG } from './config.js';
import { DOMUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';

// ==================== API响应接口定义 ====================
/**
 * 标准API响应格式
 * @typedef {Object} APIResponse
 * @property {number} code - 响应码
 * @property {string} message - 响应消息
 * @property {*} data - 响应数据
 */

/**
 * 设备列表响应数据
 * @typedef {Object} DeviceListData
 * @property {number} total - 设备总数
 * @property {Array} devices - 设备列表
 * @property {Object} stats - 统计信息
 */

// ==================== API客户端类 ====================
/**
 * API客户端类
 * 提供统一的API调用接口，包含错误处理和重试机制
 */
export class APIClient {
    constructor() {
        this.baseURL = API_CONFIG.BASE_URL;
        this.timeout = API_CONFIG.REQUEST.TIMEOUT;
        this.retryAttempts = API_CONFIG.REQUEST.RETRY_ATTEMPTS;
        this.retryDelay = API_CONFIG.REQUEST.RETRY_DELAY;
        
        // 请求拦截器
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }

    /**
     * 发送HTTP请求
     * @param {string} url 请求URL
     * @param {Object} options 请求选项
     * @returns {Promise<*>} 响应数据
     * @private
     */
    async request(url, options = {}) {
        const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        
        // 默认请求配置
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 应用请求拦截器
        for (const interceptor of this.requestInterceptors) {
            await interceptor(config);
        }

        let lastError;
        
        // 重试机制
        for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
            try {
                console.log(`[APIClient] 请求: ${config.method} ${fullURL} (尝试 ${attempt + 1}/${this.retryAttempts + 1})`);
                
                // 创建带超时的fetch请求
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                
                const response = await fetch(fullURL, {
                    ...config,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);

                // 检查HTTP状态
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 解析响应
                const data = await response.json();
                
                // 应用响应拦截器
                for (const interceptor of this.responseInterceptors) {
                    await interceptor(data, response);
                }

                // 检查业务状态码
                if (data.code !== 200) {
                    throw new Error(data.message || '请求失败');
                }

                console.log(`[APIClient] 请求成功: ${config.method} ${fullURL}`);
                return data.data;

            } catch (error) {
                lastError = error;
                console.warn(`[APIClient] 请求失败 (尝试 ${attempt + 1}): ${error.message}`);
                
                // 如果是最后一次尝试，或者是不可重试的错误，直接抛出
                if (attempt === this.retryAttempts || this.isNonRetryableError(error)) {
                    break;
                }
                
                // 等待后重试
                await this.delay(this.retryDelay * Math.pow(2, attempt));
            }
        }

        // 发送错误事件
        emit(EVENT_TYPES.ERROR_OCCURRED, {
            type: 'api_error',
            message: lastError.message,
            url: fullURL
        });

        throw lastError;
    }

    /**
     * 判断是否为不可重试的错误
     * @param {Error} error 错误对象
     * @returns {boolean} 是否不可重试
     * @private
     */
    isNonRetryableError(error) {
        // 4xx错误通常不需要重试
        return error.message.includes('HTTP 4');
    }

    /**
     * 延迟函数
     * @param {number} ms 延迟毫秒数
     * @returns {Promise} Promise对象
     * @private
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 添加请求拦截器
     * @param {Function} interceptor 拦截器函数
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }

    /**
     * 添加响应拦截器
     * @param {Function} interceptor 拦截器函数
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }

    // ==================== 设备相关API ====================

    /**
     * 获取设备列表
     * @param {Object} params 查询参数
     * @param {number} params.page 页码
     * @param {number} params.page_size 每页大小
     * @param {string} params.search 搜索关键词
     * @param {string} params.status 状态过滤
     * @param {string} params.sort_by 排序字段
     * @param {string} params.sort_order 排序方向
     * @param {string} params.start_time 开始时间
     * @param {string} params.end_time 结束时间
     * @returns {Promise<DeviceListData>} 设备列表数据
     */
    async getDeviceList(params = {}) {
        const queryParams = new URLSearchParams();
        
        // 添加查询参数
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value);
            }
        });

        const url = `${API_CONFIG.ENDPOINTS.DEVICES}?${queryParams}`;
        
        emit(EVENT_TYPES.LOADING_START, { type: 'device_list' });
        
        try {
            const data = await this.request(url);
            
            emit(EVENT_TYPES.DEVICE_LIST_UPDATED, data);
            emit(EVENT_TYPES.LOADING_END, { type: 'device_list' });
            
            return data;
        } catch (error) {
            emit(EVENT_TYPES.LOADING_END, { type: 'device_list' });
            throw error;
        }
    }

    /**
     * 获取设备详情
     * @param {string} imsi 设备IMSI
     * @returns {Promise<Object>} 设备详情数据
     */
    async getDeviceDetail(imsi) {
        if (!imsi) {
            throw new Error('IMSI参数不能为空');
        }

        const url = API_CONFIG.ENDPOINTS.DEVICE_DETAIL(imsi);
        
        emit(EVENT_TYPES.LOADING_START, { type: 'device_detail', imsi });
        
        try {
            const data = await this.request(url);
            
            emit(EVENT_TYPES.DEVICE_DETAIL_UPDATED, { imsi, device: data });
            emit(EVENT_TYPES.LOADING_END, { type: 'device_detail', imsi });
            
            return data;
        } catch (error) {
            emit(EVENT_TYPES.LOADING_END, { type: 'device_detail', imsi });
            throw error;
        }
    }

    /**
     * 获取设备历史数据
     * @param {string} imsi 设备IMSI
     * @param {number} hours 历史小时数
     * @returns {Promise<Array>} 历史数据列表
     */
    async getDeviceHistory(imsi, hours = 24) {
        if (!imsi) {
            throw new Error('IMSI参数不能为空');
        }

        const url = `${API_CONFIG.ENDPOINTS.DEVICE_HISTORY(imsi)}?hours=${hours}`;
        
        emit(EVENT_TYPES.LOADING_START, { type: 'device_history', imsi, hours });
        
        try {
            const data = await this.request(url);
            
            emit(EVENT_TYPES.DEVICE_HISTORY_UPDATED, { imsi, hours, history: data });
            emit(EVENT_TYPES.LOADING_END, { type: 'device_history', imsi, hours });
            
            return data;
        } catch (error) {
            emit(EVENT_TYPES.LOADING_END, { type: 'device_history', imsi, hours });
            throw error;
        }
    }

    /**
     * 检查系统健康状态
     * @returns {Promise<Object>} 健康状态数据
     */
    async checkHealth() {
        const url = API_CONFIG.ENDPOINTS.HEALTH;
        
        try {
            const data = await this.request(url);
            return data;
        } catch (error) {
            console.warn('[APIClient] 健康检查失败:', error.message);
            throw error;
        }
    }

    // ==================== 批量操作API ====================

    /**
     * 批量获取设备详情
     * @param {Array<string>} imsiList IMSI列表
     * @returns {Promise<Array>} 设备详情列表
     */
    async batchGetDeviceDetails(imsiList) {
        if (!Array.isArray(imsiList) || imsiList.length === 0) {
            return [];
        }

        const promises = imsiList.map(imsi => 
            this.getDeviceDetail(imsi).catch(error => {
                console.warn(`[APIClient] 获取设备详情失败 (${imsi}):`, error.message);
                return null;
            })
        );

        const results = await Promise.all(promises);
        return results.filter(result => result !== null);
    }
}

// ==================== 全局API客户端实例 ====================
export const apiClient = new APIClient();

// 添加默认的响应拦截器
apiClient.addResponseInterceptor(async (data, response) => {
    // 记录响应时间
    const responseTime = response.headers.get('X-Response-Time');
    if (responseTime) {
        console.log(`[APIClient] 响应时间: ${responseTime}ms`);
    }
});

// ==================== 便捷函数 ====================
/**
 * 获取设备列表的便捷函数
 * @param {Object} params 查询参数
 * @returns {Promise<DeviceListData>} 设备列表数据
 */
export const getDeviceList = (params) => apiClient.getDeviceList(params);

/**
 * 获取设备详情的便捷函数
 * @param {string} imsi 设备IMSI
 * @returns {Promise<Object>} 设备详情数据
 */
export const getDeviceDetail = (imsi) => apiClient.getDeviceDetail(imsi);

/**
 * 获取设备历史的便捷函数
 * @param {string} imsi 设备IMSI
 * @param {number} hours 历史小时数
 * @returns {Promise<Array>} 历史数据列表
 */
export const getDeviceHistory = (imsi, hours) => apiClient.getDeviceHistory(imsi, hours);

/**
 * 检查系统健康状态的便捷函数
 * @returns {Promise<Object>} 健康状态数据
 */
export const checkHealth = () => apiClient.checkHealth();

// ==================== 默认导出 ====================
export default apiClient;

# 设备状态追踪系统 - JavaScript模块化架构

## 📁 目录结构

```
js/
├── README.md              # 架构文档
├── config.js              # 配置管理模块
├── utils.js               # 工具函数模块
├── api-client.js          # API调用模块
├── websocket-client.js    # WebSocket通信模块
├── device-renderer.js     # 设备渲染模块
├── device-modal.js        # 设备详情弹窗模块
├── search-filter.js       # 搜索过滤模块
└── main.js               # 主控制器模块
```

## 🏗️ 模块架构设计

### 依赖关系图
```
main.js (主控制器)
├── config.js (配置)
├── utils.js (工具)
├── api-client.js (API) ← utils.js, config.js
├── websocket-client.js (WebSocket) ← utils.js, config.js
├── device-renderer.js (渲染) ← utils.js
├── device-modal.js (弹窗) ← utils.js
└── search-filter.js (搜索) ← utils.js
```

## 📋 模块接口规范

### 1. config.js - 配置管理模块
**职责**: 统一管理所有配置项
**导出接口**:
```javascript
export const Config = {
    API: {
        BASE_URL: '/api/v1',
        ENDPOINTS: { ... }
    },
    WEBSOCKET: {
        URL: 'ws://...',
        RECONNECT_ATTEMPTS: 5
    },
    UI: {
        PAGE_SIZE: 20,
        REFRESH_INTERVAL: 3000
    }
}
```

### 2. utils.js - 工具函数模块
**职责**: 提供通用工具函数
**导出接口**:
```javascript
export const DateUtils = {
    formatDateTime(date),
    formatRelativeTime(date)
}
export const StatusUtils = {
    getStatusText(status),
    getStatusIcon(status),
    getWorkModeText(mode)
}
export const DOMUtils = {
    showLoading(show),
    showError(message),
    updateLastUpdateTime()
}
```

### 3. api-client.js - API调用模块
**职责**: 封装所有API调用
**导出接口**:
```javascript
export class APIClient {
    async getDeviceList(params)
    async getDeviceDetail(imsi)
    async getDeviceHistory(imsi, hours)
    async checkHealth()
}
```

### 4. websocket-client.js - WebSocket通信模块
**职责**: 管理WebSocket连接和消息处理
**导出接口**:
```javascript
export class WebSocketClient extends EventTarget {
    connect()
    disconnect()
    getConnectionStatus()
    // 事件: 'connected', 'disconnected', 'message', 'error'
}
```

### 5. device-renderer.js - 设备渲染模块
**职责**: 处理设备列表UI渲染
**导出接口**:
```javascript
export class DeviceRenderer {
    renderDeviceTable(devices)
    renderPagination(total, current, pageSize)
    updateStatistics(stats)
    clearTable()
}
```

### 6. device-modal.js - 设备详情弹窗模块
**职责**: 管理设备详情弹窗
**导出接口**:
```javascript
export class DeviceModal {
    show(device)
    hide()
    updateContent(device)
    renderTimeline(device)
}
```

### 7. search-filter.js - 搜索过滤模块
**职责**: 处理数据搜索和过滤
**导出接口**:
```javascript
export class SearchFilter {
    applySearch(devices, searchTerm)
    applyStatusFilter(devices, status)
    applySorting(devices, sortBy, order)
    applyTimeFilter(devices, startTime, endTime)
}
```

### 8. main.js - 主控制器模块
**职责**: 协调各模块，管理应用状态
**导出接口**:
```javascript
export class DeviceTrackingApp {
    init()
    destroy()
    refreshData()
    handleSearch(term)
    handleFilter(filter)
}
```

## 🔄 模块间通信

### 事件系统
使用 `CustomEvent` 进行模块间通信：

```javascript
// 发送事件
window.dispatchEvent(new CustomEvent('device:updated', {
    detail: { devices: [...] }
}));

// 监听事件
window.addEventListener('device:updated', (event) => {
    const devices = event.detail.devices;
    // 处理设备更新
});
```

### 标准事件列表
- `device:list-updated` - 设备列表更新
- `device:detail-updated` - 设备详情更新
- `websocket:connected` - WebSocket连接成功
- `websocket:disconnected` - WebSocket连接断开
- `search:changed` - 搜索条件变更
- `filter:changed` - 过滤条件变更

## 🎯 开发规范

### 1. 模块导入导出
- 使用 ES6 模块语法 (`import/export`)
- 每个模块只导出必要的接口
- 避免循环依赖

### 2. 错误处理
- 统一的错误处理机制
- 错误信息国际化支持
- 用户友好的错误提示

### 3. 性能优化
- 避免频繁的DOM操作
- 使用事件委托
- 合理使用防抖和节流

### 4. 代码风格
- 使用 JSDoc 注释
- 遵循一致的命名规范
- 保持代码简洁可读

## 🧪 测试策略

### 单元测试
- 每个模块独立测试
- 模拟外部依赖
- 覆盖主要功能路径

### 集成测试
- 测试模块间协作
- 测试事件通信
- 测试完整用户流程

## 📦 构建和部署

### 开发环境
- 直接引入各模块文件
- 便于调试和开发

### 生产环境
- 可选择打包合并
- 代码压缩优化
- 缓存策略配置

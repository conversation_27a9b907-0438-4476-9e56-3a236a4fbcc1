/**
 * 设备渲染模块
 * 
 * 职责: 负责设备列表的DOM渲染、分页显示、表格更新等UI渲染逻辑
 * 依赖: config.js, utils.js, event-system.js
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

import { UI_CONFIG } from './config.js';
import { DateUtils, StatusUtils, DataUtils, DOMUtils } from './utils.js';
import { EVENT_TYPES, emit } from './event-system.js';

// ==================== 设备渲染器类 ====================
/**
 * 设备渲染器类
 * 负责设备列表的UI渲染和更新
 */
export class DeviceRenderer {
    constructor() {
        this.currentDevices = [];
        this.currentStats = {};
        this.currentPage = 1;
        this.pageSize = UI_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE;
        this.totalCount = 0;
        
        // DOM元素缓存
        this.elements = {
            deviceTable: null,
            deviceTableBody: null,
            pagination: null,
            statistics: null,
            emptyState: null,
            loadingState: null,
            errorState: null
        };
        
        this.initElements();
    }

    /**
     * 初始化DOM元素引用
     * @private
     */
    initElements() {
        this.elements.deviceTable = document.getElementById('deviceTable');
        this.elements.deviceTableBody = document.getElementById('deviceTableBody');
        this.elements.pagination = document.getElementById('pagination');
        this.elements.statistics = document.getElementById('statistics');
        this.elements.emptyState = document.getElementById('emptyState');
        this.elements.loadingState = document.getElementById('loadingState');
        this.elements.errorState = document.getElementById('errorState');
    }

    /**
     * 渲染设备列表
     * @param {Array} devices 设备列表
     * @param {Object} options 渲染选项
     */
    renderDeviceTable(devices = [], options = {}) {
        this.currentDevices = devices;
        
        if (!this.elements.deviceTableBody) {
            console.error('[DeviceRenderer] 设备表格容器未找到');
            return;
        }

        // 清空现有内容
        this.elements.deviceTableBody.innerHTML = '';

        // 检查是否有数据
        if (devices.length === 0) {
            this.showEmptyState();
            return;
        }

        // 隐藏空状态
        this.hideEmptyState();

        // 渲染设备行
        const fragment = document.createDocumentFragment();
        devices.forEach((device, index) => {
            const row = this.createDeviceRow(device, index);
            fragment.appendChild(row);
        });

        this.elements.deviceTableBody.appendChild(fragment);

        // 发送渲染完成事件
        emit(EVENT_TYPES.DEVICE_LIST_RENDERED, {
            count: devices.length,
            timestamp: Date.now()
        });

        console.log(`[DeviceRenderer] 已渲染 ${devices.length} 个设备`);
    }

    /**
     * 创建设备行元素
     * @param {Object} device 设备数据
     * @param {number} index 索引
     * @returns {HTMLElement} 设备行元素
     * @private
     */
    createDeviceRow(device, index) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-base-200 cursor-pointer';
        row.dataset.imsi = device.imsi;
        
        // 添加点击事件
        row.addEventListener('click', () => {
            emit(EVENT_TYPES.DEVICE_SELECTED, device);
        });

        row.innerHTML = `
            <td class="px-4 py-3">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 rounded-full ${device.current_status === 1 ? 'bg-success' : 'bg-error'}"></div>
                    <div>
                        <div class="font-medium">${this.escapeHtml(device.imsi)}</div>
                        <div class="text-sm text-base-content/70">${this.escapeHtml(device.device_name)}</div>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3">
                <span class="badge ${device.current_status === 1 ? 'badge-success' : 'badge-error'} badge-sm">
                    ${StatusUtils.getStatusText(device.current_status)}
                </span>
            </td>
            <td class="px-4 py-3">
                <div class="text-sm">
                    ${DateUtils.format(device.last_heartbeat_time, 'datetime')}
                </div>
                <div class="text-xs text-base-content/50">
                    ${DateUtils.format(device.last_heartbeat_time, 'relative')}
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="text-sm">
                    ${this.escapeHtml(device.position.location_name)}
                </div>
                <div class="text-xs text-base-content/50">
                    ${device.speed >= 0 ? device.speed + ' km/h' : '未知'}
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center gap-2">
                    <div class="radial-progress text-primary" style="--value:${device.battery_level};" role="progressbar">
                        <span class="text-xs">${device.battery_level}%</span>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3">
                <span class="badge badge-outline badge-sm">
                    ${StatusUtils.getWorkModeText(device.work_mode)}
                </span>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center gap-2">
                    <div class="rating rating-sm">
                        ${this.renderSignalStrength(device.signal_strength)}
                    </div>
                    <span class="text-xs text-base-content/50">${device.signal_strength}/5</span>
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a onclick="showDeviceDetail('${device.imsi}')">
                            <i class="fas fa-info-circle"></i> 查看详情
                        </a></li>
                        <li><a onclick="showDeviceHistory('${device.imsi}')">
                            <i class="fas fa-history"></i> 历史记录
                        </a></li>
                        <li><a onclick="exportDeviceData('${device.imsi}')">
                            <i class="fas fa-download"></i> 导出数据
                        </a></li>
                    </ul>
                </div>
            </td>
        `;

        return row;
    }

    /**
     * 渲染信号强度
     * @param {number} strength 信号强度 (1-5)
     * @returns {string} 信号强度HTML
     * @private
     */
    renderSignalStrength(strength) {
        let html = '';
        for (let i = 1; i <= 5; i++) {
            const filled = i <= strength;
            html += `<input type="radio" class="mask mask-star-2 bg-${filled ? 'warning' : 'base-300'}" disabled />`;
        }
        return html;
    }

    /**
     * 渲染分页组件
     * @param {number} total 总数量
     * @param {number} current 当前页
     * @param {number} pageSize 每页大小
     */
    renderPagination(total, current = 1, pageSize = this.pageSize) {
        this.totalCount = total;
        this.currentPage = current;
        this.pageSize = pageSize;

        if (!this.elements.pagination) {
            console.error('[DeviceRenderer] 分页容器未找到');
            return;
        }

        const totalPages = Math.ceil(total / pageSize);
        
        if (totalPages <= 1) {
            this.elements.pagination.innerHTML = '';
            return;
        }

        const pagination = this.createPaginationElement(totalPages, current);
        this.elements.pagination.innerHTML = '';
        this.elements.pagination.appendChild(pagination);
    }

    /**
     * 创建分页元素
     * @param {number} totalPages 总页数
     * @param {number} currentPage 当前页
     * @returns {HTMLElement} 分页元素
     * @private
     */
    createPaginationElement(totalPages, currentPage) {
        const container = document.createElement('div');
        container.className = 'join';

        // 上一页按钮
        const prevBtn = document.createElement('button');
        prevBtn.className = `join-item btn ${currentPage === 1 ? 'btn-disabled' : ''}`;
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                this.changePage(currentPage - 1);
            }
        });
        container.appendChild(prevBtn);

        // 页码按钮
        const maxVisible = UI_CONFIG.PAGINATION.MAX_VISIBLE_PAGES;
        const startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        const endPage = Math.min(totalPages, startPage + maxVisible - 1);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `join-item btn ${i === currentPage ? 'btn-active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.changePage(i);
            });
            container.appendChild(pageBtn);
        }

        // 下一页按钮
        const nextBtn = document.createElement('button');
        nextBtn.className = `join-item btn ${currentPage === totalPages ? 'btn-disabled' : ''}`;
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                this.changePage(currentPage + 1);
            }
        });
        container.appendChild(nextBtn);

        return container;
    }

    /**
     * 更新统计信息
     * @param {Object} stats 统计数据
     */
    updateStatistics(stats) {
        this.currentStats = stats;

        if (!this.elements.statistics) {
            console.error('[DeviceRenderer] 统计信息容器未找到');
            return;
        }

        const statsCards = [
            {
                title: '设备总数',
                value: stats.total_devices || 0,
                icon: 'fas fa-microchip',
                color: 'text-primary'
            },
            {
                title: '在线设备',
                value: stats.online_devices || 0,
                icon: 'fas fa-circle',
                color: 'text-success'
            },
            {
                title: '离线设备',
                value: stats.offline_devices || 0,
                icon: 'fas fa-circle',
                color: 'text-error'
            },
            {
                title: '在线率',
                value: DataUtils.formatPercentage(stats.online_devices || 0, stats.total_devices || 0),
                icon: 'fas fa-chart-line',
                color: 'text-info'
            }
        ];

        const statsHTML = statsCards.map(card => `
            <div class="stat">
                <div class="stat-figure ${card.color}">
                    <i class="${card.icon} text-2xl"></i>
                </div>
                <div class="stat-title">${card.title}</div>
                <div class="stat-value ${card.color}">${card.value}</div>
            </div>
        `).join('');

        this.elements.statistics.innerHTML = `
            <div class="stats shadow w-full">
                ${statsHTML}
            </div>
        `;
    }

    // ==================== 状态管理方法 ====================

    /**
     * 显示空状态
     */
    showEmptyState() {
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.remove('hidden');
        }
        if (this.elements.deviceTable) {
            this.elements.deviceTable.classList.add('hidden');
        }
    }

    /**
     * 隐藏空状态
     */
    hideEmptyState() {
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.add('hidden');
        }
        if (this.elements.deviceTable) {
            this.elements.deviceTable.classList.remove('hidden');
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        DOMUtils.showLoading(true);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        DOMUtils.showLoading(false);
    }

    // ==================== 工具方法 ====================

    /**
     * HTML转义
     * @param {string} text 文本
     * @returns {string} 转义后的文本
     * @private
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 切换页面
     * @param {number} page 页码
     * @private
     */
    changePage(page) {
        if (page !== this.currentPage) {
            emit(EVENT_TYPES.PAGE_CHANGED, {
                page,
                pageSize: this.pageSize,
                previousPage: this.currentPage
            });
        }
    }

    /**
     * 清空表格
     */
    clearTable() {
        if (this.elements.deviceTableBody) {
            this.elements.deviceTableBody.innerHTML = '';
        }
        this.currentDevices = [];
    }

    /**
     * 获取当前设备列表
     * @returns {Array} 当前设备列表
     */
    getCurrentDevices() {
        return [...this.currentDevices];
    }

    /**
     * 获取当前统计信息
     * @returns {Object} 当前统计信息
     */
    getCurrentStats() {
        return { ...this.currentStats };
    }
}

// ==================== 全局设备渲染器实例 ====================
export const deviceRenderer = new DeviceRenderer();

// ==================== 便捷函数 ====================
/**
 * 渲染设备表格的便捷函数
 * @param {Array} devices 设备列表
 * @param {Object} options 渲染选项
 */
export const renderDeviceTable = (devices, options) =>
    deviceRenderer.renderDeviceTable(devices, options);

/**
 * 渲染分页的便捷函数
 * @param {number} total 总数量
 * @param {number} current 当前页
 * @param {number} pageSize 每页大小
 */
export const renderPagination = (total, current, pageSize) =>
    deviceRenderer.renderPagination(total, current, pageSize);

/**
 * 更新统计信息的便捷函数
 * @param {Object} stats 统计数据
 */
export const updateStatistics = (stats) =>
    deviceRenderer.updateStatistics(stats);

// ==================== 默认导出 ====================
export default deviceRenderer;

/**
 * 配置管理模块
 * 
 * 职责: 统一管理所有配置项，提供配置访问接口
 * 依赖: 无
 * 作者: Device Tracking System
 * 创建时间: 2025-01-30
 */

// ==================== API配置 ====================
export const API_CONFIG = {
    // 基础URL
    BASE_URL: '/api/v1',
    
    // API端点
    ENDPOINTS: {
        DEVICES: '/devices',
        DEVICE_DETAIL: (imsi) => `/devices/${imsi}`,
        DEVICE_HISTORY: (imsi) => `/devices/${imsi}/history`,
        HEALTH: '/health'
    },
    
    // 请求配置
    REQUEST: {
        TIMEOUT: 30000, // 30秒超时
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000 // 1秒重试延迟
    }
};

// ==================== WebSocket配置 ====================
export const WEBSOCKET_CONFIG = {
    // WebSocket URL (动态生成)
    get URL() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        return `${protocol}//${window.location.host}/ws`;
    },
    
    // 连接配置
    CONNECTION: {
        MAX_RECONNECT_ATTEMPTS: 5,
        INITIAL_RECONNECT_DELAY: 1000, // 1秒
        MAX_RECONNECT_DELAY: 30000, // 30秒
        HEARTBEAT_INTERVAL: 54000, // 54秒心跳
        CONNECTION_TIMEOUT: 10000 // 10秒连接超时
    }
};

// ==================== UI配置 ====================
export const UI_CONFIG = {
    // 分页配置
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 20,
        PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
        MAX_VISIBLE_PAGES: 5
    },
    
    // 表格配置
    TABLE: {
        DEFAULT_SORT_BY: 'imsi',
        DEFAULT_SORT_ORDER: 'asc',
        SORTABLE_COLUMNS: ['imsi', 'device_name', 'current_status', 'last_heartbeat_time', 'battery_level']
    },
    
    // 搜索配置
    SEARCH: {
        DEBOUNCE_DELAY: 300, // 300ms防抖
        MIN_SEARCH_LENGTH: 2,
        SEARCH_FIELDS: ['imsi', 'device_name', 'position.location_name']
    },
    
    // 过滤配置
    FILTER: {
        STATUS_OPTIONS: [
            { value: '', label: '全部状态' },
            { value: '1', label: '在线' },
            { value: '0', label: '离线' }
        ],
        WORK_MODE_OPTIONS: [
            { value: '', label: '全部模式' },
            { value: '0', label: '待机' },
            { value: '1', label: '运行' },
            { value: '2', label: '维护' },
            { value: '3', label: '故障' }
        ]
    },
    
    // 刷新配置
    REFRESH: {
        AUTO_REFRESH_INTERVAL: 10000, // 10秒自动刷新（WebSocket模式下不使用）
        MANUAL_REFRESH_COOLDOWN: 1000 // 1秒手动刷新冷却
    },
    
    // 动画配置
    ANIMATION: {
        FADE_DURATION: 300,
        SLIDE_DURATION: 250,
        LOADING_DELAY: 100
    }
};

// ==================== 设备状态配置 ====================
export const DEVICE_CONFIG = {
    // 状态映射
    STATUS: {
        ONLINE: 1,
        OFFLINE: 0,
        UNKNOWN: -1
    },
    
    // 工作模式映射
    WORK_MODE: {
        STANDBY: 0,
        RUNNING: 1,
        MAINTENANCE: 2,
        ERROR: 3
    },
    
    // 任务状态映射
    TASK_STATUS: {
        IDLE: 0,
        RUNNING: 1,
        COMPLETED: 2,
        FAILED: 3
    },
    
    // 设备类型映射
    DEVICE_TYPE: {
        UNKNOWN: 0,
        BUS: 1,
        TRUCK: 2,
        CAR: 3
    }
};

// ==================== 系统配置 ====================
export const SYSTEM_CONFIG = {
    // 应用信息
    APP: {
        NAME: '设备状态追踪系统',
        VERSION: '1.0.0',
        DESCRIPTION: '实时监控设备状态和位置信息'
    },
    
    // 日志配置
    LOGGING: {
        LEVEL: 'info', // debug, info, warn, error
        ENABLE_CONSOLE: true,
        ENABLE_REMOTE: false
    },
    
    // 性能配置
    PERFORMANCE: {
        ENABLE_VIRTUAL_SCROLL: true, // 大数据量时启用虚拟滚动
        VIRTUAL_SCROLL_THRESHOLD: 100,
        DEBOUNCE_RESIZE: 250
    },
    
    // 缓存配置
    CACHE: {
        ENABLE_LOCAL_STORAGE: true,
        CACHE_DURATION: 5 * 60 * 1000, // 5分钟
        MAX_CACHE_SIZE: 100 // 最大缓存条目数
    }
};

// ==================== 主题配置 ====================
export const THEME_CONFIG = {
    // 默认主题
    DEFAULT_THEME: 'light',
    
    // 可用主题
    AVAILABLE_THEMES: ['light', 'dark', 'auto'],
    
    // 颜色配置
    COLORS: {
        STATUS: {
            ONLINE: '#10b981', // green-500
            OFFLINE: '#ef4444', // red-500
            WARNING: '#f59e0b', // amber-500
            INFO: '#3b82f6' // blue-500
        },
        WORK_MODE: {
            STANDBY: '#6b7280', // gray-500
            RUNNING: '#10b981', // green-500
            MAINTENANCE: '#f59e0b', // amber-500
            ERROR: '#ef4444' // red-500
        }
    }
};

// ==================== 配置管理器 ====================
/**
 * 配置管理器类
 * 提供配置的获取、设置和验证功能
 */
export class ConfigManager {
    constructor() {
        this.config = {
            api: API_CONFIG,
            websocket: WEBSOCKET_CONFIG,
            ui: UI_CONFIG,
            device: DEVICE_CONFIG,
            system: SYSTEM_CONFIG,
            theme: THEME_CONFIG
        };
    }

    /**
     * 获取配置值
     * @param {string} path 配置路径，如 'api.BASE_URL' 或 'ui.PAGINATION.DEFAULT_PAGE_SIZE'
     * @returns {*} 配置值
     */
    get(path) {
        const keys = path.split('.');
        let value = this.config;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                console.warn(`[ConfigManager] 配置路径不存在: ${path}`);
                return undefined;
            }
        }
        
        return value;
    }

    /**
     * 设置配置值
     * @param {string} path 配置路径
     * @param {*} value 配置值
     */
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target = this.config;
        
        for (const key of keys) {
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        
        target[lastKey] = value;
        console.log(`[ConfigManager] 配置已更新: ${path} = ${value}`);
    }

    /**
     * 获取完整配置对象
     * @returns {Object} 完整配置
     */
    getAll() {
        return { ...this.config };
    }

    /**
     * 重置配置为默认值
     */
    reset() {
        this.config = {
            api: API_CONFIG,
            websocket: WEBSOCKET_CONFIG,
            ui: UI_CONFIG,
            device: DEVICE_CONFIG,
            system: SYSTEM_CONFIG,
            theme: THEME_CONFIG
        };
        console.log('[ConfigManager] 配置已重置为默认值');
    }
}

// ==================== 全局配置管理器实例 ====================
export const configManager = new ConfigManager();

// ==================== 便捷访问函数 ====================
/**
 * 获取配置值的便捷函数
 * @param {string} path 配置路径
 * @returns {*} 配置值
 */
export const getConfig = (path) => configManager.get(path);

/**
 * 设置配置值的便捷函数
 * @param {string} path 配置路径
 * @param {*} value 配置值
 */
export const setConfig = (path, value) => configManager.set(path, value);

// ==================== 默认导出 ====================
export default {
    API_CONFIG,
    WEBSOCKET_CONFIG,
    UI_CONFIG,
    DEVICE_CONFIG,
    SYSTEM_CONFIG,
    THEME_CONFIG,
    ConfigManager,
    configManager,
    getConfig,
    setConfig
};

/**
 * 模块模板 - 标准模块结构
 * 
 * 每个模块都应该遵循以下结构：
 * 1. 文件头注释说明模块用途
 * 2. 导入依赖模块
 * 3. 私有变量和函数
 * 4. 公共类或函数定义
 * 5. 导出接口
 */

/**
 * 模块名称: [模块名]
 * 职责: [模块职责描述]
 * 依赖: [依赖的其他模块]
 * 作者: [作者信息]
 * 创建时间: [创建时间]
 */

// ==================== 导入依赖 ====================
// import { Config } from './config.js';
// import { DateUtils } from './utils.js';

// ==================== 私有变量 ====================
// 模块内部使用的私有变量
let privateVariable = null;

// ==================== 私有函数 ====================
/**
 * 私有函数示例
 * @param {*} param 参数说明
 * @returns {*} 返回值说明
 */
function privateFunction(param) {
    // 实现逻辑
    return param;
}

// ==================== 公共类定义 ====================
/**
 * 公共类示例
 * @class
 */
export class ModuleClass {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     */
    constructor(options = {}) {
        this.options = {
            // 默认配置
            defaultOption: true,
            ...options
        };
        
        // 初始化
        this.init();
    }

    /**
     * 初始化方法
     * @private
     */
    init() {
        // 初始化逻辑
    }

    /**
     * 公共方法示例
     * @param {*} param 参数说明
     * @returns {Promise<*>} 返回Promise
     */
    async publicMethod(param) {
        try {
            // 实现逻辑
            return await this.processData(param);
        } catch (error) {
            console.error('[ModuleClass] 方法执行失败:', error);
            throw error;
        }
    }

    /**
     * 数据处理方法
     * @param {*} data 数据
     * @returns {*} 处理后的数据
     * @private
     */
    processData(data) {
        // 数据处理逻辑
        return data;
    }

    /**
     * 销毁方法
     */
    destroy() {
        // 清理资源
        this.options = null;
    }
}

// ==================== 工具函数导出 ====================
/**
 * 工具函数示例
 * @param {*} input 输入参数
 * @returns {*} 输出结果
 */
export function utilityFunction(input) {
    return privateFunction(input);
}

// ==================== 常量导出 ====================
export const MODULE_CONSTANTS = {
    VERSION: '1.0.0',
    NAME: 'ModuleTemplate'
};

// ==================== 默认导出 ====================
export default ModuleClass;

/**
 * 模块使用示例:
 * 
 * import ModuleClass, { utilityFunction, MODULE_CONSTANTS } from './module-template.js';
 * 
 * // 创建实例
 * const instance = new ModuleClass({
 *     customOption: 'value'
 * });
 * 
 * // 调用方法
 * instance.publicMethod('data').then(result => {
 *     console.log('结果:', result);
 * });
 * 
 * // 使用工具函数
 * const result = utilityFunction('input');
 * 
 * // 访问常量
 * console.log('模块版本:', MODULE_CONSTANTS.VERSION);
 */

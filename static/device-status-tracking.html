<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备状态追踪系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义样式 */
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .loading-skeleton { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

        /* 状态指示器动画 */
        .status-indicator {
            position: relative;
            display: inline-block;
        }

        .status-indicator.online::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            width: 6px;
            height: 6px;
            background: #10b981;
            border-radius: 50%;
            transform: translateY(-50%);
            animation: pulse-dot 2s infinite;
        }

        .status-indicator.offline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -8px;
            width: 6px;
            height: 6px;
            background: #ef4444;
            border-radius: 50%;
            transform: translateY(-50%);
            opacity: 0.5;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
        }

        /* 卡片悬停效果 */
        .stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* 表格行悬停效果 */
        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: hsl(var(--b2));
            transform: scale(1.01);
        }

        /* 搜索框聚焦效果 */
        .input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 响应式表格 */
        @media (max-width: 768px) {
            .table-responsive-stack {
                display: block;
            }

            .table-responsive-stack thead {
                display: none;
            }

            .table-responsive-stack tbody,
            .table-responsive-stack tr,
            .table-responsive-stack td {
                display: block;
                width: 100%;
            }

            .table-responsive-stack tr {
                border: 1px solid hsl(var(--b3));
                margin-bottom: 1rem;
                padding: 1rem;
                border-radius: 0.5rem;
            }

            .table-responsive-stack td {
                text-align: right;
                padding-left: 50%;
                position: relative;
            }

            .table-responsive-stack td::before {
                content: attr(data-label);
                position: absolute;
                left: 1rem;
                width: 45%;
                text-align: left;
                font-weight: bold;
                color: hsl(var(--bc) / 0.7);
            }
        }

        /* 加载动画优化 */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        /* 主题适配 */
        [data-theme="dark"] .stat-card:hover {
            box-shadow: 0 8px 25px rgba(255,255,255,0.1);
        }

        /* 滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: hsl(var(--b2));
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: hsl(var(--bc) / 0.3);
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--bc) / 0.5);
        }

        /* 时间线样式优化 */
        .timeline-custom {
            position: relative;
            padding-left: 2rem;
        }

        .timeline-custom::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: hsl(var(--b3));
        }

        .timeline-item {
            position: relative;
            padding-bottom: 1.5rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.5rem;
            top: 0.25rem;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: hsl(var(--p));
            border: 2px solid hsl(var(--b1));
        }

        /* 错误状态样式 */
        .error-state {
            text-align: center;
            padding: 2rem;
            color: hsl(var(--bc) / 0.5);
        }

        .error-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: hsl(var(--er));
        }

        /* 成功状态样式 */
        .success-state {
            text-align: center;
            padding: 2rem;
            color: hsl(var(--bc) / 0.7);
        }

        .success-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: hsl(var(--su));
        }
    </style>
</head>
<body class="bg-base-200 min-h-screen">
    <!-- 主容器 -->
    <div class="container mx-auto p-4 max-w-7xl">
        
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                <i class="fas fa-car text-primary"></i>
                设备状态追踪系统
            </h1>
            <div class="text-sm breadcrumbs">
                <ul>
                    <li><a href="#" class="text-primary">首页</a></li>
                    <li>设备管理</li>
                    <li>状态追踪</li>
                </ul>
            </div>
        </div>

        <!-- 搜索过滤区域 -->
        <div class="card bg-base-100 shadow-sm mb-6">
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                    <!-- IMSI搜索 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">IMSI搜索</span>
                        </label>
                        <div class="input-group">
                            <input type="text" id="imsiSearch" placeholder="输入IMSI..." 
                                   class="input input-bordered input-sm w-full" />
                            <button class="btn btn-square btn-sm btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 状态过滤 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">状态过滤</span>
                        </label>
                        <select id="statusFilter" class="select select-bordered select-sm">
                            <option value="">全部状态</option>
                            <option value="1">在线</option>
                            <option value="0">离线</option>
                        </select>
                    </div>

                    <!-- 时间范围 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">开始时间</span>
                        </label>
                        <input type="datetime-local" id="startTime" class="input input-bordered input-sm" />
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">结束时间</span>
                        </label>
                        <input type="datetime-local" id="endTime" class="input input-bordered input-sm" />
                    </div>

                    <!-- 自动刷新 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">自动刷新</span>
                        </label>
                        <div class="flex items-center gap-2">
                            <input type="checkbox" id="autoRefresh" class="toggle toggle-primary toggle-sm" checked />
                            <span class="text-xs">10秒</span>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">操作</span>
                        </label>
                        <div class="flex gap-2">
                            <button id="refreshBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                            <button id="exportBtn" class="btn btn-outline btn-sm">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- 总设备数 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">总设备数</p>
                            <p class="text-2xl font-bold" id="totalDevices">
                                <span class="loading loading-dots loading-sm" id="totalDevicesLoading"></span>
                                <span id="totalDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-primary">
                            <i class="fas fa-car"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="text-xs text-base-content/50">设备总数统计</div>
                    </div>
                </div>
            </div>

            <!-- 在线设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">在线设备</p>
                            <p class="text-2xl font-bold text-success" id="onlineDevices">
                                <span class="loading loading-dots loading-sm" id="onlineDevicesLoading"></span>
                                <span id="onlineDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-success">
                            <i class="fas fa-wifi"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-success badge-sm" id="onlineRate">0%</div>
                    </div>
                </div>
            </div>

            <!-- 离线设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">离线设备</p>
                            <p class="text-2xl font-bold text-error" id="offlineDevices">
                                <span class="loading loading-dots loading-sm" id="offlineDevicesLoading"></span>
                                <span id="offlineDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-error">
                            <i class="fas fa-wifi" style="opacity: 0.3;"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-error badge-sm" id="offlineRate">0%</div>
                    </div>
                </div>
            </div>

            <!-- 异常设备 -->
            <div class="card bg-base-100 shadow-sm stat-card">
                <div class="card-body p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-base-content/70">异常设备</p>
                            <p class="text-2xl font-bold text-warning" id="errorDevices">
                                <span class="loading loading-dots loading-sm" id="errorDevicesLoading"></span>
                                <span id="errorDevicesValue" class="hidden">0</span>
                            </p>
                        </div>
                        <div class="text-3xl text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="badge badge-warning badge-sm">需关注</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="card bg-base-100 shadow-sm mb-6">
            <div class="card-body">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="card-title">设备状态列表</h2>
                    <div class="text-sm text-base-content/70">
                        最后更新: <span id="lastUpdateTime">--</span>
                    </div>
                </div>

                <!-- 表格容器 -->
                <div class="overflow-x-auto custom-scrollbar">
                    <table class="table table-zebra table-sm">
                        <thead>
                            <tr>
                                <th>
                                    <div class="flex items-center gap-2">
                                        <span>IMSI</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer" onclick="sortTable('imsi')"></i>
                                    </div>
                                </th>
                                <th>
                                    <div class="flex items-center gap-2">
                                        <span>状态</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer" onclick="sortTable('status')"></i>
                                    </div>
                                </th>
                                <th>
                                    <div class="flex items-center gap-2">
                                        <span>最后心跳</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer" onclick="sortTable('heartbeat')"></i>
                                    </div>
                                </th>
                                <th>位置</th>
                                <th>
                                    <div class="flex items-center gap-2">
                                        <span>速度</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer" onclick="sortTable('speed')"></i>
                                    </div>
                                </th>
                                <th>
                                    <div class="flex items-center gap-2">
                                        <span>里程</span>
                                        <i class="fas fa-sort text-xs opacity-50 cursor-pointer" onclick="sortTable('mileage')"></i>
                                    </div>
                                </th>
                                <th>任务状态</th>
                                <th>工作模式</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="deviceTableBody">
                            <!-- 数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-8 hidden">
                    <span class="loading loading-spinner loading-lg text-primary"></span>
                    <p class="mt-2 text-base-content/70">正在加载设备数据...</p>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-8 hidden">
                    <i class="fas fa-inbox text-6xl text-base-content/30 mb-4"></i>
                    <p class="text-base-content/70 mb-2">暂无设备数据</p>
                    <p class="text-sm text-base-content/50">请检查搜索条件或稍后重试</p>
                </div>

                <!-- 错误状态 -->
                <div id="errorState" class="error-state hidden">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p class="text-lg font-semibold mb-2">数据加载失败</p>
                    <p class="text-sm mb-4">请检查网络连接或稍后重试</p>
                    <button class="btn btn-primary btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        重新加载
                    </button>
                </div>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="flex justify-between items-center">
            <div class="text-sm text-base-content/70">
                显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条记录
            </div>
            
            <div class="join">
                <button id="prevPage" class="join-item btn btn-sm">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div id="pageNumbers" class="join">
                    <!-- 页码按钮将动态生成 -->
                </div>
                <button id="nextPage" class="join-item btn btn-sm">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div class="form-control">
                <select id="pageSize" class="select select-bordered select-sm">
                    <option value="20">20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 设备详情弹窗 -->
    <dialog id="deviceDetailModal" class="modal">
        <div class="modal-box w-11/12 max-w-4xl">
            <form method="dialog">
                <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
            </form>
            
            <h3 class="font-bold text-lg mb-4">设备详情</h3>
            
            <!-- 弹窗内容将通过JavaScript动态填充 -->
            <div id="deviceDetailContent">
                <!-- 详情内容 -->
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>close</button>
        </form>
    </dialog>

    <!-- JavaScript -->
    <script src="device-status-tracking.js"></script>
</body>
</html>

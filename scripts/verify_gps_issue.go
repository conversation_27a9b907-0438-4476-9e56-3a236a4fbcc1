package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	_ "github.com/go-sql-driver/mysql"
	"golang.org/x/net/context"
)

// 设备状态信息
type DeviceStatus struct {
	IMSI               string    `json:"imsi"`
	Online             int       `json:"online"`              // MySQL中的在线状态
	LastHeartbeatTs    int64     `json:"last_heartbeat_ts"`   // Redis中的心跳时间戳
	LastValidGpsTs     int64     `json:"last_valid_gps_ts"`   // Redis中的最后有效GPS时间
	CurrentTime        int64     `json:"current_time"`        // 当前时间戳
	HeartbeatTimeout   bool      `json:"heartbeat_timeout"`   // 是否心跳超时
	GpsTimeUpdating    bool      `json:"gps_time_updating"`   // GPS时间是否在更新
	IsAnomalous        bool      `json:"is_anomalous"`        // 是否异常（离线但GPS时间在更新）
}

// Redis心跳数据结构
type HeartbeatRedis struct {
	Ts                 int64   `json:"ts"`
	LastValidLatitude  float64 `json:"last_valid_lat"`
	LastValidLongitude float64 `json:"last_valid_lng"`
	LastValidGpsTs     int64   `json:"last_valid_gps_ts"`
}

// 配置信息
type Config struct {
	MySQL struct {
		Host     string
		Port     int
		User     string
		Password string
		Database string
	}
	Redis struct {
		Host     string
		Port     int
		Password string
		DB       int
	}
	HeartbeatTimeout int64 // 心跳超时时间（秒）
}

func main() {
	// 初始化配置
	config := &Config{
		MySQL: struct {
			Host     string
			Port     int
			User     string
			Password string
			Database string
		}{
			Host:     "**************",
			Port:     3306,
			User:     "root",
			Password: "pix@6688",
			Database: "pixmoving-test",
		},
		Redis: struct {
			Host     string
			Port     int
			Password string
			DB       int
		}{
			Host:     "**************",
			Port:     6379,
			Password: "Pix@121cc",
			DB:       3,
		},
		HeartbeatTimeout: 10, // 10秒超时
	}

	// 连接数据库
	db, err := connectMySQL(config)
	if err != nil {
		log.Fatalf("连接MySQL失败: %v", err)
	}
	defer db.Close()

	// 连接Redis
	rdb, err := connectRedis(config)
	if err != nil {
		log.Fatalf("连接Redis失败: %v", err)
	}
	defer rdb.Close()

	fmt.Println("🔍 开始验证设备离线但GPS时间仍在更新的问题...")
	fmt.Println("=" * 80)

	// 存储上一次的GPS时间，用于检测更新
	previousGpsTimes := make(map[string]int64)

	// 持续监控
	for i := 0; i < 10; i++ { // 监控10轮，每轮间隔30秒
		fmt.Printf("\n🕐 第 %d 轮检查 - %s\n", i+1, time.Now().Format("2006-01-02 15:04:05"))
		fmt.Println("-" * 60)

		devices, err := checkAllDevices(db, rdb, config, previousGpsTimes)
		if err != nil {
			log.Printf("检查设备失败: %v", err)
			continue
		}

		// 统计结果
		totalDevices := len(devices)
		onlineDevices := 0
		offlineDevices := 0
		anomalousDevices := 0

		for _, device := range devices {
			if device.Online == 1 {
				onlineDevices++
			} else {
				offlineDevices++
			}
			if device.IsAnomalous {
				anomalousDevices++
			}
		}

		fmt.Printf("📊 统计结果: 总设备=%d, 在线=%d, 离线=%d, 异常=%d\n",
			totalDevices, onlineDevices, offlineDevices, anomalousDevices)

		// 显示异常设备详情
		if anomalousDevices > 0 {
			fmt.Println("\n🚨 发现异常设备（离线但GPS时间在更新）:")
			for _, device := range devices {
				if device.IsAnomalous {
					printDeviceDetails(device)
				}
			}
		} else {
			fmt.Println("✅ 未发现异常设备")
		}

		// 更新上一次GPS时间记录
		for _, device := range devices {
			previousGpsTimes[device.IMSI] = device.LastValidGpsTs
		}

		if i < 9 { // 最后一轮不需要等待
			fmt.Println("\n⏳ 等待30秒后进行下一轮检查...")
			time.Sleep(30 * time.Second)
		}
	}

	fmt.Println("\n🏁 验证完成!")
}

// 连接MySQL
func connectMySQL(config *Config) (*sql.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.MySQL.User, config.MySQL.Password, config.MySQL.Host, config.MySQL.Port, config.MySQL.Database)
	
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	if err := db.Ping(); err != nil {
		return nil, err
	}

	return db, nil
}

// 连接Redis
func connectRedis(config *Config) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.Redis.Host, config.Redis.Port),
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return rdb, nil
}

// 检查所有设备状态
func checkAllDevices(db *sql.DB, rdb *redis.Client, config *Config, previousGpsTimes map[string]int64) ([]DeviceStatus, error) {
	ctx := context.Background()
	currentTime := time.Now().Unix()

	// 从MySQL获取所有设备
	query := "SELECT imsi, online FROM device WHERE imsi != '' ORDER BY imsi"
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询设备列表失败: %v", err)
	}
	defer rows.Close()

	var devices []DeviceStatus

	for rows.Next() {
		var device DeviceStatus
		if err := rows.Scan(&device.IMSI, &device.Online); err != nil {
			log.Printf("扫描设备数据失败: %v", err)
			continue
		}

		device.CurrentTime = currentTime

		// 从Redis获取心跳时间戳
		heartbeatTs, err := rdb.HGet(ctx, "DEVICE_HEARTBEAT_TS", device.IMSI).Int64()
		if err != nil && err != redis.Nil {
			log.Printf("获取设备 %s 心跳时间戳失败: %v", device.IMSI, err)
		}
		device.LastHeartbeatTs = heartbeatTs

		// 从Redis获取最后有效GPS时间
		heartbeatData, err := rdb.HGet(ctx, "LAST_HEARTBEAT", device.IMSI).Result()
		if err != nil && err != redis.Nil {
			log.Printf("获取设备 %s 心跳数据失败: %v", device.IMSI, err)
		} else if err != redis.Nil {
			var hbData HeartbeatRedis
			if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
				device.LastValidGpsTs = hbData.LastValidGpsTs
			}
		}

		// 判断心跳是否超时
		device.HeartbeatTimeout = (currentTime - device.LastHeartbeatTs) > config.HeartbeatTimeout

		// 判断GPS时间是否在更新
		if previousTime, exists := previousGpsTimes[device.IMSI]; exists {
			device.GpsTimeUpdating = device.LastValidGpsTs > previousTime
		}

		// 判断是否异常：设备离线但GPS时间在更新
		device.IsAnomalous = (device.Online == 0) && device.GpsTimeUpdating

		devices = append(devices, device)
	}

	return devices, nil
}

// 打印设备详细信息
func printDeviceDetails(device DeviceStatus) {
	fmt.Printf("  📱 设备: %s\n", device.IMSI)
	fmt.Printf("     💾 MySQL在线状态: %d (0=离线, 1=在线)\n", device.Online)
	fmt.Printf("     💓 最后心跳时间: %s (%d)\n", 
		time.Unix(device.LastHeartbeatTs, 0).Format("2006-01-02 15:04:05"), device.LastHeartbeatTs)
	fmt.Printf("     🛰️  最后有效GPS时间: %s (%d)\n", 
		time.Unix(device.LastValidGpsTs, 0).Format("2006-01-02 15:04:05"), device.LastValidGpsTs)
	fmt.Printf("     ⏰ 当前时间: %s (%d)\n", 
		time.Unix(device.CurrentTime, 0).Format("2006-01-02 15:04:05"), device.CurrentTime)
	fmt.Printf("     ⚠️  心跳超时: %t\n", device.HeartbeatTimeout)
	fmt.Printf("     🔄 GPS时间更新: %t\n", device.GpsTimeUpdating)
	fmt.Printf("     🚨 异常状态: %t\n", device.IsAnomalous)
	fmt.Println()
}

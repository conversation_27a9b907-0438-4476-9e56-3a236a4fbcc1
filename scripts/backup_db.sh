#!/bin/bash

NOW="$(date +"%Y_%m_%d_%H_%M_%S")" 

BK_DB=pixmoving
#BK_DATA_TABLES="device user options ip firmware upgrade_rules log_terminal report_login log_login log_operation log_message" #record_singal
BK_DATA_TABLES="device user options ip firmware upgrade_rules"

BK_DIR=/data/tcpserver/backup

#备份的sql文件最多保留数量
BK_RESERVED_NUM=30

BK_FILE_NAME=$BK_DIR/"$BK_DB"_"$NOW".sql

DB_USER=root
DB_PWD=qwer323w@8

if [ ! -d "$BK_DIR" ]; then
  mkdir -p $BK_DIR
  if [ "$?" -eq 0 ]; then
    echo "create backup dir success"     
  else
    echo "create backup dir fail"
    exit 1
  fi
fi

exec >$BK_DIR/../backup_db.log 2>&1


mysqldump -u$DB_USER -p$DB_PWD $BK_DB --no-data > $BK_FILE_NAME

if [ "$?" -eq 0 ]; then
  echo "backup structure success"
else
  echo "backup structure fail"
  exit 1
fi

mysqldump -v -u$DB_USER -p$DB_PWD $BK_DB --tables $BK_DATA_TABLES >> $BK_FILE_NAME

if [ "$?" -eq 0 ]; then
  echo "backup data success"
else
  echo "backup data fail"
  exit 1
fi


date=$(date "+%Y%m%d-%H%M%S")

FileNum=$(ls -l $BK_DIR | grep ^- | wc -l)

while(( $FileNum > $BK_RESERVED_NUM))
do
    OldFile=$(ls -rt $BK_DIR | head -1)
    echo  "delete:$BK_DIR/$OldFile"
    rm -rf "$BK_DIR/$OldFile"
    let "FileNum--"
done 

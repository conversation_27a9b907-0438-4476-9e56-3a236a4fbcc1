#!/bin/bash

# ccserver 启动脚本
# 支持多环境配置

# 设置默认环境为开发环境
ENV=${ENV:-dev}

# 根据环境变量设置配置文件
case "$ENV" in
    "dev")
        echo "启动开发环境（使用默认配置文件）..."
        # 不设置 GF_GCFG_FILE，使用默认的 config.toml
        ;;
    "test")
        export GF_GCFG_FILE=config.test.toml
        echo "启动测试环境..."
        ;;
    "prod")
        export GF_GCFG_FILE=config.prod.toml
        echo "启动生产环境..."
        ;;
    *)
        echo "未知环境: $ENV，使用默认开发环境"
        # 不设置 GF_GCFG_FILE，使用默认的 config.toml
        ;;
esac

if [ -n "$GF_GCFG_FILE" ]; then
    echo "使用配置文件: $GF_GCFG_FILE"
else
    echo "使用默认配置文件: config.toml"
fi

# 启动服务
./ccserver

package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接MySQL
	dsn := "root:pix@6688@tcp(47.106.248.211:3306)/pixmoving-test?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接MySQL失败: %v", err)
	}
	defer db.Close()

	imsi := "pix00001"
	
	// 检查设备当前状态
	var online int
	var updatedTime string
	err = db.QueryRow("SELECT online, updated_time FROM device WHERE imsi = ?", imsi).Scan(&online, &updatedTime)
	if err != nil {
		log.Fatalf("查询设备状态失败: %v", err)
	}

	fmt.Printf("🔍 设备 %s 当前状态:\n", imsi)
	fmt.Printf("  💾 MySQL在线状态: %d (0=离线, 1=在线)\n", online)
	fmt.Printf("  🕐 最后更新时间: %s\n", updatedTime)
	
	if online == 0 {
		fmt.Println("✅ 设备当前为离线状态，符合测试条件")
	} else {
		fmt.Println("⚠️ 设备当前为在线状态")
	}
	
	// 等待一段时间，看看状态是否会变化
	fmt.Println("\n⏳ 等待10秒，观察状态变化...")
	time.Sleep(10 * time.Second)
	
	// 再次检查状态
	err = db.QueryRow("SELECT online, updated_time FROM device WHERE imsi = ?", imsi).Scan(&online, &updatedTime)
	if err != nil {
		log.Fatalf("查询设备状态失败: %v", err)
	}

	fmt.Printf("\n🔍 10秒后设备 %s 状态:\n", imsi)
	fmt.Printf("  💾 MySQL在线状态: %d (0=离线, 1=在线)\n", online)
	fmt.Printf("  🕐 最后更新时间: %s\n", updatedTime)
}

#!/bin/bash

# GPS问题模拟测试脚本
# 模拟设备连接、发送心跳、离线后再发送心跳，验证GPS时间更新问题

set -e

echo "🧪 GPS问题模拟测试"
echo "=================="
echo "目的: 模拟设备行为，验证离线后GPS时间是否仍会更新"
echo "流程: 注册 → 正常心跳 → 停止心跳(离线) → 再次心跳 → 检查GPS时间变化"
echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 检查ccserver是否运行
if ! nc -z localhost 2020 2>/dev/null; then
    echo "❌ 错误: ccserver未运行，请先启动ccserver服务"
    echo "💡 提示: 在项目根目录运行 ./ccserver 或 go run server.go"
    exit 1
fi

# 进入脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 工作目录: $SCRIPT_DIR"
echo "🔗 TCP服务器: localhost:2020"
echo "💾 Redis服务器: **************:6379"
echo ""

# 初始化Go模块（如果需要）
if [ ! -f "go.sum" ]; then
    echo "📦 初始化Go模块依赖..."
    go mod tidy
    echo "✅ 依赖初始化完成"
    echo ""
fi

# 编译并运行模拟脚本
echo "🔨 编译模拟脚本..."
go build -o simulate_gps_issue simulate_gps_issue.go

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    echo ""
    
    echo "🚀 开始运行GPS问题模拟测试..."
    echo "⚠️  注意: 脚本将连接到本地ccserver和远程Redis"
    echo "📊 测试过程中会显示详细的状态信息"
    echo ""
    
    # 运行模拟脚本
    ./simulate_gps_issue
    
    echo ""
    echo "🏁 模拟测试执行完成"
    
    # 清理编译文件
    rm -f simulate_gps_issue
    echo "🧹 清理临时文件完成"
else
    echo "❌ 编译失败"
    exit 1
fi

echo ""
echo "📋 测试结果说明:"
echo "- 如果显示'异常发现: 设备离线后GPS时间仍在更新'，说明问题确实存在"
echo "- 如果显示'正常: 设备离线后GPS时间未更新'，说明系统工作正常"
echo ""
echo "🔍 关键检查点:"
echo "  1. 设备注册是否成功"
echo "  2. 正常心跳是否让设备在线"
echo "  3. 停止心跳后设备是否被判定为离线"
echo "  4. 离线后发送心跳，GPS时间是否更新"
echo ""
echo "💡 如果发现问题，可以检查:"
echo "  - ccserver的心跳处理逻辑"
echo "  - Redis中GPS时间戳的更新机制"
echo "  - 设备离线判定与GPS数据处理的关系"

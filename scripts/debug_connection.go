package main

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"time"
)

// 简化的调试客户端
type DebugClient struct {
	conn   net.Conn
	billNo uint32
}

// 创建调试客户端
func NewDebugClient(serverAddr string) (*DebugClient, error) {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}
	
	return &DebugClient{
		conn:   conn,
		billNo: 1,
	}, nil
}

// 关闭连接
func (c *DebugClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

// 发送原始数据并读取响应
func (c *DebugClient) sendRawData(data []byte) error {
	fmt.Printf("📤 发送数据 (%d字节):\n", len(data))
	fmt.Printf("   Hex: %s\n", hex.EncodeToString(data))
	fmt.Printf("   ASCII: %q\n", string(data))
	
	// 发送数据
	n, err := c.conn.Write(data)
	if err != nil {
		return fmt.Errorf("发送失败: %v", err)
	}
	fmt.Printf("✅ 成功发送 %d 字节\n", n)
	
	// 设置读取超时
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	
	// 读取响应
	buffer := make([]byte, 1024)
	n, err = c.conn.Read(buffer)
	if err != nil {
		fmt.Printf("⚠️  读取响应失败: %v\n", err)
		return err
	}
	
	response := buffer[:n]
	fmt.Printf("📥 收到响应 (%d字节):\n", n)
	fmt.Printf("   Hex: %s\n", hex.EncodeToString(response))
	fmt.Printf("   ASCII: %q\n", string(response))
	
	return nil
}

// 构建简单的注册消息
func (c *DebugClient) buildRegisterMessage() []byte {
	// 构建JSON消息体
	register := map[string]interface{}{
		"cmd":        1,
		"imei":       "123456789012345",
		"imsi":       "pix00001",
		"model":      "TEST_DEVICE",
		"sw":         "1.0.0",
		"hd":         "1.0.0",
		"vendor":     "TEST",
		"ci":         "TEST_CI",
		"pci":        "TEST_PCI",
		"manage_ip":  "*************",
		"position":   "测试位置",
		"lng":        113.91040802001953,
		"lat":        22.487064361572266,
	}
	
	msgBody, _ := json.Marshal(register)
	
	// 构建消息头
	var buffer bytes.Buffer
	
	// 起始标识 @<
	buffer.WriteByte('@')
	buffer.WriteByte('<')
	
	// 消息长度 (消息头46字节 + 消息体长度)
	msgLen := uint16(46 + len(msgBody))
	binary.Write(&buffer, binary.BigEndian, msgLen)
	
	// 流水号
	binary.Write(&buffer, binary.BigEndian, c.billNo)
	c.billNo++
	
	// 消息体格式 (0x0000 = JSON)
	binary.Write(&buffer, binary.BigEndian, uint16(0x0000))
	
	// 协议版本 (0x01)
	buffer.WriteByte(0x01)
	
	// 加密方式 (0x00 = 未加密)
	buffer.WriteByte(0x00)
	
	// 随机数
	binary.Write(&buffer, binary.BigEndian, uint32(0))
	
	// 时间戳
	binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix()))
	
	// 预留字段 (24字节)
	reserved := make([]byte, 24)
	buffer.Write(reserved)
	
	// 消息体
	buffer.Write(msgBody)
	
	// CRC16校验
	data := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data)
	binary.Write(&buffer, binary.BigEndian, checksum)
	
	return buffer.Bytes()
}

// 构建简单的心跳消息
func (c *DebugClient) buildHeartbeatMessage() []byte {
	// 构建JSON消息体
	heartbeat := map[string]interface{}{
		"cmd":        2,
		"uid":        "pix00001",
		"vType":      0,
		"acc":        1,
		"gear":       4,
		"tm":         100,
		"lng":        113.91040802001953,
		"lat":        22.487064361572266,
		"gpsLocated": true,
		"adLocated":  true,
		"mode":       3,
		"spd":        30,
		"pL":         80,
		"event":      0,
		"latestTask": map[string]interface{}{
			"step":         0,
			"taskId":       0,
			"taskDistance": 0,
		},
	}
	
	msgBody, _ := json.Marshal(heartbeat)
	
	// 构建消息头
	var buffer bytes.Buffer
	
	// 起始标识 @<
	buffer.WriteByte('@')
	buffer.WriteByte('<')
	
	// 消息长度 (消息头46字节 + 消息体长度)
	msgLen := uint16(46 + len(msgBody))
	binary.Write(&buffer, binary.BigEndian, msgLen)
	
	// 流水号
	binary.Write(&buffer, binary.BigEndian, c.billNo)
	c.billNo++
	
	// 消息体格式 (0x0000 = JSON)
	binary.Write(&buffer, binary.BigEndian, uint16(0x0000))
	
	// 协议版本 (0x01)
	buffer.WriteByte(0x01)
	
	// 加密方式 (0x00 = 未加密)
	buffer.WriteByte(0x00)
	
	// 随机数
	binary.Write(&buffer, binary.BigEndian, uint32(0))
	
	// 时间戳
	binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix()))
	
	// 预留字段 (24字节)
	reserved := make([]byte, 24)
	buffer.Write(reserved)
	
	// 消息体
	buffer.Write(msgBody)
	
	// CRC16校验
	data := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data)
	binary.Write(&buffer, binary.BigEndian, checksum)
	
	return buffer.Bytes()
}

// CRC16校验计算 (简化版)
func calculateCRC16(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 监听连接状态
func (c *DebugClient) monitorConnection() {
	go func() {
		reader := bufio.NewReader(c.conn)
		for {
			// 尝试读取数据
			c.conn.SetReadDeadline(time.Now().Add(1 * time.Second))
			data, err := reader.ReadByte()
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // 超时继续
				}
				fmt.Printf("🔌 连接断开: %v\n", err)
				return
			}
			fmt.Printf("📨 收到数据: 0x%02x\n", data)
		}
	}()
}

func main() {
	fmt.Println("🔍 TCP连接调试工具")
	fmt.Println("==================")
	
	// 创建调试客户端
	client, err := NewDebugClient("localhost:2020")
	if err != nil {
		log.Fatalf("创建客户端失败: %v", err)
	}
	defer client.Close()
	
	fmt.Println("✅ TCP连接建立成功")
	
	// 启动连接监控
	client.monitorConnection()
	
	// 发送注册消息
	fmt.Println("\n📝 发送注册消息:")
	registerMsg := client.buildRegisterMessage()
	if err := client.sendRawData(registerMsg); err != nil {
		log.Printf("注册失败: %v", err)
	}
	
	// 等待一下
	time.Sleep(3 * time.Second)
	
	// 发送心跳消息
	fmt.Println("\n💓 发送心跳消息:")
	heartbeatMsg := client.buildHeartbeatMessage()
	if err := client.sendRawData(heartbeatMsg); err != nil {
		log.Printf("心跳失败: %v", err)
	}
	
	// 保持连接一段时间观察
	fmt.Println("\n⏳ 保持连接10秒，观察服务端行为...")
	time.Sleep(10 * time.Second)
	
	fmt.Println("\n🏁 调试完成")
}

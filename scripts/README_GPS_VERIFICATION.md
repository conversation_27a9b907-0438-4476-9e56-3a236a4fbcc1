# GPS问题验证脚本

## 🎯 问题描述

发现一个潜在问题：**设备已经被判定为离线，但最后有效GPS时间仍在更新**。

这种情况在逻辑上是矛盾的，因为：
- 设备离线意味着没有心跳数据上报
- GPS时间更新意味着仍有数据在处理
- 两者不应该同时发生

## 🔍 验证脚本功能

### 核心检查逻辑

1. **设备状态获取**
   - 从MySQL `device` 表获取设备在线状态 (`online` 字段)
   - 从Redis `DEVICE_HEARTBEAT_TS` 获取最后心跳时间戳
   - 从Redis `LAST_HEARTBEAT` 获取心跳数据中的最后有效GPS时间戳

2. **异常检测**
   - 判断设备是否离线：`online = 0`
   - 判断GPS时间是否在更新：与上一轮检查对比
   - 识别异常：设备离线 + GPS时间在更新

3. **持续监控**
   - 10轮检查，每轮间隔30秒
   - 实时统计在线/离线/异常设备数量
   - 详细显示异常设备信息

### 检查的数据源

```go
// MySQL数据源
SELECT imsi, online FROM device WHERE imsi != ''

// Redis数据源
HGET DEVICE_HEARTBEAT_TS {imsi}        // 心跳时间戳
HGET LAST_HEARTBEAT {imsi}             // 心跳数据（包含GPS时间）
```

### 异常判定条件

```go
// 设备被判定为异常的条件
device.IsAnomalous = (device.Online == 0) && device.GpsTimeUpdating

// 其中：
// device.Online == 0: MySQL中设备状态为离线
// device.GpsTimeUpdating: GPS时间相比上一轮检查有更新
```

## 🚀 使用方法

### 方法1: 使用运行脚本（推荐）

```bash
# 进入项目根目录
cd /path/to/ccserver

# 运行验证脚本
./scripts/run_verification.sh
```

### 方法2: 手动编译运行

```bash
# 进入脚本目录
cd scripts

# 初始化Go模块
go mod tidy

# 编译
go build -o verify_gps_issue verify_gps_issue.go

# 运行
./verify_gps_issue
```

## 📊 输出示例

### 正常情况输出
```
🔍 开始验证设备离线但GPS时间仍在更新的问题...
================================================================================

🕐 第 1 轮检查 - 2024-01-15 14:30:00
------------------------------------------------------------
📊 统计结果: 总设备=25, 在线=18, 离线=7, 异常=0
✅ 未发现异常设备
```

### 发现异常时输出
```
🕐 第 2 轮检查 - 2024-01-15 14:30:30
------------------------------------------------------------
📊 统计结果: 总设备=25, 在线=18, 离线=7, 异常=2

🚨 发现异常设备（离线但GPS时间在更新）:
  📱 设备: 1234567890
     💾 MySQL在线状态: 0 (0=离线, 1=在线)
     💓 最后心跳时间: 2024-01-15 14:29:45 (1705298985)
     🛰️  最后有效GPS时间: 2024-01-15 14:30:25 (1705299025)
     ⏰ 当前时间: 2024-01-15 14:30:30 (1705299030)
     ⚠️  心跳超时: true
     🔄 GPS时间更新: true
     🚨 异常状态: true
```

## 🔧 配置说明

脚本使用的配置信息（与ccserver配置一致）：

```go
MySQL: {
    Host:     "**************",
    Port:     3306,
    User:     "root", 
    Password: "pix@6688",
    Database: "pixmoving-test",
}

Redis: {
    Host:     "**************",
    Port:     6379,
    Password: "Pix@121cc",
    DB:       3,
}

HeartbeatTimeout: 10 // 10秒超时
```

## 🐛 可能的问题原因

如果发现异常设备，可能的原因包括：

1. **缓存数据不一致**
   - MySQL中设备状态已更新为离线
   - Redis中的GPS数据未及时清理

2. **并发更新问题**
   - 心跳检测器更新设备离线状态
   - 同时有其他进程在更新GPS数据

3. **数据清理机制缺失**
   - 设备离线时未清理相关缓存数据
   - GPS数据残留在Redis中

4. **时间戳更新逻辑错误**
   - GPS时间戳更新逻辑存在bug
   - 在不应该更新的时候进行了更新

## 📝 后续处理建议

1. **如果发现异常**
   - 记录异常设备的IMSI
   - 检查相关日志文件
   - 分析GPS时间更新的来源

2. **代码审查重点**
   - 检查GPS时间戳更新的所有位置
   - 确认设备离线时的数据清理逻辑
   - 验证并发访问的安全性

3. **监控改进**
   - 将此脚本加入定时监控
   - 设置异常告警机制
   - 建立数据一致性检查流程

## 📁 文件说明

- `verify_gps_issue.go`: 主验证脚本
- `go.mod`: Go模块依赖文件
- `run_verification.sh`: 便捷运行脚本
- `README_GPS_VERIFICATION.md`: 本说明文档

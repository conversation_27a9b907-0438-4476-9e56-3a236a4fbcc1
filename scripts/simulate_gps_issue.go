package main

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"golang.org/x/net/context"
)

// YHL协议常量
const (
	YHL_MSG_HEADER_LEN = 46
	YHL_MSG_START_CHAR1 = '@'
	YHL_MSG_START_CHAR2 = '<'
	
	CMD_REGISTER  = 0x0001
	CMD_HEARTBEAT = 0x0002
	
	PROTOCOL_VERSION_V1 = 0x01
	ENCRYPTION_NONE = 0x00
)

// YHL协议消息头
type YhlMsgHeader struct {
	MsgLen        uint16
	BillNo        uint32
	MsgFormat     uint16
	ProtocolVer   uint8
	EncryptedType uint8
	EncryptedRand uint32
	Timestamp     uint64
	Reserved      [24]byte
}

// 设备注册消息
type RegisterMessage struct {
	Cmd         int     `json:"cmd"`
	IMEI        string  `json:"imei"`
	IMSI        string  `json:"imsi"`
	Model       string  `json:"model"`
	SoftwareVer string  `json:"sw"`
	HardwareVer string  `json:"hd"`
	Vendor      string  `json:"vendor"`
	CI          string  `json:"ci"`
	PCI         string  `json:"pci"`
	ManageIP    string  `json:"manage_ip"`
	Position    string  `json:"position"`
	Longitude   float64 `json:"lng"`
	Latitude    float64 `json:"lat"`
}

// 心跳消息
type HeartbeatMessage struct {
	Cmd       int     `json:"cmd"`
	UID       string  `json:"uid"`
	VType     int     `json:"vType"`
	ACC       int     `json:"acc"`
	Gear      int     `json:"gear"`
	TM        int     `json:"tm"`
	Longitude float64 `json:"lng"`
	Latitude  float64 `json:"lat"`
	LatestTask struct {
		Step         int `json:"step"`
		TaskID       int `json:"taskId"`
		TaskDistance int `json:"taskDistance"`
	} `json:"latestTask"`
	GPSLocated bool    `json:"gpsLocated"`
	ADLocated  bool    `json:"adLocated"`
	Mode       int     `json:"mode"`
	Speed      int     `json:"spd"`
	PowerLevel int     `json:"pL"`
	Event      int     `json:"event"`
}

// Redis心跳数据结构
type HeartbeatRedis struct {
	Ts                 int64   `json:"ts"`
	LastValidLatitude  float64 `json:"last_valid_lat"`
	LastValidLongitude float64 `json:"last_valid_lng"`
	LastValidGpsTs     int64   `json:"last_valid_gps_ts"`
}

// YHL协议客户端
type YhlClient struct {
	conn   net.Conn
	billNo uint32
	imsi   string
}

// 创建新的YHL客户端
func NewYhlClient(serverAddr, imsi string) (*YhlClient, error) {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}
	
	return &YhlClient{
		conn:   conn,
		billNo: 1,
		imsi:   imsi,
	}, nil
}

// 关闭连接
func (c *YhlClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

// 构建YHL协议消息
func (c *YhlClient) buildMessage(data interface{}) ([]byte, error) {
	// 序列化消息体
	msgBody, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化消息体失败: %v", err)
	}

	// 构建消息头
	header := YhlMsgHeader{
		BillNo:        c.billNo,
		MsgFormat:     0x0000, // JSON格式
		ProtocolVer:   PROTOCOL_VERSION_V1,
		EncryptedType: ENCRYPTION_NONE,
		EncryptedRand: 0,
		Timestamp:     uint64(time.Now().Unix()),
	}

	// 计算消息长度（消息头 + 消息体）
	header.MsgLen = YHL_MSG_HEADER_LEN + uint16(len(msgBody))

	// 构建完整消息
	var buffer bytes.Buffer
	
	// 写入起始标识
	buffer.WriteByte(YHL_MSG_START_CHAR1)
	buffer.WriteByte(YHL_MSG_START_CHAR2)
	
	// 写入消息头
	binary.Write(&buffer, binary.BigEndian, header.MsgLen)
	binary.Write(&buffer, binary.BigEndian, header.BillNo)
	binary.Write(&buffer, binary.BigEndian, header.MsgFormat)
	binary.Write(&buffer, binary.BigEndian, header.ProtocolVer)
	binary.Write(&buffer, binary.BigEndian, header.EncryptedType)
	binary.Write(&buffer, binary.BigEndian, header.EncryptedRand)
	binary.Write(&buffer, binary.BigEndian, header.Timestamp)
	buffer.Write(header.Reserved[:])
	
	// 写入消息体
	buffer.Write(msgBody)
	
	// 计算并写入CRC16校验
	data_for_crc := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data_for_crc)
	binary.Write(&buffer, binary.BigEndian, checksum)
	
	c.billNo++
	return buffer.Bytes(), nil
}

// CRC16校验计算
func calculateCRC16(data []byte) uint16 {
	var crcHi = [256]uint8{
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
		0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
		0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
		0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
		0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
		0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
		0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
		0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
		0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
		0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
		0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
		0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
		0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
		0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
		0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
	}
	
	var crcLo = [256]uint8{
		0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
		0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
		0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
		0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
		0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
		0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
		0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
		0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
		0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
		0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
		0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
		0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
		0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
		0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
		0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
		0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
		0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
		0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
		0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
		0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
		0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
		0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
		0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
		0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
		0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
		0x43, 0x83, 0x41, 0x81, 0x80, 0x40,
	}

	var ucCRCHi uint8 = 0xFF
	var ucCRCLo uint8 = 0xFF
	var idx uint8

	for _, b := range data {
		idx = ucCRCLo ^ b
		ucCRCLo = ucCRCHi ^ crcHi[idx]
		ucCRCHi = crcLo[idx]
	}

	return (uint16(ucCRCHi) << 8) + uint16(ucCRCLo)
}

// 发送消息
func (c *YhlClient) sendMessage(data interface{}) error {
	msg, err := c.buildMessage(data)
	if err != nil {
		return err
	}
	
	_, err = c.conn.Write(msg)
	return err
}

// 发送注册消息
func (c *YhlClient) sendRegister() error {
	register := RegisterMessage{
		Cmd:         CMD_REGISTER,
		IMEI:        "123456789012345",
		IMSI:        c.imsi,
		Model:       "TEST_DEVICE",
		SoftwareVer: "1.0.0",
		HardwareVer: "1.0.0",
		Vendor:      "TEST",
		CI:          "TEST_CI",
		PCI:         "TEST_PCI",
		ManageIP:    "*************",
		Position:    "测试位置",
		Longitude:   113.91040802001953,
		Latitude:    22.487064361572266,
	}
	
	return c.sendMessage(register)
}

// 发送心跳消息
func (c *YhlClient) sendHeartbeat(lng, lat float64) error {
	heartbeat := HeartbeatMessage{
		Cmd:       CMD_HEARTBEAT,
		UID:       c.imsi,
		VType:     0,
		ACC:       1,
		Gear:      4,
		TM:        100 + rand.Intn(50), // 随机里程变化
		Longitude: lng,
		Latitude:  lat,
		GPSLocated: true,
		ADLocated:  true,
		Mode:       3,
		Speed:      30,
		PowerLevel: 80,
		Event:      0,
	}
	
	heartbeat.LatestTask.Step = 0
	heartbeat.LatestTask.TaskID = 0
	heartbeat.LatestTask.TaskDistance = 0
	
	return c.sendMessage(heartbeat)
}

// Redis监控器
type RedisMonitor struct {
	client *redis.Client
	imsi   string
}

// 创建Redis监控器
func NewRedisMonitor(imsi string) (*RedisMonitor, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.106.248.211:6379",
		Password: "Pix@121cc",
		DB:       3,
	})

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("连接Redis失败: %v", err)
	}

	return &RedisMonitor{
		client: rdb,
		imsi:   imsi,
	}, nil
}

// 获取设备状态信息
func (m *RedisMonitor) getDeviceStatus() (map[string]interface{}, error) {
	ctx := context.Background()
	status := make(map[string]interface{})

	// 获取心跳时间戳
	heartbeatTs, err := m.client.HGet(ctx, "DEVICE_HEARTBEAT_TS", m.imsi).Int64()
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("获取心跳时间戳失败: %v", err)
	}
	status["heartbeat_ts"] = heartbeatTs

	// 获取心跳数据
	heartbeatData, err := m.client.HGet(ctx, "LAST_HEARTBEAT", m.imsi).Result()
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("获取心跳数据失败: %v", err)
	}

	if err != redis.Nil {
		var hbData HeartbeatRedis
		if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
			status["last_valid_gps_ts"] = hbData.LastValidGpsTs
			status["last_valid_lat"] = hbData.LastValidLatitude
			status["last_valid_lng"] = hbData.LastValidLongitude
		}
	}

	status["current_time"] = time.Now().Unix()
	return status, nil
}

// 打印状态信息
func (m *RedisMonitor) printStatus(prefix string) {
	status, err := m.getDeviceStatus()
	if err != nil {
		log.Printf("获取状态失败: %v", err)
		return
	}

	currentTime := status["current_time"].(int64)
	heartbeatTs := status["heartbeat_ts"].(int64)

	fmt.Printf("%s 设备状态 [%s]:\n", prefix, time.Now().Format("15:04:05"))
	fmt.Printf("  📱 设备IMSI: %s\n", m.imsi)
	fmt.Printf("  💓 心跳时间戳: %d (%s)\n", heartbeatTs,
		time.Unix(heartbeatTs, 0).Format("15:04:05"))

	if gpsTs, exists := status["last_valid_gps_ts"]; exists && gpsTs != nil {
		gpsTimestamp := gpsTs.(int64)
		fmt.Printf("  🛰️  最后有效GPS时间: %d (%s)\n", gpsTimestamp,
			time.Unix(gpsTimestamp, 0).Format("15:04:05"))

		if lat, exists := status["last_valid_lat"]; exists {
			fmt.Printf("  📍 最后有效位置: lat=%.6f, lng=%.6f\n",
				lat.(float64), status["last_valid_lng"].(float64))
		}
	} else {
		fmt.Printf("  🛰️  最后有效GPS时间: 未设置\n")
	}

	fmt.Printf("  ⏰ 当前时间: %d (%s)\n", currentTime,
		time.Unix(currentTime, 0).Format("15:04:05"))
	fmt.Printf("  ⚠️  心跳超时: %t (超时阈值: 10秒)\n",
		(currentTime - heartbeatTs) > 10)
	fmt.Println()
}

// 主测试函数
func main() {
	// 生成随机IMSI用于测试
	rand.Seed(time.Now().UnixNano())
	testIMSI := fmt.Sprintf("TEST_%d", rand.Intn(10000))

	fmt.Println("🧪 GPS问题模拟测试")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("📱 测试设备IMSI: %s\n", testIMSI)
	fmt.Printf("🎯 测试目标: 验证设备离线后GPS时间是否仍会更新\n")
	fmt.Printf("⏰ 开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println()

	// 创建TCP客户端
	client, err := NewYhlClient("localhost:2020", testIMSI)
	if err != nil {
		log.Fatalf("创建TCP客户端失败: %v", err)
	}
	defer client.Close()

	// 创建Redis监控器
	monitor, err := NewRedisMonitor(testIMSI)
	if err != nil {
		log.Fatalf("创建Redis监控器失败: %v", err)
	}

	// 步骤1: 发送注册消息
	fmt.Println("📝 步骤1: 发送设备注册消息")
	if err := client.sendRegister(); err != nil {
		log.Fatalf("发送注册消息失败: %v", err)
	}
	fmt.Println("✅ 注册消息发送成功")
	time.Sleep(2 * time.Second)

	// 步骤2: 发送正常心跳让设备在线
	fmt.Println("💓 步骤2: 发送正常心跳 (5次，间隔2秒)")
	baseLng := 113.91040802001953
	baseLat := 22.487064361572266

	for i := 0; i < 5; i++ {
		// 模拟GPS位置轻微变化
		lng := baseLng + float64(i)*0.0001
		lat := baseLat + float64(i)*0.0001

		if err := client.sendHeartbeat(lng, lat); err != nil {
			log.Printf("发送心跳失败: %v", err)
		} else {
			fmt.Printf("  ✅ 心跳 %d/5 发送成功 (位置: %.6f, %.6f)\n", i+1, lng, lat)
		}
		time.Sleep(2 * time.Second)
	}

	monitor.printStatus("🟢")

	// 步骤3: 停止发送心跳，等待设备离线
	fmt.Println("⏸️  步骤3: 停止发送心跳，等待设备离线 (15秒)")
	fmt.Println("   (心跳超时阈值为10秒，15秒后设备应该被判定为离线)")

	for i := 0; i < 15; i++ {
		fmt.Printf("  ⏳ 等待中... %d/15秒\n", i+1)
		time.Sleep(1 * time.Second)
	}

	monitor.printStatus("🔴")

	// 步骤4: 再次发送心跳，观察GPS时间是否更新
	fmt.Println("🔄 步骤4: 设备离线后再次发送心跳，观察GPS时间变化")

	// 记录发送前的GPS时间
	statusBefore, _ := monitor.getDeviceStatus()
	var gpsTimeBefore int64
	if gpsTs, exists := statusBefore["last_valid_gps_ts"]; exists && gpsTs != nil {
		gpsTimeBefore = gpsTs.(int64)
	}

	// 发送新的心跳（使用新的GPS位置）
	newLng := baseLng + 0.001  // 明显的位置变化
	newLat := baseLat + 0.001

	fmt.Printf("  📡 发送心跳 (新位置: %.6f, %.6f)\n", newLng, newLat)
	if err := client.sendHeartbeat(newLng, newLat); err != nil {
		log.Printf("发送心跳失败: %v", err)
	} else {
		fmt.Println("  ✅ 心跳发送成功")
	}

	time.Sleep(2 * time.Second)
	monitor.printStatus("🔄")

	// 检查GPS时间是否更新
	statusAfter, _ := monitor.getDeviceStatus()
	var gpsTimeAfter int64
	if gpsTs, exists := statusAfter["last_valid_gps_ts"]; exists && gpsTs != nil {
		gpsTimeAfter = gpsTs.(int64)
	}

	// 分析结果
	fmt.Println("📊 测试结果分析:")
	fmt.Printf("  🕐 发送前GPS时间: %d (%s)\n", gpsTimeBefore,
		time.Unix(gpsTimeBefore, 0).Format("15:04:05"))
	fmt.Printf("  🕐 发送后GPS时间: %d (%s)\n", gpsTimeAfter,
		time.Unix(gpsTimeAfter, 0).Format("15:04:05"))

	if gpsTimeAfter > gpsTimeBefore {
		fmt.Println("  🚨 异常发现: 设备离线后GPS时间仍在更新!")
		fmt.Printf("  📈 GPS时间增加了: %d秒\n", gpsTimeAfter - gpsTimeBefore)
		fmt.Println("  💡 这证实了问题的存在")
	} else {
		fmt.Println("  ✅ 正常: 设备离线后GPS时间未更新")
		fmt.Println("  💡 未发现异常行为")
	}

	fmt.Println()
	fmt.Println("🏁 测试完成!")
}

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
)

// Redis心跳数据结构
type HeartbeatRedis struct {
	Ts                 int64   `json:"ts"`
	LastValidLatitude  float64 `json:"last_valid_lat"`
	LastValidLongitude float64 `json:"last_valid_lng"`
	LastValidGpsTs     int64   `json:"last_valid_gps_ts"`
}

func main() {
	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.106.248.211:6379",
		Password: "Pix@121cc",
		DB:       3,
	})

	ctx := context.Background()
	
	// 测试连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("连接Redis失败: %v", err)
	}

	imsi := "pix00001"
	fmt.Printf("🔍 检查设备 %s 的Redis数据\n", imsi)
	fmt.Println(strings.Repeat("=", 40))

	// 检查心跳时间戳
	heartbeatTs, err := rdb.HGet(ctx, "DEVICE::TS::HEARTBEAT", imsi).Int64()
	if err != nil && err != redis.Nil {
		log.Printf("获取心跳时间戳失败: %v", err)
	} else if err == redis.Nil {
		fmt.Println("💓 心跳时间戳: 未设置")
	} else {
		fmt.Printf("💓 心跳时间戳: %d (%s)\n", heartbeatTs,
			time.Unix(heartbeatTs, 0).Format("2006-01-02 15:04:05"))
	}

	// 检查心跳数据
	heartbeatData, err := rdb.HGet(ctx, "DEVICE::HB::HASH", imsi).Result()
	if err != nil && err != redis.Nil {
		log.Printf("获取心跳数据失败: %v", err)
	} else if err == redis.Nil {
		fmt.Println("📊 心跳数据: 未设置")
	} else {
		fmt.Printf("📊 心跳数据 (原始): %s\n", heartbeatData)
		
		// 解析心跳数据
		var hbData HeartbeatRedis
		if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
			fmt.Printf("🛰️  最后有效GPS时间: %d (%s)\n", hbData.LastValidGpsTs,
				time.Unix(hbData.LastValidGpsTs, 0).Format("2006-01-02 15:04:05"))
			fmt.Printf("📍 最后有效位置: lat=%.6f, lng=%.6f\n", 
				hbData.LastValidLatitude, hbData.LastValidLongitude)
			fmt.Printf("⏰ 数据时间戳: %d (%s)\n", hbData.Ts,
				time.Unix(hbData.Ts, 0).Format("2006-01-02 15:04:05"))
		} else {
			fmt.Println("❌ 解析心跳数据失败")
		}
	}

	// 检查当前时间
	currentTime := time.Now().Unix()
	fmt.Printf("🕐 当前时间: %d (%s)\n", currentTime,
		time.Unix(currentTime, 0).Format("2006-01-02 15:04:05"))

	// 计算时间差
	if heartbeatTs > 0 {
		timeDiff := currentTime - heartbeatTs
		fmt.Printf("⏱️  时间差: %d秒\n", timeDiff)
		if timeDiff > 10 {
			fmt.Println("⚠️  设备应该被判定为离线 (超过10秒)")
		} else {
			fmt.Println("✅ 设备在线")
		}
	}
}

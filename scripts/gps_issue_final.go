package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"time"

	"github.com/go-redis/redis/v8"
	_ "github.com/go-sql-driver/mysql"
)

// YHL协议客户端
type YhlClient struct {
	conn   net.Conn
	billNo uint32
	imsi   string
}

// Redis心跳数据结构
type HeartbeatRedis struct {
	Ts                 int64   `json:"ts"`
	LastValidLatitude  float64 `json:"last_valid_lat"`
	LastValidLongitude float64 `json:"last_valid_lng"`
	LastValidGpsTs     int64   `json:"last_valid_gps_ts"`
}

// 创建YHL客户端
func NewYhlClient(serverAddr, imsi string) (*YhlClient, error) {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}
	
	return &YhlClient{
		conn:   conn,
		billNo: 1,
		imsi:   imsi,
	}, nil
}

// 关闭连接
func (c *YhlClient) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

// 发送注册消息
func (c *YhlClient) sendRegister() error {
	// 构建JSON消息体
	register := map[string]interface{}{
		"cmd":        1,
		"imei":       "123456789012345",
		"imsi":       c.imsi,
		"model":      "TEST_DEVICE",
		"sw":         "1.0.0",
		"hd":         "1.0.0",
		"vendor":     "TEST",
		"ci":         "TEST_CI",
		"pci":        "TEST_PCI",
		"manage_ip":  "*************",
		"position":   "测试位置",
		"lng":        113.91040802001953,
		"lat":        22.487064361572266,
	}

	msgBody, _ := json.Marshal(register)

	// 构建消息头
	var buffer bytes.Buffer

	// 起始标识 @<
	buffer.WriteByte('@')
	buffer.WriteByte('<')

	// 消息长度 (消息头46字节 + 消息体长度)
	msgLen := uint16(46 + len(msgBody))
	binary.Write(&buffer, binary.BigEndian, msgLen)

	// 流水号
	binary.Write(&buffer, binary.BigEndian, c.billNo)
	c.billNo++

	// 消息体格式 (0x0000 = JSON)
	binary.Write(&buffer, binary.BigEndian, uint16(0x0000))

	// 协议版本 (0x01)
	buffer.WriteByte(0x01)

	// 加密方式 (0x00 = 未加密)
	buffer.WriteByte(0x00)

	// 随机数
	binary.Write(&buffer, binary.BigEndian, uint32(0))

	// 时间戳
	binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix()))

	// 预留字段 (24字节)
	reserved := make([]byte, 24)
	buffer.Write(reserved)

	// 消息体
	buffer.Write(msgBody)

	// CRC16校验
	data := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data)
	binary.Write(&buffer, binary.BigEndian, checksum)

	// 发送数据
	_, err := c.conn.Write(buffer.Bytes())
	if err != nil {
		return err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return err
	}

	fmt.Printf("  📝 注册响应: %s\n", string(response[48:n-2])) // 提取JSON部分
	return nil
}

// 发送心跳消息
func (c *YhlClient) sendHeartbeat(lng, lat float64) error {
	// 构建JSON消息体
	heartbeat := map[string]interface{}{
		"cmd":        2,
		"uid":        c.imsi,
		"vType":      0,
		"acc":        1,
		"gear":       4,
		"tm":         100,
		"lng":        lng,
		"lat":        lat,
		"gpsLocated": true,
		"adLocated":  true,
		"mode":       3,
		"spd":        30,
		"pL":         80,
		"event":      0,
		"latestTask": map[string]interface{}{
			"step":         0,
			"taskId":       0,
			"taskDistance": 0,
		},
	}
	
	msgBody, _ := json.Marshal(heartbeat)
	
	// 构建消息头
	var buffer bytes.Buffer
	
	// 起始标识 @<
	buffer.WriteByte('@')
	buffer.WriteByte('<')
	
	// 消息长度 (消息头46字节 + 消息体长度)
	msgLen := uint16(46 + len(msgBody))
	binary.Write(&buffer, binary.BigEndian, msgLen)
	
	// 流水号
	binary.Write(&buffer, binary.BigEndian, c.billNo)
	c.billNo++
	
	// 消息体格式 (0x0000 = JSON)
	binary.Write(&buffer, binary.BigEndian, uint16(0x0000))
	
	// 协议版本 (0x01)
	buffer.WriteByte(0x01)
	
	// 加密方式 (0x00 = 未加密)
	buffer.WriteByte(0x00)
	
	// 随机数
	binary.Write(&buffer, binary.BigEndian, uint32(0))
	
	// 时间戳
	binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix()))
	
	// 预留字段 (24字节)
	reserved := make([]byte, 24)
	buffer.Write(reserved)
	
	// 消息体
	buffer.Write(msgBody)
	
	// CRC16校验
	data := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data)
	binary.Write(&buffer, binary.BigEndian, checksum)
	
	// 发送数据
	_, err := c.conn.Write(buffer.Bytes())
	if err != nil {
		return err
	}
	
	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return err
	}
	
	fmt.Printf("  📡 心跳响应: %s\n", string(response[48:n-2])) // 提取JSON部分
	return nil
}

// CRC16校验计算
func calculateCRC16(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 检查设备状态
func checkDeviceStatus(imsi string) {
	// 连接MySQL
	dsn := "root:pix@6688@tcp(47.106.248.211:3306)/pixmoving-test?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Printf("连接MySQL失败: %v", err)
		return
	}
	defer db.Close()

	// 连接Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.106.248.211:6379",
		Password: "Pix@121cc",
		DB:       3,
	})
	ctx := context.Background()

	// 查询MySQL设备状态
	var online int
	var updatedTime string
	err = db.QueryRow("SELECT online, updated_time FROM device WHERE imsi = ?", imsi).Scan(&online, &updatedTime)
	if err != nil {
		log.Printf("查询设备状态失败: %v", err)
		return
	}

	// 查询Redis心跳时间戳
	heartbeatTs, err := rdb.HGet(ctx, "DEVICE::TS::HEARTBEAT", imsi).Int64()
	if err != nil && err != redis.Nil {
		log.Printf("获取心跳时间戳失败: %v", err)
		heartbeatTs = 0
	}

	// 查询Redis心跳数据
	var gpsTs int64
	var lat, lng float64
	heartbeatData, err := rdb.HGet(ctx, "DEVICE::HB::HASH", imsi).Result()
	if err == nil {
		var hbData HeartbeatRedis
		if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
			gpsTs = hbData.LastValidGpsTs
			lat = hbData.LastValidLatitude
			lng = hbData.LastValidLongitude
		}
	}

	// 显示状态
	currentTime := time.Now().Unix()
	fmt.Printf("📊 设备状态 [%s]:\n", time.Now().Format("15:04:05"))
	fmt.Printf("  💾 MySQL: online=%d, updated=%s\n", online, updatedTime)
	fmt.Printf("  💓 Redis心跳时间: %d (%s)\n", heartbeatTs, 
		time.Unix(heartbeatTs, 0).Format("15:04:05"))
	fmt.Printf("  🛰️  GPS时间: %d (%s)\n", gpsTs,
		time.Unix(gpsTs, 0).Format("15:04:05"))
	fmt.Printf("  📍 GPS位置: lat=%.6f, lng=%.6f\n", lat, lng)
	fmt.Printf("  ⏰ 当前时间: %d (%s)\n", currentTime,
		time.Unix(currentTime, 0).Format("15:04:05"))
	
	if heartbeatTs > 0 {
		timeDiff := currentTime - heartbeatTs
		fmt.Printf("  ⏱️  心跳时间差: %d秒\n", timeDiff)
		if timeDiff > 10 {
			fmt.Printf("  🔴 设备应该离线 (超过10秒)\n")
		} else {
			fmt.Printf("  🟢 设备在线\n")
		}
	}
	fmt.Println()
}

func main() {
	imsi := "pix00001"
	
	fmt.Println("🧪 GPS问题完整测试")
	fmt.Println("==================")
	fmt.Printf("📱 测试设备: %s\n", imsi)
	fmt.Printf("🎯 目标: 验证设备离线后GPS时间是否仍会更新\n")
	fmt.Println()

	// 步骤1: 发送注册消息
	fmt.Println("📝 步骤1: 发送设备注册消息")

	client, err := NewYhlClient("localhost:2020", imsi)
	if err != nil {
		log.Fatalf("创建客户端失败: %v", err)
	}

	if err := client.sendRegister(); err != nil {
		log.Printf("发送注册失败: %v", err)
	} else {
		fmt.Println("  ✅ 注册成功")
	}

	time.Sleep(2 * time.Second)

	// 步骤2: 发送正常心跳让设备在线
	fmt.Println("💓 步骤2: 发送正常心跳 (3次，间隔2秒)")

	for i := 0; i < 3; i++ {
		lng := 113.91040802001953 + float64(i)*0.0001
		lat := 22.487064361572266 + float64(i)*0.0001

		fmt.Printf("  📡 发送心跳 %d/3 (位置: %.6f, %.6f)\n", i+1, lng, lat)
		if err := client.sendHeartbeat(lng, lat); err != nil {
			log.Printf("发送心跳失败: %v", err)
		}

		time.Sleep(2 * time.Second)
	}

	checkDeviceStatus(imsi)

	// 步骤3: 等待设备离线（但保持连接）
	fmt.Println("⏸️  步骤3: 等待设备离线 (15秒) - 保持连接但不发送心跳")
	for i := 0; i < 15; i++ {
		fmt.Printf("  ⏳ 等待中... %d/15秒\n", i+1)
		time.Sleep(1 * time.Second)
	}

	checkDeviceStatus(imsi)

	// 步骤4: 记录离线后的GPS时间
	fmt.Println("🔍 步骤4: 记录当前GPS时间")

	// 连接Redis获取当前GPS时间
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.106.248.211:6379",
		Password: "Pix@121cc",
		DB:       3,
	})
	ctx := context.Background()

	var gpsTimeBefore int64
	heartbeatData, err := rdb.HGet(ctx, "DEVICE::HB::HASH", imsi).Result()
	if err == nil {
		var hbData HeartbeatRedis
		if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
			gpsTimeBefore = hbData.LastValidGpsTs
		}
	}

	fmt.Printf("  🕐 离线前GPS时间: %d (%s)\n", gpsTimeBefore,
		time.Unix(gpsTimeBefore, 0).Format("15:04:05"))

	// 步骤5: 设备离线后发送心跳（使用同一个连接）
	fmt.Println("🔄 步骤5: 设备离线后发送心跳（使用同一个已授权连接）")

	newLng := 113.91040802001953 + 0.001  // 明显的位置变化
	newLat := 22.487064361572266 + 0.001

	fmt.Printf("  📡 发送心跳 (新位置: %.6f, %.6f)\n", newLng, newLat)
	if err := client.sendHeartbeat(newLng, newLat); err != nil {
		log.Printf("发送心跳失败: %v", err)
	}

	time.Sleep(2 * time.Second)
	checkDeviceStatus(imsi)

	// 关闭连接
	client.Close()

	// 步骤6: 检查GPS时间是否更新
	var gpsTimeAfter int64
	heartbeatData, err = rdb.HGet(ctx, "DEVICE::HB::HASH", imsi).Result()
	if err == nil {
		var hbData HeartbeatRedis
		if json.Unmarshal([]byte(heartbeatData), &hbData) == nil {
			gpsTimeAfter = hbData.LastValidGpsTs
		}
	}
	
	fmt.Println("📊 测试结果:")
	fmt.Printf("  🕐 离线前GPS时间: %d (%s)\n", gpsTimeBefore,
		time.Unix(gpsTimeBefore, 0).Format("15:04:05"))
	fmt.Printf("  🕐 离线后GPS时间: %d (%s)\n", gpsTimeAfter,
		time.Unix(gpsTimeAfter, 0).Format("15:04:05"))
	
	if gpsTimeAfter > gpsTimeBefore {
		fmt.Println("  🚨 异常发现: 设备离线后GPS时间仍在更新!")
		fmt.Printf("  📈 GPS时间增加了: %d秒\n", gpsTimeAfter - gpsTimeBefore)
		fmt.Println("  💡 这证实了问题的存在")
	} else {
		fmt.Println("  ✅ 正常: 设备离线后GPS时间未更新")
	}
	
	fmt.Println("\n🏁 测试完成!")
}

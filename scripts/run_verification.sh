#!/bin/bash

# GPS问题验证脚本运行器
# 用于验证设备离线但GPS时间仍在更新的问题

set -e

echo "🔍 GPS问题验证脚本"
echo "=================="
echo "目的: 验证设备离线但最后有效GPS时间仍在更新的问题"
echo "监控: 10轮检查，每轮间隔30秒"
echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 进入脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 工作目录: $SCRIPT_DIR"
echo ""

# 初始化Go模块（如果需要）
if [ ! -f "go.sum" ]; then
    echo "📦 初始化Go模块依赖..."
    go mod tidy
    echo "✅ 依赖初始化完成"
    echo ""
fi

# 编译并运行验证脚本
echo "🔨 编译验证脚本..."
go build -o verify_gps_issue verify_gps_issue.go

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    echo ""
    
    echo "🚀 开始运行验证脚本..."
    echo "⚠️  注意: 脚本将连接到生产环境的MySQL和Redis"
    echo "📊 监控过程中会显示实时统计信息"
    echo ""
    
    # 运行验证脚本
    ./verify_gps_issue
    
    echo ""
    echo "🏁 验证脚本执行完成"
    
    # 清理编译文件
    rm -f verify_gps_issue
    echo "🧹 清理临时文件完成"
else
    echo "❌ 编译失败"
    exit 1
fi

echo ""
echo "📋 验证结果说明:"
echo "- 如果发现异常设备，说明存在设备离线但GPS时间仍在更新的问题"
echo "- 异常设备会显示详细的状态信息，包括:"
echo "  * MySQL中的在线状态"
echo "  * Redis中的心跳时间戳"
echo "  * Redis中的最后有效GPS时间戳"
echo "  * 各项时间的对比分析"
echo ""
echo "💡 如需持续监控，可以将此脚本加入定时任务"

package main

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"time"
)

// 简单的心跳测试，验证GPS回退功能
func main() {
	fmt.Println("🧪 GPS回退功能测试")
	fmt.Println("==================")
	
	// 创建TCP连接
	conn, err := net.Dial("tcp", "localhost:2020")
	if err != nil {
		log.Fatalf("连接失败: %v", err)
	}
	defer conn.Close()
	
	imsi := "pix00001"
	
	// 发送注册消息
	fmt.Println("📝 发送注册消息...")
	if err := sendRegister(conn, imsi); err != nil {
		log.Fatalf("注册失败: %v", err)
	}
	
	time.Sleep(1 * time.Second)
	
	// 发送有效GPS心跳
	fmt.Println("💓 发送有效GPS心跳...")
	if err := sendHeartbeat(conn, imsi, 113.911408, 22.488064, true); err != nil {
		log.Printf("发送心跳失败: %v", err)
	}
	
	time.Sleep(2 * time.Second)
	
	// 发送无效GPS心跳（应该使用回退逻辑）
	fmt.Println("💓 发送无效GPS心跳（测试回退功能）...")
	if err := sendHeartbeat(conn, imsi, 0, 0, false); err != nil {
		log.Printf("发送心跳失败: %v", err)
	}
	
	time.Sleep(2 * time.Second)
	
	// 再发送一次有效GPS心跳
	fmt.Println("💓 发送有效GPS心跳...")
	if err := sendHeartbeat(conn, imsi, 113.912408, 22.489064, true); err != nil {
		log.Printf("发送心跳失败: %v", err)
	}
	
	fmt.Println("✅ GPS回退功能测试完成")
	fmt.Println("💡 请检查日志确认GPS回退逻辑是否正常工作")
}

func sendRegister(conn net.Conn, imsi string) error {
	register := map[string]interface{}{
		"cmd":        1,
		"imei":       "123456789012345",
		"imsi":       imsi,
		"model":      "TEST_DEVICE",
		"sw":         "1.0.0",
		"hd":         "1.0.0",
		"vendor":     "TEST",
		"ci":         "TEST_CI",
		"pci":        "TEST_PCI",
		"manage_ip":  "*************",
		"position":   "测试位置",
		"lng":        113.91040802001953,
		"lat":        22.487064361572266,
	}
	
	return sendMessage(conn, register)
}

func sendHeartbeat(conn net.Conn, imsi string, lng, lat float64, gpsValid bool) error {
	heartbeat := map[string]interface{}{
		"cmd":        2,
		"uid":        imsi,
		"vType":      0,
		"acc":        1,
		"gear":       4,
		"tm":         100,
		"lng":        lng,
		"lat":        lat,
		"gpsLocated": gpsValid,
		"adLocated":  true,
		"mode":       3,
		"spd":        30,
		"pL":         80,
		"event":      0,
		"latestTask": map[string]interface{}{
			"step":         0,
			"taskId":       0,
			"taskDistance": 0,
		},
	}
	
	return sendMessage(conn, heartbeat)
}

func sendMessage(conn net.Conn, data interface{}) error {
	msgBody, _ := json.Marshal(data)
	
	var buffer bytes.Buffer
	buffer.WriteByte('@')
	buffer.WriteByte('<')
	
	msgLen := uint16(46 + len(msgBody))
	binary.Write(&buffer, binary.BigEndian, msgLen)
	binary.Write(&buffer, binary.BigEndian, uint32(1)) // billNo
	binary.Write(&buffer, binary.BigEndian, uint16(0x0000)) // JSON格式
	buffer.WriteByte(0x01) // 协议版本
	buffer.WriteByte(0x00) // 未加密
	binary.Write(&buffer, binary.BigEndian, uint32(0)) // 随机数
	binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix())) // 时间戳
	
	reserved := make([]byte, 24)
	buffer.Write(reserved)
	buffer.Write(msgBody)
	
	// 简化的CRC16
	data_for_crc := buffer.Bytes()[2:]
	checksum := calculateCRC16(data_for_crc)
	binary.Write(&buffer, binary.BigEndian, checksum)
	
	_, err := conn.Write(buffer.Bytes())
	if err != nil {
		return err
	}
	
	// 读取响应
	response := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	n, err := conn.Read(response)
	if err != nil {
		return err
	}
	
	fmt.Printf("  📡 响应: %s\n", string(response[48:n-2]))
	return nil
}

func calculateCRC16(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

# 数据导出字段分析文档

## 📋 目录
- [1. 概述](#1-概述)
- [2. 已确认字段](#2-已确认字段)
- [3. 待确认字段](#3-待确认字段)
- [4. 数据获取SQL示例](#4-数据获取sql示例)
- [5. 实现建议](#5-实现建议)

---

## 1. 概述

### 1.1 导出字段总览
本文档分析任务数据导出功能中的21个字段，包括已确认的数据源和待确认的获取方式。

### 1.2 完整字段列表
```
运行任务、开始时间、结束时间、状态、车牌、车型、理论规划里程(M)、
计划时长(min)、实际时长(min)、出发时间、最后到达时间、任务类型、
任务车辆用途、开始里程值(M)、结束里程值(M)、实际运行里程(M)、
实际运行速度(KM/h)、任务创建人、出发点名称、目的地点名称、途径点
```

---

## 2. 已确认字段

### 2.1 基础信息字段 ✅

| 序号 | 字段名 | 数据源类型 | 具体数据源 | 获取方式 | 备注 |
|------|--------|------------|------------|----------|------|
| 1 | **运行任务** | 数据库表 | `task_child.id` | 直接取值 | 子任务ID |
| 5 | **车牌** | 数据库表 | `task.imsi` | 直接取值 | 设备标识 |
| 6 | **车型** | 数据库表 | `device.type` | 直接取值 | 0:RoboBus, 1:安防小车, 2:无人清扫车, 3:物流车, 6:饮品零售机器人 |
| 12 | **任务类型** | 数据库表 | `task_child.type` | 直接取值 | 子任务类型字段 |
| 18 | **任务创建人** | 数据库表 | `task.uid` → `users.username` | 关联查询 | 通过用户ID查询用户名 |

### 2.2 里程距离字段 ✅

| 序号 | 字段名 | 数据源 | 获取方式 | 单位转换 |
|------|--------|--------|----------|----------|
| 7 | **理论规划里程(M)** | `latestTask.taskDistance` | 心跳数据中任务开始时 | 已为米，无需转换 |

### 2.3 地点信息字段 ✅

| 序号 | 字段名 | 数据源 | 获取方式 | 备注 |
|------|--------|--------|----------|------|
| 19 | **出发点名称** | `latestTask.currentStation.name` | 心跳数据中当前站点 | 任务开始时的站点 |
| 20 | **目的地点名称** | `latestTask.targetStation.name` | 心跳数据中目标站点 | 任务目标站点 |
| 21 | **途径点** | 中间子任务站点 | 按order排序的中间站点名称 | 逗号分隔的站点列表 |

### 2.4 获取示例代码

```sql
-- 基础信息查询
SELECT 
    tc.id as 运行任务,
    t.imsi as 车牌,
    d.type as 车型,
    tc.type as 任务类型,
    u.username as 任务创建人
FROM task_child tc
JOIN task t ON tc.parent_id = t.id
JOIN device d ON t.imsi = d.imsi  
JOIN users u ON t.uid = u.id
WHERE tc.status = 3;  -- 已完成的任务

-- 途径点查询
SELECT GROUP_CONCAT(name ORDER BY `order` SEPARATOR ',') as 途径点
FROM task_child 
WHERE parent_id = ? 
AND `order` > 1 
AND `order` < (SELECT MAX(`order`) FROM task_child WHERE parent_id = ?);
```

---

## 3. 字段确认总结

### 3.0 完整字段确认表 ✅

| 序号 | 字段名 | 数据源类型 | 具体数据源 | 获取方式 | 特殊处理 |
|------|--------|------------|------------|----------|----------|
| 1 | **运行任务** | 数据库表 | `task_child.id` | 直接取值 | - |
| 2 | **开始时间** | 数据库表 | `task_log.created_time` | 任务下发日志时间 | - |
| 3 | **结束时间** | TDengine | 心跳数据step=2的时间戳 | JSON解析 | - |
| 4 | **状态** | 数据库表 | `task_child.status` | 状态码映射 | 0-7状态码转中文 |
| 5 | **车牌** | 数据库表 | `task.imsi` | 直接取值 | - |
| 6 | **车型** | 数据库表 | `device.type` | 直接取值 | 0-6类型码 |
| 7 | **理论规划里程(M)** | TDengine | `latest_task.taskDistance` | JSON解析 | 已为米单位 |
| 8 | **计划时长(min)** | TDengine | 第一次step=1的leftTime | JSON解析 | - |
| 9 | **实际时长(min)** | 计算字段 | (结束时间-出发时间)/60 | 计算 | - |
| 10 | **出发时间** | TDengine | 心跳数据step=1的时间戳 | JSON解析 | - |
| 11 | **最后到达时间** | TDengine | 心跳数据step=2的时间戳 | JSON解析 | 与结束时间相同 |
| 12 | **任务类型** | 数据库表 | `task_child.type` | 直接取值 | - |
| 13 | **任务车辆用途** | 数据库表 | `device.type` | 类型映射 | 0-6映射中文描述 |
| 14 | **开始里程值(M)** | TDengine | 任务开始时tm字段×1000 | 单位转换 | tm=-1时取备选值 |
| 15 | **结束里程值(M)** | TDengine | 任务结束时tm字段×1000 | 单位转换 | tm=-1时取备选值 |
| 16 | **实际运行里程(M)** | 计算字段 | 结束里程值-开始里程值 | 计算 | - |
| 17 | **实际运行速度(KM/h)** | 计算字段 | 实际里程(km)/实际时长(h) | 计算 | - |
| 18 | **任务创建人** | 数据库表 | `task.uid`→`user.username` | 关联查询 | - |
| 19 | **出发点名称** | TDengine | `latest_task.currentStation.name` | JSON解析 | - |
| 20 | **目的地点名称** | TDengine | `latest_task.targetStation.name` | JSON解析 | - |
| 21 | **途径点** | 数据库表 | 中间子任务站点名称 | 按order排序 | 逗号分隔 |

---

## 4. 详细字段分析

### 3.1 时间相关字段 ✅

| 序号 | 字段名 | 数据源 | 获取方式 | 确认状态 |
|------|--------|--------|----------|----------|
| 2 | **开始时间** | `task_log.created_time` | 任务下发日志时间 | ✅ 已确认 |
| 3 | **结束时间** | TDengine心跳数据 | step=2的时间戳 | ✅ 已确认 |
| 10 | **出发时间** | TDengine心跳数据 | step=1的时间戳 | ✅ 已确认 |
| 11 | **最后到达时间** | TDengine心跳数据 | step=2的时间戳(与结束时间相同) | ✅ 已确认(保留重复字段) |

#### 时间字段说明：
- **开始时间**: ccapiex任务下发时间，从task_log表获取
- **出发时间**: 设备真实开始执行任务的时间(step=1)
- **结束时间/最后到达时间**: 任务完成时间(step=2)，两个字段值相同但都保留

### 3.2 状态字段 ⏳

| 序号 | 字段名 | 推测数据源 | 获取方式 | 确认状态 |
|------|--------|------------|----------|----------|
| 4 | **状态** | `task_child.status` | 状态码或中文描述 | ⏳ 需确认显示格式 |

#### 状态映射关系：
```
0=未开始, 1=将要执行, 2=正在执行, 3=已完成, 
4=已暂停, 5=已取消, 6=已终止, 7=无法完成
```

### 3.3 里程相关字段 ✅

| 序号 | 字段名 | 数据源 | 计算方式 | 确认状态 |
|------|--------|--------|----------|----------|
| 14 | **开始里程值(M)** | TDengine心跳数据`tm`字段 | 任务开始时tm × 1000 | ✅ 已确认 |
| 15 | **结束里程值(M)** | TDengine心跳数据`tm`字段 | 任务结束时tm × 1000 | ✅ 已确认 |
| 16 | **实际运行里程(M)** | 计算字段 | 结束里程值 - 开始里程值 | ✅ 已确认 |

#### 里程字段说明：
- `tm`字段单位为km，需要×1000转换为米
- 当`tm=-1`时，取最近一条非-1的值作为备选数据

### 3.4 速度和时长字段 ✅

| 序号 | 字段名 | 数据源 | 计算方式 | 确认状态 |
|------|--------|--------|----------|----------|
| 8 | **计划时长(min)** | TDengine心跳数据 | 第一次step=1记录的leftTime字段 | ✅ 已确认 |
| 9 | **实际时长(min)** | 计算字段 | (结束时间-出发时间)/60 | ✅ 已确认 |
| 17 | **实际运行速度(KM/h)** | 计算字段 | 实际里程(km)/实际时长(h) | ✅ 已确认 |

### 3.5 车辆用途字段 ✅

| 序号 | 字段名 | 数据源 | 映射关系 | 确认状态 |
|------|--------|--------|----------|----------|
| 13 | **任务车辆用途** | `device.type` | 车型的中文描述 | ✅ 已确认 |

#### 车辆用途映射：
```
0 → "公交运输"    (RoboBus)
1 → "安防巡逻"    (安防小车)
2 → "清扫作业"    (无人清扫车)
3 → "物流运输"    (物流车)
6 → "零售服务"    (饮品零售机器人)
```

---

## 4. 数据获取SQL示例

### 4.1 基础查询框架

```sql
-- 主查询结构
SELECT 
    tc.id as 运行任务,
    -- 时间字段(待确认)
    tl.created_time as 开始时间,  -- 需确认task_log表
    NULL as 结束时间,             -- 需从心跳数据获取
    NULL as 出发时间,             -- 需从心跳数据获取
    NULL as 最后到达时间,         -- 需从心跳数据获取
    
    -- 基础信息
    tc.status as 状态,
    t.imsi as 车牌,
    d.type as 车型,
    tc.type as 任务类型,
    u.username as 任务创建人,
    
    -- 里程信息(待确认)
    NULL as 理论规划里程_M,       -- 需从心跳数据获取
    NULL as 开始里程值_M,         -- 需从心跳数据获取
    NULL as 结束里程值_M,         -- 需从心跳数据获取
    NULL as 实际运行里程_M,       -- 计算字段
    
    -- 地点信息
    NULL as 出发点名称,           -- 需从心跳数据获取
    NULL as 目的地点名称,         -- 需从心跳数据获取
    NULL as 途径点                -- 需单独查询
    
FROM task_child tc
JOIN task t ON tc.parent_id = t.id
JOIN device d ON t.imsi = d.imsi
JOIN users u ON t.uid = u.id
LEFT JOIN task_log tl ON tl.child_id = tc.id AND tl.status = 1  -- 下发日志
WHERE tc.status = 3;  -- 已完成的任务
```

### 4.2 TDengine心跳数据查询 ✅

```sql
-- 获取任务开始时的心跳数据(step=1)
SELECT ts, tm, latest_task
FROM pixmoving.device_heartbeat
WHERE imsi = ?
AND latest_task LIKE '%"taskId":' + ? + '%'
AND latest_task LIKE '%"step":1%'
ORDER BY ts ASC LIMIT 1;

-- 获取任务结束时的心跳数据(step=2)
SELECT ts, tm, latest_task
FROM pixmoving.device_heartbeat
WHERE imsi = ?
AND latest_task LIKE '%"taskId":' + ? + '%'
AND latest_task LIKE '%"step":2%'
ORDER BY ts DESC LIMIT 1;

-- 获取tm=-1时的备选数据
SELECT tm FROM pixmoving.device_heartbeat
WHERE imsi = ? AND tm != -1 AND ts <= ?
ORDER BY ts DESC LIMIT 1;

-- 解析JSON字段的示例数据结构
{
  "taskId": 457,
  "taskDistance": 439,
  "targetStation": {"name": "PIX西门", "id": 2},
  "step": 1,
  "leftTime": 15,
  "currentStation": {"name": "长通广场出发站", "id": 1}
}
```

---

## 5. 实现建议

### 5.1 分阶段实现

#### 第一阶段：基础字段 ✅
实现已确认的9个字段，验证数据获取的可行性。

#### 第二阶段：时间字段 ⏳
1. 确认task_log表结构和下发日志记录
2. 确认心跳数据存储表和查询方式
3. 实现时间相关字段

#### 第三阶段：里程字段 ⏳
1. 验证心跳数据中tm字段的可用性
2. 处理数据缺失和异常情况
3. 实现里程计算逻辑

#### 第四阶段：计算字段 ⏳
1. 基于时间和里程字段计算衍生字段
2. 实现速度和时长的计算
3. 完善数据验证和异常处理

### 5.2 数据验证策略

```go
// 数据验证示例
type TaskExportData struct {
    TaskID              int64   `json:"task_id"`
    StartTime           int64   `json:"start_time"`
    EndTime             int64   `json:"end_time"`
    DepartureTime       int64   `json:"departure_time"`
    // ... 其他字段
}

func ValidateExportData(data *TaskExportData) error {
    // 时间逻辑验证
    if data.DepartureTime > 0 && data.StartTime > 0 {
        if data.DepartureTime < data.StartTime {
            return errors.New("出发时间不能早于开始时间")
        }
    }
    
    // 里程逻辑验证
    if data.ActualMileage < 0 {
        return errors.New("实际运行里程不能为负数")
    }
    
    return nil
}
```

### 5.3 性能优化建议

1. **批量查询**: 避免逐条查询心跳数据
2. **索引优化**: 为心跳数据表建立合适索引
3. **缓存策略**: 缓存频繁查询的基础数据
4. **分页导出**: 大量数据分批处理

---

## 📝 总结

### 当前进度
- ✅ **已确认**: 21个字段 (100%)
- ⏳ **待确认**: 0个字段 (0%)
- ⏸️ **暂跳过**: 0个字段 (0%)

### 下一步工作
1. ✅ 确认所有字段的数据源和获取方式
2. 🔄 实现基础字段的导出功能
3. 🔄 实现心跳数据查询和处理逻辑
4. 🔄 实现计算字段和异常处理机制

### 关键技术要点
- TDengine心跳数据查询优化
- `tm=-1`异常值的备选数据获取
- 心跳数据中JSON字段的解析和提取
- 跨数据库查询的性能优化

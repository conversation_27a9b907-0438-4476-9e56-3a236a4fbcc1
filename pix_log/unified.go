// 统一日志接口 - 提供与 GoFrame 兼容的接口
package pix_log

// 全局兼容日志器实例
var (
	tcpLogger      *CompatLogger
	webLogger      *CompatLogger
	amsLogger      *CompatLogger
	defaultLogger  *CompatLogger
	kafkaLogger    *CompatLogger
)

// 初始化兼容日志器
func initCompatLoggers() {
	tcpLogger = NewCompatLogger("tcp")
	webLogger = NewCompatLogger("server")  // web 日志映射到 server 模块
	amsLogger = NewCompatLogger("server")  // ams 日志映射到 server 模块
	defaultLogger = NewCompatLogger("server") // 默认日志映射到 server 模块
	kafkaLogger = NewCompatLogger("kafka") // kafka 日志映射到 kafka 模块
}

// LogTcp 获取 TCP 模块兼容日志器
func LogTcp() *CompatLogger {
	if tcpLogger == nil {
		initCompatLoggers()
	}
	return tcpLogger
}

// LogWeb 获取 Web 模块兼容日志器
func LogWeb() *CompatLogger {
	if webLogger == nil {
		initCompatLoggers()
	}
	return webLogger
}

// LogAms 获取 AMS 模块兼容日志器
func LogAms() *CompatLogger {
	if amsLogger == nil {
		initCompatLoggers()
	}
	return amsLogger
}

// Log 获取默认兼容日志器
func Log() *CompatLogger {
	if defaultLogger == nil {
		initCompatLoggers()
	}
	return defaultLogger
}

// LogKafka 获取 Kafka 模块兼容日志器
func LogKafka() *CompatLogger {
	if kafkaLogger == nil {
		initCompatLoggers()
	}
	return kafkaLogger
}

// 便捷函数 - 直接使用模块名获取日志器
func GetLogger(module string) *CompatLogger {
	return NewCompatLogger(module)
}

// 模块专用便捷函数
func TcpDebug(format string, v ...interface{}) {
	LogTcp().Debugf(format, v...)
}

func TcpInfo(format string, v ...interface{}) {
	LogTcp().Infof(format, v...)
}

func TcpWarning(format string, v ...interface{}) {
	LogTcp().Warningf(format, v...)
}

func TcpError(format string, v ...interface{}) {
	LogTcp().Errorf(format, v...)
}

func WebDebug(format string, v ...interface{}) {
	LogWeb().Debugf(format, v...)
}

func WebInfo(format string, v ...interface{}) {
	LogWeb().Infof(format, v...)
}

func WebWarning(format string, v ...interface{}) {
	LogWeb().Warningf(format, v...)
}

func WebError(format string, v ...interface{}) {
	LogWeb().Errorf(format, v...)
}

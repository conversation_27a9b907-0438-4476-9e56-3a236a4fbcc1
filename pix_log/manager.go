// 日志管理器 - 支持按模块分离、按日期命名、序号管理
package pix_log

import (
	"ccserver/app/vars"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"
)

const (
	flag = log.Ldate | log.Ltime | log.Lshortfile

	// 标准化日志格式常量
	DefaultIMSI = "N/A"
	TimeFormat  = "2006-01-02 15:04:05.000"
)

// 🎨 智能颜色检测
var (
	colorSupported bool
	colorChecked   bool
)

// isColorSupported 检测终端是否支持颜色
func isColorSupported() bool {
	if colorChecked {
		return colorSupported
	}

	colorChecked = true

	// 检查环境变量
	if os.Getenv("NO_COLOR") != "" {
		colorSupported = false
		return false
	}

	if os.Getenv("FORCE_COLOR") != "" {
		colorSupported = true
		return true
	}

	// 检查TERM环境变量
	term := os.Getenv("TERM")
	if term == "" || term == "dumb" {
		colorSupported = false
		return false
	}

	// 检查是否是TTY
	if fileInfo, _ := os.Stdout.Stat(); (fileInfo.Mode() & os.ModeCharDevice) == 0 {
		colorSupported = false
		return false
	}

	// 默认支持颜色
	colorSupported = true
	return true
}

// LogManager 日志管理器
type LogManager struct {
	loggers map[string]*ModuleLogger
	mutex   sync.RWMutex
}

// ModuleLogger 模块日志器
type ModuleLogger struct {
	module        string
	filename      string
	fileLogger    *log.Logger // 文件日志器（无颜色）
	consoleLogger *log.Logger // 控制台日志器（有颜色）
	fileWriter    io.Writer
	enableStdout  bool
}

// formatLogMessage 格式化日志消息
func (ml *ModuleLogger) formatLogMessage(level, imsi, message string, forConsole bool) string {
	timestamp := time.Now().Format(TimeFormat)
	if imsi == "" {
		imsi = DefaultIMSI
	}

	if forConsole && isColorSupported() {
		// 控制台输出：带颜色（仅在支持时）
		var colorCode string
		switch level {
		case "DEBUG":
			colorCode = "\033[36m" // 青色
		case "INFO":
			colorCode = "\033[32m" // 绿色
		case "WARN":
			colorCode = "\033[33m" // 黄色
		case "ERROR":
			colorCode = "\033[31m" // 红色
		default:
			colorCode = "\033[0m" // 默认色
		}
		return fmt.Sprintf("%s[%s] [%s] [%s] [IMSI:%s] %s\033[0m",
			colorCode, timestamp, level, strings.ToUpper(ml.module), imsi, message)
	} else {
		// 文件输出：无颜色
		return fmt.Sprintf("[%s] [%s] [%s] [IMSI:%s] %s",
			timestamp, level, strings.ToUpper(ml.module), imsi, message)
	}
}

var globalManager *LogManager

// parseSize 解析大小配置字符串（如"50M"）为MB数值
func parseSize(sizeStr string) (int, error) {
	if sizeStr == "" {
		return 50, nil // 默认50MB
	}

	sizeStr = strings.ToUpper(strings.TrimSpace(sizeStr))

	// 提取数字部分和单位部分
	var numStr string
	var unit string

	for i, char := range sizeStr {
		if char >= '0' && char <= '9' || char == '.' {
			numStr += string(char)
		} else {
			unit = sizeStr[i:]
			break
		}
	}

	if numStr == "" {
		return 50, fmt.Errorf("invalid size format: %s", sizeStr)
	}

	num, err := strconv.ParseFloat(numStr, 64)
	if err != nil {
		return 50, fmt.Errorf("invalid number in size: %s", numStr)
	}

	// 转换为MB，确保最小值为1MB
	var result int
	switch unit {
	case "B", "":
		result = int(num / (1024 * 1024))
	case "K", "KB":
		result = int(num / 1024)
	case "M", "MB":
		result = int(num)
	case "G", "GB":
		result = int(num * 1024)
	default:
		return 50, fmt.Errorf("unsupported size unit: %s", unit)
	}

	// 确保最小值为1MB
	if result < 1 {
		result = 1
	}

	return result, nil
}

// ensureLogDir 确保日志目录存在
func ensureLogDir(logPath string) error {
	dir := filepath.Dir(logPath)
	if dir == "." {
		return nil // 当前目录，无需创建
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create log directory %s: %v", dir, err)
		}
		log.Printf("Created log directory: %s", dir)
	}
	return nil
}

// InitLogManager 初始化日志管理器
func InitLogManager() {
	globalManager = &LogManager{
		loggers: make(map[string]*ModuleLogger),
	}

	// 初始化各个模块的日志器
	modules := map[string]vars.LogConfig{
		"server":   vars.Config.Logging.Modules.Server,
		"tcp":      vars.Config.Logging.Modules.Tcp,
		"terminal": vars.Config.Logging.Modules.Terminal,
		"database": vars.Config.Logging.Modules.Database,
		"kafka":    vars.Config.Logging.Modules.Kafka,
	}

	for module, config := range modules {
		if err := globalManager.createModuleLogger(module, config); err != nil {
			log.Printf("Failed to create logger for module %s: %v", module, err)
		}
	}

	// 🎨 检测并报告颜色支持状态
	colorStatus := "不支持"
	if isColorSupported() {
		colorStatus = "支持"
	}

	// 🧹 启动时清理过期日志文件
	globalManager.cleanupOldLogs()

	// 🔍 验证日志轮转策略
	globalManager.validateRotationStrategy()

	// 🆕 确保当天日志文件存在（处理非零点启动的情况）
	globalManager.ensureTodayLogFiles()

	// 🆕 启动日志轮转检查器（包括日期轮转和大小轮转）
	go globalManager.startRotationChecker()

	log.Printf("Log manager initialized with %d modules, 终端颜色: %s", len(globalManager.loggers), colorStatus)
}

// createModuleLogger 创建模块日志器
func (lm *LogManager) createModuleLogger(module string, config vars.LogConfig) error {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	// 确定文件名前缀
	filename := config.Filename
	if filename == "" {
		filename = module
	}

	// 生成日志文件路径
	logPath, err := lm.generateLogPath(module, filename)
	if err != nil {
		return fmt.Errorf("failed to generate log path for %s: %v", module, err)
	}

	// 确保日志目录存在
	if err := ensureLogDir(logPath); err != nil {
		return fmt.Errorf("failed to ensure log directory for %s: %v", module, err)
	}

	// 🔧 使用lumberjack但完全禁用自动轮转
	// 我们将通过自定义检查器来控制轮转
	fileLogger := &lumberjack.Logger{
		Filename:   logPath,
		MaxSize:    9999, // 🔧 设置极大值(9999MB)确保永不触发自动轮转
		MaxBackups: 0,    // 🔧 禁用备份轮转
		MaxAge:     0,    // 🔧 禁用时间轮转
		Compress:   false,
		// LocalTime 默认为false，使用UTC时间
		// 即使触发也不会使用本地时间格式
		LocalTime: false,
	}

	// 创建模块日志器（分离文件和控制台输出）
	moduleLogger := &ModuleLogger{
		module:        module,
		filename:      filename,
		fileLogger:    log.New(fileLogger, "", 0), // 文件日志器（无颜色）
		consoleLogger: log.New(os.Stdout, "", 0),  // 控制台日志器（有颜色）
		fileWriter:    fileLogger,
		enableStdout:  config.Stdout,
	}

	lm.loggers[module] = moduleLogger

	log.Printf("Created logger for module %s: %s", module, logPath)
	return nil
}

// generateLogPath 生成日志文件路径，实现统一的日期优先轮转策略
func (lm *LogManager) generateLogPath(module, filename string) (string, error) {
	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	// 为每个模块创建独立的子目录
	moduleDir := filepath.Join(basePath, module)
	if err := ensureLogDir(moduleDir); err != nil {
		return "", fmt.Errorf("failed to ensure module directory: %v", err)
	}

	dateFormat := vars.Config.Logging.DateFormat
	if dateFormat == "" {
		dateFormat = "2006-01-02"
	}

	today := time.Now().Format(dateFormat)
	maxFiles := vars.Config.Logging.MaxFilesPerDay
	if maxFiles <= 0 {
		maxFiles = 99 // 默认每天最多99个文件
	}

	// 🎯 统一日志轮转策略：日期优先 + 大小限制
	return lm.findOrCreateTodayLogFile(moduleDir, filename, today, maxFiles)
}

// findOrCreateTodayLogFile 查找或创建今天的日志文件
func (lm *LogManager) findOrCreateTodayLogFile(moduleDir, filename, today string, maxFiles int) (string, error) {
	// 解析文件大小限制
	maxSizeBytes, err := parseSize(vars.Config.Logging.RotateSize)
	if err != nil {
		log.Printf("Warning: failed to parse rotate_size: %v, using default 50MB", err)
		maxSizeBytes = 50
	}
	maxSizeBytes *= 1024 * 1024 // 转换为字节

	// 遍历可能的文件序号
	for i := 1; i <= maxFiles; i++ {
		logFileName := fmt.Sprintf("%s-%s_%02d.log", filename, today, i)
		logPath := filepath.Join(moduleDir, logFileName)

		// 检查文件是否存在
		if _, err := os.Stat(logPath); os.IsNotExist(err) {
			// 文件不存在，创建新文件
			log.Printf("📝 [LOG_ROTATION] 创建新日志文件: %s", logFileName)
			return logPath, nil
		}

		// 文件存在，检查是否可以继续使用
		if info, err := os.Stat(logPath); err == nil {
			// 1. 验证文件确实是今天的（通过文件名中的日期）
			if !lm.isFileFromToday(logFileName, today) {
				log.Printf("⚠️ [LOG_ROTATION] 跳过非今日文件: %s", logFileName)
				continue
			}

			// 2. 检查文件大小
			if info.Size() < int64(maxSizeBytes) {
				// 文件未满，可以继续使用
				log.Printf("🔄 [LOG_ROTATION] 继续使用现有文件: %s (大小: %.2fMB/%.0fMB)",
					logFileName, float64(info.Size())/(1024*1024), float64(maxSizeBytes)/(1024*1024))
				return logPath, nil
			}

			// 文件已满，继续寻找下一个序号
			log.Printf("📊 [LOG_ROTATION] 文件已满，寻找下一个序号: %s (大小: %.2fMB)",
				logFileName, float64(info.Size())/(1024*1024))
		}
	}

	return "", fmt.Errorf("exceeded maximum files per day (%d) for %s on %s", maxFiles, filename, today)
}

// isFileFromToday 检查文件名中的日期是否是今天
func (lm *LogManager) isFileFromToday(fileName, today string) bool {
	// 从文件名中提取日期部分
	// 格式：module-YYYY-MM-DD_NN.log
	// 例如：tcp-2025-07-11_01.log

	// 查找日期模式：YYYY-MM-DD
	datePattern := `\d{4}-\d{2}-\d{2}`
	re := regexp.MustCompile(datePattern)
	matches := re.FindString(fileName)

	if matches == "" {
		log.Printf("⚠️ [LOG_ROTATION] 无法从文件名提取日期: %s", fileName)
		return false
	}

	// 比较提取的日期与今天的日期
	return matches == today
}

// 🧹 cleanupOldLogs 清理过期的日志文件（统一策略）
func (lm *LogManager) cleanupOldLogs() {
	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	maxAge := vars.Config.Logging.RotateBackupLimit
	if maxAge <= 0 {
		maxAge = 7 // 默认保留7天
	}

	// 计算截止日期（基于日期而不是文件修改时间）
	cutoffDate := time.Now().AddDate(0, 0, -maxAge).Format("2006-01-02")
	deletedCount := 0
	totalSize := int64(0)

	log.Printf("🧹 [LOG_CLEANUP] 开始清理日志，保留 %d 天，截止日期: %s", maxAge, cutoffDate)

	// 遍历日志目录
	err := filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续处理其他文件
		}

		// 只处理.log文件
		if !strings.HasSuffix(info.Name(), ".log") {
			return nil
		}

		// 从文件名中提取日期
		fileDate := lm.extractDateFromFileName(info.Name())
		if fileDate == "" {
			// 无法提取日期，使用文件修改时间作为备选
			fileDate = info.ModTime().Format("2006-01-02")
			log.Printf("⚠️ [LOG_CLEANUP] 无法从文件名提取日期，使用修改时间: %s -> %s", info.Name(), fileDate)
		}

		// 比较日期字符串（YYYY-MM-DD格式可以直接比较）
		if fileDate < cutoffDate {
			log.Printf("🗑️ [LOG_CLEANUP] 删除过期日志: %s (日期: %s, 大小: %.2fMB)",
				path, fileDate, float64(info.Size())/(1024*1024))

			totalSize += info.Size()
			if err := os.Remove(path); err != nil {
				log.Printf("❌ [LOG_CLEANUP] 删除失败: %s, 错误: %v", path, err)
			} else {
				deletedCount++
			}
		}

		return nil
	})

	if err != nil {
		log.Printf("⚠️ [LOG_CLEANUP] 清理过程中出错: %v", err)
	}

	log.Printf("✅ [LOG_CLEANUP] 清理完成: 删除 %d 个文件，释放 %.2fMB 空间，保留最近 %d 天的日志",
		deletedCount, float64(totalSize)/(1024*1024), maxAge)
}

// extractDateFromFileName 从文件名中提取日期
func (lm *LogManager) extractDateFromFileName(fileName string) string {
	// 查找日期模式：YYYY-MM-DD
	datePattern := `\d{4}-\d{2}-\d{2}`
	re := regexp.MustCompile(datePattern)
	return re.FindString(fileName)
}

// 🔍 validateRotationStrategy 验证日志轮转策略
func (lm *LogManager) validateRotationStrategy() {
	log.Printf("🔍 [LOG_VALIDATION] 验证统一日志轮转策略")

	// 验证配置参数
	rotateSize := vars.Config.Logging.RotateSize
	backupLimit := vars.Config.Logging.RotateBackupLimit
	maxFiles := vars.Config.Logging.MaxFilesPerDay

	log.Printf("📋 [LOG_VALIDATION] 轮转配置:")
	log.Printf("   - 单文件最大大小: %s", rotateSize)
	log.Printf("   - 保留天数: %d", backupLimit)
	log.Printf("   - 每天最大文件数: %d", maxFiles)

	// 验证今天的日志文件命名
	today := time.Now().Format("2006-01-02")
	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	// 检查各模块的日志文件
	modules := []string{"tcp", "server", "kafka", "database", "terminal"}
	for _, module := range modules {
		moduleDir := filepath.Join(basePath, module)
		if _, err := os.Stat(moduleDir); os.IsNotExist(err) {
			continue // 模块目录不存在，跳过
		}

		// 查找今天的日志文件
		todayFiles := lm.findTodayLogFiles(moduleDir, today)
		if len(todayFiles) > 0 {
			log.Printf("✅ [LOG_VALIDATION] %s模块今日日志文件: %v", module, todayFiles)
		} else {
			log.Printf("ℹ️ [LOG_VALIDATION] %s模块暂无今日日志文件", module)
		}
	}

	log.Printf("✅ [LOG_VALIDATION] 日志轮转策略验证完成")
}

// findTodayLogFiles 查找今天的日志文件
func (lm *LogManager) findTodayLogFiles(moduleDir, today string) []string {
	var todayFiles []string

	files, err := os.ReadDir(moduleDir)
	if err != nil {
		return todayFiles
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".log") {
			if lm.extractDateFromFileName(file.Name()) == today {
				todayFiles = append(todayFiles, file.Name())
			}
		}
	}

	return todayFiles
}

// GetModuleLogger 获取模块日志器
func (lm *LogManager) GetModuleLogger(module string) *ModuleLogger {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()
	return lm.loggers[module]
}

// 模块日志器的日志方法（标准格式）
func (ml *ModuleLogger) Debug(format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("DEBUG", DefaultIMSI, message)
	}
}

func (ml *ModuleLogger) Info(format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("INFO", DefaultIMSI, message)
	}
}

func (ml *ModuleLogger) Warning(format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("WARN", DefaultIMSI, message)
	}
}

func (ml *ModuleLogger) Error(format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("ERROR", DefaultIMSI, message)
	}
}

// 带 IMSI 的日志方法
func (ml *ModuleLogger) DebugWithIMSI(imsi, format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("DEBUG", imsi, message)
	}
}

func (ml *ModuleLogger) InfoWithIMSI(imsi, format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("INFO", imsi, message)
	}
}

func (ml *ModuleLogger) WarningWithIMSI(imsi, format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("WARN", imsi, message)
	}
}

func (ml *ModuleLogger) ErrorWithIMSI(imsi, format string, v ...interface{}) {
	if ml != nil {
		message := fmt.Sprintf(format, v...)
		ml.writeLog("ERROR", imsi, message)
	}
}

// writeLog 统一的日志写入方法
func (ml *ModuleLogger) writeLog(level, imsi, message string) {
	// 写入文件（无颜色）
	if ml.fileLogger != nil {
		fileFormatted := ml.formatLogMessage(level, imsi, message, false)
		ml.fileLogger.Println(fileFormatted)
	}

	// 写入控制台（有颜色）
	if ml.enableStdout && ml.consoleLogger != nil {
		consoleFormatted := ml.formatLogMessage(level, imsi, message, true)
		ml.consoleLogger.Println(consoleFormatted)
	}
}

// 全局便捷函数 - TCP模块（保持向后兼容）
func Debug(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.Debug(format, v...)
		}
	}
}

func Info(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.Info(format, v...)
		}
	}
}

func Warning(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.Warning(format, v...)
		}
	}
}

func Error(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.Error(format, v...)
		}
	}
}

// 带 IMSI 的全局便捷函数
func DebugWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.DebugWithIMSI(imsi, format, v...)
		}
	}
}

func InfoWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.InfoWithIMSI(imsi, format, v...)
		}
	}
}

func WarningWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.WarningWithIMSI(imsi, format, v...)
		}
	}
}

func ErrorWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.ErrorWithIMSI(imsi, format, v...)
		}
	}
}

// 模块专用日志函数
func ServerInfo(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("server"); logger != nil {
			logger.Info(format, v...)
		}
	}
}

func ServerError(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("server"); logger != nil {
			logger.Error(format, v...)
		}
	}
}

func SqlInfo(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.Info(format, v...)
		}
	}
}

func SqlError(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.Error(format, v...)
		}
	}
}

func DatabaseInfo(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.Info(format, v...)
		}
	}
}

func DatabaseError(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.Error(format, v...)
		}
	}
}

// InitLogWithConfig 保持向后兼容的初始化函数
func InitLogWithConfig(enableStdout bool) {
	// 设置TCP模块的stdout配置
	vars.Config.Logging.Modules.Tcp.Stdout = enableStdout

	// 初始化新的日志管理器
	InitLogManager()
}

// CompatLogger GoFrame 兼容日志器
type CompatLogger struct {
	module string
}

// NewCompatLogger 创建兼容日志器
func NewCompatLogger(module string) *CompatLogger {
	return &CompatLogger{module: module}
}

// GoFrame 兼容接口实现
func (cl *CompatLogger) Debugf(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.Debug(format, v...)
		}
	}
}

func (cl *CompatLogger) Infof(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.Info(format, v...)
		}
	}
}

func (cl *CompatLogger) Warningf(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.Warning(format, v...)
		}
	}
}

func (cl *CompatLogger) Errorf(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.Error(format, v...)
		}
	}
}

// Printf 兼容 GoFrame 的 Printf 方法（映射到 Info 级别）
func (cl *CompatLogger) Printf(format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.Info(format, v...)
		}
	}
}

// 带 IMSI 的兼容接口
func (cl *CompatLogger) DebugfWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.DebugWithIMSI(imsi, format, v...)
		}
	}
}

func (cl *CompatLogger) InfofWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.InfoWithIMSI(imsi, format, v...)
		}
	}
}

func (cl *CompatLogger) WarningfWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.WarningWithIMSI(imsi, format, v...)
		}
	}
}

func (cl *CompatLogger) ErrorfWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(cl.module); logger != nil {
			logger.ErrorWithIMSI(imsi, format, v...)
		}
	}
}

// 全局兼容函数
func GetCompatLogger(module string) *CompatLogger {
	return NewCompatLogger(module)
}

// GetGlobalManager 获取全局日志管理器
func GetGlobalManager() *LogManager {
	return globalManager
}

// 🔄 startRotationChecker 启动日志轮转检查器
// 负责两种轮转：
// 1. 日期轮转：每天零点（00:00）精确创建新的日期文件
// 2. 大小轮转：当文件接近大小限制时创建新的序号文件
func (lm *LogManager) startRotationChecker() {
	// 解析配置的文件大小限制
	maxSizeBytes, err := parseSize(vars.Config.Logging.RotateSize)
	if err != nil {
		log.Printf("Warning: failed to parse rotate_size: %v, using default 50MB", err)
		maxSizeBytes = 50
	}
	maxSizeBytes *= 1024 * 1024 // 转换为字节

	log.Printf("🔄 [LOG_ROTATION] 启动日志轮转检查器，大小限制: %dMB", maxSizeBytes/(1024*1024))

	// 记录上次检查的日期，用于检测日期变化
	lastDate := time.Now().Format("2006-01-02")

	// 🆕 启动双重检查机制：
	// 1. 高频检查（每分钟）：用于大小轮转和精确日期轮转
	// 2. 零点检查：确保在00:00时立即执行日期轮转
	go lm.startMidnightChecker()

	// 主检查循环（每分钟检查一次）
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		// 获取当前日期
		currentDate := time.Now().Format("2006-01-02")

		// 检查是否跨天
		if currentDate != lastDate {
			log.Printf("📅 [LOG_ROTATION] 检测到日期变化: %s → %s，执行日期轮转", lastDate, currentDate)
			lm.performDateRotation(currentDate)
			lastDate = currentDate
		}

		// 检查文件大小并执行大小轮转
		lm.performSizeRotation(maxSizeBytes)

		// 🆕 验证日志文件命名规范
		lm.validateLogFileNaming()
	}
}

// 📅 performDateRotation 执行日期轮转
// 当检测到日期变化时，为所有模块创建新的日期文件
func (lm *LogManager) performDateRotation(newDate string) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	log.Printf("📅 [DATE_ROTATION] 开始执行日期轮转，新日期: %s", newDate)

	// 验证日期格式
	if !lm.isValidDateFormat(newDate) {
		log.Printf("❌ [DATE_ROTATION] 日期格式无效: %s，使用当前日期", newDate)
		newDate = time.Now().Format("2006-01-02")
	}

	for module, logger := range lm.loggers {
		// 跳过没有文件日志的模块
		if logger.fileWriter == nil {
			continue
		}

		// 检查当前日志文件是否已经是今天的
		currentPath := ""
		if lumberjackLogger, ok := logger.fileWriter.(*lumberjack.Logger); ok {
			currentPath = lumberjackLogger.Filename
		}

		// 如果当前文件已经是今天的日期文件，跳过
		if lm.isFileFromDate(currentPath, newDate) && lm.isValidLogFileName(filepath.Base(currentPath)) {
			log.Printf("ℹ️ [DATE_ROTATION] 当前已是新日期文件，跳过: module=%s, path=%s", module, currentPath)
			continue
		}

		// 生成新的日志文件路径，确保使用序号01
		moduleDir := filepath.Join(vars.Config.Logging.BasePath, module)
		if vars.Config.Logging.BasePath == "" {
			moduleDir = filepath.Join("logs", module)
		}

		// 确保目录存在
		if err := ensureLogDir(moduleDir); err != nil {
			log.Printf("❌ [DATE_ROTATION] 创建日志目录失败: %s, error=%v", moduleDir, err)
			continue
		}

		// 创建新日期的第一个文件（序号01）
		newFileName := fmt.Sprintf("%s-%s_01.log", logger.filename, newDate)
		newPath := filepath.Join(moduleDir, newFileName)

		// 检查文件是否已存在
		if _, err := os.Stat(newPath); err == nil {
			log.Printf("ℹ️ [DATE_ROTATION] 新日期文件已存在，使用现有文件: %s", newPath)
		}

		// 安全关闭当前日志文件
		if err := lm.safeCloseLogger(logger); err != nil {
			log.Printf("⚠️ [DATE_ROTATION] 关闭日志文件失败: %v", err)
			// 继续执行，尝试创建新文件
		}

		// 创建新的 lumberjack 日志写入器
		newLogger := &lumberjack.Logger{
			Filename:   newPath,
			MaxSize:    9999, // 设置极大值避免自动轮转
			MaxBackups: 0,
			MaxAge:     0,
			Compress:   false,
			LocalTime:  false,
		}

		// 更新日志器
		logger.fileWriter = newLogger
		logger.fileLogger = log.New(newLogger, "", 0)

		log.Printf("📝 [DATE_ROTATION] 创建新日期日志文件: module=%s, path=%s", module, newPath)
	}

	log.Printf("✅ [DATE_ROTATION] 日期轮转完成")
}

// 🔍 isValidDateFormat 验证日期格式
func (lm *LogManager) isValidDateFormat(date string) bool {
	// 验证格式: YYYY-MM-DD
	pattern := `^\d{4}-\d{2}-\d{2}$`
	matched, _ := regexp.MatchString(pattern, date)
	return matched
}

// 🔍 isFileFromDate 检查文件是否属于指定日期
func (lm *LogManager) isFileFromDate(filePath, date string) bool {
	if filePath == "" {
		return false
	}

	fileName := filepath.Base(filePath)
	return strings.Contains(fileName, date)
}

// 📝 generateNextLogFile 生成下一个序号的日志文件路径
// 专门用于大小轮转，强制创建新的序号文件
func (lm *LogManager) generateNextLogFile(module, filename string) (string, error) {
	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	// 为每个模块创建独立的子目录
	moduleDir := filepath.Join(basePath, module)
	if err := ensureLogDir(moduleDir); err != nil {
		return "", fmt.Errorf("failed to ensure module directory: %v", err)
	}

	today := time.Now().Format("2006-01-02")
	maxFiles := vars.Config.Logging.MaxFilesPerDay
	if maxFiles <= 0 {
		maxFiles = 99 // 默认每天最多99个文件
	}

	// 查找下一个可用的序号
	for i := 1; i <= maxFiles; i++ {
		logFileName := fmt.Sprintf("%s-%s_%02d.log", filename, today, i)
		logPath := filepath.Join(moduleDir, logFileName)

		// 检查文件是否存在
		if _, err := os.Stat(logPath); os.IsNotExist(err) {
			// 文件不存在，可以使用这个序号
			log.Printf("📝 [NEXT_LOG_FILE] 生成新序号文件: %s", logFileName)
			return logPath, nil
		}
	}

	return "", fmt.Errorf("exceeded maximum files per day (%d) for %s on %s", maxFiles, filename, today)
}

// 🕛 startMidnightChecker 启动零点检查器
// 确保在每天00:00时精确执行日期轮转
func (lm *LogManager) startMidnightChecker() {
	for {
		now := time.Now()

		// 计算到下一个零点的时间
		nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
		durationToMidnight := nextMidnight.Sub(now)

		log.Printf("🕛 [MIDNIGHT_CHECKER] 下次零点轮转时间: %s (还有 %v)",
			nextMidnight.Format("2006-01-02 15:04:05"), durationToMidnight)

		// 等待到零点
		time.Sleep(durationToMidnight)

		// 执行零点日期轮转
		newDate := time.Now().Format("2006-01-02")
		log.Printf("🕛 [MIDNIGHT_CHECKER] 零点到达，执行日期轮转: %s", newDate)
		lm.performDateRotation(newDate)

		// 等待1分钟，避免重复触发
		time.Sleep(1 * time.Minute)
	}
}

// 🔍 validateLogFileNaming 验证日志文件命名规范
// 检查所有日志文件是否符合预期的命名格式，发现异常文件进行处理
func (lm *LogManager) validateLogFileNaming() {
	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	// 遍历所有模块目录
	modules := []string{"tcp", "server", "kafka", "database", "terminal"}
	for _, module := range modules {
		moduleDir := filepath.Join(basePath, module)
		if _, err := os.Stat(moduleDir); os.IsNotExist(err) {
			continue // 模块目录不存在，跳过
		}

		lm.validateModuleLogFiles(module, moduleDir)
	}
}

// 🔍 validateModuleLogFiles 验证模块日志文件命名
// 检查指定模块的日志文件是否符合预期的命名格式，处理异常文件
func (lm *LogManager) validateModuleLogFiles(module, moduleDir string) {
	files, err := os.ReadDir(moduleDir)
	if err != nil {
		log.Printf("❌ [VALIDATION] 无法读取模块目录: %s, error: %v", moduleDir, err)
		return
	}

	today := time.Now().Format("2006-01-02")
	validPattern := regexp.MustCompile(`^[a-zA-Z]+-\d{4}-\d{2}-\d{2}_\d{2}\.log$`)
	timestampPattern := regexp.MustCompile(`-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}\.log$`)

	for _, file := range files {
		if file.IsDir() {
			continue // 跳过子目录
		}

		fileName := file.Name()

		// 检查是否是日志文件
		if !strings.HasSuffix(fileName, ".log") {
			continue
		}

		// 检查是否符合预期格式
		if validPattern.MatchString(fileName) {
			// 文件格式正确
			continue
		}

		// 检测带时间戳后缀的文件（lumberjack自动轮转产生的）
		if timestampPattern.MatchString(fileName) {
			log.Printf("⚠️ [VALIDATION] 发现带时间戳后缀的日志文件: %s", fileName)

			// 尝试提取基本文件名和日期
			baseName := strings.Split(fileName, "-2")[0] // 提取模块名前缀
			dateMatch := regexp.MustCompile(`\d{4}-\d{2}-\d{2}`).FindString(fileName)

			if baseName != "" && dateMatch != "" {
				// 创建符合规范的新文件名
				newFileName := fmt.Sprintf("%s-%s_01.log", baseName, dateMatch)
				oldPath := filepath.Join(moduleDir, fileName)
				newPath := filepath.Join(moduleDir, newFileName)

				// 检查新文件是否已存在
				if _, err := os.Stat(newPath); os.IsNotExist(err) {
					// 重命名文件
					if err := os.Rename(oldPath, newPath); err == nil {
						log.Printf("✅ [VALIDATION] 已重命名异常文件: %s → %s", fileName, newFileName)
					} else {
						log.Printf("❌ [VALIDATION] 重命名异常文件失败: %s, error: %v", fileName, err)
					}
				} else {
					log.Printf("⚠️ [VALIDATION] 无法重命名异常文件，目标文件已存在: %s", newFileName)
				}
			}
		}
	}

	// 确保今天的日志文件存在
	todayFilePattern := fmt.Sprintf("%s-%s_", module, today)
	todayFileExists := false

	for _, file := range files {
		if strings.HasPrefix(file.Name(), todayFilePattern) {
			todayFileExists = true
			break
		}
	}

	// 如果今天的日志文件不存在，创建一个
	if !todayFileExists {
		log.Printf("ℹ️ [VALIDATION] 未找到今日日志文件，将创建: %s-%s_01.log", module, today)

		// 获取模块日志器
		if logger := lm.GetModuleLogger(module); logger != nil {
			// 关闭当前日志文件
			if closer, ok := logger.fileWriter.(io.Closer); ok {
				closer.Close()
			}

			// 生成新的日志文件路径
			newPath, err := lm.generateLogPath(module, logger.filename)
			if err != nil {
				log.Printf("❌ [VALIDATION] 生成新日志路径失败: %v", err)
				return
			}

			// 创建新的 lumberjack 日志写入器
			newLogger := &lumberjack.Logger{
				Filename:   newPath,
				MaxSize:    9999, // 设置极大值避免自动轮转
				MaxBackups: 0,
				MaxAge:     0,
				Compress:   false,
				LocalTime:  false,
			}

			// 更新日志器
			logger.fileWriter = newLogger
			logger.fileLogger = log.New(newLogger, "", 0)

			log.Printf("✅ [VALIDATION] 已创建今日日志文件: %s", newPath)
		}
	}
}

// 📊 performSizeRotation 执行大小轮转
// 检查所有模块的日志文件大小，如果接近限制则创建新的序号文件
func (lm *LogManager) performSizeRotation(maxSizeBytes int) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	for module, logger := range lm.loggers {
		// 跳过没有文件日志的模块
		if logger.fileWriter == nil {
			continue
		}

		// 获取当前日志文件路径
		lumberjackLogger, ok := logger.fileWriter.(*lumberjack.Logger)
		if !ok {
			log.Printf("⚠️ [SIZE_ROTATION] 无法获取日志写入器: module=%s", module)
			continue
		}

		currentPath := lumberjackLogger.Filename
		info, err := os.Stat(currentPath)
		if err != nil {
			if !os.IsNotExist(err) {
				log.Printf("⚠️ [SIZE_ROTATION] 无法获取文件信息: path=%s, error=%v", currentPath, err)
			}
			continue // 文件不存在或无法访问，跳过
		}

		// 如果文件大小超过限制的95%，主动进行轮转
		if info.Size() > int64(float64(maxSizeBytes)*0.95) {
			log.Printf("📊 [SIZE_ROTATION] 文件接近大小限制，执行轮转: module=%s, file=%s, size=%.2fMB/%.0fMB",
				module, filepath.Base(currentPath), float64(info.Size())/(1024*1024), float64(maxSizeBytes)/(1024*1024))

			// 🔒 确保安全关闭当前日志文件
			if err := lm.safeCloseLogger(logger); err != nil {
				log.Printf("⚠️ [SIZE_ROTATION] 关闭日志文件失败: %v", err)
				// 继续执行，尝试创建新文件
			}

			// 生成新的日志文件路径（强制创建新的序号文件）
			newPath, err := lm.generateNextLogFile(module, logger.filename)
			if err != nil {
				log.Printf("❌ [SIZE_ROTATION] 生成新日志路径失败: module=%s, error=%v", module, err)
				continue
			}

			// 🔒 验证新文件名格式
			if !lm.isValidLogFileName(filepath.Base(newPath)) {
				log.Printf("❌ [SIZE_ROTATION] 生成的文件名格式无效: %s", newPath)

				// 尝试修复文件名
				today := time.Now().Format("2006-01-02")
				fixedPath := filepath.Join(filepath.Dir(newPath), fmt.Sprintf("%s-%s_01.log", module, today))
				log.Printf("🔧 [SIZE_ROTATION] 尝试修复文件名: %s → %s", newPath, fixedPath)
				newPath = fixedPath
			}

			// 创建新的 lumberjack 日志写入器
			newLogger := &lumberjack.Logger{
				Filename:   newPath,
				MaxSize:    9999, // 设置极大值避免自动轮转
				MaxBackups: 0,
				MaxAge:     0,
				Compress:   false,
				LocalTime:  false,
			}

			// 更新日志器
			logger.fileWriter = newLogger
			logger.fileLogger = log.New(newLogger, "", 0)

			log.Printf("📝 [SIZE_ROTATION] 创建新序号日志文件: module=%s, path=%s", module, newPath)
		}
	}
}

// 🔒 safeCloseLogger 安全关闭日志写入器
func (lm *LogManager) safeCloseLogger(logger *ModuleLogger) error {
	if logger == nil || logger.fileWriter == nil {
		return nil
	}

	// 尝试关闭日志文件
	if closer, ok := logger.fileWriter.(io.Closer); ok {
		return closer.Close()
	}

	return nil
}

// 🔍 isValidLogFileName 验证日志文件名格式
func (lm *LogManager) isValidLogFileName(fileName string) bool {
	// 验证格式: module-YYYY-MM-DD_NN.log
	pattern := `^[a-zA-Z]+-\d{4}-\d{2}-\d{2}_\d{2}\.log$`
	matched, _ := regexp.MatchString(pattern, fileName)
	return matched
}

// 📅 ensureTodayLogFiles 确保当天日志文件存在
// 在服务启动时检查每个模块是否有当天的日志文件，如果没有则创建
func (lm *LogManager) ensureTodayLogFiles() {
	today := time.Now().Format("2006-01-02")
	log.Printf("📅 [STARTUP] 检查当天(%s)日志文件是否存在", today)

	basePath := vars.Config.Logging.BasePath
	if basePath == "" {
		basePath = "logs"
	}

	// 遍历所有模块
	for module, logger := range lm.loggers {
		// 跳过没有文件日志的模块
		if logger.fileWriter == nil {
			continue
		}

		// 检查当前日志文件是否已经是今天的
		currentPath := ""
		if lumberjackLogger, ok := logger.fileWriter.(*lumberjack.Logger); ok {
			currentPath = lumberjackLogger.Filename
		}

		// 如果当前文件已经是今天的日期文件，跳过
		if lm.isFileFromDate(currentPath, today) && lm.isValidLogFileName(filepath.Base(currentPath)) {
			log.Printf("✅ [STARTUP] 模块 %s 已有今日日志文件: %s", module, filepath.Base(currentPath))
			continue
		}

		// 检查模块目录中是否有今天的日志文件
		moduleDir := filepath.Join(basePath, module)
		if _, err := os.Stat(moduleDir); os.IsNotExist(err) {
			if err := os.MkdirAll(moduleDir, 0755); err != nil {
				log.Printf("❌ [STARTUP] 创建模块目录失败: %s, error=%v", moduleDir, err)
				continue
			}
		}

		// 查找今天的日志文件
		todayFiles := lm.findTodayLogFiles(moduleDir, today)

		if len(todayFiles) > 0 {
			// 已有今天的日志文件，使用最新的一个
			sort.Strings(todayFiles) // 按文件名排序
			latestFile := todayFiles[len(todayFiles)-1]
			log.Printf("✅ [STARTUP] 发现今日日志文件，使用: %s", latestFile)

			// 关闭当前日志文件
			if err := lm.safeCloseLogger(logger); err != nil {
				log.Printf("⚠️ [STARTUP] 关闭日志文件失败: %v", err)
			}

			// 使用找到的今天日志文件
			newPath := filepath.Join(moduleDir, latestFile)
			newLogger := &lumberjack.Logger{
				Filename:   newPath,
				MaxSize:    9999, // 设置极大值避免自动轮转
				MaxBackups: 0,
				MaxAge:     0,
				Compress:   false,
				LocalTime:  false,
			}

			// 更新日志器
			logger.fileWriter = newLogger
			logger.fileLogger = log.New(newLogger, "", 0)
		} else {
			// 没有今天的日志文件，创建新的
			log.Printf("ℹ️ [STARTUP] 未找到今日日志文件，为模块 %s 创建新文件", module)

			// 创建今天的第一个日志文件（序号01）
			newFileName := fmt.Sprintf("%s-%s_01.log", logger.filename, today)
			newPath := filepath.Join(moduleDir, newFileName)

			// 关闭当前日志文件
			if err := lm.safeCloseLogger(logger); err != nil {
				log.Printf("⚠️ [STARTUP] 关闭日志文件失败: %v", err)
			}

			// 创建新的日志文件
			newLogger := &lumberjack.Logger{
				Filename:   newPath,
				MaxSize:    9999, // 设置极大值避免自动轮转
				MaxBackups: 0,
				MaxAge:     0,
				Compress:   false,
				LocalTime:  false,
			}

			// 更新日志器
			logger.fileWriter = newLogger
			logger.fileLogger = log.New(newLogger, "", 0)

			log.Printf("✅ [STARTUP] 已创建今日日志文件: %s", newPath)
		}
	}

	log.Printf("✅ [STARTUP] 当天日志文件检查完成")
}

// 日志辅助函数 - 提供便捷的日志记录方法
package pix_log

import (
	"ccserver/app/vars"
	"context"
	"fmt"
)

// LogContext 日志上下文，用于携带 IMSI 等信息
type LogContext struct {
	IMSI   string
	Module string
}

// NewLogContext 创建日志上下文
func NewLogContext(imsi, module string) *LogContext {
	return &LogContext{
		IMSI:   imsi,
		Module: module,
	}
}

// 从 Terminal 结构体提取 IMSI 的便捷函数
func ExtractIMSI(term *vars.Terminal) string {
	if term != nil && term.Prop.IMSI != "" {
		return term.Prop.IMSI
	}
	return DefaultIMSI
}

// 模块专用的带 IMSI 日志函数

// TCP 模块日志
func TcpDebugWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.DebugWithIMSI(imsi, format, v...)
		}
	}
}

func TcpInfoWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.InfoWithIMSI(imsi, format, v...)
		}
	}
}

func TcpWarningWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.WarningWithIMSI(imsi, format, v...)
		}
	}
}

func TcpErrorWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("tcp"); logger != nil {
			logger.ErrorWithIMSI(imsi, format, v...)
		}
	}
}

// Server 模块日志
func ServerInfoWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("server"); logger != nil {
			logger.InfoWithIMSI(imsi, format, v...)
		}
	}
}

func ServerErrorWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("server"); logger != nil {
			logger.ErrorWithIMSI(imsi, format, v...)
		}
	}
}

// Database 模块日志
func DatabaseInfoWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.InfoWithIMSI(imsi, format, v...)
		}
	}
}

func DatabaseErrorWithIMSI(imsi, format string, v ...interface{}) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("database"); logger != nil {
			logger.ErrorWithIMSI(imsi, format, v...)
		}
	}
}

// 便捷的设备相关日志函数
func LogDeviceEvent(imsi, event, details string) {
	TcpInfoWithIMSI(imsi, "设备事件: %s - %s", event, details)
}

func LogDeviceError(imsi, operation, error string) {
	TcpErrorWithIMSI(imsi, "设备操作失败: %s - %s", operation, error)
}

func LogDeviceConnection(imsi, action, details string) {
	TcpInfoWithIMSI(imsi, "连接事件: %s - %s", action, details)
}

// 业务场景专用日志函数
func LogRegistration(imsi string, success bool, details string) {
	if success {
		TcpInfoWithIMSI(imsi, "设备注册成功: %s", details)
	} else {
		TcpErrorWithIMSI(imsi, "设备注册失败: %s", details)
	}
}

func LogHeartbeat(imsi string, step int, taskId int64, details string) {
	TcpInfoWithIMSI(imsi, "心跳处理: Step=%d, TaskId=%d, %s", step, taskId, details)
}

func LogTaskExecution(imsi string, taskId, executionId int64, status, details string) {
	TcpInfoWithIMSI(imsi, "任务执行: TaskId=%d, ExecutionId=%d, Status=%s, %s", 
		taskId, executionId, status, details)
}

func LogSecurityEvent(imsi, eventType, details string) {
	TcpWarningWithIMSI(imsi, "安全事件: %s - %s", eventType, details)
}

// Context 相关的日志函数（为未来扩展准备）
func LogWithContext(ctx context.Context, level, module, format string, v ...interface{}) {
	// 从 context 中提取 IMSI（如果有的话）
	imsi := DefaultIMSI
	if ctx != nil {
		if ctxIMSI := ctx.Value("imsi"); ctxIMSI != nil {
			if imsiStr, ok := ctxIMSI.(string); ok {
				imsi = imsiStr
			}
		}
	}
	
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(module); logger != nil {
			switch level {
			case "DEBUG":
				logger.DebugWithIMSI(imsi, format, v...)
			case "INFO":
				logger.InfoWithIMSI(imsi, format, v...)
			case "WARN":
				logger.WarningWithIMSI(imsi, format, v...)
			case "ERROR":
				logger.ErrorWithIMSI(imsi, format, v...)
			}
		}
	}
}

// 格式化 IMSI 显示
func FormatIMSI(imsi string) string {
	if imsi == "" || imsi == DefaultIMSI {
		return DefaultIMSI
	}
	// 可以在这里添加 IMSI 格式化逻辑，比如脱敏显示
	return imsi
}

// Kafka 专用日志函数
func LogKafkaConnection(brokers []string, success bool, details string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			if success {
				logger.Info("Kafka 连接成功: brokers=%v, %s", brokers, details)
			} else {
				logger.Error("Kafka 连接失败: brokers=%v, %s", brokers, details)
			}
		}
	}
}

func LogKafkaPartition(partition int32, success bool, details string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			if success {
				logger.Info("Kafka 分区消费者启动成功: partition=%d, %s", partition, details)
			} else {
				logger.Error("Kafka 分区消费者启动失败: partition=%d, %s", partition, details)
			}
		}
	}
}

func LogKafkaMessage(imsi string, topic string, partition int32, offset int64, cmd uint16, details string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			logger.InfoWithIMSI(imsi, "Kafka 消息接收: topic=%s, partition=%d, offset=%d, cmd=0x%04X, %s",
				topic, partition, offset, cmd, details)
		}
	}
}

func LogKafkaMessageError(topic string, partition int32, offset int64, error, rawMessage string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			logger.Error("Kafka 消息解析失败: topic=%s, partition=%d, offset=%d, error=%s, raw=%s",
				topic, partition, offset, error, rawMessage)
		}
	}
}

func LogKafkaCommand(imsi string, cmd uint16, success bool, details string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			if success {
				logger.InfoWithIMSI(imsi, "Kafka 命令处理成功: cmd=0x%04X, %s", cmd, details)
			} else {
				logger.ErrorWithIMSI(imsi, "Kafka 命令处理失败: cmd=0x%04X, %s", cmd, details)
			}
		}
	}
}

func LogKafkaDeviceSend(imsi string, cmd uint16, success bool, details string) {
	if globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			if success {
				logger.InfoWithIMSI(imsi, "Kafka 设备指令下发成功: cmd=0x%04X, %s", cmd, details)
			} else {
				logger.ErrorWithIMSI(imsi, "Kafka 设备指令下发失败: cmd=0x%04X, %s", cmd, details)
			}
		}
	}
}

// 批量日志记录（用于性能敏感场景）
type BatchLogger struct {
	module string
	imsi   string
	logs   []string
}

func NewBatchLogger(module, imsi string) *BatchLogger {
	return &BatchLogger{
		module: module,
		imsi:   imsi,
		logs:   make([]string, 0),
	}
}

func (bl *BatchLogger) Add(level, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	bl.logs = append(bl.logs, fmt.Sprintf("[%s] %s", level, message))
}

func (bl *BatchLogger) Flush() {
	if len(bl.logs) == 0 {
		return
	}

	if globalManager != nil {
		if logger := globalManager.GetModuleLogger(bl.module); logger != nil {
			for _, logMsg := range bl.logs {
				logger.InfoWithIMSI(bl.imsi, "批量日志: %s", logMsg)
			}
		}
	}

	bl.logs = bl.logs[:0] // 清空但保留容量
}

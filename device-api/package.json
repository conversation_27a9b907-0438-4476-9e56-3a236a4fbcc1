{"name": "device-status-api", "version": "1.0.0", "description": "设备状态追踪API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["device", "status", "tracking", "tdengine", "api"], "author": "Device Tracking System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "moment": "^2.29.4", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}
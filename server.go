package main

import (
	"ccserver/app/library/common"
	dbService "ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/boot"
	"ccserver/pix_log"
	"context"
	_ "embed"
	"fmt"
	"log"
	"net"
	"time"

	"github.com/gogf/gf/frame/g"
)

//go:embed version.txt
var version string

func init() {
	vars.App.Ver = version

	// 设置全局 DNS 解析器
	net.DefaultResolver = &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: time.Second * 2,
			}
			return d.DialContext(ctx, "udp", "*******:53")
		},
	}
}

// initConfig 初始化配置信息
func initConfig() error {
	// 直接使用GoFrame默认配置机制，通过环境变量或命令行参数指定配置文件
	// 例如：export GF_GCFG_FILE=config.prod.toml 或 ./ccserver --gf.gcfg.file=config.prod.toml
	if e := g.Cfg().Struct(&vars.Config); e != nil {
		return e
	}

	// 修正时间单位
	vars.Config.Business.PushInterval *= time.Second
	vars.Config.Business.HeartbeatCheckInterval *= time.Second
	vars.Config.Business.HeartbeatTimeout *= time.Second
	vars.Config.Schedule.UpgradeHeartbeat *= time.Second
	vars.Config.Schedule.UpgradeTimeout *= time.Second
	vars.Config.Server.Tcp.KeepaliveTimeout *= time.Second

	// 🆕 MQTT配置时间单位修正
	vars.Config.Server.Mqtt.ConnectTimeout *= time.Second
	vars.Config.Server.Mqtt.KeepaliveTimeout *= time.Second
	vars.Config.Server.Mqtt.PingTimeout *= time.Second
	vars.Config.Server.Mqtt.MaxReconnectInterval *= time.Second

	vars.Config.Redis.Default.IdleTimeout *= time.Second

	// 🔍 检查配置迁移状态
	checkConfigMigration()

	// 修正安全配置时间单位
	vars.Config.Security.ConnectionLimit.InitialBanDuration *= time.Second
	vars.Config.Security.ConnectionLimit.MaxBanDuration *= time.Second
	vars.Config.Security.ConnectionLimit.CleanupInterval *= time.Second
	vars.Config.Security.ConnectionLimit.CacheExpiration *= time.Second
	vars.Config.Security.ConnectionLimit.LogSuppressionDuration *= time.Second
	vars.Config.Security.IPLimit.BanDuration *= time.Second

	// 构建Redis连接字符串并设置到GoFrame配置中
	redisConnStr := buildRedisConnectionString(vars.Config.Redis.Default)
	g.Cfg().Set("redis.default", redisConnStr)

	return nil
}

// 🔍 检查MQTT配置状态
func checkConfigMigration() {
	mqttConfig := vars.Config.Server.Mqtt

	if mqttConfig.Broker != "" {
		pix_log.Info("✅ [CONFIG] 使用MQTT配置: %s", mqttConfig.Broker)
		pix_log.Info("📋 [CONFIG] MQTT配置: broker=%s, prefix=%s",
			mqttConfig.Broker, mqttConfig.TopicPrefix)
	} else {
		pix_log.Error("❌ [CONFIG] MQTT配置缺失，请检查 [server.mqtt] 配置节")
	}
}

// buildRedisConnectionString 构建Redis连接字符串
func buildRedisConnectionString(config vars.RedisConfig) string {
	// 格式：host:port,db,pass?idleTimeout=timeout
	connStr := fmt.Sprintf("%s:%d,%d", config.Host, config.Port, config.Db)

	if config.Pass != "" {
		connStr += "," + config.Pass
	}

	if config.IdleTimeout > 0 {
		seconds := int(config.IdleTimeout.Seconds())
		connStr += fmt.Sprintf("?idleTimeout=%ds", seconds)
	}

	return connStr
}

func main() {
	defer func() {
		dbService.RecordAllDevicesOffline(false)
	}()

	if e := initConfig(); e != nil {
		fmt.Errorf("(Config) %s", e)
		return
	}

	err, _ := common.CheckKey()
	if err != nil {
		log.Fatalf("证书认证失败, %s", err.Error())
		return
	}

	// 根据配置初始化日志（检查TCP模块的stdout配置）
	enableStdout := vars.Config.Logging.Modules.Tcp.Stdout
	pix_log.InitLogWithConfig(enableStdout)

	if err := boot.Start(); err != nil {
		pix_log.Error("Failed to start server:", err.Error())
	} else {
		pix_log.Info("boot start success")
	}
}

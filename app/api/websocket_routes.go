package api

import (
	websocketService "ccserver/app/websocket"
	"ccserver/pix_log"
	"net/http"

	"github.com/gogf/gf/net/ghttp"
	"github.com/gorilla/websocket"
)

// WebSocket升级器配置
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的连接（生产环境中应该更严格）
		return true
	},
}

// WebSocketHandler WebSocket连接处理器
func WebSocketHandler(r *ghttp.Request) {
	pix_log.Info("[WEBSOCKET] 收到WebSocket连接请求，来源: %s", r.GetClientIp())

	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(r.Response.ResponseWriter, r.Request, nil)
	if err != nil {
		pix_log.Error("[WEBSOCKET] WebSocket升级失败: %v", err)
		r.Response.WriteStatus(http.StatusBadRequest)
		return
	}

	pix_log.Info("[WEBSOCKET] WebSocket连接升级成功")

	// 确保WebSocket服务已初始化
	if websocketService.GlobalWebSocketService == nil {
		pix_log.Error("[WEBSOCKET] WebSocket服务未初始化")
		conn.Close()
		return
	}

	// 添加客户端连接
	websocketService.GlobalWebSocketService.AddClient(conn)

	// 发送欢迎消息
	welcomeMsg := map[string]interface{}{
		"type":    "welcome",
		"message": "WebSocket连接成功",
		"server":  "ccserver",
		"version": "1.0.0",
	}
	conn.WriteJSON(welcomeMsg)

	// 立即发送一次设备列表更新
	go websocketService.GlobalWebSocketService.SendDeviceListUpdate()

	// 处理客户端消息
	go handleClientMessages(conn)
}

// handleClientMessages 处理客户端消息
func handleClientMessages(conn *websocket.Conn) {
	defer func() {
		// 连接关闭时移除客户端
		if websocketService.GlobalWebSocketService != nil {
			websocketService.GlobalWebSocketService.RemoveClient(conn)
		}
	}()

	for {
		// 读取客户端消息
		messageType, data, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				pix_log.Error("[WEBSOCKET] WebSocket连接异常关闭: %v", err)
			} else {
				pix_log.Info("[WEBSOCKET] WebSocket连接正常关闭")
			}
			break
		}

		// 处理消息
		if websocketService.GlobalWebSocketService != nil {
			websocketService.GlobalWebSocketService.HandleMessage(conn, messageType, data)
		}
	}
}

// GetWebSocketStats 获取WebSocket统计信息
func GetWebSocketStats(r *ghttp.Request) {
	stats := map[string]interface{}{
		"connected_clients": 0,
		"service_status":    "stopped",
	}

	if websocketService.GlobalWebSocketService != nil {
		stats["connected_clients"] = websocketService.GlobalWebSocketService.GetClientCount()
		stats["service_status"] = "running"
	}

	r.Response.WriteJsonExit(APIResponse{
		Code:    200,
		Message: "success",
		Data:    stats,
	})
}

// RegisterWebSocketRoutes 注册WebSocket路由
func RegisterWebSocketRoutes(s *ghttp.Server) {
	pix_log.Info("[WEBSOCKET] 注册WebSocket路由")

	// WebSocket连接端点
	s.BindHandler("/ws", WebSocketHandler)
	
	// WebSocket统计信息端点
	s.BindHandler("/api/v1/websocket/stats", GetWebSocketStats)

	pix_log.Info("[WEBSOCKET] WebSocket路由注册完成")
	pix_log.Info("[WEBSOCKET] WebSocket端点: ws://localhost:8080/ws")
	pix_log.Info("[WEBSOCKET] 统计信息端点: http://localhost:8080/api/v1/websocket/stats")
}

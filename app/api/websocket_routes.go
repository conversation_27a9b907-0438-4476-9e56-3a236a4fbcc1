package api

import (
	"ccserver/app/websocket"
	"ccserver/pix_log"
	"net/http"

	"github.com/gogf/gf/net/ghttp"
)

var (
	// 全局WebSocket服务实例
	wsService *websocket.DeviceWebSocketService
)

// InitWebSocketService 初始化WebSocket服务
func InitWebSocketService() {
	wsService = websocket.NewDeviceWebSocketService()
	wsService.Start()
	pix_log.Info("[API] WebSocket服务已启动")
}

// GetWebSocketService 获取WebSocket服务实例
func GetWebSocketService() *websocket.DeviceWebSocketService {
	return wsService
}

// RegisterWebSocketRoutes 注册WebSocket路由
func RegisterWebSocketRoutes(s *ghttp.Server) {
	// 初始化WebSocket服务
	InitWebSocketService()

	// WebSocket连接端点
	s.BindHandler("/ws/devices", handleWebSocketConnection)
	
	pix_log.Info("[API] WebSocket路由已注册: /ws/devices")
}

// handleWebSocketConnection 处理WebSocket连接
func handleWebSocketConnection(r *ghttp.Request) {
	// 将GoFrame的请求转换为标准的http.Request和http.ResponseWriter
	w := r.Response.Writer
	req := r.Request
	
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	
	if req.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 处理WebSocket升级
	if wsService != nil && wsService.GetHub() != nil {
		wsService.GetHub().HandleWebSocket(w, req)
	} else {
		pix_log.Error("[WebSocket] WebSocket服务未初始化")
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("WebSocket service not available"))
	}
}

// StopWebSocketService 停止WebSocket服务
func StopWebSocketService() {
	if wsService != nil {
		wsService.Stop()
		pix_log.Info("[API] WebSocket服务已停止")
	}
}

package system

// 获取终端信息数据 - 终端回调给服务器
// AddLog

import (
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"fmt"

	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtime"
)

// ReceiveDeviceGetInfo ReceiveDeviceGetInfo
type ReceiveDeviceGetInfo struct {
	base

	dataAddr
	dataIP
	dataRs
	dataTZ
	lan
	account
	heartbeat
	msgid int64

	term *vars.Terminal
	data []byte
}

// NewReceiveDeviceGetInfo 初始化
func NewReceiveDeviceGetInfo(device *protocol.HeartbeatDevice, term *vars.Terminal, d []byte) *ReceiveDeviceGetInfo {
	t := &ReceiveDeviceGetInfo{}
	t.term = term
	t.data = d

	// 使用新的 HeartbeatDevice 模型
	t.operate = device.Cmd
	t.msgid = device.Msgid

	switch t.operate {
	case protocol.CbAddrGet: // 位置信息
		t.Position = device.Position
		t.Longitude = device.Longitude
		t.Latitude = device.Latitude

	case protocol.CbMonitoringGet, // 监控中心IP
		protocol.CbUnmpGet: // 平台IP
		t.IP = device.IP
		t.Port = device.Port

	case protocol.CbRs232Get, // RS232
		protocol.CbRs485Get: // RS485
		t.Baud = device.Baud
		t.DataBit = device.DataBit
		t.Parity = device.Parity
		t.StopBit = device.StopBit

	case protocol.CbTzGet: // TimeZone
		t.Tz = device.Tz
		t.NtpServer = device.NtpServer
		t.Interval = device.Interval

	case protocol.CbLanGet:
		t.Dhcp = device.Dhcp
		t.LanIp = device.LanIp
		t.Netmask = device.Netmask
		t.StartIp = device.StartIp
		t.EndIp = device.EndIp

	case protocol.CbAccountGet:
		t.Name = device.Name
		t.Password = device.Password

	case protocol.CbHeartbeatGet:
		t.Interval = device.Time
	}

	return t
}

// DoReply 终端上发获取终端信息
func (t *ReceiveDeviceGetInfo) DoReply() {
	defer t.writeLog()

	data := gdb.Map{
		"online":       1,
		"updated_time": gtime.Datetime(),
	}
	topicFlag := "" // 最后的标识
	switch t.operate {
	case protocol.CbAddrGet: // 位置信息
		topicFlag = "addr"
		data["position"] = t.Position
		data["longitude"] = t.Longitude
		data["latitude"] = t.Latitude

	case protocol.CbMonitoringGet: // 监控中心IP
		topicFlag = "monitoring"
		data["monitoring_ip"] = t.IP
		data["monitoring_port"] = t.Port

	case protocol.CbUnmpGet: // 平台IP
		topicFlag = "unmp"
		data["unmp_ip"] = t.IP
		data["unmp_port"] = t.Port

	case protocol.CbRs232Get: // RS232
		topicFlag = "rs232"
		data["baud_232"] = t.Baud
		data["data_bit_232"] = t.DataBit
		data["parity_232"] = t.Parity
		data["stop_bit_232"] = t.StopBit

	case protocol.CbRs485Get: // RS485
		topicFlag = "rs485"
		data["baud_485"] = t.Baud
		data["data_bit_485"] = t.DataBit
		data["parity_485"] = t.Parity
		data["stop_bit_485"] = t.StopBit

	case protocol.CbTzGet: // TimeZone
		topicFlag = "timezone"
		data["tz"] = t.Tz
		data["ntp_server"] = t.NtpServer
		data["interval"] = t.Interval

	case protocol.CbLanGet:
		topicFlag = "lan"
		data["dhcp"] = t.Dhcp
		data["lan_ip"] = t.LanIp
		data["netmask"] = t.Netmask
		data["start_ip"] = t.StartIp
		data["end_ip"] = t.EndIp

	case protocol.CbAccountGet:
		topicFlag = "account"
		data["name"] = t.Name
		data["password"] = t.Password

	case protocol.CbHeartbeatGet:
		topicFlag = "heartbeat"
		data["interval"] = t.Time
	}

	// 发布消息到 MQTT
	topic := fmt.Sprintf("%s/%s/info/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		t.term.Prop.IMSI,
		topicFlag,
	)
	token := mqtt.SharedClient.Publish(topic, 0, false, string(t.data))
	token.Wait()

	if t.operate != protocol.CbLanGet && t.operate != protocol.CbAccountGet && t.operate != protocol.CbHeartbeatGet {
		if _, err := model.NewOrm(vars.TableDevice).Mod(g.Map{"id": t.term.Prop.ID}, data); err != nil {
			v.LogTcp().Warningf("获取终端信息失败: %s", err)
			return
		}
	}
}

func (t *ReceiveDeviceGetInfo) writeLog() error {
	msg := vars.MessageLog{
		OperateId:   t.operate,
		OperationId: t.msgid,
		IMSI:        t.term.Prop.IMSI,
		Content:     PbNameMap[t.operate],
		ErrCode:     0,
		Status:      StatusSuccessfully,
	}
	_, err := t.AddMessageLog(msg, t.data)
	return err
}

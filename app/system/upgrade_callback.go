// 终端升级回调
// AddLog

package system

import (
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	"ccserver/app/module/upgrade"
	"ccserver/app/service/cache"
	db2 "ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
)

// UpgradeCallback LAN 口告警
type UpgradeCallback struct {
	base
	msgid,
	results int64
	term *vars.Terminal
	data g.Map
}

type QueryStatusBody struct {
	Step     int     `json:"step"`
	DwnMode  string  `json:"dwnMode"`
	InstMode int     `json:"instMode"`
	Reboot   bool    `json:"reboot"`
	Progress float64 `json:"progress"`
	Msg      string  `json:"msg"`
	Ver      string  `json:"ver"`
}

// NewUpgradeCallback UpgradeCallback init
// func NewUpgradeCallback(j *gjson.Json, term *vars.Terminal, d []byte) *UpgradeCallback {
//	t := &UpgradeCallback{}
//	t.term = term
//	t.data = d
//	t.analyticalData(j)
//	return t
// }

// FindFirmware 查询最新的固件版本
func FindFirmware(term *vars.Terminal, swVer string) *UpgradeCallback {
	var firmware vars.TbFirmware
	var device model.Device
	t := &UpgradeCallback{}
	t.term = term

	db2.GetPixmoving().Table("device").Where("imsi = ?", term.Prop.IMSI).First(&device)
	db2.GetPixmoving().Table("firmware").Where("type = ?", device.Type).Where("uid = ?", device.Uid).Order("software_ver desc").First(&firmware)
	hasNew := 0

	if firmware.Name[:15] != swVer {
		hasNew = 1
	}

	body := g.Map{
		"hasNew": hasNew,
		"swVer":  firmware.Name[:15],
		"url":    firmware.Url,
		"size":   firmware.Size,
		"update": "",
		"time":   firmware.UpdatedTime,
		"mode":   1,
		"msg":    firmware.Msg,
	}

	t.data = body
	// t.analyticalData(j)
	return t
}

// Download 处理下载回调
func Download(term *vars.Terminal, status int) {
	topic := fmt.Sprintf("%s/download/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if mqtt.SharedClient.IsConnected() {
		token := mqtt.SharedClient.Publish(topic, 0, false, fmt.Sprintf("%d", status))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)

		if token.Error() != nil {
			v.Log().Debugf("mqtt publish failed:%s", token.Error())
		}
	} else {
		v.Log().Warningf("mqtt not connected, ignore publish")
	}

	if status == 0 {
		var log vars.TbLogFirmware
		db2.GetPixmoving().Table("log_firmware").
			Where("device_imsi = ?", term.Prop.IMSI).
			Order("id desc").
			First(&log)

		db2.GetPixmoving().Model(&log).Update("status", 4)
	}
}

// Install 处理安装回调
func Install(term *vars.Terminal, status int) {
	topic := fmt.Sprintf("%s/install/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if mqtt.SharedClient.IsConnected() {
		token := mqtt.SharedClient.Publish(topic, 0, false, fmt.Sprintf("%d", status))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)

		if token.Error() != nil {
			v.Log().Debugf("mqtt publish failed:%s", token.Error())
		}
	} else {
		v.Log().Warningf("mqtt not connected, ignore publish")
	}

	if status == 0 {
		var log vars.TbLogFirmware
		db2.GetPixmoving().Table("log_firmware").
			Where("device_imsi = ?", term.Prop.IMSI).
			Order("id desc").
			First(&log)

		db2.GetPixmoving().Model(&log).Update("status", 7)
	}
}

// QueryStatus 查询ota升级状态
func QueryStatus(term *vars.Terminal, body *gjson.Json) {
	step := body.GetInt("step")
	reboot := body.GetBool("reboot")
	progress := body.GetFloat64("progress")
	instMode := body.GetInt("instMode")
	dwnMode := body.GetString("dwnMode")
	ver := body.GetString("ver")
	msg := body.GetString("msg")

	topic := fmt.Sprintf("%s/ota/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if common.InArray(step, []int{3, 4, 7, 8}) {
		isReboot := 0
		if reboot {
			isReboot = 1
		}

		var log vars.TbLogFirmware
		var firmware vars.TbFirmware

		db2.GetPixmoving().Table("log_firmware").
			Where("device_imsi = ?", term.Prop.IMSI).
			Order("id desc").
			First(&log)

		db2.GetPixmoving().Table("firmware").Where("name LIKE ?", ver+"%").First(&firmware)

		if log.Status == step {
			db2.GetPixmoving().Model(&log).Updates(map[string]interface{}{
				"is_reboot": isReboot,
				"progress":  progress,
				"status":    step,
			})
		} else {
			var newLog vars.TbLogFirmware
			newLog.DeviceId = log.DeviceId
			newLog.DeviceImsi = log.DeviceImsi
			newLog.FirmwareId = firmware.ID
			newLog.Mode = instMode
			newLog.DownloadMode = common.ChangeStringToInt(dwnMode[0:1])
			newLog.Progress = progress
			newLog.Status = step
			newLog.Info = msg
			newLog.CreatedTime = time.Now().Unix()
			newLog.UpgradeTime = time.Now().Unix()
			newLog.OperateId = 1
			newLog.IsCheck = 1
			newLog.IsReboot = isReboot
			db2.GetPixmoving().Create(&newLog)
		}

		if step == 8 {
			db2.GetPixmoving().Table("device").Where("imsi =  ?", term.Prop.IMSI).Update("software_ver", ver)
		}
	}

	if mqtt.SharedClient.IsConnected() {
		returnData, _ := body.ToJsonString()
		token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)
		if token.Error() != nil {
			pix_log.Error("mqtt publish failed:%s", token.Error())
		} else {
			pix_log.Info("mqtt发送成功：" + returnData)
		}
		return
	} else {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}
}

// QueryTask 查询清扫任务
func QueryTask(term *vars.Terminal, j *gjson.Json) {
	topic := fmt.Sprintf("%s/task/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	res := j.GetInt("res")
	name := j.GetString("name")
	status := "1" // 有任务
	if name == "" {
		status = "0" // 无任务
	}

	if res != 0 {
		status = "-1" // 查询失败
	}

	if mqtt.SharedClient.IsConnected() {
		token := mqtt.SharedClient.Publish(topic, 0, false, status)
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)

		if token.Error() != nil {
			v.Log().Debugf("mqtt publish failed:%s", token.Error())
		}
	} else {
		v.Log().Warningf("mqtt not connected, ignore publish")
	}
}

// UpdateTask 更新任务状态
func UpdateTask(term *vars.Terminal, status int) {
	lastHbJsonStr := cache.GetRedisDeviceHeartbeat(term.Prop.IMSI)
	var heartData vars.RedisHeart
	err := json.Unmarshal([]byte(lastHbJsonStr), &heartData)
	if err != nil {
		v.Log().Debugf("心跳数据解析失败")
		return
	}

	db2.GetPixmoving().Table("task_child").Where("id = ?", heartData.LatestTask.RouteId).
		Update("status", status)
}

// analyticalData 解析值数据
func (t *UpgradeCallback) analyticalData(j *gjson.Json) {
	t.operate = j.GetInt("operate")
	t.msgid = j.GetInt64("msgid")
	t.results = j.GetInt64("results")
}

// DoReply DoReply
// func (t *UpgradeCallback) DoReply() {
//	t.removeFromUpgradeDeviceMapIfNeeded()
//	t.writeLog()
// }

// GetReply GetReply
func (t *UpgradeCallback) GetReply(billNo uint32) ([]byte, error) {
	params := g.Map{
		"cmd":  protocol.FindVCUFirmware,
		"bn":   billNo,
		"res":  0,
		"body": t.data,
	}

	return gjson.Encode(params)
}

// func (t *UpgradeCallback) writeLog() error {
//	status := StatusFailed
//	if t.results == 0 {
//		status = StatusSuccessfully
//	}
//
//	msg := vars.MessageLog{
//		OperateId:   t.operate,
//		OperationId: t.msgid,
//		IMSI:        t.term.Prop.IMSI,
//		Content:     "终端固件升级",
//		ErrCode:     t.results,
//		Status:      status,
//	}
//	_, err := t.AddMessageLog(msg, t.data)
//	return err
// }

// 更新升级队列中的
func (t *UpgradeCallback) removeFromUpgradeDeviceMapIfNeeded() {
	upgradeIsSucceed := StatusFailed
	if t.results == 0 {
		upgradeIsSucceed = StatusSuccessfully
	}

	upgradeDevice := upgrade.GetNeededUpgradeDevice(t.term.Prop.IMSI)
	if upgradeDevice == nil {
		return
	}

	if upgradeIsSucceed == StatusSuccessfully {
		v.LogTcp().Debugf("cb, remove from upgrade list:%s", t.term.Prop.IMSI)
		upgrade.RemoveNeededUpgradeDevice(t.term.Prop.IMSI)
		return
	}
	upgradeDevice.CmdSentTime = time.Now()
}

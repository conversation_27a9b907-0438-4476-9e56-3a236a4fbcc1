package system

import (
	"ccserver/app/model"
	"ccserver/app/module/analysis"
	cacheservice "ccserver/app/service/cache"
	"ccserver/app/vars"
	"ccserver/pix_log"
	"time"

	"github.com/gogf/gf/database/gdb"
)

// StartHeartbeatChecker 启动心跳检测器
func StartHeartbeatChecker() {
	// 从配置中获取检测间隔，如果未配置则使用默认值2秒
	checkInterval := vars.Config.Business.HeartbeatCheckInterval
	if checkInterval == 0 {
		checkInterval = 2 * time.Second
		pix_log.Warning("心跳检测间隔未配置，使用默认值: 2秒")
	}

	// 🆕 获取心跳超时配置用于启动日志
	heartbeatTimeout := int64(vars.Config.Business.HeartbeatTimeout.Seconds())
	if heartbeatTimeout == 0 {
		heartbeatTimeout = 10
	}

	pix_log.Info("🚀 [HEARTBEAT_CHECKER] 启动统一在线状态检测器")
	pix_log.Info("📋 [HEARTBEAT_CHECKER] 配置参数: 检测间隔=%v, 超时时间=%d秒", checkInterval, heartbeatTimeout)
	pix_log.Info("🎯 [HEARTBEAT_CHECKER] 职责: 统一负责所有设备的在线/离线状态判断和数据库更新")

	go func() {
		for {
			checkHeartbeat()
			time.Sleep(checkInterval)
		}
	}()
}

// checkHeartbeat 检查心跳
func checkHeartbeat() {
	// 获取所有设备的最后心跳时间
	heartbeats := cacheservice.GetAllRedisHeartbeatTs()
	if len(heartbeats) == 0 {
		return
	}

	// 从配置中获取心跳超时时间，如果未配置则使用默认值10秒
	heartbeatTimeout := int64(vars.Config.Business.HeartbeatTimeout.Seconds())
	if heartbeatTimeout == 0 {
		heartbeatTimeout = 10
		pix_log.Warning("心跳超时时间未配置，使用默认值: 10秒")
	}
	// 🗑️ 移除频繁的配置日志

	now := time.Now().Unix()
	for imsi, lastHeartbeat := range heartbeats {
		// 先查询设备当前状态
		deviceRes, err := model.NewOrm(vars.TableDevice).One(gdb.Map{"imsi": imsi}, "online")
		if err != nil {
			pix_log.ErrorWithIMSI(imsi, "查询设备状态失败: %v", err)
			continue
		}

		currentOnline := deviceRes["online"].Int()

		// 检查是否超时
		if now-lastHeartbeat > heartbeatTimeout {
			// 只有当前状态为在线(1)时才进行更新和通知
			if currentOnline == 1 {
				// 删除 Redis 记录
				cacheservice.DelRedisHeartbeatTs(imsi)

				// 更新设备状态为离线
				if _, err := model.NewOrm(vars.TableDevice).Mod(gdb.Map{"imsi": imsi}, gdb.Map{"online": 0}); err != nil {
					pix_log.ErrorWithIMSI(imsi, "更新设备离线状态失败: %v", err)
					continue
				}

				// 发布设备心跳离线分析消息
				if err := analysis.SharedPublisher.PublishDeviceMessage(imsi, "heartbeat_offline"); err != nil {
					pix_log.ErrorWithIMSI(imsi, "发布心跳离线分析消息失败: %v", err)
				}

				// 🆕 增强日志：记录状态变更详情
				timeoutDuration := now - lastHeartbeat
				pix_log.InfoWithIMSI(imsi, "🔴 [STATUS_CHANGE] 设备状态变更: 在线→离线, 原因=心跳超时, 超时时长=%d秒, 配置超时=%d秒",
					timeoutDuration, heartbeatTimeout)
			}
			// 🗑️ 移除频繁的调试日志：设备已离线时不记录日志
		} else {
			// 🆕 检查是否需要设置为在线状态
			if currentOnline == 0 {
				// 更新设备状态为在线
				if _, err := model.NewOrm(vars.TableDevice).Mod(gdb.Map{"imsi": imsi}, gdb.Map{"online": 1}); err != nil {
					pix_log.ErrorWithIMSI(imsi, "更新设备在线状态失败: %v", err)
					continue
				}

				// 发布设备心跳在线分析消息
				if err := analysis.SharedPublisher.PublishDeviceMessage(imsi, "heartbeat_online"); err != nil {
					pix_log.ErrorWithIMSI(imsi, "发布心跳在线分析消息失败: %v", err)
				}

				// 🆕 增强日志：记录状态变更详情
				pix_log.InfoWithIMSI(imsi, "🟢 [STATUS_CHANGE] 设备状态变更: 离线→在线, 原因=心跳恢复")
			}
		}
	}
}

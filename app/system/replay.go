package system

import (
	"ccserver/app/module/mqtt"
	"ccserver/app/vars"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
)

// Replay 获取操作结果通知
func Replay(term *vars.Terminal, cmd int, res int, bn int) {
	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/cmd_resp/%d",
		vars.Config.Server.Mqtt.TopicPrefix,
		// term.Prop.IMSI,
		term.Prop.UID,
	)

	data := map[string]interface{}{
		"deviceId": term.Prop.IMSI,
		"cmd":      cmd,
		"res":      res,
		"bn":       bn,
	}

	if mqtt.SharedClient.IsConnected() {
		returnData, _ := json.Marshal(data)
		token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)
		if token.Error() != nil {
			pix_log.Error("mqtt publish failed:%s", token.Error())
		}
		return
	} else {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}
}

package system

import (
	"ccserver/app/module/mqtt"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
)

// SelectMap 查询自驾地图
func SelectMap(term *vars.Terminal, mapList []interface{}) {
	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/map/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if mqtt.SharedClient.IsConnected() {
		returnData, _ := json.Marshal(mapList)
		token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)
		if token.Error() != nil {
			pix_log.Error("mqtt publish failed:%s", token.Error())
		}
		return
	} else {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}
}

// SelectMapInfo 查询自驾地图进度
func SelectMapInfo(term *vars.Terminal, mapInfo *protocol.MapInfo) {
	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/map_info/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if mqtt.SharedClient.IsConnected() {
		returnData, err := json.Marshal(mapInfo)
		if err != nil {
			pix_log.Error("map info marshal failed:", err.Error())
			return
		}
		token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)
		if token.Error() != nil {
			pix_log.Error("mqtt publish failed:%s", token.Error())
		}
		return
	} else {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}
}

// SetMapInfo 设置地图回调
func SetMapInfo(term *vars.Terminal) {
	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/set_map/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	if mqtt.SharedClient.IsConnected() {
		token := mqtt.SharedClient.Publish(topic, 0, false, "1")
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)
		if token.Error() != nil {
			pix_log.Error("mqtt publish failed:%s", token.Error())
		}
		return
	} else {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}
}

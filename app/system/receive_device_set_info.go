package system

// 终端回调给服务器 (设置终端数据)
// AddLog

import (
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"fmt"

	"github.com/gogf/gf/frame/g"
)

// ReceiveDeviceSetInfo 获取参数
type ReceiveDeviceSetInfo struct {
	base
	msgid,
	results int64
	term *vars.Terminal
	data []byte
}

// NewReceiveDeviceSetInfo 初始化
func NewReceiveDeviceSetInfo(device *protocol.HeartbeatDevice, term *vars.Terminal, d []byte) *ReceiveDeviceSetInfo {
	t := &ReceiveDeviceSetInfo{}
	t.term = term
	t.data = d

	// 只解析基本字段
	t.analyticalData(device)
	return t
}

// analyticalData 解析值数据
func (t *ReceiveDeviceSetInfo) analyticalData(device *protocol.HeartbeatDevice) {
	// 只需要基本字段
	t.operate = device.Cmd
	t.msgid = device.Msgid
	t.results = device.Results
}

// DoReply DoReply
func (t *ReceiveDeviceSetInfo) DoReply() {
	defer t.writeLog()

	if t.results == 1 { // 操作失败不入库
		return
	}

	data := g.Map{}
	var err error
	var topicFlag string // 最后的标识
	switch t.operate {
	// 更新位置
	case protocol.CbAddrSet:
		topicFlag = "addr"
		data, err = t.getAddrCache()
		break

	// 更新监控中心IP
	// case protocol.CbMonitoringSet:
	//	topicFlag = "monitoring"
	//	data, err = t.getMonitorIPAddrCache()
	//	break
	//
	// // 更新管理平台IP
	// case protocol.CbUnmpSet:
	//	topicFlag = "unmp"
	//	data, err = t.getUnmpIPAddrCache()
	//	break

	// 更新 RS232
	case protocol.CbRs232Set:
		topicFlag = "rs232"
		data, err = t.getRs232Cache()
		break

	// 更新 RS485
	case protocol.CbRs485Set:
		topicFlag = "rs485"
		data, err = t.getRs485Cache()
		break

	// 更新时区
	case protocol.CbTzSet:
		topicFlag = "timezone"
		data, err = t.getTzCache()
		break

	case protocol.CbLanSet:
		topicFlag = "lan"
		data, err = t.getLanCache()
		break

	case protocol.CbAccountSet:
		topicFlag = "account"
		data, err = t.getAccountCache()
		break

	case protocol.CbHeartbeatSet:
		topicFlag = "heartbeat"
		data, err = t.getHeartbeatCache()
		break

	default:
		return
	}

	if err != nil {
		v.LogTcp().Printf("Get data failed from redis: %s", err)
		return
	}

	// 🆕 直接使用MQTT配置发布消息到 MQTT
	topic := fmt.Sprintf("%s/%s/info/%s_set",
		vars.Config.Server.Mqtt.TopicPrefix,
		t.term.Prop.IMSI,
		topicFlag,
	)
	token := mqtt.SharedClient.Publish(topic, 0, false, string(t.data))
	token.Wait()

	if t.operate != protocol.CbLanSet && t.operate != protocol.CbAccountSet && t.operate != protocol.CbHeartbeatSet {
		if _, err := model.NewOrm(vars.TableDevice).Mod(g.Map{"imsi": t.term.Prop.IMSI}, data); err != nil {
			v.LogTcp().Warningf("更新终端信息失败: %s", err.Error())
			return
		}
	}
}

func (t *ReceiveDeviceSetInfo) getAddrCache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.AddrSet)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm dataAddr
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"position":  dataForm.Position,
		"latitude":  dataForm.Latitude,
		"longitude": dataForm.Longitude,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getRs232Cache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.Rs232Set)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm dataRs
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"baud_232":     dataForm.Baud,
		"data_bit_232": dataForm.DataBit,
		"parity_232":   dataForm.Parity,
		"stop_bit_232": dataForm.StopBit,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getRs485Cache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.Rs485Set)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm dataRs
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"baud_485":     dataForm.Baud,
		"data_bit_485": dataForm.DataBit,
		"parity_485":   dataForm.Parity,
		"stop_bit_485": dataForm.StopBit,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getTzCache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.TzSet)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm dataTZ
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"tz":         dataForm.Tz,
		"ntp_server": dataForm.NtpServer,
		"interval":   dataForm.Interval,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getLanCache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.LanSet)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm lan
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"dhcp":     dataForm.Dhcp,
		"lan_ip":   dataForm.LanIp,
		"netmask":  dataForm.Netmask,
		"start_ip": dataForm.StartIp,
		"end_ip":   dataForm.EndIp,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getAccountCache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.AccountSet)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm account
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"name":     dataForm.Name,
		"password": dataForm.Password,
	}, nil
}

func (t *ReceiveDeviceSetInfo) getHeartbeatCache() (data g.Map, err error) {
	redisKey := fmt.Sprintf(vars.RedisOperationPbMsg, t.term.Prop.IMSI, protocol.HeartbeatSet)
	redisVar, err := v.Redis().DoVar("GET", redisKey)
	if err != nil {
		return nil, err
	}
	var dataForm heartbeat
	if err := redisVar.Struct(&dataForm); err != nil {
		return nil, err
	}
	return g.Map{
		"time": dataForm.Time,
	}, nil
}

func (t *ReceiveDeviceSetInfo) writeLog() error {
	status := StatusFailed
	if t.results == 0 {
		status = StatusSuccessfully
	}
	msg := vars.MessageLog{
		OperateId:   t.operate,
		OperationId: t.msgid,
		IMSI:        t.term.Prop.IMSI,
		Content:     PbNameMap[t.operate],
		ErrCode:     t.results,
		Status:      status,
	}
	_, err := t.AddMessageLog(msg, t.data)
	return err
}

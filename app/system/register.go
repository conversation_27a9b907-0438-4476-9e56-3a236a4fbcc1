// 终端注册 - 终端上线
// AddLog

package system

import (
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/analysis"
	"ccserver/app/module/client"
	"ccserver/app/module/mqtt"
	"ccserver/app/module/upgrade"
	"ccserver/app/security"
	cacheservice "ccserver/app/service/cache"
	db2 "ccserver/app/service/db"
	dbservice "ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"errors"
	"fmt"
	"time"

	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtime"
)

// Register 注册协议
// type Register struct {
// 	base
// 	registerData
// 	term *vars.Terminal
// 	data []byte
// }
//
// // 参数值
// type registerData struct {
// 	imei,
// 	imsi,
// 	model,
// 	softwareVer,
// 	hardwareVer,
// 	vender,
// 	ci,
// 	pci,
// 	manageIp,
// 	position,
// 	longitude,
// 	latitude string
// }
//
// // NewRegister 注册初始化
// func NewRegister(j *gjson.Json, term *vars.Terminal, d []byte) *Register {
// 	t := &Register{}
// 	t.term = term
// 	t.data = d
// 	t.parseData(j)
// 	return t
// }
//
// // parseData 解析值数据
// func (register *Register) parseData(j *gjson.Json) {
// 	register.operate = j.GetInt("operate")
// 	register.imei = j.GetString("imei")
// 	register.imsi = j.GetString("imsi")
// 	register.model = j.GetString("model")
// 	register.softwareVer = j.GetString("sw")
// 	register.hardwareVer = j.GetString("hd")
// 	register.vender = j.GetString("vendor")
// 	register.ci = j.GetString("ci")
// 	register.pci = j.GetString("pci")
// 	register.manageIp = j.GetString("manage_ip")
// 	register.position = j.GetString("position")
// 	register.longitude = j.GetString("longitude")
// 	register.latitude = j.GetString("latitude")
// }
//
// // GetReply GetReply
// func (register *Register) GetReply(billNo uint32) ([]byte, error) {
// 	var res bool
// 	var resCode int
// 	if err := register.process(); err != nil {
// 		pix_log.Error("终端注册失败:%s", err)
// 	} else { // 数据库操作成功
// 		res = true
// 	}
//
// 	params := g.Map{
// 		"cmd": protocol.CbRegister,
// 		"res": resCode,
// 		"bn":  billNo,
// 	}
//
// 	if res {
// 		register.term.Prop.SoftwareVer = register.softwareVer
//
// 		c := client.Get(register.term.Prop.IMSI)
// 		// DebugOfOffline v.LogTcp().Debugf("client.Add start, IMSI:%s, id:%d", register.term.Prop.IMSI, register.term.Prop.ID)
// 		client.Add(register.term.Prop.IMSI, register.term)
// 		// DebugOfOffline v.LogTcp().Debugf("client.Add end, IMSI:%s, id:%d", register.term.Prop.IMSI, register.term.Prop.ID)
// 		if c != nil { // 终端异常断开连接(如突然断电)后又连接或有重复的IMSI设备上线
// 			// 不在登录日志表中记录此种情况的上线
// 			term := c.(*vars.Terminal)
// 			term.Prop.QuitReason = vars.DISCONNECT_REASON_DUPLICATE
// 			term.IsConnected = false
// 			term.Prop.Quit <- true
// 			v.LogTcp().Warningf("IMSI(%s)已存在旧连接，将断开旧连接, IMSI可能重复或终端异常断开连接(如突然断电)后又连接:old(%s), new(%s)", register.term.Prop.IMSI, term.Conn.RemoteAddr(), register.term.Conn.RemoteAddr())
// 			pix_log.Warning("IMSI(%s)已存在旧连接，将断开旧连接, IMSI可能重复或终端异常断开连接(如突然断电)后又连接:old(%s), new(%s)", register.term.Prop.IMSI, term.Conn.RemoteAddr(), register.term.Conn.RemoteAddr())
// 		} else {
// 			if _, e := dbservice.AddLoginLog(register.term.Prop.IMSI, "终端上线", 1); e != nil {
// 				v.LogTcp().Errorf("终端登录的记录写入失败: %s", e)
// 				pix_log.Error("终端登录的记录写入失败: %s", e)
// 			}
// 		}
//
// 		register.checkIsNeededUpgrade() // 判断是否需要升级固件
// 	}
//
// 	return gjson.Encode(params)
// }

// Register 注册结构体
type Register struct {
	base
	registerData
	term *vars.Terminal
	data []byte
}

// 参数值
type registerData struct {
	imei,
	imsi,
	model,
	softwareVer,
	hardwareVer,
	vender,
	ci,
	pci,
	manageIp,
	position,
	longitude,
	latitude string
}

// NewRegister 注册初始化
func NewRegister(device *protocol.HeartbeatDevice, term *vars.Terminal, d []byte) *Register {
	t := &Register{}
	t.term = term
	t.data = d
	t.parseFromDevice(device)
	return t
}

// parseFromDevice 从 HeartbeatDevice 解析数据
func (register *Register) parseFromDevice(device *protocol.HeartbeatDevice) {
	register.operate = int(device.Cmd)
	register.imei = device.IMEI
	register.imsi = device.IMSI
	register.model = device.Model
	register.softwareVer = device.SoftwareVer
	register.hardwareVer = device.HardwareVer
	register.vender = device.Vendor
	register.ci = device.CI
	register.pci = device.PCI
	register.manageIp = device.ManageIP
	register.position = device.Position
	register.longitude = fmt.Sprintf("%f", device.Longitude)
	register.latitude = fmt.Sprintf("%f", device.Latitude)
}

// GetReply GetReply
func (register *Register) GetReply(billNo uint32) ([]byte, error) {
	var res bool
	var resCode int
	if err := register.process(); err != nil {
		pix_log.Error("终端注册失败:%s", err)
	} else { // 数据库操作成功
		res = true
	}

	params := g.Map{
		"cmd": protocol.CbRegister,
		"res": resCode,
		"bn":  billNo,
	}

	if res {
		register.term.Prop.SoftwareVer = register.softwareVer

		c := client.Get(register.term.Prop.IMSI)

		// 🆕 增强日志：记录客户端缓存状态
		if c != nil {
			oldTerm := c.(*vars.Terminal)
			pix_log.WarningWithIMSI(register.term.Prop.IMSI, "🔄 [REGISTER] 检测到重复IMSI连接：旧连接=%s, 新连接=%s, 旧连接时间=%s, 新连接时间=%s",
				oldTerm.Conn.RemoteAddr(), register.term.Conn.RemoteAddr(),
				oldTerm.Prop.ConnectAt.Format("15:04:05.000"), register.term.Prop.ConnectAt.Format("15:04:05.000"))
		} else {
			pix_log.InfoWithIMSI(register.term.Prop.IMSI, "✅ [REGISTER] 首次连接或正常重连：IP=%s", register.term.Prop.IP)
		}

		client.Add(register.term.Prop.IMSI, register.term)

		if c != nil { // 终端异常断开连接(如突然断电)后又连接或有重复的IMSI设备上线
			// 不在登录日志表中记录此种情况的上线
			term := c.(*vars.Terminal)
			term.Prop.QuitReason = vars.DISCONNECT_REASON_DUPLICATE
			term.IsConnected = false

			// 🆕 增强日志：详细记录旧连接断开原因
			oldConnectionDuration := time.Since(term.Prop.ConnectAt)
			oldLastActivity := time.Since(term.Prop.UpdatedAt)
			pix_log.WarningWithIMSI(register.term.Prop.IMSI, "🔄 [REGISTER] 断开旧连接：连接时长=%v, 最后活动=%v前, 授权状态=%t",
				oldConnectionDuration, oldLastActivity, term.Prop.Authorized)

			term.Prop.Quit <- true
			v.LogTcp().Warningf("IMSI(%s)已存在旧连接，将断开旧连接, IMSI可能重复或终端异常断开连接(如突然断电)后又连接:old(%s), new(%s)",
				register.term.Prop.IMSI, term.Conn.RemoteAddr(), register.term.Conn.RemoteAddr())

			token := mqtt.SharedClient.Publish(fmt.Sprintf("/pix/device/%s/reconnect", register.term.Prop.IMSI), 0, false, fmt.Sprintf("IMSI(%s)已存在旧连接，将断开旧连接, IMSI可能重复或终端异常断开连接(如突然断电)后又连接:old(%s), new(%s)",
				register.term.Prop.IMSI, term.Conn.RemoteAddr(), register.term.Conn.RemoteAddr()))
			token.Wait()
		} else {
			if _, e := dbservice.AddLoginLog(register.term.Prop.IMSI, "终端上线", 1); e != nil {
				v.LogTcp().Errorf("终端登录的记录写入失败: %s", e)
				pix_log.ErrorWithIMSI(register.term.Prop.IMSI, "终端登录的记录写入失败: %v", e)
			}
		}

		register.checkIsNeededUpgrade() // 判断是否需要升级固件
	}

	return gjson.Encode(params)
}

// process process
func (register *Register) process() error {
	// 🆕 安全检查：检查IMSI连接是否被允许
	if allowed, reason := security.CheckConnectionAllowed(register.imsi, register.term.Prop.IP); !allowed {
		errorMsg := fmt.Sprintf("设备注册被安全防护拒绝：IMSI=%s, IP=%s, 原因=%s",
			register.imsi, register.term.Prop.IP, reason)
		pix_log.Warning("🚫 [Register] %s", errorMsg)

		// 记录安全事件
		register.recordSecurityEvent("CONNECTION_BLOCKED_BY_GUARD", errorMsg)
		register.disconnectUnauthorizedDevice("连接被安全防护拒绝")

		return errors.New(reason)
	}

	deviceRes, err := model.NewOrm(vars.TableDevice).One(gdb.Map{"imsi": register.imsi}, "")
	if err != nil {
		// 增强日志：记录数据库查询失败的详细信息
		errorMsg := fmt.Sprintf("设备注册时数据库查询失败：错误=%v", err)
		pix_log.WarningWithIMSI(register.imsi, errorMsg)
		v.LogTcp().Warningf("⚠️ [Register] %s, IP=%s", errorMsg, register.term.Prop.IP)
	}

	// 🗑️ 移除心跳记录时间戳初始化，因为现在所有心跳都会记录
	cacheservice.SetRedisHeartbeatTs(register.imsi, time.Now().Unix())

	// 🎯 设备注册成功时设置在线状态（合理，因为刚建立连接）
	// 注意：后续的在线/离线状态变更由心跳检测器统一负责
	if !deviceRes.IsEmpty() {
		if _, err := model.NewOrm(vars.TableDevice).Mod(gdb.Map{"imsi": register.imsi}, gdb.Map{"online": 1}); err != nil {
			pix_log.ErrorWithIMSI(register.imsi, "更新设备在线状态失败: %v", err)
		} else {
			pix_log.InfoWithIMSI(register.imsi, "🟢 [REGISTER] 设备注册成功，初始状态设置为在线")
		}
	}

	var siteName string
	var ipModel model.Model
	ipModel = model.NewOrm(vars.TableIP)
	ipRes, err := ipModel.One(g.Map{"ip": register.term.Prop.IP}, "")
	if err == nil {
		// IP 池不存在 IP 数据则添加
		if ipRes.IsEmpty() {
			ip := register.term.Prop.IP
			if !deviceRes.IsEmpty() {
				if deviceRes["name"].String() != "" {
					siteName = deviceRes["name"].String()
				} else {
					siteName = ip
				}
			} else {
				siteName = ip
			}

			ipModel.Add(g.Map{"ip": ip, "name": siteName})

			// refresh ip pool
			v.Redis().Do("SADD", vars.RedisNetworkIPPool, ip)
		} else {
			if deviceRes["name"].String() != "" {
				siteName = deviceRes["name"].String()
			} else {
				var ip vars.TbIP
				if e := ipRes.Struct(&ip); e == nil {
					siteName = ip.Name
				}
			}
		}
	}

	nowTime := gtime.Datetime()
	data := gdb.Map{
		"imei":         register.imei,
		"model":        register.model,
		"software_ver": register.softwareVer,
		"hardware_ver": register.hardwareVer,
		"vender":       register.vender,
		"ci":           register.ci,
		"pci":          register.pci,
		"manage_ip":    register.manageIp,
		"online":       1,
		"name":         siteName,
		"ip":           register.term.Prop.IP,
		"online_time":  nowTime,
		"updated_time": nowTime,
	}
	if deviceRes.IsEmpty() { // 新终端
		// 增强日志：记录详细的注册失败信息
		errorMsg := fmt.Sprintf("设备注册失败：不存在于device表中，暂不允许新终端注册")
		pix_log.WarningWithIMSI(register.imsi, errorMsg)
		v.LogTcp().Warningf("❌ [Register] %s, IP=%s, IMEI=%s", errorMsg, register.term.Prop.IP, register.imei)

		// 🆕 记录到安全防护系统
		security.RecordFailedConnection(register.imsi, register.term.Prop.IP, "设备不存在于device表中")

		// 🆕 安全增强：主动断开未授权设备连接
		register.recordSecurityEvent("UNAUTHORIZED_DEVICE_REGISTRATION", errorMsg)
		register.disconnectUnauthorizedDevice("设备注册失败：未授权设备")

		return errors.New("暂不允许新终端注册：" + register.imsi)
		//
		// data["imsi"] = register.imsi
		// data["created_time"] = nowTime
		// data["position"] = register.position
		// data["longitude"] = register.longitude
		// data["latitude"] = register.latitude
		// data["online_times"] = 1
		//
		// deviceCount := cacheservice.GetRedisDeviceAuthorizedCnt()
		//
		// licenseCount := (int64)(v.GetLicense().Total)
		// if deviceCount >= licenseCount { // 校验授权的终端数
		// 	v.Redis().Do("HSET", vars.RedisDeviceUnauthorized, register.term.Prop.IMSI, data)
		// 	info := fmt.Sprintf("授权终端数为%d,当前已登记终端数为%d,该终端(IMSI:%s)已超出授权", (int)(licenseCount), (int)(deviceCount), register.term.Prop.IMSI)
		// 	// v.LogTcp().Warningf(info)
		// 	return errors.New(info)
		// } else { // 添加终端
		// 	cacheservice.IncreaseRRedisDeviceAuthorizedCnt(1)
		// 	r, err := model.NewOrm(vars.TableDevice).Add(data)
		// 	if err != nil {
		// 		cacheservice.IncreaseRRedisDeviceAuthorizedCnt(-1)
		// 		v.LogTcp().Errorf("添加新终端到数据库失败:%s", err)
		// 		return err
		// 	}
		// 	cacheservice.SetRedisDeviceOfflineTs(register.imsi, -1)
		// 	register.term.Prop.Authorized = true
		// 	register.term.Prop.ID, _ = r.LastInsertId()
		// }
	} else { // 旧终端
		if !deviceRes.IsEmpty() {
			if deviceRes["expire_time"].Int64() != 0 && deviceRes["expire_time"].Int64() <= time.Now().Unix() {
				// 增强日志：记录设备过期的详细信息
				expireTime := time.Unix(deviceRes["expire_time"].Int64(), 0).Format("2006-01-02 15:04:05")
				errorMsg := fmt.Sprintf("设备注册失败：IMSI=%s 已过期，过期时间=%s", register.imsi, expireTime)
				pix_log.Warning(errorMsg)
				v.LogTcp().Warningf("❌ [Register] %s, DeviceID=%d, IP=%s", errorMsg, deviceRes["id"].Int64(), register.term.Prop.IP)

				return errors.New("该终端已过期")
			}

			register.term.Prop.ID = deviceRes["id"].Int64()
			register.term.Prop.UID = deviceRes["uid"].Int64()
			register.term.Prop.IsMaintain = deviceRes["maintain_state"].Int()

			c := client.Get(register.term.Prop.IMSI)

			if c == nil { // 不是终端异常断开连接(如突然断电)后又连接或有重复的IMEI设备上线
				data["online_times"] = 1

				db2.GetPixmoving().Table("device").Where("imsi = ?", register.imsi).Updates(data)

				cacheservice.SetRedisDeviceOfflineTs(register.imsi, -1)
				// DebugOfOffline v.LogTcp().Debugf("注册老设备成功, IMSI:%s, id:%d", register.term.Prop.IMSI, register.term.Prop.ID)
			}

			register.term.Prop.Authorized = true

			// 🆕 记录成功连接到安全防护系统
			security.RecordSuccessfulConnection(register.imsi, register.term.Prop.IP)

			// 增强日志：记录设备注册成功的详细信息
			successMsg := fmt.Sprintf("设备注册成功：DeviceID=%d, UID=%d, IP=%s",
				register.term.Prop.ID, register.term.Prop.UID, register.term.Prop.IP)
			pix_log.InfoWithIMSI(register.imsi, successMsg)
			v.LogTcp().Infof("✅ [Register] %s", successMsg)
		}
	}

	// 发布设备注册分析消息
	if err := analysis.SharedPublisher.PublishDeviceMessage(register.term.Prop.IMSI, "register"); err != nil {
		pix_log.ErrorWithIMSI(register.term.Prop.IMSI, "发布注册分析消息失败: %v", err)
	}

	return nil
}

// recordSecurityEvent 记录安全事件
func (register *Register) recordSecurityEvent(event string, reason string) {
	// 简化日志输出，避免重复
	pix_log.WarningWithIMSI(register.imsi, "🔒 [SECURITY] %s: IP=%s", event, register.term.Prop.IP)
}

// disconnectUnauthorizedDevice 断开未授权设备连接
func (register *Register) disconnectUnauthorizedDevice(reason string) {
	register.term.Prop.QuitReason = reason

	// 🆕 增强日志：记录安全断开的详细信息
	connectionDuration := time.Since(register.term.Prop.ConnectAt)
	pix_log.WarningWithIMSI(register.imsi, "🔒 [SECURITY] 准备断开未授权设备：IP=%s, 原因=%s, 连接时长=%v, 授权状态=%t",
		register.term.Prop.IP, reason, connectionDuration, register.term.Prop.Authorized)

	// 延迟断开连接，确保响应消息能够发送
	go func() {
		defer func() {
			if r := recover(); r != nil {
				pix_log.ErrorWithIMSI(register.imsi, "断开连接时发生panic: %v", r)
			}
		}()

		// 等待1秒，确保响应消息发送完成
		time.Sleep(1 * time.Second)

		// 标记连接为断开状态
		register.term.IsConnected = false

		select {
		case register.term.Prop.Quit <- true:
			pix_log.InfoWithIMSI(register.imsi, "✅ [SECURITY] 未授权设备连接已断开：IP=%s, Reason=%s, 延迟=1秒",
				register.term.Prop.IP, reason)
			v.LogTcp().Infof("✅ [SECURITY] 未授权设备连接已断开：IMSI=%s, IP=%s", register.imsi, register.term.Prop.IP)
		case <-time.After(5 * time.Second):
			// 超时处理：强制标记连接为断开状态
			pix_log.WarningWithIMSI(register.imsi, "⚠️ [SECURITY] 断开连接信号发送超时：IP=%s, 可能存在死锁", register.term.Prop.IP)
		}
	}()
}

func (register *Register) checkIsNeededUpgrade() {
	if register.term.Prop.IsMaintain != 0 { // 正在维护中
		upgrade.RemoveNeededUpgradeDevice(register.term.Prop.IMSI)
		return
	}

	destVersion := upgrade.GetDestUpgradeVer(register.term.Prop.SoftwareVer)
	if destVersion == "" { // 不需要升级
		upgrade.RemoveNeededUpgradeDevice(register.term.Prop.IMSI)
		return
	}

	upgradeDevice := &upgrade.UpgradeDevice{
		IMEI:        register.term.Prop.IMEI,
		IMSI:        register.term.Prop.IMSI,
		SrcVersion:  register.term.Prop.SoftwareVer,
		DestVersion: destVersion,
		CmdSentTime: time.Now(),
		CmdSentCnt:  0,
	}
	upgrade.AddNeededUpgradeDevice(register.term.Prop.IMSI, upgradeDevice)
}

// LAN 口状态告警
// AddLog

package system

import (
	"fmt"
	"github.com/gogf/gf/encoding/gjson"
	"ccserver/app/vars"
	"time"
)

var lanWarningFormat = "LAN%d 口状态告警: %s"

// LanWarning LAN 口告警
type LanWarning struct {
	base
	lanWarningData
	term *vars.Terminal
	data []byte
	ts   int64 // 当前时间戳
}

type lanWarningData struct {
	lan,
	linkStatus,
	level int64
}

// NewLanWarning NewLanWarning
func NewLanWarning(j *gjson.Json, term *vars.Terminal, d []byte) *LanWarning {
	t := &LanWarning{}
	t.term = term
	t.data = d
	t.ts = time.Now().Unix()
	t.analyticalData(j)
	return t
}

// analyticalData 解析值数据
func (t *LanWarning) analyticalData(j *gjson.Json) {
	t.operate = j.GetInt("operate")
	t.lan = j.GetInt64("lan")
	t.linkStatus = j.GetInt64("link_status")
	t.level = j.GetInt64("level")
}

// DoReply DoReply
func (t *LanWarning) DoReply() {
	defer t.writeLog()

	if t.lan == 1 { // 检测 LAN1 口的状态变更，以及通知
		t.UpdateLan1OfflineTime(t.term.Prop.IMSI, t.linkStatus, t.ts)
	}

	if t.lan == 2 { // 检测 LAN2 口的状态变更，以及通知
		t.UpdateLan2OfflineTime(t.term.Prop.IMSI, t.linkStatus, t.ts)
	}
}

func (t *LanWarning) writeLog() error {
	lanStatus := "断开"
	if t.linkStatus == 1 {
		lanStatus = "连接"
	}
	msg := vars.MessageLog{
		OperateId: t.operate,
		IMSI:      t.term.Prop.IMSI,
		Content:   fmt.Sprintf(lanWarningFormat, t.lan, lanStatus),
		ErrCode:   0,
		Status:    StatusSuccessfully,
	}
	_, err := t.AddMessageLog(msg, t.data)
	return err
}

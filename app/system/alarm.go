package system

import (
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	"ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
)

// SelectAlarm 处理告警列表
func SelectAlarm(term *vars.Terminal, alarmList []protocol.AlarmInfo) {
	topic := fmt.Sprintf("%s/alarm/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		term.Prop.IMSI,
	)

	var result []protocol.ResultAlarm

	// 处理每个告警信息
	for _, alarm := range alarmList {
		// 查询告警配置
		var alarmConfig model.AlarmConfig
		if err := db.GetPixmoving().Where("code = ?", alarm.Code).First(&alarmConfig).Error; err != nil {
			pix_log.Error("查询告警配置失败:", err.Error())
			continue
		}

		// 构建结果
		result = append(result, protocol.ResultAlarm{
			Id:           alarmConfig.Id,
			Code:         alarm.Code,
			Level:        alarm.Level,
			Type:         alarm.Type,
			Msg:          alarm.Msg,
			Ts:           alarm.Ts,
			FirstDomain:  alarmConfig.FirstDomain,
			SecondDomain: alarmConfig.SecondDomain,
		})
	}

	// 检查 MQTT 连接
	if !mqtt.SharedClient.IsConnected() {
		pix_log.Warning("mqtt not connected, ignore publish")
		return
	}

	// 发送数据
	returnData, err := json.Marshal(result)
	if err != nil {
		pix_log.Error("告警数据序列化失败:", err.Error())
		return
	}

	token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
	token.Wait()
	if token.Error() != nil {
		pix_log.Error("mqtt publish failed:%s", token.Error())
	}
}

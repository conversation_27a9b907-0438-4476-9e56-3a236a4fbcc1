package system

import (
	"ccserver/app/library/v"
	db2 "ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"fmt"
	"time"
)

// CheckOrder 确认终端已收到，修改订单状态为制作中
func CheckOrder(billNo string, taskId int) {
	db2.GetCoffee().Table("order").Where("order_no = ? and `order` = ?", billNo, taskId).Updates(map[string]interface{}{
		"make_status":  0,
		"updated_time": time.Now().Unix(),
	})
	v.Redis().Do("HDEL", vars.RedisCoffeeOrderCheck, fmt.Sprintf("%s%d", billNo, taskId))
}

// SyncOrder 同步订单状态
func SyncOrder(orders []protocol.OrderInfo) {
	for _, order := range orders {

		// var myOrder protocol.OrderInfo
		// err := json.Unmarshal(jsonData, &myOrder)
		// if err != nil {
		// 	pix_log.Error("json解析失败，", err.Error())
		// } else {
		// 	pix_log.Info("shipPort：", myOrder.ShipPort)
		// }
		column := "make_status"
		value := 2
		if order.OrderStatus == 1 {
			value = 0
		} else if order.OrderStatus == 2 {
			value = 1
		} else if order.OrderStatus == 3 {
			column = "status"
			value = 1
		} else if order.OrderStatus == 4 {
			// todo 制作失败 需要重新下发或者取消订单
		}
		db2.GetCoffee().Table("order").Where("order_no = ? and `order` = ?", order.BillNo, order.TaskId).Updates(map[string]interface{}{
			column:         value,
			"updated_time": time.Now().Unix(),
			"ship_port":    order.ShipPort,
		})
	}
}

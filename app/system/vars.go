package system

import "ccserver/app/vars/protocol"

// 位置信息
type dataAddr struct {
	Position  string  `json:"position"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// IP 端口
type dataIP struct {
	IP   string `json:"ip"`
	Port int64  `json:"port"`
}

// RS
type dataRs struct {
	Baud    int64   `json:"baud"`
	DataBit int64   `json:"data_bit"`
	Parity  int64   `json:"parity"`
	StopBit float64 `params:"stop_bit" json:"stop_bit"`
}

// 时区
type dataTZ struct {
	Tz        string `json:"tz"`
	NtpServer string `json:"ntp_server"`
	Interval  int64  `json:"interval"`
}

type lan struct {
	Dhcp    int64  `json:"dhcp"`
	LanIp   string `json:"lan_ip"`
	Netmask string `json:"netmask"`
	StartIp string `json:"start_ip"`
	EndIp   string `json:"end_ip"`
}

type account struct {
	Name     string `json:"name"`
	Password string `json:"password"`
}

type heartbeat struct {
	Time int64 `json:"time"`
}

var (
	PbNameMap = map[int]string{
		protocol.CbAddrSet:       "更新位置信息",
		protocol.CbMonitoringSet: "更新监控中心IP",
		protocol.CbUnmpSet:       "更新管理平台IP",
		protocol.CbRs232Set:      "更新RS232信息",
		protocol.CbRs485Set:      "更新RS485信息",
		protocol.CbTzSet:         "更新时区信息",
		protocol.CbAccountSet:    "更新账户信息",
		protocol.CbLanSet:        "更新DHCP信息",
		protocol.CbHeartbeatSet:  "更新心跳间隔信息",
		protocol.CbAddrGet:       "获取位置信息",
		protocol.CbMonitoringGet: "获取监控中心IP",
		protocol.CbUnmpGet:       "获取管理平台IP",
		protocol.CbRs232Get:      "获取RS232信息",
		protocol.CbRs485Get:      "获取RS485信息",
		protocol.CbTzGet:         "获取时区信息",
		protocol.CbAccountGet:    "获取账户信息",
		protocol.CbLanGet:        "获取DHCP信息",
		protocol.CbHeartbeatGet:  "获取心跳间隔信息",
	}
)

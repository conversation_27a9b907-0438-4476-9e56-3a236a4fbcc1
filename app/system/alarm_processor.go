package system

import (
	"ccserver/app/library/common"
	"ccserver/app/model"
	"ccserver/app/module/analysis"
	"ccserver/app/module/mqtt"
	"ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// AlarmProcessor 告警处理器
type AlarmProcessor struct {
	heartbeat *Heartbeat    // 心跳数据
	device    *model.Device // 设备信息
	user      *model.User   // 用户信息
}

// NewAlarmProcessor 创建告警处理器
func NewAlarmProcessor(heartbeat *Heartbeat, device *model.Device, user *model.User) *AlarmProcessor {
	return &AlarmProcessor{
		heartbeat: heartbeat,
		device:    device,
		user:      user,
	}
}

// ProcessAlarms 处理所有告警
func (p *AlarmProcessor) ProcessAlarms() {
	// 处理心跳中的告警信息
	p.processHeartbeatAlarm()

	// 处理环境告警
	p.processEnvironmentAlarms()
}

// getAlarmInfo 获取告警信息
func (p *AlarmProcessor) getAlarmInfo() (code, message string) {
	// 检查故障告警
	if len(p.heartbeat.heartbeatDevice.Warning) > 0 && p.heartbeat.heartbeatDevice.Warning[0] != 0 {
		return "0X0001", "设备故障，故障码为：" + fmt.Sprintf("%d", p.heartbeat.heartbeatDevice.Warning[0])
	}

	// 检查电量告警
	if p.heartbeat.heartbeatDevice.PowerLevel < 5 {
		return "0X0004", "电量低于5%"
	}

	return "", ""
}

// processHeartbeatAlarm 处理心跳中的告警信息
func (p *AlarmProcessor) processHeartbeatAlarm() {
	// 1. 检查告警列表是否为空
	if p.heartbeat.heartbeatDevice.AlarmList == nil || len(p.heartbeat.heartbeatDevice.AlarmList) <= 0 {
		return
	}

	// 2. 遍历告警列表
	for _, alarm := range p.heartbeat.heartbeatDevice.AlarmList {
		if alarm.Code == 0 {
			continue // 跳过无效告警
		}

		// 3. 查询该设备该告警码的最新记录
		var lastAlarm model.AlarmDevice
		err := db.GetPixmoving().Where("imsi = ? AND code = ?",
			p.heartbeat.term.Prop.IMSI,
			alarm.Code).
			Order("ts DESC").
			First(&lastAlarm).Error

		// 4. 判断是否需要插入新记录
		shouldInsert := false
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 4.1 如果没有历史记录，则需要插入
			shouldInsert = true
		} else {
			// 4.2 如果有历史记录，只有当告警状态发生变化时才插入
			// lastAlarm.Type != alarm.Type 确保状态发生了变化
			shouldInsert = lastAlarm.Type != alarm.Type
		}

		// 5. 如果需要插入，则保存新记录并发送通知
		if shouldInsert {
			// 5.1 保存告警记录
			newAlarm := model.AlarmDevice{
				Imsi:  p.heartbeat.term.Prop.IMSI,
				Code:  alarm.Code,
				Level: alarm.Level,
				Type:  alarm.Type,
				Msg:   alarm.Msg,
				Ts:    alarm.Ts,
			}
			if err := db.GetPixmoving().Create(&newAlarm).Error; err != nil {
				pix_log.Error("创建告警记录失败:", err.Error())
				continue
			}

			// 5.2 发送分析消息
			if err := analysis.SharedPublisher.PublishDeviceMessage(
				p.heartbeat.term.Prop.IMSI,
				"alarm"); err != nil {
				pix_log.Error("发送告警分析消息失败:", err.Error())
			}

			// 5.3 处理告警配置和MQTT发布
			p.processAlarmConfig([]protocol.AlarmInfo{alarm})
		}
	}
}

// processEnvironmentAlarms 处理环境告警
func (p *AlarmProcessor) processEnvironmentAlarms() {
	// 处理CO2告警
	if p.heartbeat.heartbeatDevice.CO2 <= 270 && p.heartbeat.heartbeatDevice.CO2 > 0 {
		p.handleCO2Alarm()
	}

	// 处理烟雾告警
	if p.heartbeat.heartbeatDevice.Smoke > 1 {
		p.handleSmokeAlarm()
	}
}

// handleCO2Alarm 处理CO2告警
func (p *AlarmProcessor) handleCO2Alarm() {
	notification := model.Notification{
		DeviceId:    int(p.heartbeat.term.Prop.ID),
		Type:        105,
		Content:     fmt.Sprintf("您的车辆%s当前车内二氧化碳浓度超过1000ppm", p.heartbeat.term.Prop.IMSI),
		Lng:         p.heartbeat.heartbeatDevice.Longitude,
		Lat:         p.heartbeat.heartbeatDevice.Latitude,
		MessageTime: int(p.heartbeat.ts),
		CreatedTime: int(p.heartbeat.ts),
		CompanyId:   p.device.CompanyId,
	}
	db.GetPixmoving().Create(&notification)

	// 计算CO2数值
	num := float64(0)
	if p.heartbeat.heartbeatDevice.CO2 > 258 && p.heartbeat.heartbeatDevice.CO2 <= 270 {
		num = float64(290-p.heartbeat.heartbeatDevice.CO2) / 0.02
	} else if p.heartbeat.heartbeatDevice.CO2 > 236 && p.heartbeat.heartbeatDevice.CO2 <= 258 {
		num = 276.53 - float64(p.heartbeat.heartbeatDevice.CO2)/0.0116
	} else if p.heartbeat.heartbeatDevice.CO2 > 224 && p.heartbeat.heartbeatDevice.CO2 <= 236 {
		num = 264 - float64(p.heartbeat.heartbeatDevice.CO2)/0.008
	} else if p.heartbeat.heartbeatDevice.CO2 < 224 {
		num = 5000
	}

	lastNum := strconv.FormatFloat(num, 'f', 2, 64)

	if p.user.Check == 1 && time.Now().Unix()-p.user.CoLastSend >= 300 {
		err := common.SendSms(p.user.Tel, p.device.IMSI, "二氧化碳", lastNum, time.Now().Format("2006-01-02 15:04:05"))
		if err != nil {
			pix_log.Error("发送短信失败：" + err.Error())
			return
		}
		db.GetPixmoving().Model(&model.User{}).Where("id", p.user.Id).Update("co_last_send", time.Now().Unix())
	}
}

// handleSmokeAlarm 处理烟雾告警
func (p *AlarmProcessor) handleSmokeAlarm() {
	// 创建通知
	notification := model.Notification{
		DeviceId:    int(p.heartbeat.term.Prop.ID),
		Type:        106,
		Content:     fmt.Sprintf("您的车辆%s当前车内烟雾浓度过高", p.heartbeat.term.Prop.IMSI),
		Lng:         p.heartbeat.heartbeatDevice.Longitude,
		Lat:         p.heartbeat.heartbeatDevice.Latitude,
		MessageTime: int(p.heartbeat.ts),
		CreatedTime: int(p.heartbeat.ts),
		CompanyId:   p.device.CompanyId,
	}
	db.GetPixmoving().Create(&notification)

	// 处理短信通知
	if p.user.Check == 1 && time.Now().Unix()-p.user.SmokeLastSend >= 300 {
		err := common.SendSms(p.user.Tel, p.device.IMSI, "烟雾", "超标范围", time.Now().Format("2006-01-02 15:04:05"))
		if err != nil {
			pix_log.Error("发送短信失败：" + err.Error())
			return
		}
		db.GetPixmoving().Model(&model.User{}).Where("id", p.user.Id).Update("smoke_last_send", time.Now().Unix())
	}
}

// processAlarmConfig 处理告警配置和MQTT发布
func (p *AlarmProcessor) processAlarmConfig(alarms []protocol.AlarmInfo) {
	topic := fmt.Sprintf("%s/alarm/device/%s",
		vars.Config.Server.Mqtt.TopicPrefix,
		p.heartbeat.term.Prop.IMSI)

	var result []protocol.ResultAlarm
	for _, alarm := range alarms {
		var alarmConfig model.AlarmConfig
		if err := db.GetPixmoving().Where("code = ?", alarm.Code).First(&alarmConfig).Error; err != nil {
			pix_log.Error("查询告警配置失败:", err.Error())
			continue
		}

		result = append(result, protocol.ResultAlarm{
			Id:           alarmConfig.Id,
			Code:         alarm.Code,
			Level:        alarm.Level,
			Type:         alarm.Type,
			Msg:          alarm.Msg,
			Ts:           alarm.Ts,
			FirstDomain:  alarmConfig.FirstDomain,
			SecondDomain: alarmConfig.SecondDomain,
		})
	}

	if mqtt.SharedClient.IsConnected() {
		if returnData, err := json.Marshal(result); err == nil {
			mqtt.SharedClient.Publish(topic, 0, false, returnData)
		}
	}
}

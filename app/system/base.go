package system

import (
	lTime "ccserver/app/library/time"
	"ccserver/app/module/client"
	cacheservice "ccserver/app/service/cache"
	dbservice "ccserver/app/service/db"
	"ccserver/app/vars"
	"database/sql"
	"errors"
)

const (
	StatusProcessing   int64 = iota // 0.处理中
	StatusFailed                    // 1.处理失败
	StatusSuccessfully              // 2.处理成功
)

type base struct {
	operate int
}

// SendToAllReply 获取位置消息下发给指定终端
func (t *base) SendToAllReply(deviceIdList []string, data []byte) error {
	if len(deviceIdList) == 0 {
		return errors.New("终端列表为空")
	}
	var send bool
	for _, deviceId := range deviceIdList {
		if client.All().Contains(deviceId) {
			client.Get(deviceId).(*vars.Terminal).Prop.Message <- data
			send = true
		}

	}
	if !send {
		return errors.New("消息发送失败")
	}
	return nil
}

// SendToReply 设置位置消息下发给指定终端
func (t *base) SendToReply(deviceId string, data []byte) error {
	if !client.All().Contains(deviceId) {
		return errors.New("终端不在线")
	}
	client.Get(deviceId).(*vars.Terminal).Prop.Message <- data
	return nil
}

// UpdateLan1OfflineTime 处理 LAN1 的状态变更
// 	status: 1 (连接正常), 0 (连接已断开)
func (t *base) UpdateLan1OfflineTime(deviceId string, status, ts int64) {
	if status == 1 { // 连接正常
		cacheservice.SetRedisLan1OfflineTs(deviceId, -1)
		return
	}
	lastOfflineTs := lTime.NewOffline().SetDeviceId(deviceId).Lan1OfflineTime()
	if lastOfflineTs == 0 || lastOfflineTs == -1 {
		cacheservice.SetRedisLan1OfflineTs(deviceId, ts)
	}
}

// UpdateLan2OfflineTime 处理 LAN2 的状态变更
// 	status: 1 (连接正常), 0 (连接已断开)
func (t *base) UpdateLan2OfflineTime(deviceId string, status, ts int64) {
	if status == 1 { // 连接正常
		cacheservice.SetRedisLan2OfflineTs(deviceId, -1)
		return
	}
	lastOfflineTs := lTime.NewOffline().SetDeviceId(deviceId).Lan2OfflineTime()
	if lastOfflineTs == 0 || lastOfflineTs == -1 {
		cacheservice.SetRedisLan2OfflineTs(deviceId, ts)
	}
}

// AddMessageLog 添加终端消息记录
func (t *base) AddMessageLog(msg vars.MessageLog, operate []byte) (result sql.Result, err error) {
	return dbservice.AddMessageLog(msg, operate)
}

package vars

import (
	"github.com/gogf/gf/net/gtcp"
	"sync"
	"time"
)

const DISCONNECT_REASON_DUPLICATE = "duplicate"
const DISCONNECT_REASON_EOF = "终端主动断开"
const DISCONNECT_REASON_DETECT = "终端已断开连接(服务器判断)"

// Terminal 终端
type Terminal struct {
	Conn        *gtcp.Conn // 底层 Conn
	Prop        Property   // 端属性
	IsConnected bool       // TCP是否连接
	Mutex       sync.Mutex // 有些操作需要同步
	Parser      ClientProtocol
}

// Property 终端属性
type Property struct {
	ID               int64         // 终端 ID
	UID              int64         // 终端归属
	IMEI             string        // IMEI
	IMSI             string        // IMSI
	IP               string        // IP 地址
	RSRP             int64         // rsrp
	SINR             int64         // sinr
	SoftwareVer      string        // 终端软件版本号
	Authorized       bool          // 是否已授权
	Quit             chan bool     // 退出
	QuitReason       string        // 退出理由
	Message          chan []byte   // 消息
	Msg              string        // 明文消息，方便调试
	ConnectAt        time.Time     // 上线时间
	UpdatedAt        time.Time     // 最后更新时间
	KeepaliveTimeout time.Duration // 心跳超时间隔
	IsMaintain       int           // 维护状态 1.正在维护
	RSSI             int64
	RSRQ             int64
	WanRx            int64 // 数据接收流量，单位字节
	WanTx            int64 // 数据发送流量，单位字节
	UpgradeState     string
	UpgradeProgress  string
}

type ClientProtocolType int

const (
	CLIENT_PROTOLOL_TYPE_UNKNOWN ClientProtocolType = 0
	CLIENT_PROTOLOL_TYPE_JT808   ClientProtocolType = 1
	CLIENT_PROTOLOL_TYPE_ZCL     ClientProtocolType = 2
)

type ClientProtocol interface {
	HandleRecv(data []byte, term *Terminal)
	// ClientId() string
}

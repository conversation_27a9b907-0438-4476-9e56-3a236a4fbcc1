package protocol

import (
	"ccserver/app/model"
	"math"
)

type HeartbeatUesrDeviceMqtt struct {
	HeartbeatBase // 直接继承心跳基础数据结构

	// 经纬度，与HeartbeatBase字段不一样，存入redis的是lng和lat，这里要把redis的对应字段转成这个字段
	Latitude  float64 `json:"latitude"`  // 纬度
	Longitude float64 `json:"longitude"` // 经度

	// GPS相关字段
	LastValidGpsTs int64 `json:"last_valid_gps_ts"` // 上一次有效GPS时间戳

	// 设备基本信息
	ID          int64  `json:"id"`           // 设备ID
	IMEI        string `json:"imei"`         // 设备IMEI号
	IMSI        string `json:"imsi"`         // 设备IMSI号
	IP          string `json:"ip"`           // 设备IP地址
	Name        string `json:"name"`         // 设备名称
	Vender      string `json:"vender"`       // 设备厂商
	Model       string `json:"model"`        // 设备型号
	ManageIp    string `json:"manage_ip"`    // 管理IP地址
	SoftwareVer string `json:"software_ver"` // 软件版本号
	HardwareVer string `json:"hardware_ver"` // 硬件版本号

	// 用户相关
	UserId int64 `json:"user_id"` // 所属用户ID
	Online int   `json:"online"`  // 在线状态(0:离线 1:在线)

	// 在线时间统计
	OnlineBigint int64  `json:"online_bigint"` // 本次在线时长(秒)
	TimeSpending int64  `json:"time_spending"` // 本周在线总时长(秒)
	OnlineTimes  int64  `json:"online_times"`  // 上线次数统计
	OfflineTimes int64  `json:"offline_times"` // 离线次数统计
	OnlineTime   string `json:"online_time"`   // 最近上线时间
	OfflineTime  string `json:"offline_time"`  // 最近离线时间

	// 时间戳
	CreatedTime string `json:"created_time"` // 设备创建时间
	UpdatedTime string `json:"updated_time"` // 设备信息更新时间
	ExpireTime  int64  `json:"expire_time"`  // 设备过期时间

	// 广告相关
	Advert     string `json:"advert"`      // 广告内容
	AdvertType int    `json:"advert_type"` // 广告类型

	// 地图和任务相关
	MapId     int             `json:"map_id"`     // 地图ID
	Task      model.Task      `json:"task"`       // 当前任务信息
	TaskChild model.TaskChild `json:"task_child"` // 当前子任务信息
	Type      int             `json:"type"`       // 设备类型

	// 时序
	Ts int64 `json:"ts"`
}

// NewHeartbeatUesrDeviceMqtt 创建新的MQTT设备响应数据
func NewHeartbeatUesrDeviceMqtt(device model.Device, redisData HeartbeatRedis,
	currentOnlineTotalTime, currentWeekOnlineTotalTime int64,
	task model.Task, taskChild model.TaskChild) *HeartbeatUesrDeviceMqtt {

	return &HeartbeatUesrDeviceMqtt{
		HeartbeatBase: redisData.HeartbeatBase, // 直接使用 Redis 数据中的 HeartbeatBase

		ID:           int64(device.Id),
		IMEI:         device.IMEI,
		IMSI:         device.IMSI,
		IP:           device.Ip,
		Name:         device.Name,
		Vender:       device.Vender,
		Model:        device.Model,
		ManageIp:     device.ManageIp,
		SoftwareVer:  device.SoftwareVer,
		HardwareVer:  device.HardwareVer,
		UserId:       device.Uid,
		Online:       device.Online,
		OnlineBigint: currentOnlineTotalTime,
		TimeSpending: currentWeekOnlineTotalTime,
		OnlineTimes:  int64(device.OnlineTimes),
		OfflineTimes: int64(device.OfflineTimes),
		OnlineTime:   device.OnlineTime,
		OfflineTime:  device.OfflineTime,
		CreatedTime:  device.CreatedTime,
		UpdatedTime:  device.UpdatedTime,
		ExpireTime:   device.ExpireTime,
		Advert:       device.Advert,
		AdvertType:   device.AdvertType,
		MapId:        device.MapId,
		Task:         task,
		TaskChild:    taskChild,
		Type:         device.Type,
		Longitude:    CeilWithPrecision(redisData.HeartbeatBase.Longitude, 6),
		Latitude:     CeilWithPrecision(redisData.HeartbeatBase.Latitude, 6),
		LastValidGpsTs: redisData.LastValidGpsTs, // 添加上一次有效GPS时间戳
		Ts:           redisData.Ts,
	}
}

func CeilWithPrecision(val float64, precision int) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Ceil(val*ratio) / ratio
}

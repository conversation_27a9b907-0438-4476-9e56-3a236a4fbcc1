package protocol

type HeartbeatMqtt struct {
	HeartbeatBase // 嵌入基础结构

	// MQTT特有字段
	Time int64 `json:"time" copier:"-"`

	// MQTT 特殊字段映射（与基础字段名称不同的情况）
	UpgradeState    string `json:"upgrade_state" copier:"UpgradeState"`
	UpgradeProgress string `json:"upgrade_progress" copier:"UpgradeProgress"`
	VehicleType     int8   `json:"v_type" copier:"VType"`
	HeadMode        int8   `json:"head_mode" copier:"HeadMode"`
	HeadLight       int    `json:"head_light" copier:"HeadLight"`

	// bool 转 int 的字段，注意 copier 标签中的字段名首字母大写
	Located    int `json:"located" copier:"Located"`
	IPCToCha   int `json:"ipcToCha" copier:"IPCToChannel"`
	ADLocated  int `json:"adLocated" copier:"ADLocated"`
	GPSLocated int `json:"gpsLocated" copier:"GPSLocated"`
}

func NewHeartbeatMqtt(ts int64) *HeartbeatMqtt {
	return &HeartbeatMqtt{
		Time: ts,
	}
}

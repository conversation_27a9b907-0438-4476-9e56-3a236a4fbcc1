package protocol

const (
	// Register 注册协议
	Register = 0x0001
	// CbRegister 注册协议应答
	CbRegister = 0x0001

	// Heartbeat 心跳协议
	Heartbeat = 0x0002
	// CbHeartbeat 心跳协议 回调
	CbHeartbeat = 0x0002

	// QueryHeartbeat 主动查询心跳
	QueryHeartbeat   = 0x001A
	QueryHeartbeatCb = 0x801A

	// AddrSet 设置地址
	AddrSet = 0x0003
	// Unlock 开中控锁
	Unlock = 0x0004

	// Lock 关中控锁
	Lock = 0x0005

	// FindCar 寻车
	FindCar = 0x0006

	// StartCar 启动车辆
	StartCar = 0x0007

	// StopCar 熄火车辆
	StopCar = 0x0008

	// Rs232Set 设置 Rs232
	Rs232Set = 0x0009
	// Rs232Get 获取 Rs232
	Rs232Get = 0x000A

	// Rs485Set 设置 Rs485
	Rs485Set = 0x000B
	// Rs485Get 获取 Rs485
	Rs485Get = 0x000C

	// TzSet 设置时区
	TzSet = 0x000D
	// TzGet 获取时区
	TzGet = 0x000E

	// LanSet 设置LAN口地址、开启或关闭DHCP服务、设置DHCP服务器的起止IP地址池
	LanSet = 0x0014
	// LanGet 获取设LAN口地址、开启或关闭DHCP服务、设置DHCP服务器的起止IP地址池
	LanGet = 0x0015

	// AccountSet 设置设备web登陆用户名、密码
	AccountSet = 0x0016
	// AccountGet 获取设备web登陆用户名、密码
	AccountGet = 0x0017

	// HeartbeatSet 心跳间隔设置
	HeartbeatSet = 0x0018
	// HeartbeatGet 心跳间隔获取
	HeartbeatGet = 0x0019

	// Upgrade 更新固件
	Upgrade = 0x000F
	// CbUpgrade 更新固件 回调
	CbUpgrade = 0x800F

	// Reboot 重启路由
	Reboot = 0x0010
	// LanChanged LAN 口状态变化
	LanChanged = 0x0011

	// TerminalLogGet 采集终端日志
	TerminalLogGet = 0x0012
	// Reset 恢复出厂设置
	Reset = 0x0013

	// CbAddrSet 设置地址
	CbAddrSet = 0x8003
	// CbAddrGet 获取地址
	CbAddrGet = 0x8004

	// CbMonitoringSet 设置数据监控中心信息
	CbMonitoringSet = 0x8005
	// CbMonitoringGet 获取数据监控中心信息
	CbMonitoringGet = 0x8006

	// CbUnmpSet 设置
	CbUnmpSet = 0x8007
	// CbUnmpGet 获取
	CbUnmpGet = 0x8008

	// CbRs232Set 设置 Rs232
	CbRs232Set = 0x8009
	// CbRs232Get 获取 Rs232
	CbRs232Get = 0x800A

	// CbRs485Set 设置 Rs485
	CbRs485Set = 0x800B
	// CbRs485Get 获取 Rs485
	CbRs485Get = 0x800C

	// CbTzSet 设置时区
	CbTzSet = 0x800D
	// CbTzGet 获取时区
	CbTzGet = 0x800E

	// LanSet 回调 设置LAN口地址、开启或关闭DHCP服务、设置DHCP服务器的起止IP地址池
	CbLanSet = 0x8014
	// LanGet 回调 获取设LAN口地址、开启或关闭DHCP服务、设置DHCP服务器的起止IP地址池
	CbLanGet = 0x8015

	// CbAccountSet 回调 设置设备web登陆用户名、密码
	CbAccountSet = 0x8016
	// CbAccountGet 回调 获取设备web登陆用户名、密码
	CbAccountGet = 0x8017

	// CbHeartbeatSet 心跳间隔设置
	CbHeartbeatSet = 0x8018
	// CbHeartbeatGet 心跳间隔获取
	CbHeartbeatGet = 0x8019

	// CbTerminalLogGet 采集终端日志 回调
	CbTerminalLogGet = 0x8012

	// CbReset 恢复出厂设置 回调
	CbReset = 0x8013

	// 移动到指定位置
	GoPoint          = 0x0032
	GoHome           = 0x0031
	StartAutoDriving = 0x0023
	StopAutoDriving  = 0x0025

	// FindFirmware 查询是否有最新固件
	FindFirmware = 0x9000

	// FindVCUFirmware 查询是否有最新VCU固件
	FindVCUFirmware = 0xA000

	// Task 下发清扫路径任务
	Task = 0x0018

	// Download OTA软件包下载开始
	Download = 0x9004

	// VCUDownload VCU软件包下载开始
	VCUDownload = 0xA004

	// Install OTA软件包安装开始
	Install = 0x9005

	// VCUInstall VCU软件包安装开始
	VCUInstall = 0xA005

	// QueryOTAStatus 查询ota升级状态
	QueryOTAStatus = 0x9002

	// VCUQueryOTAStatus 查询vcu升级状态
	VCUQueryOTAStatus = 0xA002

	// QueryTask 查询清扫任务
	QueryTask = 0x0601

	// StartAuto 启动执行自动驾驶任务
	StartAuto = 0x0023

	// CancelAuto 取消自动驾驶任务
	CancelAuto = 0x0025

	// StopAuto 停止自动驾驶任务
	StopAuto = 0x0026

	// EmergencyStop 紧急停车
	EmergencyStop = 0x0034

	// PauseAuto 暂停自动驾驶任务
	PauseAuto = 0x0024

	// MakeOrder 制作饮品
	MakeOrder = 0x0610

	// CheckOrder 订单状态同步
	CheckOrder = 0x0612

	// SetSpeed 设置最高限速
	SetSpeed = 0x0040

	// SelectSpeed 查询最高限速
	SelectSpeed = 0x0041

	// SetMap 设置自驾地图
	SetMap = 0x0042

	// SelectMap 查询自驾地图
	SelectMap = 0x0043

	// DeviceSync 车端地图同步到云端
	DeviceSync = 0x0044

	// CloudSync 云端地图同步到车端
	CloudSync = 0x0046

	// DeviceSyncInfo 车端地图同步到云端进度
	DeviceSyncInfo = 0x0045

	// CloudSyncInfo 云端地图同步到车端进度
	CloudSyncInfo = 0x0047

	// SelectAlarm 查询实时故障
	SelectAlarm = 0x0039
)

var (
	// MapSet 设置信息组
	// MapSet = map[string]int64{
	//	"addr":      AddrSet,
	//	"unmp":      UnmpSet,
	//	"rs232":     Rs232Set,
	//	"rs485":     Rs485Set,
	//	"tz":        TzSet,
	//	"lan":       LanSet,
	//	"account":   AccountSet,
	//	"heartbeat": HeartbeatSet,
	// }
	//
	// // MapGet 获取信息组
	// MapGet = map[string]int64{
	//	"unmp":       UnmpGet,
	//	"rs232":      Rs232Get,
	//	"rs485":      Rs485Get,
	//	"tz":         TzGet,
	//	"lan":        LanGet,
	//	"account":    AccountGet,
	//	"heartbeat":  HeartbeatGet,
	// }

	// EventData 消息类型
	EventData = map[uint8]string{
		1:   "ACC变化",
		2:   "SOS",
		3:   "终端设备断电",
		4:   "小电池出现欠压",
		5:   "超速",
		6:   "越界（出电子围栏区域）",
		7:   "GPS长时间不定位",
		10:  "充电状态变化",
		12:  "门开关变化",
		16:  "后备箱开关变化",
		18:  "中控锁开关变化",
		19:  "灯开关变化",
		25:  "挡位变化",
		26:  "安全带松系变化",
		27:  "左转向灯变化",
		28:  "右转向灯变化",
		29:  "遥控器按键事件",
		30:  "进入区域",
		31:  "发生震动",
		33:  "使用密码盘超级密码进行开锁",
		34:  "使用密码盘普通用户密码进行开锁",
		35:  "使用密码盘进行关锁",
		100: "驾驶模式切换到自动驾驶模式",
		101: "驾驶模式切换到遥控模块",
		102: "启动自动驾驶",
		103: "自动驾驶结束",
	}

	// EmergencyMessage 紧急信息
	EmergencyMessage = []uint8{2, 3, 4, 5, 6, 7, 31}

	// CommonMessage 一般消息
	// CommonMessage = []uint8{1, 10, 12, 16, 18, 19, 25, 26, 27, 28, 29, 30, 33, 34, 35, 100, 101, 102, 103}
)

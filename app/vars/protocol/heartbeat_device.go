package protocol

import (
	"encoding/json"
)

type HeartbeatDevice struct {
	HeartbeatBase // 嵌入基础结构

	// 基本字段
	Cmd   int    `json:"cmd"`
	Msgid int64  `json:"msgid"`
	IMEI  string `json:"imei"`
	IMSI  string `json:"imsi"`
	Model string `json:"model"`
	UID   string `json:"uid"` // 用户ID

	// 升级相关
	Body UpgradeBody `json:"body"` // 升级相关的body

	// IP相关
	IP   string `json:"ip"`
	Port int64  `json:"port"`

	// RS232/RS485相关
	Baud    int64   `json:"baud"`
	DataBit int64   `json:"data_bit"`
	Parity  int64   `json:"parity"`
	StopBit float64 `json:"stop_bit"`

	// 时区相关
	Tz        string `json:"tz"`
	NtpServer string `json:"ntp_server"`
	Interval  int64  `json:"interval"`

	// LAN相关
	Dhcp    int64  `json:"dhcp"`
	LanIp   string `json:"lan_ip"`
	Netmask string `json:"netmask"`
	StartIp string `json:"start_ip"`
	EndIp   string `json:"end_ip"`

	// 账户相关
	Name     string `json:"name"`
	Password string `json:"password"`

	// 心跳相关
	Time int64 `json:"time"`

	// 结果相关
	Results int64 `json:"results"`

	// 基本信息
	SoftwareVer string `json:"sw"`
	HardwareVer string `json:"hd"`
	Vendor      string `json:"vendor"`
	CI          string `json:"ci"`
	PCI         string `json:"pci"`
	ManageIP    string `json:"manage_ip"`

	// 其他状态
	Seat          [6]int        `json:"seat"`
	SweeperStatus []uint32      `json:"sweeper_status"`
	Orders        []OrderInfo   `json:"orders"`
	MapList       []interface{} `json:"mapList"`
	MapInfo       MapInfo       `json:"map"`
	Response      int           `json:"res"`
	BillNo        string        `json:"billNo"`

	// 响应相关
	Bn int `json:"bn"` // 业务编号
}

type UpgradeBody struct {
	DownloadMode int    `json:"dwnMode"`  // 下载模式
	InstallMode  int    `json:"instMode"` // 安装模式
	Message      string `json:"msg"`      // 消息
	Progress     int    `json:"progress"` // 进度
	Reboot       bool   `json:"reboot"`   // 是否重启
	Step         int    `json:"step"`     // 步骤
	Version      string `json:"ver"`      // 版本
}

// LanStatus LAN口状态
type LanStatus struct {
	Name       string `json:"name"`
	LinkStatus int64  `json:"link_status"`
}

// TaskInfo 任务信息
type TaskInfo struct {
	Step           int8          `json:"step"`           // 步骤
	RouteID        int           `json:"routeId"`        // 路线ID
	TaskID         int64         `json:"taskId"`         // 执行实例ID（字段名保持兼容，语义已变更）
	Progress       int8          `json:"progress"`       // 进度
	StartTime      int64         `json:"startTime"`      // 开始时间
	Name           string        `json:"name"`           // 名称
	Desp           string        `json:"desp"`           // 描述
	Point          string        `json:"point"`          // 当前位置点
	NextPoint      string        `json:"nextPoint"`      // 下一个位置点
	LeftTime       int           `json:"leftTime"`       // 剩余时间
	// ExecutionId 字段已完全移除，功能合并到TaskID
	TargetDistance int           `json:"targetDistance"` // 目标距离
	TaskDistance   int           `json:"taskDistance"`   // 任务距离
	CurrentStation SimpleStation `json:"currentStation"` // 当前站点
	TargetStation  SimpleStation `json:"targetStation"`  // 目标站点
	ActionList     []ActionInfo  `json:"actionList"`     // 动作列表
	RetailBot      interface{}   `json:"retailBot"`      // 零售机器人信息
	Other          string        `json:"other"`          // 其他信息
}

// SimpleStation 简化版站点信息
type SimpleStation struct {
	ID   int    `json:"id"`   // 站点ID
	Name string `json:"name"` // 站点名称
}

// ActionInfo 动作信息
type ActionInfo struct {
	Step        int    `json:"step"`        // 步骤
	Action      string `json:"action"`      // 动作
	PST         int    `json:"pST"`         // 计划开始时间
	PET         int    `json:"pET"`         // 计划结束时间
	ST          int    `json:"sT"`          // 实际开始时间
	ET          int    `json:"eT"`          // 实际结束时间
	Prog        int    `json:"prog"`        // 进度
	Type        int    `json:"type"`        // 动作类型
	Status      int    `json:"status"`      // 动作状态
	Description string `json:"description"` // 描述
}

// RetailBot 零售机器人信息
type RetailBot struct {
	BillNo       string   `json:"billNo"`       // 订单编号
	ProductID    int      `json:"productId"`    // 产品ID
	TaskID       int      `json:"taskId"`       // 任务ID
	DeviceStatus int      `json:"deviceStatus"` // 设备状态
	ActionList   []int    `json:"actionList"`   // 动作列表
	TotalTime    int      `json:"totalTime"`    // 总执行时间
	RTime        int      `json:"rTime"`        // 剩余时间
	ActionIdx    int      `json:"actionIdx"`    // 当前动作索引
	ActionTime   int      `json:"actionTime"`   // 动作执行时间
	ActionRTime  int      `json:"actionRTime"`  // 动作剩余时间
	ErrCode      []int    `json:"errCode"`      // 错误代码列表
	ErrMsg       []string `json:"errMsg"`       // 错误信息列表
	CupHolders   int      `json:"cupHolders"`   // 杯架状态
	Liquid       int      `json:"liquid"`       // 液体存量
	Solid        int      `json:"solid"`        // 固体存量
	LiquidCup    int      `json:"liquidCup"`    // 液体杯数量
	SolidCup     int      `json:"solidCup"`     // 固体杯数量
	Ports        int      `json:"ports"`        // 接口状态
	ArmPosX      int      `json:"armPosX"`      // 机械臂X轴位置
	OtherStatus1 int      `json:"otherStatus1"` // 其他状态1
	OtherStatus2 int      `json:"otherStatus2"` // 其他状态2
	CarP         int      `json:"carP"`         // 小车位置
	CarT         int      `json:"carT"`         // 小车温度
	CarStatus    int      `json:"carStatus"`    // 小车状态
	InvV         int      `json:"invV"`         // 逆变器电压
	InvF         int      `json:"invF"`         // 逆变器频率
	InvC         int      `json:"invC"`         // 逆变器电流
	InvT         int      `json:"invT"`         // 逆变器温度
	Time         int      `json:"time"`         // 当前时间
	RunTime      int      `json:"runTime"`      // 运行时间
	BootCnt      int      `json:"bootCnt"`      // 启动次数
}

// AlarmInfo 告警信息
type AlarmInfo struct {
	Code  int    `json:"code"`  // 告警代码
	Level uint8  `json:"level"` // 告警级别
	Type  uint8  `json:"type"`  // 告警类型
	Msg   string `json:"msg"`   // 告警消息
	Ts    int64  `json:"ts"`    // 时间戳
}

// OrderInfo 订单信息
type OrderInfo struct {
	BillNo      string `json:"billNo"`
	TaskId      int    `json:"taskId"`
	OrderStatus int    `json:"orderStatus"`
	ShipPort    int    `json:"shipPort"`
}

// MapInfo 地图信息
type MapInfo struct {
	Data map[string]interface{} `json:"data,omitempty"`
}

// MarshalJSON 自定义序列化方法
func (m MapInfo) MarshalJSON() ([]byte, error) {
	if m.Data == nil {
		return []byte("{}"), nil
	}
	return json.Marshal(m.Data)
}

// UnmarshalJSON 自定义反序列化方法
func (m *MapInfo) UnmarshalJSON(data []byte) error {
	if string(data) == "{}" || string(data) == "null" {
		m.Data = make(map[string]interface{})
		return nil
	}
	return json.Unmarshal(data, &m.Data)
}

// ToJSON 序列化为JSON
// func (d *HeartbeatRedis) ToJSON() (string, error) {
// 	data, err := json.Marshal(d)
// 	if err != nil {
// 		pix_log.Error("Redis数据序列化失败:", err.Error())
// 		return "", err
// 	}
// 	return string(data), nil
// }
//
// // ToJSON 序列化为JSON
// func (d *protocol.HeartbeatMqtt) ToJSON() (string, error) {
// 	data, err := json.Marshal(d)
// 	if err != nil {
// 		pix_log.Error("MQTT数据序列化失败:", err.Error())
// 		return "", err
// 	}
// 	return string(data), nil
// }

// ResultAlarm 告警结果信息
type ResultAlarm struct {
	Id           uint   `json:"id"`            // ID
	Code         int    `json:"code"`          // 告警代码
	Level        uint8  `json:"level"`         // 告警级别
	Type         uint8  `json:"type"`          // 告警类型
	Msg          string `json:"msg"`           // 告警消息
	Ts           int64  `json:"ts"`            // 时间戳
	FirstDomain  string `json:"first_domain"`  // 一级域
	SecondDomain string `json:"second_domain"` // 二级域
}

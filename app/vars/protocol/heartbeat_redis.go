package protocol

// type HeartbeatRedis struct {
// 	Ts int64 `json:"ts"`
//
// 	// 网络状态
// 	Lan             []LanStatus `json:"lan"`
// 	UpgradeState    string      `json:"state"`
// 	UpgradeProgress string      `json:"percent"`
//
// 	// 位置信息
// 	Latitude  float64 `json:"latitude"`
// 	Longitude float64 `json:"longitude"`
//
// 	// 状态信息
// 	Located       int8    `json:"located"`
// 	Status        int8    `json:"sta"`
// 	Mode          int8    `json:"mode"`
// 	ACC           int8    `json:"acc"`
// 	Gear          int8    `json:"gear"`
// 	Door          int8    `json:"door"`
// 	Light         int8    `json:"light"`
// 	Window        int8    `json:"win"`
// 	TotalMileage   int32   `json:"tm"`
// 	RemainMileage   int16   `json:"rm"`
// 	SteeringAngle float32 `json:"ste"`
// 	BrakeStatus   float32 `json:"brk"`
// 	HeadMode      int8    `json:"head_mode"`
// 	Signal        int8    `json:"sgn"`
// 	Speed         int16   `json:"spd"`
// 	SpeedLimit    int16   `json:"spdL"`
// 	PowerLevel    int8    `json:"pL"`
// 	PowerVoltage  int16   `json:"pV"`
// 	PowerCurrent  int16   `json:"pC"`
// 	PowerCharging int8    `json:"pCh"`
// 	BatteryStatus int16   `json:"bat"`
// 	Altitude      float32 `json:"alt"`
// 	Angle         float32 `json:"angle"`
// 	SatCount      int8    `json:"sat_cnt"`
// 	Error         string  `json:"err"`
// 	Event         int8    `json:"event"`
// 	HeadLight     int     `json:"headLight"`
//
// 	// 车辆状态
// 	VType           int8    `json:"vType"`
// 	VehicleStatus   int8    `json:"vSta"`
// 	IPCToChannel    bool    `json:"ipcToCha"`
// 	Throttle        float32 `json:"thr"`
// 	ADLocated       bool    `json:"adLocated"`
// 	GPSLocated      bool    `json:"gpsLocated"`
// 	GPSStrength     int8    `json:"gpsStrength"`
// 	Shutdown        int8    `json:"shutdown"`
// 	EmergencyStatus int8    `json:"emgSta"`
//
// 	// 环境状态
// 	AirCondition     int8  `json:"airCon"`
// 	InnerTemperature int8  `json:"inTemp"`
// 	OuterTemperature int8  `json:"outTemp"`
// 	Smoke            int32 `json:"smoke"`
// 	CO2              int32 `json:"co2"`
// 	Seatbelt         int8  `json:"seatbelt"`
//
// 	// 任务相关
// 	LatestTask  TaskInfo  `json:"latestTask"`
// 	RetailBot   RetailBot `json:"retailBot"`
// 	AlarmCount1 []int     `json:"alarmCnt1"`
// 	AlarmCount2 []int     `json:"alarmCnt2"`
// 	Alarm       AlarmInfo `json:"alarm"`
// 	RC          int       `json:"rc"`
//
// 	// 错误状态
// 	BotStatus []string `json:"botStatus,omitempty"`
// 	BotErr    []string `json:"botErr,omitempty"`
// 	CErr      []string `json:"cErr,omitempty"`
// 	AutoErr   []string `json:"autoErr,omitempty"`
//
// 	// 其他状态
// 	PosQuality float32 `json:"pos_q"`
// 	Warning    []int   `json:"warning"`
// }

// HeartbeatRedis Redis心跳数据
type HeartbeatRedis struct {
	HeartbeatBase // 嵌入基础结构

	// Redis特有字段
	Ts int64 `json:"ts"`

	// 上一次有效经纬度相关字段
	LastValidLatitude  float64 `json:"last_valid_lat"`  // 上一次有效纬度
	LastValidLongitude float64 `json:"last_valid_lng"`  // 上一次有效经度
	LastValidGpsTs     int64   `json:"last_valid_gps_ts"` // 上一次有效GPS时间戳
}

// NewHeartbeatRedis 创建新的 Redis 心跳数据
func NewHeartbeatRedis(ts int64) *HeartbeatRedis {
	return &HeartbeatRedis{
		Ts: ts,
	}
}

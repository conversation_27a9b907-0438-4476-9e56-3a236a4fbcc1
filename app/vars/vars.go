package vars

// UserLevel 用户等级类型
type UserLevel int

// 用户等级常量
const (
	SGLevel UserLevel = 1 // 省级管理员
	SZLevel UserLevel = 2 // 省级值班员
	XGLevel UserLevel = 3 // 县级管理员
	XZLevel UserLevel = 4 // 县级值班员
)

// License 授权码参数
type License struct {
	CpuID   string `json:"cpuid"`    // CPU ID
	HddID   string `json:"hddid"`    // 硬盘序列号
	Total   int    `json:"total"`    // 授权终端数量
	EndTime string `json:"end_time"` // 有效时间(yyyy-MM-dd
}

// MessageLog 消息日志结构体
type MessageLog struct {
	OperateId   int    `json:"operate_id"`   // 消息 ID
	OperationId int64  `json:"operation_id"` // 操作消息日志 ID
	IMSI        string `json:"imsi"`
	Content     string `json:"content"`
	ErrCode     int64  `json:"err_code"`
	Status      int64  `json:"status"`
}

// Lans LAN 口结构体
type Lans struct {
	Name   string `json:"name"`
	Status int64  `json:"status"`
}

// RedisHeart Redis心跳数据结构
type RedisHeart struct {
	LatestTask struct {
		RouteId int `json:"routeId"`
	} `json:"latestTask"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
}
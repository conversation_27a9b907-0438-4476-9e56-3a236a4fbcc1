package vars

import "time"

var (
	// TableDevice 终端表
	TableDevice = "device"

	// TableFirmware 固件表
	TableFirmware = "firmware"

	// TableIP 广播 IP 地址池表
	TableIP = "ip"

	// TableLoginLog 终端上下线日志表
	TableLoginLog = "log_login"

	// TableMessageLog 终端消息日志表
	TableMessageLog = "log_message"

	// TableOperationLog 用户操作日志表
	TableOperationLog = "log_operation"

	// TableTerminalLog 终端日志表
	TableTerminalLog = "log_terminal"

	// TableOption 配置选项表
	TableOption = "options"

	// TableRecordLan 终端LAN记录表
	//TableRecordLan = "record_lan"

	// TableRecordSignal 终端信号记录表
	TableRecordSignal = "record_signal"

	// TableReportLogin 终端登录周报表
	TableReportLogin = "report_login"

	// TableUpgradeRules 固件升级策略表
	TableUpgradeRules = "upgrade_rules"

	// TableUser 用户表
	TableUser = "user"

	TablePoint = "point"

	// TableHdMap 高清地图
	TableHdMap = "hd_map"
)

// TbDevice 终端表结构
type TbDevice struct {
	ID             int64   `json:"id"`
	IMEI           string  `json:"imei"`
	IMSI           string  `json:"imsi"`
	IP             string  `json:"ip"`
	Name           string  `json:"name"`
	Model          string  `json:"model"`
	SoftwareVer    string  `json:"software_ver"`
	HardwareVer    string  `json:"hardware_ver"`
	UID            int64   `json:"uid"`
	Vender         string  `json:"vender"`
	CI             string  `json:"ci"`
	PCI            string  `json:"pci"`
	ManageIp       string  `json:"manage_ip"`
	Position       string  `json:"position"`
	Longitude      string  `json:"longitude"`
	Latitude       string  `json:"latitude"`
	Status         int     `json:"status"`
	MaintainState  int     `json:"maintain_state"`
	Online         int     `json:"online"`
	Tz             string  `json:"tz"`
	NtpServer      string  `json:"ntp_server"`
	Interval       int64   `json:"interval"`
	MonitoringIP   string  `json:"monitoring_ip"`
	MonitoringPort int64   `json:"monitoring_port"`
	UnmpIP         string  `json:"unmp_ip"`
	UnmpPort       int64   `json:"unmp_port"`
	Baud232        int64   `json:"baud_232"`
	DataBit232     int64   `json:"data_bit_232"`
	Parity232      int64   `json:"parity_232"`
	StopBit232     float64 `json:"stop_bit_232"`
	Baud485        int64   `json:"baud_485"`
	DataBit485     int64   `json:"data_bit_485"`
	Parity485      int64   `json:"parity_485"`
	StopBit485     float64 `json:"stop_bit_485"`
	TimeSpending   int64   `json:"time_spending"` // 本周在线时长
	OnlineTimes    int64   `json:"online_times"`  // 本周上线次数
	OfflineTimes   int64   `json:"offline_times"` // 本周下线次数
	OnlineTime     string  `json:"online_time"`
	OfflineTime    string  `json:"offline_time"`
	ResetTime      int64   `json:"reset_time"`
	CreatedTime    string  `json:"created_time"`
	UpdatedTime    string  `json:"updated_time"`
}

// TbFirmware 固件版本表结构
type TbFirmware struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Version     string `json:"version"`
	URL         string `json:"url"`
	Url         string `json:"url"`         // 兼容字段
	MD5         string `json:"md5"`
	Size        int64  `json:"size"`        // 文件大小
	Msg         string `json:"msg"`         // 更新说明
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

// TbIP 广播 IP 地址池表结构
type TbIP struct {
	ID   int64  `json:"id"`
	IP   string `json:"ip"`
	Name string `json:"name"`
}

// TbLoginLog 登录日志表结构
type TbLoginLog struct {
	ID        int64  `json:"id"`
	IMSI      string `json:"imsi"`
	Status    int64  `json:"status"`
	Reason    string `json:"reason"`
	CreatedTs int64  `json:"created_ts"`
}

// TbMessageLog 终端消息表结构
type TbMessageLog struct {
	ID          int64  `json:"id"`
	OperateId   int    `json:"operate_id"`
	OperationId int64  `json:"operation_id"`
	IMSI        string `json:"imsi"`
	Message     string `json:"message"`
	Content     string `json:"content"`
	ErrorCode   int64  `json:"error_code"`
	ErrorMsg    string `json:"error_msg"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

// TbOperationLog 用户操作日志表结构
type TbOperationLog struct {
	ID          int64  `json:"id"`
	UID         int64  `json:"uid"`
	Username    string `json:"username"`
	Message     string `json:"message"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

// TbTerminalLog 终端日志表结构
type TbTerminalLog struct {
	ID          int64     `json:"id"`
	IMSI        string    `json:"imsi"`
	Filename    string    `json:"filename"`
	UpdatedTime time.Time `json:"updated_time"`
}

// TbOption 配置选项表结构
type TbOption struct {
	ID                int64     `json:"id"`
	OptionKey         string    `json:"option_key"`
	OptionValue       string    `json:"option_value"`
	OptionDescription string    `json:"option_description"`
	UpdatedTime       time.Time `json:"updated_time"`
}

// TbRecordLan 终端LAN记录表结构
type TbRecordLan struct {
	ID             int64  `json:"id"`
	DeviceID       int64  `json:"device_id"`
	Imei           string `json:"imei"`
	Name           string `json:"name"`
	Status         int64  `json:"status"`
	RecordSignalId int64  `json:"record_signal_id"`
	CreatedTime    string `json:"created_time"`
}

// TbRecordSignal 终端信号记录表结构
type TbRecordSignal struct {
	ID          int64  `json:"id"`
	DeviceId    int64  `json:"device_id"`
	IMEI        string `json:"imei"`
	UID         int64  `json:"uid"`
	Rsrp        int64  `json:"rsrp"`
	Rssi        int64  `json:"rssi"`
	Sinr        int64  `json:"sinr"`
	Rsrq        int64  `json:"rsrq"`
	WanRx       int64  `json:"wan_rx"`
	WanTx       int64  `json:"wan_tx"`
	CreatedTime string `json:"created_time"`
}

// TbReportLogin 终端登录周报表结构
type TbReportLogin struct {
	ID           int64   `json:"id"`
	IMSI         string  `json:"imsi"`
	TimeSpending int64   `json:"time_spending"` // 在线时长
	OnlineTimes  int64   `json:"online_times"`  // 上线次数
	OfflineTimes int64   `json:"offline_times"` // 下线次数
	StartTime    string  `json:"start_time"`    //报告开始时间yymmddhhmmss
	EndTime      string  `json:"end_time"`      //报告结束时间yymmddhhmmss
	OnelineRatio float64 `json:"online_ratio"`
	CreatedTime  string  `json:"created_time"`
}

// TbReportLoginEx 终端登录周报联表查询结果表结构
type TbReportLoginEx struct {
	ID           int64   `json:"id"`
	IMSI         string  `json:"imsi"`
	TimeSpending int64   `json:"time_spending"` // 在线时长
	OnlineTimes  int64   `json:"online_times"`  // 上线次数
	OfflineTimes int64   `json:"offline_times"` // 下线次数
	StartTime    string  `json:"start_time"`    //报告开始时间yymmddhhmmss
	EndTime      string  `json:"end_time"`      //报告结束时间yymmddhhmmss
	OnelineRatio float64 `json:"online_ratio"`
	CreatedTime  string  `json:"created_time"`
	IMEI         string  `json:"imei"`
	IP           string  `json:"ip"`
	Name         string  `json:"name"`
	Model        string  `json:"model"`
	SoftwareVer  string  `json:"software_ver"`
	HardwareVer  string  `json:"hardware_ver"`
	UID          int64   `json:"uid"`
	Vender       string  `json:"vender"`
	CI           string  `json:"ci"`
	PCI          string  `json:"pci"`
	ManageIp     string  `json:"manage_ip"`
	Position     string  `json:"position"`
	Longitude    string  `json:"longitude"`
	Latitude     string  `json:"latitude"`
}

// TbUpgradeRules 固件升级策略表结构
type TbUpgradeRules struct {
	ID          int64  `json:"id"`
	SrcVersion  string `json:"src_version"`
	DestVersion string `json:"dest_version"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

// TbUser 用户表结构
type TbUser struct {
	ID          int64     `json:"id"`
	Username    string    `json:"username"`
	Password    string    `json:"password"`
	Name        string    `json:"name"`
	Tip         string    `json:"tip"`
	Level       UserLevel `json:"level"`
	Status      int64     `json:"status"`
	Avatar      string    `json:"avatar"`
	ParentID    int64     `json:"parent_id"`
	Path        string    `json:"path"`
	OrderID     int       `json:"order_id"`
	HdMapId     int64     `json:"hd_map_id"`
	CreatedTime string    `json:"created_time"`
	UpdatedTime string    `json:"updated_time"`
}

// TbHdMap 高精地图结构
type TbHdMap struct {
	ID              int64  `json:"id"`
	Name            string `json:"name"`
	Version         string `json:"version"`
	ProtocolVersion string `json:"protocol_version"`
	Data            string `json:"data"`
	CreatedTime     int    `json:"created_time"`
	UpdatedTime     int    `json:"updated_time"`
}

// TbLogFirmware 固件升级日志表结构
type TbLogFirmware struct {
	ID           int64  `json:"id"`
	DeviceId     int64  `json:"device_id"`
	DeviceImsi   string `json:"device_imsi"`
	FirmwareId   int64  `json:"firmware_id"`
	Mode         int    `json:"mode"`
	DownloadMode int    `json:"download_mode"`
	Progress     float64 `json:"progress"`
	Status       int    `json:"status"`
	Info         string `json:"info"`
	CreatedTime  int64  `json:"created_time"`
	UpgradeTime  int64  `json:"upgrade_time"`
	OperateId    int64  `json:"operate_id"`
	IsCheck      int    `json:"is_check"`
	IsReboot     int    `json:"is_reboot"`
}

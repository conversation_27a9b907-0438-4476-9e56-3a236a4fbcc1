package vars

// Option 配置选项变量
var Option struct {
	TerminalLogTimeout int64 `json:"terminal_log_timeout"`

	// UDP 广播选项
	BroadcastServerStart  bool   `json:"broadcast_server_start"`
	BroadcastLoopInterval int64  `json:"broadcast_loop_interval"`
	BroadcastSendInterval int64  `json:"broadcast_send_interval"`
	BroadcastUdpPort      int    `json:"broadcast_udp_port"`
	BroadcastMsgIP        string `json:"broadcast_msg_ip"`
	BroadcastMsgPort      int    `json:"broadcast_msg_port"`

	// 网络 IP 选项
	ManageIP   string `json:"manage_ip"`   // 网管网 IP
	BusinessIP string `json:"business_ip"` // 业务网 IP
}

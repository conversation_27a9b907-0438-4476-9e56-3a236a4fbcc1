package vars

//redis docs
//HLEN key (hash 成员数)

//set
//SCARD key 获取集合的成员数
//SADD key member1 [member2] 向集合添加一个或多个成员
//SISMEMBER key member 判断 member 元素是否是集合 key 的成员
//SMEMBERS key 返回集合中的所有成员
//SPOP key 移除并返回集合中的一个随机元素
//SREM key member1 [member2] 移除集合中一个或多个成员

var (
	// RedisInfoSyncExpired 实时信息有效时长 (s)
	RedisInfoSyncExpired = 60

	// RedisNetworkIPPool IP 地址池 (set)
	RedisNetworkIPPool = "NETWORK::IP_POOL"

	// RedisDeviceUnauthorized 未授权终端 (hash)
	RedisDeviceUnauthorized = "DEVICE::UNAUTHORIZED::LIST"

	// RedisDeviceOwnerUID 终端拥有者 (set)
	RedisDeviceOwnerUID = "DEVICE::OWNER::UID_"

	// RedisOperationLogURL 终端操作日志下载(setex) params: imei
	RedisOperationLogURL = "OPERATION::LOG::%s::URL"
	// RedisOperationPbMsg 设置终端信息信息 params: imei, operate
	RedisOperationPbMsg = "OPERATION::PB::%s::%d"

	// RedisDeviceHeartbeatTS 终端心跳时间 (hash) (未上过线: -1)
	RedisDeviceHeartbeatTS = "DEVICE::TS::HEARTBEAT"
	// 🗑️ 已删除 RedisDeviceHeartbeatRecordTS 常量，因为移除了 record_min_interval 配置
	// RedisDeviceLanOfflineTS 终端 LAN1 离线时间 (正常(初始化时为 0): -1, 0, 其它: 离线时间戳) (hash)
	RedisDeviceLanOfflineTS = "DEVICE::TS::LAN_1_OFFLINE"
	// RedisDeviceAllInfo 所有终端信息
	RedisDeviceAllInfo = "DEVICE::All"
	// RedisDeviceLan2OfflineTS 终端 LAN1 离线时间 (正常(初始化时为 0): -1, 0, 其它: 离线时间戳) (hash)
	RedisDeviceLan2OfflineTS = "DEVICE::TS::LAN_2_OFFLINE"

	// RedisDeviceOfflineTS 默认为0，表示设备未上线； 设置为-1，表示设备在线； 设置为其他非负数，表示设备离线时间；
	RedisDeviceOfflineTS = "DEVICE::TS::DEVICE_OFFLINE"

	// RedisDeviceAuthorizedCnt 已授权的设备总量
	RedisDeviceAuthorizedCnt = "DEVICE::AUTHORIZED::CNT"

	// RedisLastHeartbeat 设备的最后心跳数据,即使server退出，都保留在Redis中，server重启也不清空
	RedisLastHeartbeat = "DEVICE::HB::HASH"

	// RedisUserInfof 用户信息 (key)
	RedisUserInfof = "USER::INFO::UID_"
	// RedisSessionTokenToUid 储存会话 UID
	RedisSessionTokenToUid = "SESSION::TOKEN_UID"
	// RedisSessionUidTokenHash 储存会话 TOKEN
	RedisSessionUidTokenHash = "SESSION::UID_TOKEN"
	// RedisSessionExpireTsHash 储存会话过期时间戳的散列
	RedisSessionExpireTsHash = "SESSION::EXPIRE_TIMESTAMP"

	// RedisSessionMultiTokenUid  token -> uid 多用户
	RedisSessionMultiTokenUid = "SESSION::MULTI::TOKEN_%s"
	// RedisSessionMultiUidToken uid -> token
	RedisSessionMultiUidToken = "SESSION::MULTI::UID_%d"

	RedisCoffeeOrderCheck = "PIX::COFFEE::ORDER::CHECK"
)

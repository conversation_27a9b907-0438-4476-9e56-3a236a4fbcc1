package vars

import "time"

type DataMaintenanceConfig struct {
	Table          string `json:"table"`
	RetainedDays   int    `json:"retained_days"`
	CleanupOnStart bool   `json:"cleanup_on_start"`
	Cron           string `json:"cron"`
	TimeColumn     string `json:"time_column"`
	TimeType       string `json:"time_type"`
}

type RedisConfig struct {
	Host        string        `json:"host"`
	Port        int           `json:"port"`
	Db          int           `json:"db"`
	Pass        string        `json:"pass"`
	IdleTimeout time.Duration `json:"idle_timeout"`
}

// Config 配置信息
var Config struct {
	App struct {
		Name    string `json:"name"`
		Version string `json:"version"`
	}

	Business struct {
		OfflineInterval        int64         `json:"offline_interval"`
		LanOfflineInterval     int64         `json:"lan_offline_interval"`
		OfflineReportThreshold int64         `json:"offline_report_threshold"`
		PushInterval           time.Duration `json:"push_interval"`
		DiskName               string        `json:"disk_name"`

		// 心跳检测相关配置
		HeartbeatCheckInterval time.Duration `json:"heartbeat_check_interval"`
		HeartbeatTimeout       time.Duration `json:"heartbeat_timeout"`
	}

	Schedule struct {
		DbBackupCron       string        `json:"db_backup_cron"`
		CpuCheckCron       string        `json:"cpu_check_cron"`
		ReportGenerateCron string        `json:"report_generate_cron"`
		ReportResetCron    string        `json:"report_reset_cron"`
		UpgradeHeartbeat   time.Duration `json:"upgrade_heartbeat"`
		UpgradeTimeout     time.Duration `json:"upgrade_timeout"`
	}

	Server struct {
		Tcp struct {
			Host             string        `json:"host"`
			Port             int           `json:"port"`
			KeepaliveTimeout time.Duration `json:"keepalive_timeout"`
		}

		// 🆕 MQTT配置（重构后的清晰配置）
		Mqtt struct {
			Broker               string        `json:"broker"`                 // MQTT broker地址 (ws://host:port/path)
			TopicPrefix          string        `json:"topic_prefix"`           // MQTT topic前缀
			ClientID             string        `json:"client_id"`              // MQTT客户端ID前缀
			CleanSession         bool          `json:"clean_session"`          // MQTT clean session
			ConnectTimeout       time.Duration `json:"connect_timeout"`        // 连接超时时间
			KeepaliveTimeout     time.Duration `json:"keepalive_timeout"`      // 保活超时时间
			PingTimeout          time.Duration `json:"ping_timeout"`           // Ping超时时间
			AutoReconnect        bool          `json:"auto_reconnect"`         // 自动重连
			MaxReconnectInterval time.Duration `json:"max_reconnect_interval"` // 最大重连间隔
		}

		// 🗑️ WebSocket配置已移除，完全使用MQTT配置
	}

	Logging struct {
		BasePath            string `json:"base_path"`
		RotateSize          string `json:"rotate_size"`
		RotateBackupLimit   int    `json:"rotate_backup_limit"`
		RotateCheckInterval string `json:"rotate_check_interval"`
		DateFormat          string `json:"date_format"`
		MaxFilesPerDay      int    `json:"max_files_per_day"`

		Modules struct {
			Server   LogConfig `json:"server"`
			Tcp      LogConfig `json:"tcp"`
			Terminal LogConfig `json:"terminal"`
			Database LogConfig `json:"database"`
			Kafka    LogConfig `json:"kafka"`
		} `json:"modules"`
	}

	Database struct {
		DefaultCharset      string `json:"default_charset"`
		DefaultTimeout      string `json:"default_timeout"`
		DefaultReadTimeout  string `json:"default_read_timeout"`
		DefaultWriteTimeout string `json:"default_write_timeout"`

		Default  DBConfig `json:"default"`
		Wurenche DBConfig `json:"wurenche"`
		Jzy      DBConfig `json:"jzy"`
		Coffee   DBConfig `json:"coffee"`
	}

	Redis struct {
		Default RedisConfig `json:"default"`
	}

	Middleware struct {
		Kafka struct {
			Brokers string `json:"brokers"`
			Topic   string `json:"topic"`
		} `json:"kafka"`
	}

	// 加密配置
	Encryption struct {
		SM4 struct {
			Enabled bool   `json:"enabled"` // 是否启用SM4-GCM加密
			Key     string `json:"key"`     // SM4密钥（16字节，hex编码）
		} `json:"sm4"`
	} `json:"encryption"`

	// 安全防护配置
	Security struct {
		// 连接限制配置
		ConnectionLimit struct {
			Enabled                bool          `json:"enabled"`                  // 是否启用连接限制
			InitialBanDuration     time.Duration `json:"initial_ban_duration"`     // 首次禁止时长（秒）
			MaxBanDuration         time.Duration `json:"max_ban_duration"`         // 最大禁止时长（秒）
			BackoffMultiplier      float64       `json:"backoff_multiplier"`       // 退避倍数
			MaxAttempts            int           `json:"max_attempts"`             // 最大尝试次数
			CleanupInterval        time.Duration `json:"cleanup_interval"`         // 清理间隔（秒）
			CacheExpiration        time.Duration `json:"cache_expiration"`         // 缓存过期时间（秒）
			LogSuppressionDuration time.Duration `json:"log_suppression_duration"` // 日志抑制时长（秒）
		} `json:"connection_limit"`

		// IP级别限制配置
		IPLimit struct {
			Enabled             bool          `json:"enabled"`                // 是否启用IP限制
			MaxConnectionsPerIP int           `json:"max_connections_per_ip"` // 每个IP最大连接数
			BanDuration         time.Duration `json:"ban_duration"`           // IP禁止时长（秒）
		} `json:"ip_limit"`
	} `json:"security"`

	DataMaintenance []DataMaintenanceConfig
}

// 🗑️ 兼容性函数已移除，直接使用 vars.Config.Server.Mqtt

// Broadcast 广播参数
type Broadcast struct {
	Enabled bool `json:"enabled"`

	UDPPort      int    `json:"udp_port"`
	Interval     int64  `json:"interval"`
	SendInterval int64  `json:"send_interval"`
	MsgIP        string `json:"msg_ip"`
	MsgPort      int    `json:"msg_port"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `json:"level"`
	Stdout   bool   `json:"stdout"`
	Filename string `json:"filename"`
}

// DBConfig 数据库配置
type DBConfig struct {
	Host string `json:"host"`
	Port int    `json:"port"`
	User string `json:"user"`
	Pass string `json:"pass"`
	Name string `json:"name"`
	Type string `json:"type"`
}

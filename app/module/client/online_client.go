package client

import (
	"github.com/gogf/gf/container/gmap"
)

var (
	clientMap = gmap.NewStrAnyMap(true) //只存在线设备
)

// Add client
func Add(clientId string, c interface{}) {
	clientMap.Set(clientId, c)
}

// Get Get client
func Get(clientId string) interface{} {
	return clientMap.Get(clientId)
}

// All All clientMap
func All() *gmap.StrAnyMap {
	return clientMap
}

// Del Del client
func Del(clientId string) {
	clientMap.Remove(clientId)
}

package license

import (
	"ccserver/app/library/genstd"
	"ccserver/app/library/v"
	cacheservice "ccserver/app/service/cache"
	dbservice "ccserver/app/service/db"
	"ccserver/app/vars"
	"errors"
	"github.com/gogf/gf/os/gfile"
	"os"
	"strings"
	"time"
)

// Load 加载许可证
func Load() error {
	productMode := false

	if !productMode {
		key := &vars.License{}
		key.EndTime = "2099-12-31 23:59:59"
		key.Total = 10000
		v.SetLicense(key)
		return nil
	}

	// 判断是否为开发模式（通过环境变量判断）
	if isDevMode() {
		key := &vars.License{}
		key.EndTime = "2099-12-31 23:59:59"
		key.Total = 10000 // 开发环境设置较大的授权数
		v.SetLicense(key)
		return nil
	}

	str := gfile.GetContents("keygen")
	if err := v.NewLicense(str); err != nil {
		return err
	}
	license := v.GetLicense()
	if license.CpuID == "" || license.HddID == "" || license.Total == 0 || license.EndTime == "" {
		return errors.New("许可证无效")
	}
	if cpuid, err := genstd.GetCPUID(); err != nil {
		return errors.New("硬件参数1提取失败")
	} else if strings.Compare(license.CpuID, cpuid) != 0 {
		return errors.New("硬件参数1不匹配")
	}
	hddsn, err := genstd.GetHDDSN()
	if err != nil {
		hddsn, err = genstd.GetSSDSN()
	}
	if err != nil {
		return errors.New("硬件参数2提取失败")
	}
	if strings.Compare(license.HddID, hddsn) != 0 {
		return errors.New("硬件参数2不匹配")
	}
	if et, e := time.ParseInLocation("2006-01-02 15:04:05", license.EndTime, time.Local); e != nil {
		return errors.New("许可证有效期无效")
	} else if time.Now().Sub(et) > 0 {
		return errors.New("许可证已过期")
	}

	deviceCount, err := dbservice.GetDeviceCount()
	if err != nil {
		return errors.New("获取已授权的设备数量失败")
	}

	cacheservice.InitRedisDeviceAuthorizedCnt(int64(deviceCount))
	count := cacheservice.GetRedisDeviceAuthorizedCnt()

	v.Log().Debugf("终端授权数:%d", license.Total)

	if count > (int64)(license.Total) {
		v.Log().Warningf("终端数量已超出授权数,请联系经销商升级:当前授权终端数为%d,已登记数为%d,将只接入其中%d台终端", license.Total, count, license.Total)
		return nil
	}
	return nil
}

// isDevMode 判断是否为开发模式
// 通过检查环境变量GF_GCFG_FILE或配置文件名来判断
func isDevMode() bool {
	// 检查环境变量
	if configFile := os.Getenv("GF_GCFG_FILE"); configFile != "" {
		return strings.Contains(configFile, "dev")
	}

	// 检查默认配置文件是否为开发配置
	// 如果没有指定配置文件，默认为开发模式
	return true
}

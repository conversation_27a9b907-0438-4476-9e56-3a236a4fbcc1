package upgrade

import (
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/client"
	dbService "ccserver/app/service/db"
	"ccserver/app/vars"
	"fmt"
	"github.com/gogf/gf/container/gmap"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtimer"
	"time"
)

// UpgradeDevice 升级的终端参数值
type UpgradeDevice struct {
	IMEI        string    `json:"imei"`
	IMSI        string    `json:"imsi"`
	SrcVersion  string    `json:"src_version"`  // 源版本号
	DestVersion string    `json:"dest_version"` // 目标版本号
	CmdSentTime time.Time `json:"updated_time"` // 上次发送时间
	CmdSentCnt  int       `json:"times"`        // 发送次数
}

var (
	// upgradeDeviceMap 升级终端列表 (map[imei]UpgradeDevice)
	upgradeDeviceMap = gmap.NewStrAnyMap(true)

	// upgradeRuleMap 正在升级的策略(map[src_version]dest_version)
	upgradeRuleMap = gmap.NewStrStrMap(true) //注意这个map是协程安全的

	// VersionMap 版本队列(map[version]download_url)
	VersionMap = gmap.NewStrAnyMap(true)
)

func Start() {
	// 检测时间间隔, 最小 5 秒
	if vars.Config.Schedule.UpgradeHeartbeat < 5 {
		vars.Config.Schedule.UpgradeHeartbeat = 5
	}
	vars.Config.Schedule.UpgradeHeartbeat *= time.Second

	// 超时重发时间间隔, 最小 300 秒
	if vars.Config.Schedule.UpgradeTimeout < 300 {
		vars.Config.Schedule.UpgradeTimeout = 300
	}
	vars.Config.Schedule.UpgradeTimeout *= time.Second

	loadVersions()
	loadUpgradeRules()

	gtimer.Add(vars.Config.Schedule.UpgradeHeartbeat, sendUpgradeCmdIfNeeded)
}

func sendUpgradeCmdIfNeeded() {
	var removeList []string

	// 读取升级队列中的终端
	upgradeDeviceMap.Iterator(func(k string, val interface{}) bool {
		device := val.(*UpgradeDevice)
		// 从未升级过或升级超时
		if device.CmdSentCnt == 0 || time.Since(device.CmdSentTime) > vars.Config.Schedule.UpgradeTimeout {
			destVersion := upgradeRuleMap.Get(device.SrcVersion)
			if destVersion == "" {
				removeList = append(removeList, k)
				return true
			}

			upgradeCmd := VersionMap.Get(destVersion)
			// 目标版本号为空数据, ?是否刷新时间
			if upgradeCmd == nil {
				return true
			}

			if term := client.Get(device.IMSI); term != nil {
				v.LogTcp().Debugf("send upgrade cmd to:" + device.IMSI)
				term.(*vars.Terminal).Prop.Message <- upgradeCmd.([]byte)
				device.CmdSentTime = time.Now()
				device.CmdSentCnt++
			}
		}
		return true
	})

	if len(removeList) > 0 {
		upgradeDeviceMap.Removes(removeList)
	}
}

// AddNeededUpgradeDevices 将需要升级终端的添加到升级列表中
func AddNeededUpgradeDevices(srcVersion, destVersion string) {
	upgradeRuleMap.Set(srcVersion, destVersion)

	// 读取需要升级的终端列表, 并将其写入队列
	deviceRes, err := model.NewOrm(vars.TableDevice).All(g.Map{"software_ver": srcVersion, "maintain_state": 0}, "")
	if err == nil && !deviceRes.IsEmpty() {
		var devices []vars.TbDevice
		deviceRes.Structs(&devices)

		timeNow := time.Now()
		for _, value := range devices {
			upgradeDevice := &UpgradeDevice{
				IMEI:        value.IMEI,
				IMSI:        value.IMSI,
				SrcVersion:  value.SoftwareVer,
				DestVersion: destVersion,
				CmdSentTime: timeNow,
				CmdSentCnt:  0,
			}
			AddNeededUpgradeDevice(value.IMSI, upgradeDevice)
		}
	}
}

// RemoveNeededUpgradeDevices 从升级列表中列表中移除指定版本的设备
func RemoveNeededUpgradeDevices(srcVersion string) {
	upgradeRuleMap.Remove(srcVersion)
	var removeList []string
	upgradeDeviceMap.Iterator(func(k string, v interface{}) bool {
		device := v.(*UpgradeDevice)
		if device.SrcVersion == srcVersion {
			//RemoveNeededUpgradeDevice(k)
			removeList = append(removeList, k)
		}
		return true
	})

	if len(removeList) > 0 {
		upgradeDeviceMap.Removes(removeList)
	}
}

func GetDestUpgradeVer(srcVer string) string {
	return upgradeRuleMap.Get(srcVer)
}

func AddNeededUpgradeDevice(deviceId string, device *UpgradeDevice) {
	upgradeDeviceMap.Set(deviceId, device)
}

func RemoveNeededUpgradeDevice(deviceId string) {
	upgradeDeviceMap.Remove(deviceId)
}

func GetNeededUpgradeDevice(deviceId string) *UpgradeDevice {
	upgradeDevice := upgradeDeviceMap.Get(deviceId)

	if upgradeDevice == nil {
		return nil
	}

	return upgradeDevice.(*UpgradeDevice)
}

// 初始化时读取正在升级的策略
func loadUpgradeRules() {
	res, err := dbService.GetUpgradeRulesEnable()
	if err != nil {
		v.LogTcp().Errorf("[loadUpgradeRules]: %s", err)
		return
	}
	if res.IsEmpty() {
		return
	}

	var upgradeRules []vars.TbUpgradeRules
	if err := res.Structs(&upgradeRules); err != nil {
		v.LogTcp().Errorf("[loadUpgradeRules]: %s", err)
		return
	}

	for _, value := range upgradeRules {
		AddNeededUpgradeDevices(value.SrcVersion, value.DestVersion)
	}
}

func loadVersions() {
	res, err := model.NewOrm(vars.TableFirmware).All(1, "")
	if err != nil {
		v.LogTcp().Errorf("[loadVersions]: %s", err)
		return
	}

	var versions []vars.TbFirmware
	if err := res.Structs(&versions); err != nil {
		v.LogTcp().Errorf("[loadVersions]: %s", err)
		return
	}

	for _, value := range versions {
		if data, err := common.CreateUpgradeData(value.Name, value.MD5); err == nil {
			VersionMap.Set(value.Version, data)
		}
	}
}

// ReloadVersions 重置更新版本的信息
func ReloadVersions() {
	loadVersions()
}

func GetDebugInfo() string {
	return fmt.Sprintf("UpgradeRuleMap:\n%+v\n\n\nVersionMap:\n%+v\n\n\nUpgradeDeviceMap:\n%+v", upgradeRuleMap, VersionMap, upgradeDeviceMap)
}

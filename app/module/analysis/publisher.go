package analysis

import (
	"ccserver/app/module/mqtt"
	"ccserver/pix_log"
	"encoding/json"
)

const (
	// TopicPrefix MQTT主题前缀
	TopicPrefix = "pixmoving/analysis/heartbeat"
)

// Publisher MQTT分析消息发布器
type Publisher struct{}

// SharedPublisher 单例实例
var SharedPublisher = &Publisher{}

// PublishDeviceMessage 发布设备分析消息
// imsi: 设备IMSI
// source: 消息来源(如:"heartbeat", "register", "alarm"等)
func (p *Publisher) PublishDeviceMessage(imsi string, source string) error {
	// 构建消息
	message := map[string]interface{}{
		"imsi":   imsi,
		"source": source,
	}

	// 序列化数据
	messageJSON, err := json.Marshal(message)
	if err != nil {
		pix_log.Error("序列化分析消息失败:", err.Error())
		return err
	}

	// 发布消息到MQTT
	token := mqtt.SharedClient.Publish(TopicPrefix, 0, false, string(messageJSON))
	token.Wait()

	if token.Error() != nil {
		pix_log.Error("发布分析消息失败:", token.Error())
		return token.Error()
	}

	// pix_log.Info("发布分析消息成功 [source:", source, "] [imsi:", imsi, "]")
	return nil
}

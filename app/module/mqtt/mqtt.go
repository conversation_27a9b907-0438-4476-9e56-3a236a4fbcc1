package mqtt

import (
	"ccserver/app/vars"
	"encoding/json"
	"fmt"
	"net"
	"net/url"
	"os"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gogf/gf/os/gtime"
)

var (
	SharedClient mqtt.Client // MQTT Client
	// publishHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	//	fmt.Printf("TOPIC: %s\n", msg.Topic())
	//	fmt.Printf("MSG: %s\n", msg.Payload())
	// }
	//
	// connectionLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	//	v.Log().Debugf("mqtt client connection lost:%s\n", err)
	// }
	//
	// onConnectionHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	//	v.Log().Debugf("mqtt client connected")
	// }
)

// Start mqtt client
// func Start() error {
//	clientId := fmt.Sprintf("ccserver_%s", gtime.Datetime())
//	opts := mqtt.NewClientOptions().AddBroker(vars.Config.Server.Websocket.LanURI).SetClientID(clientId)
//	//opts.SetConnectTimeout(vars.Config.Server.Websocket.ConnectTimeout * time.Second)
//	//opts.SetKeepAlive(vars.Config.Server.Websocket.KeepaliveTimeout * time.Second)
//	//opts.SetPingTimeout(vars.Config.Server.Websocket.PingTimeout * time.Second)
//	//opts.SetAutoReconnect(true)
//	//opts.SetMaxReconnectInterval(1 * time.Second)
//	//
//	//opts.SetOnConnectHandler(onConnectionHandler)
//	//opts.SetConnectionLostHandler(connectionLostHandler)
//	//opts.SetDefaultPublishHandler(publishHandler)
//
//	SharedClient = mqtt.NewClient(opts)
//	if token := SharedClient.Connect(); token.Wait() && token.Error() != nil {
//		return fmt.Errorf("[WEBSOCKET]服务器连接失败，请联系管理员: %s", token.Error())
//	}
//	return nil
// }

func Start() error {
	// 🆕 直接使用新的MQTT配置
	mqttConfig := vars.Config.Server.Mqtt

	// 🔧 简化日志：只显示关键信息
	uri := mqttConfig.Broker

	// 🔍 调试模式下显示详细配置（可通过环境变量控制）
	if os.Getenv("MQTT_DEBUG") == "true" {
		configJSON, _ := json.MarshalIndent(vars.Config, "", "  ")
		fmt.Printf("详细配置信息：%s\n", string(configJSON))
	}

	parsedURL, err := url.Parse(uri)
	if err != nil {
		return fmt.Errorf("解析 MQTT URI 失败: %v", err)
	}

	// DNS 解析（静默处理）
	ips, err := net.LookupIP(parsedURL.Hostname())
	if err != nil {
		return fmt.Errorf("DNS 解析失败: %v", err)
	}

	if len(ips) == 0 {
		return fmt.Errorf("未找到有效的 IP 地址")
	}

	// 使用第一个 IP 地址
	ip := ips[0].String()
	newURI := fmt.Sprintf("%s://%s:%s%s",
		parsedURL.Scheme,
		ip,
		parsedURL.Port(),
		parsedURL.Path)

	// 🆕 使用配置中的MQTT参数
	clientId := fmt.Sprintf("%s_%s", mqttConfig.ClientID, gtime.Datetime())
	opts := mqtt.NewClientOptions().AddBroker(newURI).SetClientID(clientId)

	// 🆕 使用配置中的超时参数（注意：配置中的时间已经是time.Duration类型）
	opts.SetConnectTimeout(mqttConfig.ConnectTimeout)
	opts.SetKeepAlive(mqttConfig.KeepaliveTimeout)
	opts.SetPingTimeout(mqttConfig.PingTimeout)
	opts.SetAutoReconnect(mqttConfig.AutoReconnect)
	opts.SetMaxReconnectInterval(mqttConfig.MaxReconnectInterval)
	opts.SetCleanSession(mqttConfig.CleanSession)

	// 🔧 添加更多调试选项
	opts.SetProtocolVersion(4) // 使用MQTT 3.1.1
	opts.SetOrderMatters(false)
	opts.SetResumeSubs(true)

	connected := make(chan bool)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		connected <- true
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		fmt.Printf("❌ MQTT 连接断开: %v\n", err)
	})

	// 🔧 简化连接信息：只显示关键参数
	fmt.Printf("🔗 正在连接 MQTT: %s (客户端: %s)\n",
		mqttConfig.Broker, mqttConfig.ClientID)

	t1 := time.Now()

	SharedClient = mqtt.NewClient(opts)
	token := SharedClient.Connect()

	select {
	case <-connected:
		fmt.Printf("✅ MQTT 连接成功 (耗时: %v)\n", time.Since(t1))
		return nil
	case <-time.After(10 * time.Second):
		if token.Error() != nil {
			return fmt.Errorf("❌ MQTT 连接失败 (耗时: %v): %v", time.Since(t1), token.Error())
		}
		return fmt.Errorf("❌ MQTT 连接超时 (耗时: %v)", time.Since(t1))
	}
}

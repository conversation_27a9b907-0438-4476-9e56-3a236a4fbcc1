package dashboard

import (
	"ccserver/app/library/common"
	lTime "ccserver/app/library/time"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/client"
	"ccserver/app/module/mqtt"
	cacheservice "ccserver/app/service/cache"
	db2 "ccserver/app/service/db"
	dbservice "ccserver/app/service/db"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/app/web/dto"
	"ccserver/pix_log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	mqtt2 "github.com/eclipse/paho.mqtt.golang"
	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtimer"
	"github.com/gogf/gf/util/gconv"
)

// 🆕 Dashboard初始化标记
var dashboardInitialized = false

// Push push
func Push() {
	// 定时执行任务worker，注：当执行任务时只有当函数worker返回时，才重新定时执行下一次任务，若worker函数一直不返回，则不会定时执行下一次任务。
	// 第一次执行任务是立即执行,下一次任务执行的时间=上次任务执行完成 + 定时的时间长度
	gtimer.AddSingleton(vars.Config.Business.PushInterval, worker)

	// 第一次执行任务是立即执行,下一次任务执行的时间=上次任务执行完成 + 定时的时间长度
	// gtimer.AddSingleton(vars.Config.Business.PushInterval, coffee)

	// 获取设备信息
	gtimer.AddSingleton(vars.Config.Business.PushInterval, deviceInfo)

	// 获取紧急通知信息
	gtimer.AddSingleton(vars.Config.Business.PushInterval, notificationMsg)

	// gtimer.AddSingleton(1*time.Second, test)
	// 数据大屏
	// gtimer.AddSingleton(vars.Config.Setting.PushInterval*time.Second, analysis)
}

type NotificationMsg struct {
	Id          int     `json:"id"`
	Content     string  `json:"content"`
	DeviceId    int     `json:"device_id"`
	Type        int     `json:"type"`
	Lng         float64 `json:"lng"`
	Lat         float64 `json:"lat"`
	MessageTime int     `json:"message_time"`
	Status      int     `json:"status"`
}

// UserList 用户列表
type UserList struct {
	Id          int64  `json:"id"`
	Username    string `json:"username"`
	Name        string `json:"name"`
	Tip         string `json:"tip"`
	Level       int    `json:"level"`
	Status      int    `json:"status"`
	ParentId    int    `json:"parent_id"`
	HdMapId     int    `json:"hd_map_id"`
	HdMapName   string `json:"hd_map_name"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
	RealName    string `json:"real_name"`
	Tel         string `json:"tel"`
	Email       string `json:"email"`
	Type        int    `json:"type"`
	IsDelete    int    `json:"is_delete"`
	MapIds      string `json:"map_ids"`
	MapIdsArr   []int  `json:"map_ids_arr"`
}

type MachineList struct {
	Id               int      `json:"id"`
	Imsi             string   `json:"imsi"`
	BillNo           string   `json:"bill_no"`
	ProductId        int      `json:"product_id"`
	ProductName      string   `json:"product_name"`
	TaskId           int      `json:"task_id"`
	DeviceStatus     int      `json:"device_status"`
	Action           int      `json:"action"`
	TotalTime        int      `json:"total_time"`
	RTime            int      `json:"r_time"`
	ActionTime       int      `json:"action_time"`
	ActionRTime      int      `json:"action_r_time"`
	ErrCode          string   `json:"err_code"`
	ErrMsg           string   `json:"err_msg"`
	CupHolders       int      `json:"cup_holders"`
	Liquid           int      `json:"liquid"`
	Solid            int      `json:"solid"`
	LiquidCup        int      `json:"liquid_cup"`
	SolidCup         int      `json:"solid_cup"`
	Ports            int      `json:"ports"`
	ArmPosX          int      `json:"arm_pos_x"`
	OtherStatus1     int      `json:"other_status_1" gorm:"column:other_status_1"`
	OtherStatus2     int      `json:"other_status_2" gorm:"column:other_status_2"`
	CarP             int      `json:"car_p"`
	CarT             int      `json:"car_t"`
	CarStatus        int      `json:"car_status"`
	InvV             int      `json:"inv_v"`
	InvF             int      `json:"inv_f"`
	InvC             int      `json:"inv_c"`
	InvT             int      `json:"inv_t"`
	Time             int      `json:"time"`
	RunTime          int      `json:"run_time"`
	BootCnt          int      `json:"boot_cnt"`
	CreatedTime      int64    `json:"created_time"`
	UpdatedTime      int64    `json:"updated_time"`
	Uid              int64    `json:"uid"`
	CupHoldersChecks []string `json:"cup_holders_checks"`
	LiquidChecks     []string `json:"liquid_checks"`
	SolidChecks      []string `json:"solid_checks"`
	LiquidCupChecks  []string `json:"liquid_cup_checks"`
	SolidCupChecks   []string `json:"solid_cup_checks"`
	PortsChecks      []string `json:"ports_checks"`
	Air              string   `json:"air"`
	Hot              []string `json:"hot"`
	Username         string   `json:"username"`
	Cup              []string `json:"cup"`
	ActionListLen    int      `json:"action_list_len"`
	ActionIndex      int      `json:"action_index"`
	Online           int      `json:"online"`
	DeviceName       string   `json:"device_name"`
	Status           int      `json:"status"`
}

// 统计并发送
func worker() {
	var users []model.User
	db2.GetPixmoving().Where("status = 0 AND is_delete = 0").Find(&users)

	for _, user := range users {
		u := user
		var offlineCount, lanCount int64
		redisVar, _ := cacheservice.GetOwnDevices(u.Id)
		deviceArray := redisVar.Array()
		off := lTime.NewOffline()
		for _, val := range redisVar.Array() {
			off.SetDeviceId(gconv.String(val))
			if off.IsTermOffline() {
				offlineCount++
			} else {
				if off.IsLan1Offline() && off.IsLan2Offline() {
					lanCount++
				}
			}
		}
		payload := g.Map{
			"id":      u.Id,             // 用户id
			"count":   len(deviceArray), // 车辆总数
			"offline": offlineCount,     // 离线数量
			"fault":   lanCount,         // 故障数量
		}
		// 🆕 直接使用MQTT配置
		topic := fmt.Sprintf("%s/dashboard/user/%d",
			vars.Config.Server.Mqtt.TopicPrefix,
			u.Id,
		)
		payloadJSON, _ := gjson.Encode(payload)

		token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
	}
}

type Alarm struct {
	Code  int    `json:"code"`
	Level uint8  `json:"level"`
	Type  uint8  `json:"type"`
	Msg   string `json:"msg"`
	Ts    int64  `json:"ts"`
}

// 统计并发送
func coffee() {
	var machines []MachineList
	db2.GetCoffee().Find(&machines)

	db2.GetCoffee().Raw(fmt.Sprintf(`select machine.*,product.name as product_name,user.name as username,device.online,device.name as device_name,device.status from machine left join product on machine.product_id = product.id
		 left join user on machine.uid = user.id left join device on machine.imsi = device.imsi`)).Scan(&machines)

	for _, machine := range machines {
		// 🆕 直接使用MQTT配置
		topic := fmt.Sprintf("%s/dashboard/coffee/%s",
			vars.Config.Server.Mqtt.TopicPrefix,
			machine.Imsi,
		)

		var cupHoldersChecks []string
		var liquidChecks []string
		var solidChecks []string
		var liquidCupChecks []string
		var portsChecks []string
		var hot []string
		var solidCupChecks []string
		var cup []string
		var air string

		outCupHolders := common.ConvertToBin(machine.CupHolders, 32)
		outCupLiquid := common.ConvertToBin(machine.Liquid, 32)
		outSolid := common.ConvertToBin(machine.Solid, 16)
		outLiquidCup := common.ConvertToBin(machine.LiquidCup, 32)
		outSolidCup := common.ConvertToBin(machine.SolidCup, 16)
		outPorts := common.ConvertToBin(machine.Ports, 8)
		outHot := common.ConvertToBin(machine.OtherStatus1, 4)
		outCup := common.ConvertToBin(machine.OtherStatus2, 4)

		for i := len(outCupHolders); i > 0; i-- {
			cupHoldersChecks = append(cupHoldersChecks, outCupHolders[i-1:i])
		}

		for i := len(outCupLiquid); i > 0; i-- {
			liquidChecks = append(liquidChecks, outCupLiquid[i-1:i])
		}

		for i := len(outSolid); i > 0; i-- {
			solidChecks = append(solidChecks, outSolid[i-1:i])
		}

		for i := len(outLiquidCup); i > 0; i-- {
			liquidCupChecks = append(liquidCupChecks, outLiquidCup[i-1:i])
		}

		for i := len(outSolidCup); i > 0; i-- {
			solidCupChecks = append(solidCupChecks, outSolidCup[i-1:i])
		}

		for i := len(outPorts); i > 0; i-- {
			portsChecks = append(portsChecks, outPorts[i-1:i])
		}

		for i := len(outHot); i > 0; i-- {
			hot = append(hot, outHot[i-1:i])
		}

		for i := len(outCup); i > 0; i-- {
			cup = append(cup, outCup[i-1:i])
		}

		outAir := common.ConvertToBin(machine.CarStatus, 8)
		air = outAir[len(outAir)-1:]

		payload := g.Map{
			"id":                 machine.Id,
			"imsi":               machine.Imsi,
			"bill_no":            machine.BillNo,
			"product_id":         machine.ProductId,
			"product_name":       machine.ProductName,
			"task_id":            machine.TaskId,
			"device_status":      machine.DeviceStatus,
			"action":             machine.Action,
			"total_time":         machine.TotalTime,
			"r_time":             machine.RTime,
			"action_time":        machine.ActionTime,
			"action_r_time":      machine.ActionRTime,
			"err_code":           machine.ErrCode,
			"err_msg":            machine.ErrMsg,
			"cup_holders":        machine.CupHolders,
			"liquid":             machine.Liquid,
			"solid":              machine.Solid,
			"liquid_cup":         machine.LiquidCup,
			"solid_cup":          machine.SolidCup,
			"ports":              machine.Ports,
			"arm_pos_x":          machine.ArmPosX,
			"other_status_1":     machine.OtherStatus1,
			"other_status_2":     machine.OtherStatus2,
			"car_p":              machine.CarP,
			"car_t":              machine.CarT,
			"car_status":         machine.CarStatus,
			"inv_v":              machine.InvV,
			"inv_f":              machine.InvF,
			"inv_c":              machine.InvC,
			"inv_t":              machine.InvT,
			"time":               machine.Time,
			"run_time":           machine.RunTime,
			"boot_cnt":           machine.BootCnt,
			"created_time":       machine.CreatedTime,
			"updated_time":       machine.UpdatedTime,
			"uid":                machine.Uid,
			"cup_holders_checks": cupHoldersChecks,
			"liquid_checks":      liquidChecks,
			"solid_checks":       solidChecks,
			"liquid_cup_checks":  liquidCupChecks,
			"solid_cup_checks":   solidCupChecks,
			"ports_checks":       portsChecks,
			"hot":                hot,
			"cup":                cup,
			"air":                air,
			"username":           machine.Username,
			"action_list_len":    machine.ActionListLen,
			"action_index":       machine.ActionIndex,
			"online":             machine.Online,
			"device_name":        machine.DeviceName,
			"status":             machine.Status,
		}

		payloadJSON, _ := gjson.Encode(payload)

		token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
	}
}

// 统计并发送设备信息
func deviceInfo() {
	nowTs := time.Now().Unix()
	mondayTs := common.GetMondayTs()

	// 🆕 首次运行时记录Dashboard职责说明
	if !dashboardInitialized {
		pix_log.Info("🚀 [DASHBOARD] 启动设备信息推送服务")
		pix_log.Info("🎯 [DASHBOARD] 职责: 定时推送设备数据，直接使用数据库online字段，不做状态判断")
		pix_log.Info("📋 [DASHBOARD] 推送间隔: %d秒", vars.Config.Business.PushInterval)
		dashboardInitialized = true
	}

	var devices []model.Device
	var users []model.User
	db2.GetPixmoving().Where("status = 0 AND is_delete = 0").Find(&users)

	for _, user := range users {
		u := user
		// db2.GetPixmoving().Debug().Where("uid = ?", u.Id).Find(&devices)
		db2.GetPixmoving().Where("uid = ?", u.Id).Find(&devices)
		respData := make([]protocol.HeartbeatUesrDeviceMqtt, 0)
		for _, value := range devices {
			item := getDeviceData(mondayTs, nowTs, value)

			// 🎯 直接使用数据库中的在线状态，不再做额外判断
			// 在线状态完全由心跳检测器负责维护
			item.Online = value.Online

			// 🗑️ 移除频繁的调试日志，避免日志过多

			respData = append(respData, *item)
		}

		payloadJSON, _ := gjson.Encode(respData)
		// 🆕 直接使用MQTT配置
		topic := fmt.Sprintf("%s/dashboard/device/%d",
			vars.Config.Server.Mqtt.TopicPrefix,
			u.Id,
		)

		token := mqtt.SharedClient.Publish(topic, 0, true, string(payloadJSON))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
	}
}

// 统计并发送紧急通知
func notificationMsg() {
	var user []vars.TbUser
	userRes, _ := dbservice.AllUser(false)
	if userRes.IsEmpty() {
		return
	}
	if err := userRes.Structs(&user); err != nil {
		fmt.Println("err", err.Error())
		return
	}

	for _, u := range user {
		var notification []NotificationMsg
		var read int64
		var all int64

		db2.GetPixmoving().Table("notification_user").
			Select("notification_user.id,notification_user.status,"+
				"notification.content,notification.device_id,notification.type,"+
				"notification.lng,notification.lat,notification.message_time").
			Joins("left join notification on notification_user.notification_id = notification.id").
			Where("notification_user.user_id = ? and notification_user.status = 0", u.ID).
			Order("notification_user.id desc").
			Scan(&notification)

		db2.GetPixmoving().Table("notification_user").Where("user_id = ?", u.ID).Count(&all)
		db2.GetPixmoving().Table("notification_user").Where("user_id = ? and status = 1", u.ID).Count(&read)
		payload := g.Map{
			"len":  len(notification),
			"data": notification,
			"all":  all,
			"read": read,
		}
		// 🆕 直接使用MQTT配置
		topic := fmt.Sprintf("%s/web/notification/%d",
			vars.Config.Server.Mqtt.TopicPrefix,
			u.ID,
		)

		payloadJSON, _ := gjson.Encode(payload)

		if mqtt.SharedClient.IsConnected() {
			// token1 := mqtt.SharedClient.Subscribe(topic, 0, nil)
			// token1.Wait()
			token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
			token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
			// token.WaitTimeout(15 * time.Second)

			if token.Error() != nil {
				v.Log().Debugf("mqtt publish failed:%s", token.Error())
			}
		} else {
			v.Log().Warningf("mqtt not connected, ignore publish")
		}
	}
}

// app/module/dashboard/dashboard.go
func getDeviceData(mondayTs int64, nowTs int64, device model.Device) *protocol.HeartbeatUesrDeviceMqtt {
	currentWeekOnlineTotalTime := int64(device.TimeSpending)
	var currentOnlineTotalTime int64

	// 从Redis获取心跳数据
	lastHbJsonStr := cacheservice.GetRedisDeviceHeartbeat(device.IMSI)

	// 使用 HeartbeatRedis 结构体解析数据
	var redisData protocol.HeartbeatRedis
	if len(lastHbJsonStr) > 0 && lastHbJsonStr != "" {
		if err := json.Unmarshal([]byte(lastHbJsonStr), &redisData); err != nil {
			v.LogTcp().Errorf("[getDeviceData] Redis数据解析出错:%s", err)
		}
	}

	// 在线时间计算
	if client.All().Contains(device.IMSI) {
		term := client.Get(device.IMSI).(*vars.Terminal)
		thisLoginTs := term.Prop.ConnectAt.Unix()
		fixLoginTs := thisLoginTs
		if fixLoginTs < mondayTs {
			fixLoginTs = mondayTs
		}
		currentWeekOnlineTotalTime += nowTs - fixLoginTs
		currentOnlineTotalTime = nowTs - thisLoginTs
	}

	// 获取任务信息
	var task model.Task
	var taskChild model.TaskChild
	db2.GetPixmoving().Table("task_child").Where("id = ?", device.TaskId).First(&taskChild)
	db2.GetPixmoving().Table("task").Where("id = ?", taskChild.ParentId).First(&task)

	// 使用新的构造函数创建响应数据
	return protocol.NewHeartbeatUesrDeviceMqtt(
		device,
		redisData,
		currentOnlineTotalTime,
		currentWeekOnlineTotalTime,
		task,
		taskChild,
	)
}

type MqttMsg struct {
	Cmd  int                    `json:"cmd"`
	Body map[string]interface{} `json:"body"`
}

var (
	wg sync.WaitGroup
)

func GetMqttMsg() {
	var msg MqttMsg
	msgRcvd := func(client mqtt2.Client, message mqtt2.Message) {
		wg.Add(1)
		err := json.Unmarshal(message.Payload(), &msg)
		if err != nil {
			return
		}

		body := msg.Body["imsi"]
		// pix_log.Info("收到mqtt消息：imsi:", body)

		if body != nil {
			switch msg.Cmd {
			case 1: // 查询ota升级状态
				data, _ := gjson.Encode(g.Map{
					"cmd": protocol.QueryOTAStatus,
				})
				cmd := common.MakeCmd(protocol.QueryOTAStatus, 0, data)
				if err = sendToDevice(body.(string), cmd); err != nil {
					fmt.Println("发送命令到终端失败：" + err.Error())
				}

			case 2: // 查询设备的清扫任务
				data, _ := gjson.Encode(g.Map{
					"cmd": protocol.QueryTask,
				})

				cmd := common.MakeCmd(protocol.QueryTask, 0, data)
				if err = sendToDevice(body.(string), cmd); err != nil {
					fmt.Println("发送命令到终端失败：" + err.Error())
				}

			case 3: // 查询ota升级状态
				data, _ := gjson.Encode(g.Map{
					"cmd": protocol.VCUQueryOTAStatus,
				})
				cmd := common.MakeCmd(protocol.VCUQueryOTAStatus, 0, data)
				if err = sendToDevice(body.(string), cmd); err != nil {
					pix_log.Error("发送命令到终端失败：" + err.Error())
				} else {
					pix_log.Info("发送A002成功")
				}
			}
		}

		wg.Done()
	}

	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/cmd", vars.Config.Server.Mqtt.TopicPrefix)
	token1 := mqtt.SharedClient.Subscribe(topic, 0, msgRcvd)
	token1.Wait()

	time.Sleep(10 * time.Second)
	wg.Wait()
}

func Mqtt() {
	var (
		keepaliveTimeout time.Duration
	)

	if keepaliveTimeout = vars.Config.Server.Tcp.KeepaliveTimeout; keepaliveTimeout == 0 {
		keepaliveTimeout = 150
	}
	keepaliveTimeout *= time.Second

	msgRcvd := func(client mqtt2.Client, message mqtt2.Message) {
		wg.Add(1)
		data := message.Payload()
		term := &vars.Terminal{}
		// term.Conn = conn
		term.IsConnected = true
		// remoteAddr := strings.Split(conn.RemoteAddr().String(), ":")
		// ipAddr := net.ParseIP(remoteAddr[0]).String()
		// term.Prop.IP = ipAddr
		term.Prop.Authorized = false
		term.Prop.Quit = make(chan bool, 1)
		term.Prop.Message = make(chan []byte, 20)
		term.Prop.ConnectAt = time.Now()
		term.Prop.UpdatedAt = time.Now()
		term.Prop.KeepaliveTimeout = keepaliveTimeout
		// term.Parser = new(boot.YhlProtocol)

		if len(data) > 0 {
			term.Parser.HandleRecv(data, term)
		}
		// boot.DoSendAndHandleDisconnectMqtt(term)
		wg.Done()
	}

	// 🆕 直接使用MQTT配置
	topic := fmt.Sprintf("%s/mqtt/client", vars.Config.Server.Mqtt.TopicPrefix)
	token1 := mqtt.SharedClient.Subscribe(topic, 0, msgRcvd)
	token1.Wait()

	time.Sleep(10 * time.Second)
	wg.Wait()
}

// SendToDevice 消息下发给指定终端
func sendToDevice(deviceId string, data []byte) error {
	if !client.All().Contains(deviceId) {
		return errors.New("终端与服务器的连接已断开")
	}
	client.Get(deviceId).(*vars.Terminal).Prop.Message <- data
	return nil
}

func analysis() {
	var userLists []vars.TbUser
	userRes, _ := dbservice.AllUser(false)
	if userRes.IsEmpty() {
		return
	}
	if err := userRes.Structs(&userLists); err != nil {
		fmt.Println("err", err.Error())
		return
	}

	for _, userList := range userLists {
		children, _ := child(userList.ID)

		var idsStr []string
		var idsInt []int64
		for _, ren := range children {
			idsStr = append(idsStr, fmt.Sprintf("%d", ren.ID))
			idsInt = append(idsInt, ren.ID)
		}

		allId := strings.Join(idsStr, ",")

		var (
			re          dto.RespDeviceForm
			do          string
			devices     []model.Device
			tasks       []model.Task
			taskLogs    []model.TaskLog
			china       []model.China
			message     []model.Message
			deviceTotal = 0
			taskTotal   = 0
			online      = 0
			offline     = 0

			mileageMonthTotal = 0
			mileageDayTotal   = 0
			deviceType0       = 0
			deviceType1       = 0
			deviceType2       = 0
			deviceType3       = 0
			deviceType4       = 0

			taskIdsStr []string
			taskDoing  = 0
			taskDone   = 0
			taskUndo   = 0
			taskMonth  = 0
			taskDay    = 0

			neverOnline     = 0
			offlineDay      = 0
			offlineDayThree = 0
			offlineDayFive  = 0
			offlineDaySeven = 0
			offlineDayLong  = 0

			now        = time.Now().Unix()
			nowHour    = time.Now().Hour()
			firstHour  = common.GetZeroTime3(time.Now(), nowHour)
			firstMonth = common.GetFirstDateOfMonth(time.Now())
			firstDay   = common.GetZeroTime2(time.Now())

			taskInfo     []map[string]interface{}
			city         []map[string]interface{}
			notification []map[string]interface{}
			alarm        []map[string]interface{}
			messageList  []map[string]interface{}
			users        []dto.AnalysisUser

			event1   = 0
			event10  = 0
			event12  = 0
			event16  = 0
			event18  = 0
			event19  = 0
			event25  = 0
			event26  = 0
			event27  = 0
			event28  = 0
			event29  = 0
			event30  = 0
			event33  = 0
			event34  = 0
			event35  = 0
			event100 = 0
			event101 = 0
			event102 = 0
			event103 = 0
			event104 = 0
		)

		where := fmt.Sprintf(" uid in (%s)", allId)
		where2 := fmt.Sprintf(" user_id in (%s)", allId)

		db2.GetPixmoving().Raw(fmt.Sprintf("select * from device where %s", where)).Scan(&devices)

		db2.GetPixmoving().Raw(fmt.Sprintf("select * from task where uid in (%s)", allId)).Scan(&tasks)

		db2.GetPixmoving().Raw(fmt.Sprintf("select * from message where %s", where2)).Scan(&message)

		db2.GetPixmoving().Raw("select * from china where pid = 0 and id > 0").Scan(&china)

		if len(tasks) > 0 {
			for _, task := range tasks {
				taskIdsStr = append(taskIdsStr, fmt.Sprintf("%d", task.Id))
			}

			allTaskId := strings.Join(taskIdsStr, ",")
			db2.GetPixmoving().Raw(fmt.Sprintf("select * from task_log where id in(%s) GROUP BY child_id order by id desc;", allTaskId)).Scan(&taskLogs)
		}

		for d, u := range idsInt {
			var user model.User
			db2.GetPixmoving().First(&user, 10)

			users = append(users, dto.AnalysisUser{
				No:       d + 1,
				Uid:      u,
				Username: user.Username,
			})
		}

		taskTotal = len(taskLogs)

		for _, taskLog := range taskLogs {
			if taskLog.Status == 2 {
				taskDoing++
			} else if taskLog.Status == 3 {
				taskDone++
			}

			if taskLog.CreatedTime >= firstMonth.Unix() {
				taskMonth++
			}

			if taskLog.CreatedTime >= firstDay.Unix() {
				taskDay++
			}
		}

		deviceTotal = len(devices)

		for k, device := range devices {
			do = cacheservice.GetRedisDeviceHeartbeat(device.IMSI)
			if do != "" {
				json.Unmarshal([]byte(do), &re)
			}

			var monthHeart model.RecordSignal
			var dayHeart model.RecordSignal

			db2.GetPixmoving().Raw(fmt.Sprintf("select * from record_signal where imsi = '%s' and created_time > %d limit 1", device.IMSI, firstMonth.Unix())).Scan(&monthHeart)
			db2.GetPixmoving().Raw(fmt.Sprintf("select * from record_signal where imsi = '%s' and created_time > %d limit 1", device.IMSI, firstDay.Unix())).Scan(&dayHeart)

			mileageMonthTotal += int(re.Tm) - int(monthHeart.Tm)
			mileageDayTotal += int(re.Tm) - int(dayHeart.Tm)

			if device.Online == 1 {
				online++
			} else {
				offline++
			}

			if device.Type == 0 {
				deviceType0++
			} else if device.Type == 1 {
				deviceType1++
			} else if device.Type == 2 {
				deviceType2++
			} else if device.Type == 3 {
				deviceType3++
			} else if device.Type == 4 {
				deviceType4++
			}

			if device.OnlineTime == "" {
				neverOnline++
			}

			if device.OfflineTime != "" && device.Online == 0 {
				timeInt := common.ChangeTimeToInt(device.OfflineTime)
				timeArea := now - timeInt
				if timeArea < 86400 {
					offlineDay++
				} else if timeArea < 86400*3 && timeArea >= 86400 {
					offlineDayThree++
				} else if timeArea < 86400*5 && timeArea >= 86400*3 {
					offlineDayFive++
				} else if timeArea < 86400*7 && timeArea >= 86400*5 {
					offlineDaySeven++
				} else if timeArea >= 86400*7 {
					offlineDayLong++
				}
			}

			taskDoneDevice := 0
			taskTotalDevice := 0
			for _, taskLog := range taskLogs {
				if taskLog.Status == 3 && taskLog.Imsi == device.IMSI {
					taskDoneDevice++
				}
				if taskLog.Imsi == device.IMSI {
					taskTotalDevice++
				}
			}

			taskInfo = append(taskInfo, map[string]interface{}{
				"no":          k + 1,
				"device_name": device.Name,
				"task_done":   taskDoneDevice,
				"task_undo":   taskTotalDevice - taskDoneDevice,
			})

			num := 0
			db2.GetPixmoving().Raw(fmt.Sprintf("select count(*) from notification where device_id = %d", device.Id)).Scan(&num)
			alarm = append(alarm, map[string]interface{}{
				"no":          k + 1,
				"device_name": device.Name,
				"number":      num,
			})

			for r, u := range users {
				if int64(device.Uid) == u.Uid {
					users[r].Total++
					users[r].Notification += num
					if device.Online == 1 {
						users[r].Online++
					} else {
						users[r].Offline++
					}
				}
			}
		}

		for _, province := range china {
			num := 0
			messageNum := 0
			for _, device := range devices {
				if device.Province == province.Name {
					num++
				}

				for _, e := range message {
					if device.Id == e.DeviceId {
						messageNum++
					}
				}
			}
			city = append(city, map[string]interface{}{
				"position":    province.Name,
				"number":      num,
				"message_num": messageNum,
			})
		}

		taskUndo = taskTotal - taskDoing - taskDone

		for _, m := range message {
			switch m.Type {
			case 1:
				event1++
				break
			case 10:
				event10++
				break
			case 12:
				event12++
				break
			case 16:
				event16++
				break
			case 18:
				event18++
				break
			case 19:
				event19++
				break
			case 25:
				event25++
				break
			case 26:
				event26++
				break
			case 27:
				event27++
				break
			case 28:
				event28++
				break
			case 29:
				event29++
				break
			case 30:
				event30++
				break
			case 33:
				event33++
				break
			case 34:
				event34++
				break
			case 35:
				event35++
				break
			case 100:
				event100++
				break
			case 101:
				event101++
				break
			case 102:
				event102++
				break
			case 103:
				event103++
				break
			case 104:
				event104++
				break
			}
		}

		for i := 9; i >= 0; i-- {
			var countHeart int64
			var countAlarm int64

			lastTime := nowHour - i
			if lastTime < 0 {
				lastTime += 24
			}

			lastHour := firstHour.Unix() - int64(i*60*60)
			lastOne := lastHour + 60*60

			db2.GetPixmoving().Raw(fmt.Sprintf("select count(*) from record_signal where uid in (%s) and created_time >= %d and created_time < %d", allId, lastHour, lastOne)).Scan(&countHeart)
			db2.GetPixmoving().Raw(fmt.Sprintf("select count(*) from message where user_id in (%s) and created_time >= %d and created_time < %d", allId, lastHour, lastOne)).Scan(&countAlarm)

			notification = append(notification, map[string]interface{}{
				"time":  fmt.Sprintf("%d:00", lastTime),
				"heart": countHeart,
				"alarm": countAlarm,
			})
		}

		for i := 20; i >= 0; i -= 2 {
			var listNum int64

			lastTime := nowHour - i
			if lastTime < 0 {
				lastTime += 24
			}

			lastHour := firstHour.Unix() - int64(i*60*60)
			lastOne := lastHour + 60*60

			db2.GetPixmoving().Raw(fmt.Sprintf("select count(*) from message where %s and message_time >= %d and message_time < %d", where2, lastHour, lastOne)).Scan(&listNum)

			messageList = append(messageList, map[string]interface{}{
				"time": fmt.Sprintf("%d时", lastTime),
				"num":  listNum,
			})
		}

		returnData := map[string]interface{}{
			"device_total":        deviceTotal,
			"online":              online,
			"offline":             offline,
			"task_total":          taskTotal,
			"mileage_month_total": mileageMonthTotal,
			"mileage_day_total":   mileageDayTotal,
			"device_type_0":       deviceType0,
			"device_type_1":       deviceType1,
			"device_type_2":       deviceType2,
			"device_type_3":       deviceType3,
			"device_type_4":       deviceType4,
			"task_doing":          taskDone,
			"task_done":           taskDone,
			"task_undo":           taskUndo,
			"task_month":          taskMonth,
			"task_day":            taskDay,
			"never_online":        neverOnline,
			"offline_day":         offlineDay,
			"offline_day_three":   offlineDayThree,
			"offline_day_five":    offlineDayFive,
			"offline_day_seven":   offlineDaySeven,
			"offline_day_long":    offlineDayLong,
			"task_info":           taskInfo,
			"area":                city,
			"message":             notification,
			"alarm":               alarm,
			"users":               users,
			"message_list":        messageList,
		}

		// 🆕 直接使用MQTT配置
		topic := fmt.Sprintf("%s/device/analysis/%d",
			vars.Config.Server.Mqtt.TopicPrefix,
			userList.ID,
		)

		payloadJSON, _ := gjson.Encode(returnData)

		if mqtt.SharedClient.IsConnected() {
			// token1 := mqtt.SharedClient.Subscribe(topic, 0, nil)
			// token1.Wait()
			token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
			token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
			// token.WaitTimeout(15 * time.Second)

			if token.Error() != nil {
				v.Log().Debugf("mqtt publish failed:%s", token.Error())
			}
		} else {
			v.Log().Warningf("mqtt not connected, ignore publish")
		}
	}
}

func child(userId int64) ([]vars.TbUser, error) {
	userList, _ := dbservice.AllUser(false)
	var data []vars.TbUser
	var user []vars.TbUser
	userList.Structs(&user)

	data = append(data, vars.TbUser{
		ID: userId,
	})

	data = append(data, getChild(userId, user)...)

	return data, nil
}

func getChild(id int64, userLIst []vars.TbUser) []vars.TbUser {
	var childList []vars.TbUser
	var last []vars.TbUser

	for _, list := range userLIst {
		if list.ParentID == id {
			childList = append(childList, list)
			last = getChild(list.ID, userLIst)
			childList = append(childList, last...)
		}
	}

	return childList
}

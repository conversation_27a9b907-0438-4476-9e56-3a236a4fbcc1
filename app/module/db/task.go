package db

import (
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/mqtt"
	dbService "ccserver/app/service/db"
	"ccserver/app/vars"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gcron"
	"github.com/gogf/gf/os/gtime"
	"io/ioutil"
	"os/exec"
	"time"
)

// Info 报告信息
type Info struct {
	IMSI         string  `json:"imsi"`          // IMSI
	TimeSpending int64   `json:"time_spending"` // 在线时长
	OnlineTimes  int64   `json:"online_times"`  // 上线次数
	OfflineTimes int64   `json:"offline_times"` // 下线次数
	OnlineRatio  float64 `json:"online_ratio"`  // 在线率
}

/*
type LatestTask struct {
	Step      int8   `json:"step"`
	RouteId   int    `json:"routeId"`
	Progress  int8   `json:"progress"`
	StartTime int64  `json:"startTime"`
	Point     string `json:"point"`
	NextPoint string `json:"nextPoint"`
	LeftTime  int    `json:"leftTime"`
	Wurenche     string `json:"other"`
}

type Heart struct {
	DeviceId        int        `json:"device_id"`
	Imsi            string     `json:"imsi"`
	Uid             int        `json:"uid"`
	Lan1Status      int        `json:"lan1_status"`
	Lan2Status      int        `json:"lan2_status"`
	UpgradeState    string     `json:"upgrade_state"`
	UpgradeProgress string     `json:"upgrade_progress"`
	Lat             float64    `json:"lat"`
	Lng             float64    `json:"lng"`
	Located         int        `json:"located"`
	Sta             int        `json:"sta"`
	Mode            int        `json:"mode"`
	Acc             int        `json:"acc"`
	Gear            int        `json:"gear"`
	Door            int        `json:"door"`
	Light           int        `json:"light"`
	Win             int        `json:"win"`
	Tm              int        `json:"tm"`
	Rm              int        `json:"rm"`
	Ste             float64    `json:"ste"`
	Brk             float64    `json:"brk"`
	HeadMode        int        `json:"head_mode"`
	Sgn             int        `json:"sgn"`
	Spd             int        `json:"spd"`
	PL              int        `json:"pL" orm:"column(pL)"`
	PV              float64    `json:"pV" orm:"column(pV)"`
	PC              float64    `json:"pC" orm:"column(pC)"`
	PCh             int        `json:"pCh" orm:"column(pCh)"`
	Bat             float64    `json:"bat"`
	Alt             float64    `json:"alt"`
	Angle           float64    `json:"angle"`
	SatCnt          int        `json:"sat_cnt"`
	Err             string     `json:"err"`
	Event           int        `json:"event"`
	CreatedTime     int        `json:"created_time"`
	SweeperStatus   []uint32   `json:"sweeper_status"`
	LatestTask      LatestTask `json:"latest_task"`
	VType           uint8      `json:"v_type"`
}*/

// InitDbCrontabTask 启动
func InitDbCrontabTask() error {
	if _, e := gcron.Add(vars.Config.Schedule.ReportGenerateCron, createAllDevicesLoginWeekReportsEx); e != nil {
		return e
	}

	gcron.Add(vars.Config.Schedule.ReportResetCron, resetCurrentWeekLoginReportsEx)
	addCrontabAutoClearDbOldData()
	gcron.Add(vars.Config.Schedule.DbBackupCron, backupDb)

	// gcron.Add(vars.Config.Schedule.CpuCheckCron, checkCPU)

	return nil
}

func checkCPU() {
	cmd := exec.Command("/bin/bash", "-c", "vmstat")
	output, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("无法获取命令的标准输出管道", err.Error())
	}

	// 执行Linux命令
	if err := cmd.Start(); err != nil {
		fmt.Println("Linux命令执行失败，请检查命令输入是否有误", err.Error())
	}

	// 读取所有输出
	readAll, err := ioutil.ReadAll(output)
	if err != nil {
		fmt.Println("打印异常，请检查")
	}

	if err := cmd.Wait(); err != nil {
		fmt.Println("Wait", err.Error())
	}

	prints := string(readAll)
	// fmt.Println("打印内存信息：\n\n", prints)

	// index := strings.Index(prints, "id")
	// fmt.Println("index:", index)

	id := prints[len(prints)-9 : len(prints)-7]
	free := prints[174:181]

	topic := "/pix/checkCPU"
	data := map[string]interface{}{
		"id":   id,
		"free": free,
	}
	payloadJSON, _ := json.Marshal(data)

	if mqtt.SharedClient.IsConnected() {
		// token1 := mqtt.SharedClient.Subscribe(topic, 0, nil)
		// token1.Wait()
		token := mqtt.SharedClient.Publish(topic, 0, false, string(payloadJSON))
		token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
		// token.WaitTimeout(15 * time.Second)

		if token.Error() != nil {
			v.Log().Debugf("mqtt publish failed:%s", token.Error())
		}
	} else {
		v.Log().Warningf("mqtt not connected, ignore publish")
	}
}

func backupDb() {
	info := "执行定时任务:backup"
	dbService.AddSysAutoOperationLog(info, 0)

	backupCmd := "scripts/backup_db.sh"
	cmd := exec.Command(backupCmd) // 初始化Cmd
	err := cmd.Start()             // 运行脚本

	if err != nil {
		v.Log().Infof("backup fail:%s", err)
	}

	err = cmd.Wait() // 等待执行完成
	if err != nil {
		v.Log().Infof("backup fail:%s", err)
	}

	if err == nil {
		dbService.AddSysAutoOperationLog(info+",成功", 2)
	} else {
		dbService.AddSysAutoOperationLog(info+",失败", 1)
	}
}

// GetDeviceLoginReport 获取终端周报(计算总在线时长进行统计)
func GetDeviceLoginReport(imsi string, startTime, endTime time.Time) *Info {
	startTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", startTime.Year()%100, startTime.Month(), startTime.Day(), startTime.Hour(), startTime.Minute(), startTime.Second())
	endTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", endTime.Year()%100, endTime.Month(), endTime.Day(), endTime.Hour(), endTime.Minute(), endTime.Second())

	var startTs, endTs int64

	startTs = startTime.Unix()
	endTs = endTime.Unix()

	where := g.Map{
		"created_ts >= ": startTs,
		"created_ts < ":  endTs,
		"imsi":           imsi,
	}
	loginRes, err := dbService.GetLoginHistory(where, "id asc")
	if err != nil {
		v.Log().Warningf("CreateLastWeekLogin login_log err: %s\n", err)
		return nil
	}

	var logs []vars.TbLoginLog
	loginRes.Structs(&logs)

	var totalOnlineTime, onlineTimes, offlineTimes int64 // 上次在线状态, 周统计

	var onlineRatio float64

	var logsLen = len(logs)

	// 无记录 则一直离线 或 一直在线
	if logsLen == 0 {
		if res, e := model.NewOrm(vars.TableLoginLog).One(g.Map{"imsi": imsi, "created_ts < ": startTs}, "id desc"); e == nil {
			var loginLog vars.TbLoginLog
			res.Struct(&loginLog)
			if loginLog.Status == 1 { // 上次记录是在线的
				onlineRatio = 1
				totalOnlineTime = endTs - startTs
			} else {
				onlineRatio = 0
				totalOnlineTime = 0
			}
		}
	} else {
		var lastOnlineTime int64
		// var lastOfflineTime int64
		var preLogStatus int64

		for idx, loginLog := range logs {
			if idx == 0 && loginLog.Status == 0 { // 第一条记录为下线记录
				totalOnlineTime += loginLog.CreatedTs - startTs
				offlineTimes++
				// lastOfflineTime = loginLog.CreatedTs
			} else if (idx == logsLen-1) && loginLog.Status == 1 { // 最后一条记录为上线记录
				totalOnlineTime += endTs - loginLog.CreatedTs
				onlineTimes++
			} else { // 其他情况数据：第一条记录为上线记录、或最后一条记录为下线记录、或第2条到倒数第一条数据
				if loginLog.Status == 0 {
					offlineTimes++

					if lastOnlineTime != 0 {
						if preLogStatus == 0 { // 特别情况，连续出现下线记录(如：上线、下线、下线、下线、上线)，则只认第一个下线记录,避免重复计算上线时长
							continue
						}
						totalOnlineTime += loginLog.CreatedTs - lastOnlineTime
						// lastOfflineTime = loginLog.CreatedTs
					} else {
						// 未找到匹配的上线记录,统计会不准确
						v.Log().Warningf("未找到匹配的上线记录, startTime:%s, endTime:%s, idx:%d\n", startTimeStr, endTimeStr, idx)
					}
				} else {
					lastOnlineTime = loginLog.CreatedTs
					onlineTimes++
				}
			}
			preLogStatus = loginLog.Status
		}

		if totalOnlineTime != 0 {
			onlineRatio = float64(totalOnlineTime) / float64(endTs-startTs)
		}
	}

	return &Info{
		IMSI:         imsi,
		TimeSpending: totalOnlineTime,
		OnlineTimes:  onlineTimes,
		OfflineTimes: offlineTimes,
		OnlineRatio:  onlineRatio,
	}
}

// GetDeviceLoginReportEx 获取终端周报(计算总离线时长进行统计)
func GetDeviceLoginReportEx(imsi string, startTime, endTime time.Time) *Info {
	var offlineReportThreshold = vars.Config.Business.OfflineReportThreshold

	startTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", startTime.Year()%100, startTime.Month(), startTime.Day(), startTime.Hour(), startTime.Minute(), startTime.Second())
	endTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", endTime.Year()%100, endTime.Month(), endTime.Day(), endTime.Hour(), endTime.Minute(), endTime.Second())

	var startTs, endTs int64

	startTs = startTime.Unix()
	endTs = endTime.Unix()

	where := g.Map{
		"created_ts >= ": startTs,
		"created_ts < ":  endTs,
		"imsi":           imsi,
	}
	loginRes, err := dbService.GetLoginHistory(where, "id asc")
	if err != nil {
		v.Log().Warningf("CreateLastWeekLogin login_log err: %s\n", err)
		return nil
	}

	var logs []vars.TbLoginLog
	loginRes.Structs(&logs)

	var totalOnlineTime, totalOfflineTime, onlineTimes, offlineTimes int64 // 上次在线状态, 周统计

	var onlineRatio float64

	var logsLen = len(logs)

	// 无记录 则一直离线 或 一直在线
	if logsLen == 0 {
		if res, e := model.NewOrm(vars.TableLoginLog).One(g.Map{"imsi": imsi, "created_ts < ": startTs}, "id desc"); e == nil {
			var loginLog vars.TbLoginLog
			res.Struct(&loginLog)
			if loginLog.Status == 1 { // 上次记录是在线的
				onlineRatio = 1
				totalOnlineTime = endTs - startTs
			} else {
				onlineRatio = 0
				totalOnlineTime = 0
			}
		}
	} else {
		var lastOfflineTime int64
		var preLogStatus int64
		for idx, loginLog := range logs {
			if idx == 0 && loginLog.Status == 1 { // 第一条记录为上线记录
				if (loginLog.CreatedTs - startTs) >= offlineReportThreshold {
					totalOfflineTime += loginLog.CreatedTs - startTs
				}
				onlineTimes++
				// lastOfflineTime = loginLog.CreatedTs
			} else if (idx == logsLen-1) && loginLog.Status == 0 { // 最后一条记录为下线记录
				if (endTs - loginLog.CreatedTs) >= offlineReportThreshold {
					totalOfflineTime += endTs - loginLog.CreatedTs
				}
				offlineTimes++
			} else { // 其他情况数据：第一条记录为下线记录、或最后一条记录为上线记录、或第2条到倒数第一条数据
				if loginLog.Status == 1 {
					if preLogStatus == 1 { // 特别情况，连续出现上线记录(如：下线、上线、上线、上线、下线)，则只认第一个上线记录,避免重复计算离线时长
						continue
					}

					onlineTimes++

					if lastOfflineTime != 0 {
						if loginLog.CreatedTs-lastOfflineTime > offlineReportThreshold {
							totalOfflineTime += loginLog.CreatedTs - lastOfflineTime
						}
					} else {
						// 未找到匹配的下线记录,统计会不准确
						v.Log().Warningf("未找到匹配的下线记录, startTime:%s, endTime:%s, idx:%d\n", startTimeStr, endTimeStr, idx)
					}
				} else {
					lastOfflineTime = loginLog.CreatedTs
					offlineTimes++
				}
			}

			preLogStatus = loginLog.Status
		}

		totalOnlineTime = endTs - startTs - totalOfflineTime
		onlineRatio = float64(totalOnlineTime) / float64(endTs-startTs)
	}

	return &Info{
		IMSI:         imsi,
		TimeSpending: totalOnlineTime,
		OnlineTimes:  onlineTimes,
		OfflineTimes: offlineTimes,
		OnlineRatio:  onlineRatio,
	}
}

// CreateAllDevicesLoginReports 生成所有设备指定时间范围的在线统计
func CreateAllDevicesLoginReports(startTime, endTime time.Time, isAuto bool) {
	info := fmt.Sprintf("执行定时任务:create %s", vars.TableReportLogin)
	if isAuto {
		dbService.AddSysAutoOperationLog(info, 0)
	} else {
		info = fmt.Sprintf("启动,执行定时任务:create %s", vars.TableReportLogin)
		dbService.AddSysAutoOperationLog(info, 0)
	}
	ok := true

	startTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", startTime.Year()%100, startTime.Month(), startTime.Day(), startTime.Hour(), startTime.Minute(), startTime.Second())
	endTimeStr := fmt.Sprintf("%02d%02d%02d%02d%02d%02d", endTime.Year()%100, endTime.Month(), endTime.Day(), endTime.Hour(), endTime.Minute(), endTime.Second())
	var devices []vars.TbDevice
	deviceRes, err := dbService.GetAllDevicesIMSI()
	nowTime := gtime.Datetime()

	if err != nil {
		v.Log().Warningf("CreateLastWeekLogin devices err: %s\n", err)
		ok = false
		goto CreateAllDevicesLoginReportsEnd
	}

	deviceRes.Structs(&devices)

	for _, device := range devices {
		where := gdb.Map{
			"imsi":       device.IMSI,
			"start_time": startTimeStr,
			"end_time":   endTimeStr,
		}

		var defModel model.Model
		defModel = model.NewOrm(vars.TableReportLogin)
		reportRes, err := defModel.One(where, "")
		if err != nil {
			v.Log().Warningf("MySql报告查询失败")
			ok = false
			goto CreateAllDevicesLoginReportsEnd
		}

		if !reportRes.IsEmpty() {
			// v.Log().Warningf("上下线报告已存在,无需再创建,IMSI:%s,startTime:%s,endTime:%s", device.IMSI, startTimeStr, endTimeStr)
			continue
		}

		var info *Info

		if vars.Config.Business.OfflineReportThreshold == 0 { // 按实际的在线时间进行统计
			info = GetDeviceLoginReport(device.IMSI, startTime, endTime)
		} else { // 按离线时长方式进行统计，可能会加入偏差
			info = GetDeviceLoginReportEx(device.IMSI, startTime, endTime)
		}

		reportData := vars.TbReportLogin{
			IMSI:         info.IMSI,
			TimeSpending: info.TimeSpending,
			OnlineTimes:  info.OnlineTimes,
			OfflineTimes: info.OfflineTimes,
			StartTime:    startTimeStr,
			EndTime:      endTimeStr,
			OnelineRatio: info.OnlineRatio,
			CreatedTime:  nowTime,
		}

		_, err = model.NewOrm(vars.TableReportLogin).Add(reportData)

		if err != nil {
			v.Log().Warningf("在线统计报告失败: %s", err)
			ok = false
			goto CreateAllDevicesLoginReportsEnd
		}
	}

CreateAllDevicesLoginReportsEnd:
	if ok {
		dbService.AddSysAutoOperationLog(info+",成功", 2)
	} else {
		dbService.AddSysAutoOperationLog(info+",失败", 1)
	}
}

func createAllDevicesLoginWeekReportsEx() {
	CreateAllDevicesLoginWeekReports(true)
}

// CreateAllDevicesLoginWeekReports 创建前1周的上下线周报
func CreateAllDevicesLoginWeekReports(isAuto bool) {
	now := time.Now()
	year, week := now.ISOWeek()

	v.Log().Infof("start create login reports\n")

	lastWeekStartTime, lastWeekEndTime := common.GetWeekDateRange(year, week-1)
	CreateAllDevicesLoginReports(lastWeekStartTime, lastWeekEndTime, isAuto)

	v.Log().Infof("end create login reports\n")

	// 前周
	// last2WeekStartTime, last2WeekEndTime := common.GetWeekDateRange(year, week - 2)
	// CreateAllDevicesLoginReports(last2WeekStartTime, last2WeekEndTime, isAuto)
	// 大前周
	// last3WeekStartTime, last3WeekEndTime := common.GetWeekDateRange(year, week - 3)
	// CreateAllDevicesLoginReports(last3WeekStartTime, last3WeekEndTime, isAuto)
}

func resetCurrentWeekLoginReportsEx() {
	ResetCurrentWeekLoginReports(nil, true)
}

// ResetCurrentWeekLoginReports 重置所有设备本周在线统计
// devicesReset: 当devicesReset为nil的时候，会从device表中查询所有的设备
func ResetCurrentWeekLoginReports(devicesReset []vars.TbDevice, isAuto bool) {
	info := fmt.Sprintf("执行定时任务:reset %s", vars.TableReportLogin)
	if isAuto {
		dbService.AddSysAutoOperationLog(info, 0)
	} else {
		info = fmt.Sprintf("启动,执行定时任务:reset %s", vars.TableReportLogin)
		dbService.AddSysAutoOperationLog(info, 0)
	}

	now := time.Now()
	year, week := now.ISOWeek()

	v.Log().Infof("start reset current week login reports\n")

	currentWeekStartTime := common.GetWeekStart(year, week)

	var devices []vars.TbDevice

	ok := false

	if devicesReset == nil {
		deviceRes, err := dbService.GetAllDevicesImsiAndResetTime()
		if err != nil {
			v.Log().Warningf("CreateLastWeekLogin devices err: %s\n", err)
			goto ResetCurrentWeekLoginReportsEnd
		}

		deviceRes.Structs(&devices)
	} else {
		devices = devicesReset
	}

	for _, device := range devices {
		if (device.ResetTime == 0) || (device.ResetTime < currentWeekStartTime.Unix()) {
			where := gdb.Map{
				"imsi": device.IMSI,
			}

			ts := time.Now()
			data := g.Map{
				"reset_time":    ts.Unix(),
				"time_spending": 0,
				"online_times":  0,
				"offline_times": 0,
				"updated_time":  gtime.Datetime(),
			}

			var defModel model.Model
			defModel = model.NewOrm(vars.TableDevice)
			_, err := defModel.Mod(where, data)
			if err != nil {
				v.Log().Warningf("重置设备周统计失败:%s", err)
				continue
			}
		}
	}

	ok = true

	v.Log().Infof("end reset current week login reports\n")

ResetCurrentWeekLoginReportsEnd:
	if ok {
		dbService.AddSysAutoOperationLog(info+",成功", 2)
	} else {
		dbService.AddSysAutoOperationLog(info+",失败", 1)
	}
}

func DoReportRelatedTask(isStarting bool) {
	ResetCurrentWeekLoginReports(nil, !isStarting)
	CreateAllDevicesLoginWeekReports(!isStarting)
}

func addCrontab(pattern string, job func()) {
	if _, e := gcron.Add(pattern, job); e != nil {
		v.Log().Warningf("添加定时任务失败::%s", e)
	}
}

func addCrontabAutoClearDbOldData() {
	for _, dataMaintenanceConfig := range vars.Config.DataMaintenance {
		switch dataMaintenanceConfig.Table {
		case vars.TableOperationLog:
			addCrontab(dataMaintenanceConfig.Cron, autoClearTableOldDataOperationLog)
		case vars.TableMessageLog:
			addCrontab(dataMaintenanceConfig.Cron, autoClearTableOldDataMessageLog)
		case vars.TableLoginLog:
			addCrontab(dataMaintenanceConfig.Cron, autoClearTableOldDataLoginLog)
		case vars.TableReportLogin:
			addCrontab(dataMaintenanceConfig.Cron, autoClearTableOldDataLoginReport)
		}
	}
}

func findDataMaintenanceConfig(table string) (vars.DataMaintenanceConfig, bool) {
	for _, dataMaintenanceConfig := range vars.Config.DataMaintenance {
		if dataMaintenanceConfig.Table == table {
			return dataMaintenanceConfig, true
		}
	}

	return vars.DataMaintenanceConfig{}, false
}

func autoClearTableOldData(dataMaintenanceConfig vars.DataMaintenanceConfig, isAuto bool) {
	info := fmt.Sprintf("执行定时任务:clear %s", dataMaintenanceConfig.Table)
	if isAuto {
		dbService.AddSysAutoOperationLog(info, 0)
	} else {
		info = fmt.Sprintf("启动,执行定时任务:clear %s", dataMaintenanceConfig.Table)
		dbService.AddSysAutoOperationLog(info, 0)
	}

	tableName := dataMaintenanceConfig.Table
	timeColumnName := dataMaintenanceConfig.TimeColumn
	retainedDays := dataMaintenanceConfig.RetainedDays

	where := g.Map{}

	now := time.Now()
	beginTime := now.AddDate(0, 0, -retainedDays)

	if dataMaintenanceConfig.TimeType == "datetime" {
		beginTimeStr := beginTime.Format("2006-01-02 15:04:05")
		where[timeColumnName+" < "] = beginTimeStr
	} else {
		where[timeColumnName+" < "] = beginTime.Unix()
	}

	ok := false

	for tryCnt := 0; tryCnt < 3; tryCnt++ {
		if _, err := model.NewOrm(tableName).Del(where); err != nil {
			v.Log().Warningf("数据清理：删除%s表中%d天之前的数据失败:%s\n, try cnt:%d", tableName, retainedDays, err, tryCnt)
		} else {
			ok = true
			break
		}
	}

	if ok {
		dbService.AddSysAutoOperationLog(info+",成功", 2)
	} else {
		dbService.AddSysAutoOperationLog(info+",失败", 1)
	}
}

func autoClearTableOldDataRecordSignal() {
	dataMaintenanceConfig, ok := findDataMaintenanceConfig(vars.TableRecordSignal)

	if ok {
		v.Log().Debugf("执行定时任务:%s", vars.TableRecordSignal)
		autoClearTableOldData(dataMaintenanceConfig, true)
	}
}

func autoClearTableOldDataOperationLog() {
	dataMaintenanceConfig, ok := findDataMaintenanceConfig(vars.TableOperationLog)

	if ok {
		v.Log().Debugf("执行定时任务:%s", vars.TableOperationLog)
		autoClearTableOldData(dataMaintenanceConfig, true)
	}
}

func autoClearTableOldDataMessageLog() {
	dataMaintenanceConfig, ok := findDataMaintenanceConfig(vars.TableMessageLog)

	if ok {
		v.Log().Debugf("执行定时任务:%s", vars.TableMessageLog)
		autoClearTableOldData(dataMaintenanceConfig, true)
	}
}

func autoClearTableOldDataLoginLog() {
	dataMaintenanceConfig, ok := findDataMaintenanceConfig(vars.TableLoginLog)

	if ok {
		autoClearTableOldData(dataMaintenanceConfig, true)
	}
}

func autoClearTableOldDataLoginReport() {
	dataMaintenanceConfig, ok := findDataMaintenanceConfig(vars.TableReportLogin)

	if ok {
		v.Log().Debugf("执行定时任务:%s", vars.TableReportLogin)
		autoClearTableOldData(dataMaintenanceConfig, true)
	}
}

/*
DoAutoClearDbOldDataTask
isStarting true 服务在启动阶段中
*/
func DoAutoClearDbOldDataTask(isStarting bool) {
	for _, dataMaintenanceConfig := range vars.Config.DataMaintenance {
		if isStarting {
			if dataMaintenanceConfig.CleanupOnStart {
				autoClearTableOldData(dataMaintenanceConfig, false)
			}
		} else {
			autoClearTableOldData(dataMaintenanceConfig, true)
		}
	}
}

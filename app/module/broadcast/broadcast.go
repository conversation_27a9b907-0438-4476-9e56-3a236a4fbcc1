package broadcast

import (
	"errors"
	"fmt"
	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtimer"
	"net"
	"ccserver/app/library/v"
	"ccserver/app/module/client"
	"ccserver/app/vars"
	"time"
)

var (
	Server          *net.UDPConn
	Enabled         bool
	enabledErrorMsg = errors.New("UDP 广播服务器已中止服务，请重启服务器")

	isStarting  bool
	payloadJSON []byte

	ipChannel    = make(chan net.IP)
	startChannel = make(chan bool)

	wkProgress *gtimer.Entry
)

// Run 启动广播服务器
func Run() error {
	conn, err := net.ListenUDP("udp", &net.UDPAddr{
		IP:   net.ParseIP(""),
		Port: 0,
	})
	if err != nil {
		return err
	}
	Server = conn
	Enabled = true
	go broadcast()
	if vars.Option.BroadcastServerStart {
		Start()
	}
	return nil
}

// LoadCfg 重新加载配置
func LoadCfg() (err error) {
	if !Enabled {
		return enabledErrorMsg
	}
	if vars.Option.BroadcastLoopInterval <= 0 {
		vars.Option.BroadcastLoopInterval = 5
	}
	if vars.Option.BroadcastUdpPort <= 0 || vars.Option.BroadcastUdpPort > 65535 {
		return fmt.Errorf("无效的终端广播端口(%d). \n", vars.Option.BroadcastUdpPort)
	}
	if nw := net.ParseIP(vars.Option.BroadcastMsgIP); nw == nil {
		return fmt.Errorf("无效的广播消息 TCP IP 地址 (%s). \n", vars.Option.BroadcastMsgIP)
	} else {
		vars.Option.BroadcastMsgIP = nw.String()
	}
	if vars.Option.BroadcastMsgPort <= 0 || vars.Option.BroadcastMsgPort > 65535 {
		return fmt.Errorf("无效的广播消息 TCP 端口 (%d). \n", vars.Option.BroadcastMsgPort)
	}
	payloadMsg := g.Map{
		"ip":   vars.Option.BroadcastMsgIP,
		"port": vars.Option.BroadcastMsgPort,
	}
	payloadJSON, err = gjson.Encode(payloadMsg)
	if err != nil {
		return
	}
	return
}

// Start 开启广播
func Start() error {
	if !Enabled {
		return enabledErrorMsg
	}
	if err := LoadCfg(); err != nil {
		isStarting = false
		return err
	}
	if isStarting {
		//log.Printf("已经是启动的状态\n")
		resetWorker()
		return nil
	}
	startChannel <- true
	return nil
}

func resetWorker() {
	defer func() {
		wkProgress = gtimer.AddSingleton(time.Duration(vars.Option.BroadcastLoopInterval)*time.Second, readIPProgress)
	}()
	if wkProgress != nil {
		wkProgress.Close()
	}
}

// Stop 关闭广播
func Stop() {
	if Status() {
		startChannel <- false
	}
}

// Status UDP 广播服务状态
func Status() bool {
	return isStarting && Enabled
}

// broadcast 广播
func broadcast() {
	for {
		select {
		case ip := <-ipChannel:
			//log.Printf("broadcast ip: %+v", ip)
			_, err := Server.WriteToUDP(payloadJSON, &net.UDPAddr{
				IP:   ip,
				Port: vars.Option.BroadcastUdpPort,
			})
			if err != nil {
				v.LogTcp().Debugf("udp write: %s\n", err)
			}

		case startStatus := <-startChannel:
			isStarting = false
			if startStatus {
				isStarting = true
				resetWorker()
			}
		}

	}
}

// 读取 IP 地址
func readIPProgress() {
	if !isStarting {
		wkProgress.Close()
		return
	}
	redisVar, _ := v.Redis().DoVar("SMEMBERS", vars.RedisNetworkIPPool)
	for _, ip := range redisVar.Strings() {
		if nw := net.ParseIP(ip); nw != nil {
			// 若在线则跳过发送数据的操作
			if checkOnlineIP(nw.String()) {
				continue
			}
			ipChannel <- nw
			time.Sleep(time.Duration(vars.Option.BroadcastSendInterval) * time.Millisecond)
		}
	}
}

// 判断该 IP 的终端是否在线
func checkOnlineIP(ip string) bool {
	terminals := client.All()
	if terminals.IsEmpty() {
		return false
	}
	var isOnline bool
	terminals.Iterator(func(k string, v interface{}) bool {
		if v.(*vars.Terminal).Prop.IP == ip {
			isOnline = true
			return false
		}
		return true
	})
	return isOnline
}

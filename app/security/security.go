// 安全模块 - 提供全局安全功能接口

package security

import (
	"ccserver/pix_log"
)

// CheckConnectionAllowed 检查连接是否被允许（全局函数）
func CheckConnectionAllowed(imsi, ip string) (allowed bool, reason string) {
	if GlobalConnectionGuard == nil {
		return true, ""
	}
	return GlobalConnectionGuard.CheckConnectionAllowed(imsi, ip)
}

// RecordFailedConnection 记录连接失败（全局函数）
func RecordFailedConnection(imsi, ip, reason string) {
	if GlobalConnectionGuard == nil {
		return
	}
	GlobalConnectionGuard.RecordFailedConnection(imsi, ip, reason)
}

// RecordSuccessfulConnection 记录连接成功
func RecordSuccessfulConnection(imsi, ip string) {
	if GlobalConnectionGuard == nil {
		return
	}
	
	// 连接成功时，可以考虑重置某些计数器或记录成功日志
	// 这里暂时只记录日志
	pix_log.InfoWithIMSI(imsi, "✅ [Security] 设备连接成功：IP=%s", ip)
}

// GetSecurityStats 获取安全统计信息（全局函数）
func GetSecurityStats() map[string]interface{} {
	if GlobalConnectionGuard == nil {
		return map[string]interface{}{
			"connection_guard": map[string]interface{}{
				"enabled": false,
			},
		}
	}
	
	return map[string]interface{}{
		"connection_guard": GlobalConnectionGuard.GetStats(),
	}
}

// LogSecurityEvent 记录安全事件
func LogSecurityEvent(eventType, imsi, ip, details string) {
	pix_log.WarningWithIMSI(imsi, "🔒 [SECURITY] %s: IP=%s, Details=%s", eventType, ip, details)
}

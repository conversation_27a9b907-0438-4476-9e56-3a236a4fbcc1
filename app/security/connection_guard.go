// 连接防护模块 - 防止未授权设备频繁连接攻击
// 实现基于IMSI和IP的连接限制、指数退避、日志抑制等功能

package security

import (
	"ccserver/app/vars"
	"ccserver/pix_log"
	"fmt"
	"sync"
	"time"
)

// ConnectionAttempt 连接尝试记录
type ConnectionAttempt struct {
	IMSI           string    `json:"imsi"`
	IP             string    `json:"ip"`
	AttemptCount   int       `json:"attempt_count"`
	FirstAttempt   time.Time `json:"first_attempt"`
	LastAttempt    time.Time `json:"last_attempt"`
	BannedUntil    time.Time `json:"banned_until"`
	LastLogTime    time.Time `json:"last_log_time"`    // 最后日志记录时间
	CurrentBanTime time.Duration `json:"current_ban_time"` // 当前禁止时长
}

// IPConnectionTracker IP连接跟踪器
type IPConnectionTracker struct {
	IP              string    `json:"ip"`
	ConnectionCount int       `json:"connection_count"`
	BannedUntil     time.Time `json:"banned_until"`
	LastUpdate      time.Time `json:"last_update"`
}

// ConnectionGuard 连接防护器
type ConnectionGuard struct {
	// IMSI级别的连接尝试记录
	imsiAttempts map[string]*ConnectionAttempt
	// IP级别的连接跟踪
	ipTrackers map[string]*IPConnectionTracker
	// 读写锁
	mutex sync.RWMutex
	// 清理定时器
	cleanupTicker *time.Ticker
	// 停止信号
	stopChan chan bool
}

// 全局连接防护器实例
var GlobalConnectionGuard *ConnectionGuard

// InitConnectionGuard 初始化连接防护器
func InitConnectionGuard() {
	if !vars.Config.Security.ConnectionLimit.Enabled {
		pix_log.Info("🔒 [ConnectionGuard] 连接限制功能已禁用")
		return
	}

	GlobalConnectionGuard = &ConnectionGuard{
		imsiAttempts:  make(map[string]*ConnectionAttempt),
		ipTrackers:    make(map[string]*IPConnectionTracker),
		cleanupTicker: time.NewTicker(vars.Config.Security.ConnectionLimit.CleanupInterval),
		stopChan:      make(chan bool),
	}

	// 启动清理协程
	go GlobalConnectionGuard.startCleanupRoutine()

	pix_log.Info("🔒 [ConnectionGuard] 连接防护器已启动，清理间隔: %v", 
		vars.Config.Security.ConnectionLimit.CleanupInterval)
}

// StopConnectionGuard 停止连接防护器
func StopConnectionGuard() {
	if GlobalConnectionGuard != nil {
		GlobalConnectionGuard.stopChan <- true
		GlobalConnectionGuard.cleanupTicker.Stop()
		pix_log.Info("🔒 [ConnectionGuard] 连接防护器已停止")
	}
}

// CheckConnectionAllowed 检查连接是否被允许（专注IMSI级别防护）
func (cg *ConnectionGuard) CheckConnectionAllowed(imsi, ip string) (allowed bool, reason string) {
	if !vars.Config.Security.ConnectionLimit.Enabled {
		return true, ""
	}

	// 如果IMSI为空，跳过检查（用于IP预检查场景）
	if imsi == "" {
		return true, ""
	}

	cg.mutex.Lock()
	defer cg.mutex.Unlock()

	now := time.Now()

	// 检查IMSI级别限制
	attempt, exists := cg.imsiAttempts[imsi]
	if !exists {
		// 首次连接，创建记录
		cg.imsiAttempts[imsi] = &ConnectionAttempt{
			IMSI:           imsi,
			IP:             ip,
			AttemptCount:   1,
			FirstAttempt:   now,
			LastAttempt:    now,
			CurrentBanTime: 0,
		}
		pix_log.InfoWithIMSI(imsi, "📝 [ConnectionGuard] 新设备首次连接：IP=%s", ip)
		return true, ""
	}

	// 检查是否在禁止期内
	if now.Before(attempt.BannedUntil) {
		// 在禁止期内，增加尝试次数并延长禁止时间
		attempt.AttemptCount++
		attempt.LastAttempt = now

		// 计算新的禁止时长（指数退避）
		newBanDuration := time.Duration(float64(attempt.CurrentBanTime) * vars.Config.Security.ConnectionLimit.BackoffMultiplier)
		if newBanDuration > vars.Config.Security.ConnectionLimit.MaxBanDuration {
			newBanDuration = vars.Config.Security.ConnectionLimit.MaxBanDuration
		}
		attempt.CurrentBanTime = newBanDuration
		attempt.BannedUntil = now.Add(newBanDuration)

		// 日志抑制：只在间隔时间后才记录日志
		if cg.shouldLogAttempt(attempt, now) {
			pix_log.WarningWithIMSI(imsi, "🚫 [ConnectionGuard] 设备在禁止期内尝试连接：IP=%s, 尝试次数=%d, 禁止至=%s",
				ip, attempt.AttemptCount, attempt.BannedUntil.Format("2006-01-02 15:04:05"))
			attempt.LastLogTime = now
		}

		return false, fmt.Sprintf("设备被禁止连接至 %s", attempt.BannedUntil.Format("2006-01-02 15:04:05"))
	}

	// 不在禁止期内，更新尝试记录
	attempt.AttemptCount++
	attempt.LastAttempt = now
	attempt.IP = ip // 更新IP（可能变化）

	pix_log.InfoWithIMSI(imsi, "✅ [ConnectionGuard] 设备连接检查通过：IP=%s, 尝试次数=%d", ip, attempt.AttemptCount)
	return true, ""
}

// RecordFailedConnection 记录连接失败
func (cg *ConnectionGuard) RecordFailedConnection(imsi, ip, reason string) {
	if !vars.Config.Security.ConnectionLimit.Enabled {
		return
	}

	cg.mutex.Lock()
	defer cg.mutex.Unlock()

	now := time.Now()

	// 更新IP跟踪信息（仅用于统计）
	cg.updateIPTracker(ip, now)

	attempt, exists := cg.imsiAttempts[imsi]
	if !exists {
		// 不应该发生，但为了安全起见创建记录
		attempt = &ConnectionAttempt{
			IMSI:           imsi,
			IP:             ip,
			AttemptCount:   1,
			FirstAttempt:   now,
			LastAttempt:    now,
			CurrentBanTime: 0,
		}
		cg.imsiAttempts[imsi] = attempt
	}

	// 如果达到最大尝试次数，开始禁止
	if attempt.AttemptCount >= vars.Config.Security.ConnectionLimit.MaxAttempts {
		if attempt.CurrentBanTime == 0 {
			attempt.CurrentBanTime = vars.Config.Security.ConnectionLimit.InitialBanDuration
		} else {
			// 指数退避
			attempt.CurrentBanTime = time.Duration(float64(attempt.CurrentBanTime) * vars.Config.Security.ConnectionLimit.BackoffMultiplier)
			if attempt.CurrentBanTime > vars.Config.Security.ConnectionLimit.MaxBanDuration {
				attempt.CurrentBanTime = vars.Config.Security.ConnectionLimit.MaxBanDuration
			}
		}
		
		attempt.BannedUntil = now.Add(attempt.CurrentBanTime)

		// 记录禁止日志（不受抑制影响）
		pix_log.WarningWithIMSI(imsi, "🔒 [ConnectionGuard] 设备因频繁失败连接被禁止：IP=%s, 失败次数=%d, 禁止时长=%v, 禁止至=%s",
			ip, attempt.AttemptCount, attempt.CurrentBanTime, attempt.BannedUntil.Format("2006-01-02 15:04:05"))
	} else {
		// 日志抑制：只在间隔时间后才记录日志
		if cg.shouldLogAttempt(attempt, now) {
			pix_log.WarningWithIMSI(imsi, "⚠️ [ConnectionGuard] 设备连接失败：IP=%s, 失败次数=%d/%d, 原因=%s",
				ip, attempt.AttemptCount, vars.Config.Security.ConnectionLimit.MaxAttempts, reason)
			attempt.LastLogTime = now
		}
	}
}

// updateIPTracker 更新IP跟踪信息（仅用于统计，不进行限制）
func (cg *ConnectionGuard) updateIPTracker(ip string, now time.Time) {
	tracker, exists := cg.ipTrackers[ip]
	if !exists {
		// 首次连接，创建跟踪记录
		cg.ipTrackers[ip] = &IPConnectionTracker{
			IP:              ip,
			ConnectionCount: 1,
			LastUpdate:      now,
		}
		return
	}

	// 更新连接计数和时间（仅用于统计）
	tracker.ConnectionCount++
	tracker.LastUpdate = now
}

// shouldLogAttempt 判断是否应该记录日志（日志抑制）
func (cg *ConnectionGuard) shouldLogAttempt(attempt *ConnectionAttempt, now time.Time) bool {
	return attempt.LastLogTime.IsZero() || 
		now.Sub(attempt.LastLogTime) >= vars.Config.Security.ConnectionLimit.LogSuppressionDuration
}

// startCleanupRoutine 启动清理协程
func (cg *ConnectionGuard) startCleanupRoutine() {
	for {
		select {
		case <-cg.cleanupTicker.C:
			cg.cleanup()
		case <-cg.stopChan:
			return
		}
	}
}

// cleanup 清理过期记录
func (cg *ConnectionGuard) cleanup() {
	cg.mutex.Lock()
	defer cg.mutex.Unlock()

	now := time.Now()
	expiration := vars.Config.Security.ConnectionLimit.CacheExpiration

	// 清理IMSI记录
	imsiCleaned := 0
	for imsi, attempt := range cg.imsiAttempts {
		if now.Sub(attempt.LastAttempt) > expiration && now.After(attempt.BannedUntil) {
			delete(cg.imsiAttempts, imsi)
			imsiCleaned++
		}
	}

	// 清理IP记录（仅基于时间过期，无禁止状态）
	ipCleaned := 0
	for ip, tracker := range cg.ipTrackers {
		if now.Sub(tracker.LastUpdate) > expiration {
			delete(cg.ipTrackers, ip)
			ipCleaned++
		}
	}

	if imsiCleaned > 0 || ipCleaned > 0 {
		pix_log.Info("🧹 [ConnectionGuard] 清理完成：IMSI记录=%d, IP记录=%d, 剩余IMSI=%d, 剩余IP=%d",
			imsiCleaned, ipCleaned, len(cg.imsiAttempts), len(cg.ipTrackers))
	}
}

// GetStats 获取统计信息
func (cg *ConnectionGuard) GetStats() map[string]interface{} {
	if cg == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	cg.mutex.RLock()
	defer cg.mutex.RUnlock()

	now := time.Now()
	activeBans := 0

	for _, attempt := range cg.imsiAttempts {
		if now.Before(attempt.BannedUntil) {
			activeBans++
		}
	}

	return map[string]interface{}{
		"enabled":             vars.Config.Security.ConnectionLimit.Enabled,
		"total_imsi_records":  len(cg.imsiAttempts),
		"total_ip_records":    len(cg.ipTrackers), // 仅用于统计
		"active_imsi_bans":    activeBans,
		"ip_limit_enabled":    vars.Config.Security.IPLimit.Enabled, // 显示IP限制状态
		"config": map[string]interface{}{
			"initial_ban_duration":   vars.Config.Security.ConnectionLimit.InitialBanDuration,
			"max_ban_duration":       vars.Config.Security.ConnectionLimit.MaxBanDuration,
			"backoff_multiplier":     vars.Config.Security.ConnectionLimit.BackoffMultiplier,
			"max_attempts":           vars.Config.Security.ConnectionLimit.MaxAttempts,
			"log_suppression_duration": vars.Config.Security.ConnectionLimit.LogSuppressionDuration,
		},
	}
}

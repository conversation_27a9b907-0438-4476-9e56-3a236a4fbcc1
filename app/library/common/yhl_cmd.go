package common

import "time"

const (
	YHL_MSG_START_CHAR1      = '@'
	YHL_MSG_START_CHAR2      = '<'
	YHL_MSG_RESP_START_CHAR2 = '>'
)

func MakeCmd(cmd uint16, billNo uint32, msgBody []byte) []byte {
	var outLen uint16 = 0
	var idx uint16 = 0

	var tempBuff []byte = make([]byte, 2056, 2056)

	tempBuff[outLen] = YHL_MSG_START_CHAR1
	outLen++

	tempBuff[outLen] = YHL_MSG_RESP_START_CHAR2
	outLen++

	//长度
	tempBuff[outLen] = byte(0>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(0) & 0xff
	outLen++

	//流水号从0开始递增，为发送信息的流水号，用于接收方检测是否有信息的丢失。程序开始运行时等于零，发送第一各消息时开始计数，到最大数后自动归零
	//var billNo uint32 = msgHeader.billNo
	tempBuff[outLen] = byte(billNo>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo) & 0xff
	outLen++
	billNo++

	//消息体格式 0x0000: 消息体为UTF8编码的JSON格式的数据，或JSON格式的数据经过加密后得到的数据  0x0001: 消息体为字节序列，即任意数据
	tempBuff[outLen] = 0
	outLen++
	tempBuff[outLen] = 0
	outLen++

	//协议版本号; 0x01: 表示版本1，0x02: 表示版本2，以此类推
	tempBuff[outLen] = 0x01
	outLen++

	//加密方式; 0x00: 表示未加密 0x01:表示用国密SM4 128位加密
	tempBuff[outLen] = 0
	outLen++

	//随机数
	tempBuff[outLen] = byte(0>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(0>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(0>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(0 & 0xff)
	outLen++

	//时间戳
	ts := time.Now().Unix()
	tempBuff[outLen] = byte(ts>>56) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>48) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>40) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>32) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts & 0xff)
	outLen++

	//预留
	for idx = 0; idx < 24; idx++ {
		tempBuff[outLen] = byte(0 & 0xff)
		outLen++
	}

	var createdMsgBody []byte

	if len(msgBody) != 0 {
		createdMsgBody = msgBody
	}
	/*
		else{
			switch cmd{
			case protocol.Register:
				//createdMsgBody = makeRespRegisterMsgBody(res, msgHeader.billNo)
				break
			case protocol.Heartbeat:
				break
			}
		}*/
	for idx := 0; idx < len(createdMsgBody); idx++ {
		tempBuff[outLen] = createdMsgBody[idx]
		outLen++
	}

	//长度
	tempBuff[2] = byte((outLen-2-2+2)>>8) & 0xff //减去头@> 并加上两字节的crc16校验
	tempBuff[3] = byte(outLen-2-2+2) & 0xff

	//crc16校验和
	checksum := Crc16CheckSum(tempBuff[2:], uint32(outLen-2))
	tempBuff[outLen] = byte(checksum>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(checksum) & 0xff
	outLen++

	return tempBuff[0:outLen]
}

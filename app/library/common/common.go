package common

import (
	"bytes"
	"ccserver/app/module/client"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"os"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
	"github.com/tjfoc/gmsm/sm4"
	"io/ioutil"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

var (
	// UserLevelArr 用户等级组
	UserLevelArr = map[vars.UserLevel]string{
		vars.SGLevel: "h1",
		vars.SZLevel: "h2",
		vars.XGLevel: "l1",
		vars.XZLevel: "l2",
	}
)

const (
	UserLevelManager  = "manager"
	UserLevelEditable = "editable"
)

// GetRoles 获取角色列表
func GetRoles(level vars.UserLevel) []string {
	// var roles []string
	roles := make([]string, 0)
	// roles := []string{"public"}
	roles = append(roles, UserLevelArr[level])
	return roles
}

// GetRoleList 获取角色列表
func GetRoleList(level vars.UserLevel) []string {
	// var roles []string
	// roles := make([]string, 0)
	roles := []string{"public"}
	if IsShengJiLevel(level) {
		roles = append(roles, UserLevelManager)
	}
	if IsEditableLevel(level) {
		roles = append(roles, UserLevelEditable)
	}
	return roles
}

// IsShengJiLevel 是否是省级管理员或省级值班员
func IsShengJiLevel(level vars.UserLevel) bool {
	return level < vars.XGLevel
}

// IsEditableLevel 可写权限账号, 单数为具有管理权限
func IsEditableLevel(level vars.UserLevel) bool {
	return level%2 == 1
}

// ValidateUsername 校验用户名
func ValidateUsername(pwd string) error {
	// rule := `required|regex:^[a-zA-Z]{1}\w{2,15}$`
	// msgs := map[string]string{
	//	"required": "请输入用户名",
	//	"regex":    "请输入以字母开头的3到16位用户名",
	// }
	// if e := gvalid.Check(pwd, rule, msgs); e != nil {
	//	return errors.New(e.FirstString())
	// }
	return nil
}

// ValidatePassword 校验密码
func ValidatePassword(pwd string) error {
	// rule := `required|regex:^[a-zA-Z]{1}\w{5,15}$`
	// msg := map[string]string{
	//	"required": "请输入密码",
	//	"regex":    "请输入以字母开头的6到16位密码",
	// }
	// if e := gvalid.Check(pwd, rule, msg); e != nil {
	//	return errors.New(e.FirstString())
	// }
	return nil
}

// CreateDownloadURL 创建下载地址
func CreateDownloadURL(name string) string {
	return fmt.Sprintf("http://%s/files/firmwares/%s", vars.Option.BusinessIP, name)
}

// CreateUpgradeData 生成终端升级消息
func CreateUpgradeData(name, md5 string) ([]byte, error) {
	return gjson.Encode(g.Map{
		"operate": protocol.Upgrade,
		"msgid":   0,
		"md5":     md5,
		"url":     CreateDownloadURL(name),
	})
}

// GetDateTimeOfWeek  获取第N周的第M天的此时
// differ 距离本周间隔, 0为本周, 正数为后n周, 负数为前n周
// weekday 获取该周的第M天
func GetDateTimeOfWeek(differ int, getWeekday time.Weekday) time.Time {
	now := time.Now()
	nowWeekday := now.Weekday()
	if nowWeekday == 0 {
		nowWeekday = 7
	}
	if getWeekday == 0 {
		getWeekday = 7
	}
	offset := int(getWeekday - nowWeekday)
	offset += 7 * differ
	return now.Local().AddDate(0, 0, offset)
}

// GetZeroTime 获取零点的时间
func GetZeroTime(t *time.Time) {
	*t = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetMidnightTime 获取 23:59:59 的时间
func GetMidnightTime(t *time.Time) {
	*t = time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
}

// GetMondayTs 获取本周一的时间戳
func GetMondayTs() int64 {
	monday := GetDateTimeOfWeek(0, time.Monday)
	GetZeroTime(&monday)
	return monday.Unix()
}

// GetWeekStart 获取指定年份、指定周(索引从1开始)的开始日期(星期一)
func GetWeekStart(year, week int) time.Time {
	// Start from the middle of the year:
	t := time.Date(year, 7, 1, 0, 0, 0, 0, time.Local)

	// Roll back to Monday:
	if wd := t.Weekday(); wd == time.Sunday {
		t = t.AddDate(0, 0, -6)
	} else {
		t = t.AddDate(0, 0, -int(wd)+1)
	}

	// Difference in weeks:
	_, w := t.ISOWeek()
	t = t.AddDate(0, 0, (week-w)*7)

	return t
}

// GetWeekDateRange 获取指定年份、指定周(索引从1开始)的开始日期(星期一)和结束日期(下周一的00:00:00)
func GetWeekDateRange(year, week int) (start, end time.Time) {
	start = GetWeekStart(year, week)
	end = start.AddDate(0, 0, 7)
	return start, end
}

// InArray 判断是否在数组中
func InArray(need int, haystack []int) bool {
	for _, item := range haystack {
		if item == need {
			return true
		}
	}
	return false
}

// InArrayUint 判断是否在数组中
func InArrayUint(need uint8, haystack []uint8) bool {
	for _, item := range haystack {
		if item == need {
			return true
		}
	}
	return false
}

// Str2DEC 二进制字符串转十进制
func Str2DEC(s string) (num int) {
	l := len(s)
	for i := l - 1; i >= 0; i-- {
		num += (int(s[l-i-1]) & 0xf) << uint8(i)
	}
	return
}

func Sm4Decrypt(key, iv, cipherText []byte) ([]byte, error) {
	block, err := sm4.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(cipherText))
	blockMode.CryptBlocks(origData, cipherText)
	origData = pkcs5UnPadding(origData)
	return origData, nil
}

// pkcs5填充
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

func pkcs5UnPadding(src []byte) []byte {
	length := len(src)
	unpadding := int(src[length-1])
	return src[:(length - unpadding)]
}

func ChangeStringToInt(str string) int {
	r, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return r
}

// SendToDevice 消息下发给指定终端
func SendToDevice(deviceId string, data []byte) error {
	if !client.All().Contains(deviceId) {
		return errors.New("终端与服务器的连接已断开")
	}
	client.Get(deviceId).(*vars.Terminal).Prop.Message <- data

	// // 将二进制数据转换为可读的JSON格式
	// var jsonData interface{}
	// if err := json.Unmarshal(data, &jsonData); err != nil {
	// 	pix_log.Warning("下发消息解析JSON失败: %v, raw data: %s", err, string(data))
	// } else {
	// 	prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
	// 	pix_log.Info("下发消息到设备[%s]: %s", deviceId, string(prettyJSON))
	// }

	return nil
}

// GetFirstDateOfMonth 获取传入的时间所在月份的第一天，即某月第一天的0点。如传入time.Now(), 返回当前月份的第一天0点时间。
func GetFirstDateOfMonth(d time.Time) time.Time {
	d = d.AddDate(0, 0, -d.Day()+1)
	return GetZeroTime2(d)
}

// GetZeroTime2 获取某一天的0点时间
func GetZeroTime2(d time.Time) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
}

// GetZeroTime3 获取某一天的某点时间
func GetZeroTime3(d time.Time, hour int) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), hour, 0, 0, 0, d.Location())
}

// ChangeTimeToInt 把字符串时间变成时间戳
func ChangeTimeToInt(timeStr string) int64 {
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")
	theTime, _ := time.ParseInLocation(timeLayout, timeStr, loc)
	return theTime.Unix()
}

// CheckKey 证书认证
func CheckKey() (error, int) {

	// 判断是否为开发模式（通过环境变量判断）
	// 开发环境通常使用 config.dev.toml，可以通过检查配置文件名或环境变量来判断
	if isDevMode() {
		return nil, 999 // 开发模式返回较大授权数
	}

	key := []byte("78fe3379a291ece5")
	iv := make([]byte, sm4.BlockSize)
	data, err := sm4.ReadKeyFromPemFile("key.pem", nil)

	if err != nil {
		fmt.Println("证书文件读取失败，" + err.Error())
		return err, 0
	}
	deData, err := Sm4Decrypt(key, iv, data)
	// fmt.Println("deData:", string(deData))

	date := string(deData[0:10])
	// fmt.Println("date:", date)

	now := time.Now().Unix()
	dateInt := ChangeStringToInt(date)
	if int64(dateInt) < now {
		fmt.Println("证书已过期")
		return err, 0
	}

	maxNumString := string(deData[30:])
	maxNum := ChangeStringToInt(maxNumString)
	end := string(deData[10:30])

	diskName := vars.Config.Business.DiskName
	cmdStr := "udevadm info --query=all --name=" + diskName + " | grep ID_SERIAL | head -n 1"
	// fmt.Println("cmd:", cmdStr)
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	output, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("无法获取命令的标准输出管道", err.Error())
		return err, 0
	}

	// 执行Linux命令
	if err := cmd.Start(); err != nil {
		fmt.Println("Linux命令执行失败，请检查命令输入是否有误", err.Error())
		return err, 0
	}
	// 读取输出
	prints, err := ioutil.ReadAll(output)
	if err != nil {
		fmt.Println("打印异常，请检查")
		return err, 0
	}
	if err := cmd.Wait(); err != nil {
		fmt.Println("Wait", err.Error())
		return err, 0
	}

	outs := string(prints)

	// fmt.Println("out:", outs)
	index := strings.Index(outs, "=")
	// fmt.Println("index:", outs)
	diskNo := outs[index+1 : len(outs)-1]

	// fmt.Println("end:", end)
	if end != diskNo {
		fmt.Println("证书认证失败")
		return err, 0
	}

	return nil, maxNum
}

// ConvertToBin bin表示转化后的位数
func ConvertToBin(n int, bin int) string {
	var b string
	switch {
	case n == 0:
		for i := 0; i < bin; i++ {
			b += "0"
		}
	case n > 0:
		for ; n > 0; n /= 2 {
			b = strconv.Itoa(n%2) + b
		}
		j := bin - len(b)
		for i := 0; i < j; i++ {
			b = "0" + b
		}
	case n < 0:
		n = n * -1
		s := ConvertToBin(n, bin)
		// 取反
		for i := 0; i < len(s); i++ {
			if s[i:i+1] == "1" {
				b += "0"
			} else {
				b += "1"
			}
		}
		n, err := strconv.ParseInt(b, 2, 64)
		if err != nil {
			pix_log.Error(err.Error())
		}
		b = ConvertToBin(int(n+1), bin)
	}
	return b
}

func GenerateHmacSHA256Signature(key string, message string) string {
	// 将密钥转换为字节数组
	keyBytes := []byte(key)

	// 创建HMAC-SHA256哈希对象
	hash := hmac.New(sha256.New, keyBytes)

	// 写入要签名的消息
	hash.Write([]byte(message))

	// 计算签名并转换为十六进制字符串
	signature := hex.EncodeToString(hash.Sum(nil))

	return signature
}

/**
 * 使用AK&SK初始化账号Client
 * @param accessKeyId
 * @param accessKeySecret
 * @return Client
 * @throws Exception
 */
func CreateClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi20170525.Client, _err error) {
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: accessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: accessKeySecret,
	}
	// Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	_result = &dysmsapi20170525.Client{}
	_result, _err = dysmsapi20170525.NewClient(config)
	return _result, _err
}

func SendSms(phone string, imsi string, name string, num string, time string) error {
	signName := "pixmoving"
	templateCode := "SMS_465336288"
	myCode := "{\"imsi\":" + imsi + ",\"name\":" + name + ",\"num\":" + num + ", \"time\":" + time + "}"
	AccessKeyId := "LTAI5tKnqGSPn3shdAEqNyJx"
	AccessKeySecret := "******************************"
	createClient, _err := CreateClient(tea.String(AccessKeyId), tea.String(AccessKeySecret))
	if _err != nil {
		return _err
	}

	request := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  &phone,
		TemplateCode:  &templateCode,
		SignName:      &signName,
		TemplateParam: &myCode,
	}

	_, err := createClient.SendSms(request)
	if err != nil {
		return err
	}

	return nil
}

// isDevMode 判断是否为开发模式
// 通过检查环境变量GF_GCFG_FILE或配置文件名来判断
func isDevMode() bool {
	// 检查环境变量
	if configFile := os.Getenv("GF_GCFG_FILE"); configFile != "" {
		return strings.Contains(configFile, "dev")
	}

	// 检查默认配置文件是否为开发配置
	// 如果没有指定配置文件，默认为开发模式
	return true
}

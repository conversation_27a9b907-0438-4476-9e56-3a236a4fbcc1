package redis_util

import (
	"ccserver/app/library/v"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"strconv"
)

// RedisUtil Redis工具类，封装常用Redis操作
type RedisUtil struct {
	logPrefix string // 日志前缀，用于标识调用模块
}

// NewRedisUtil 创建Redis工具实例
func NewRedisUtil(logPrefix string) *RedisUtil {
	return &RedisUtil{
		logPrefix: logPrefix,
	}
}

// 默认实例
var Default = NewRedisUtil("REDIS")

// ============================================================================
// Hash 操作
// ============================================================================

// HSet 设置Hash字段值
func (r *RedisUtil) HSet(key, field string, value interface{}) error {
	_, err := v.Redis().Do("HSET", key, field, value)
	if err != nil {
		pix_log.Error("[%s] HSet失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return err
	}
	return nil
}

// HGet 获取Hash字段值
func (r *RedisUtil) HGet(key, field string) (string, error) {
	result, err := v.Redis().DoVar("HGET", key, field)
	if err != nil {
		pix_log.Error("[%s] HGet失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return "", err
	}
	return result.String(), nil
}

// HGetInt64 获取Hash字段值并转换为int64
func (r *RedisUtil) HGetInt64(key, field string) (int64, error) {
	result, err := v.Redis().DoVar("HGET", key, field)
	if err != nil {
		pix_log.Error("[%s] HGetInt64失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return 0, err
	}
	return result.Int64(), nil
}

// HGetFloat64 获取Hash字段值并转换为float64
func (r *RedisUtil) HGetFloat64(key, field string) (float64, error) {
	result, err := v.Redis().DoVar("HGET", key, field)
	if err != nil {
		pix_log.Error("[%s] HGetFloat64失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return 0, err
	}
	return result.Float64(), nil
}

// HDel 删除Hash字段
func (r *RedisUtil) HDel(key, field string) error {
	_, err := v.Redis().Do("HDEL", key, field)
	if err != nil {
		pix_log.Error("[%s] HDel失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return err
	}
	return nil
}

// HGetAll 获取Hash所有字段
func (r *RedisUtil) HGetAll(key string) (map[string]string, error) {
	result, err := v.Redis().DoVar("HGETALL", key)
	if err != nil {
		pix_log.Error("[%s] HGetAll失败: key=%s, error=%v", r.logPrefix, key, err)
		return nil, err
	}

	resultMap := make(map[string]string)
	array := result.Interfaces()

	for i := 0; i < len(array); i += 2 {
		if i+1 < len(array) {
			field := fmt.Sprintf("%v", array[i])
			value := fmt.Sprintf("%v", array[i+1])
			resultMap[field] = value
		}
	}

	return resultMap, nil
}

// HGetAllInt64 获取Hash所有字段并转换为map[string]int64
func (r *RedisUtil) HGetAllInt64(key string) (map[string]int64, error) {
	result, err := v.Redis().DoVar("HGETALL", key)
	if err != nil {
		pix_log.Error("[%s] HGetAllInt64失败: key=%s, error=%v", r.logPrefix, key, err)
		return nil, err
	}

	resultMap := make(map[string]int64)
	array := result.Interfaces()

	for i := 0; i < len(array); i += 2 {
		if i+1 < len(array) {
			field := fmt.Sprintf("%v", array[i])
			valueStr := fmt.Sprintf("%v", array[i+1])
			if value, parseErr := strconv.ParseInt(valueStr, 10, 64); parseErr == nil {
				resultMap[field] = value
			} else {
				pix_log.Error("[%s] HGetAllInt64解析失败: key=%s, field=%s, value=%s, error=%v", 
					r.logPrefix, key, field, valueStr, parseErr)
			}
		}
	}

	return resultMap, nil
}

// ============================================================================
// String 操作
// ============================================================================

// Set 设置字符串值
func (r *RedisUtil) Set(key string, value interface{}) error {
	_, err := v.Redis().Do("SET", key, value)
	if err != nil {
		pix_log.Error("[%s] Set失败: key=%s, error=%v", r.logPrefix, key, err)
		return err
	}
	return nil
}

// SetEx 设置字符串值并指定过期时间
func (r *RedisUtil) SetEx(key string, value interface{}, expireSeconds int) error {
	_, err := v.Redis().Do("SETEX", key, expireSeconds, value)
	if err != nil {
		pix_log.Error("[%s] SetEx失败: key=%s, expire=%d, error=%v", r.logPrefix, key, expireSeconds, err)
		return err
	}
	return nil
}

// Get 获取字符串值
func (r *RedisUtil) Get(key string) (string, error) {
	result, err := v.Redis().DoVar("GET", key)
	if err != nil {
		pix_log.Error("[%s] Get失败: key=%s, error=%v", r.logPrefix, key, err)
		return "", err
	}
	return result.String(), nil
}

// GetInt64 获取字符串值并转换为int64
func (r *RedisUtil) GetInt64(key string) (int64, error) {
	result, err := v.Redis().DoVar("GET", key)
	if err != nil {
		pix_log.Error("[%s] GetInt64失败: key=%s, error=%v", r.logPrefix, key, err)
		return 0, err
	}
	return result.Int64(), nil
}

// ============================================================================
// Set 操作
// ============================================================================

// SAdd 向集合添加成员
func (r *RedisUtil) SAdd(key string, members ...interface{}) error {
	args := []interface{}{key}
	args = append(args, members...)
	_, err := v.Redis().Do("SADD", args...)
	if err != nil {
		pix_log.Error("[%s] SAdd失败: key=%s, error=%v", r.logPrefix, key, err)
		return err
	}
	return nil
}

// SRem 从集合移除成员
func (r *RedisUtil) SRem(key string, members ...interface{}) error {
	args := []interface{}{key}
	args = append(args, members...)
	_, err := v.Redis().Do("SREM", args...)
	if err != nil {
		pix_log.Error("[%s] SRem失败: key=%s, error=%v", r.logPrefix, key, err)
		return err
	}
	return nil
}

// SCard 获取集合成员数量
func (r *RedisUtil) SCard(key string) (int64, error) {
	result, err := v.Redis().DoVar("SCARD", key)
	if err != nil {
		pix_log.Error("[%s] SCard失败: key=%s, error=%v", r.logPrefix, key, err)
		return 0, err
	}
	return result.Int64(), nil
}

// ============================================================================
// List 操作
// ============================================================================

// LPush 向列表左侧添加元素
func (r *RedisUtil) LPush(key string, values ...interface{}) error {
	args := []interface{}{key}
	args = append(args, values...)
	_, err := v.Redis().Do("LPUSH", args...)
	if err != nil {
		pix_log.Error("[%s] LPush失败: key=%s, error=%v", r.logPrefix, key, err)
		return err
	}
	return nil
}

// RPop 从列表右侧弹出元素
func (r *RedisUtil) RPop(key string) (string, error) {
	result, err := v.Redis().DoVar("RPOP", key)
	if err != nil {
		pix_log.Error("[%s] RPop失败: key=%s, error=%v", r.logPrefix, key, err)
		return "", err
	}
	return result.String(), nil
}

// ============================================================================
// 通用操作
// ============================================================================

// Del 删除键
func (r *RedisUtil) Del(keys ...string) error {
	args := make([]interface{}, len(keys))
	for i, key := range keys {
		args[i] = key
	}
	_, err := v.Redis().Do("DEL", args...)
	if err != nil {
		pix_log.Error("[%s] Del失败: keys=%v, error=%v", r.logPrefix, keys, err)
		return err
	}
	return nil
}

// Exists 检查键是否存在
func (r *RedisUtil) Exists(key string) (bool, error) {
	result, err := v.Redis().DoVar("EXISTS", key)
	if err != nil {
		pix_log.Error("[%s] Exists失败: key=%s, error=%v", r.logPrefix, key, err)
		return false, err
	}
	return result.Int64() > 0, nil
}

// Expire 设置键过期时间
func (r *RedisUtil) Expire(key string, seconds int) error {
	_, err := v.Redis().Do("EXPIRE", key, seconds)
	if err != nil {
		pix_log.Error("[%s] Expire失败: key=%s, seconds=%d, error=%v", r.logPrefix, key, seconds, err)
		return err
	}
	return nil
}

// ============================================================================
// JSON 操作辅助方法
// ============================================================================

// SetJSON 设置JSON对象到Hash字段
func (r *RedisUtil) SetJSON(key, field string, obj interface{}) error {
	jsonData, err := json.Marshal(obj)
	if err != nil {
		pix_log.Error("[%s] SetJSON序列化失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return err
	}
	return r.HSet(key, field, string(jsonData))
}

// GetJSON 从Hash字段获取JSON对象
func (r *RedisUtil) GetJSON(key, field string, obj interface{}) error {
	jsonStr, err := r.HGet(key, field)
	if err != nil {
		return err
	}
	if jsonStr == "" {
		return fmt.Errorf("key=%s, field=%s 不存在", key, field)
	}
	
	err = json.Unmarshal([]byte(jsonStr), obj)
	if err != nil {
		pix_log.Error("[%s] GetJSON反序列化失败: key=%s, field=%s, error=%v", r.logPrefix, key, field, err)
		return err
	}
	return nil
}

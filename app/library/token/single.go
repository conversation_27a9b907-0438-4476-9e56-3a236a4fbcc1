package token

import (
	"errors"
	"fmt"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/net/ghttp"
	"github.com/gogf/gf/util/gconv"
	"ccserver/app/library/v"
	"ccserver/app/vars"
	"time"
)

// delSingleToken 删除 TOKEN
func delSingleToken(token string) error {
	_, err := v.Redis().Do("HDEL", vars.RedisSessionTokenToUid, token)
	return err
}

// delSingleUidToken 根据 UID 删除 TOKEN
func delSingleUidToken(uid int64) error {
	oldTokenRes, err := v.Redis().DoVar("HGET", vars.RedisSessionUidTokenHash, uid)
	//lg.LogWeb().Debugf("oldToken: %+v, err: %v", oldToken, err)
	if err != nil {
		return err
	}
	oldToken := oldTokenRes.String()
	if oldToken == "" {
		return nil
	}
	return delSingleToken(oldToken)
}

// tokenSingleUserInfo 用户 token 获取用户信息
func tokenSingleUserInfo(r *ghttp.Request) (userInfo vars.UserInfo, err error) {
	userToken := GetToken(r)
	if userToken == "" {
		err = errors.New("令牌不存在")
		return
	}

	// 从 TOKEN 中获取 UID
	uid, err := v.Redis().DoVar("HGET", vars.RedisSessionTokenToUid, userToken)
	if err != nil {
		err = fmt.Errorf("获取用户 ID 失败： %s", err.Error())
		return
	}

	uid64 := uid.Int64()
	// 从 UID 中获取 TOKEN 过期时间
	expireTimeTS, err := v.Redis().Do("HGET", vars.RedisSessionExpireTsHash, uid64)
	if err != nil {
		err = fmt.Errorf("获取令牌有效期失败： %s", err.Error())
		return
	}

	// 判断过期
	if gconv.Int64(expireTimeTS) < time.Now().Unix() {
		err = errors.New("令牌已过期")
		return
	}

	return UserInfo(uid64)
}

// addSingleToken 添加 TOKEN 与 UID 对应表
func addSingleToken(uid int64, token string) error {
	// 以用户 ID 为字段，将令牌和到期时间戳分别储存到两个散列里面
	_ = delSingleUidToken(uid)
	expiration := g.Cfg().GetInt64("server.web.token_expiration")
	// 若未设置,则默认 1 天
	if expiration == 0 {
		expiration = 86400
	}

	if _, err := v.Redis().Do("HSET", vars.RedisSessionTokenToUid, token, uid); err != nil {
		return err
	}
	if _, err := v.Redis().Do("HSET", vars.RedisSessionUidTokenHash, uid, token); err != nil {
		return err
	}
	if _, err := v.Redis().Do("HSET", vars.RedisSessionExpireTsHash, uid, time.Now().Unix()+expiration); err != nil {
		return err
	}
	return nil
}

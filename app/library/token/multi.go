package token

import (
	"errors"
	"fmt"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/net/ghttp"
	"ccserver/app/library/v"
	"ccserver/app/vars"
)

// delMultiToken 删除 TOKEN
func delMultiToken(token string) error {
	_, err := v.Redis().Do("DEL", fmt.Sprintf(vars.RedisSessionMultiTokenUid, token))
	return err
}

// delMultiUidToken 根据 UID 删除 TOKEN
func delMultiUidToken(uid int64) error {
	oldTokenRes, err := v.Redis().DoVar("GET", fmt.Sprintf(vars.RedisSessionMultiUidToken, uid))
	//lg.LogWeb().Debugf("oldToken: %+v, err: %v", oldToken, err)
	if err != nil {
		return err
	}
	oldToken := oldTokenRes.String()
	if oldToken == "" {
		return nil
	}
	return delMultiToken(oldToken)
}

// tokenMultiUserInfo 用户 token 获取用户信息
func tokenMultiUserInfo(r *ghttp.Request) (userInfo vars.UserInfo, err error) {
	userToken := GetToken(r)
	if userToken == "" {
		err = errors.New("令牌不存在")
		return
	}

	// 从 TOKEN 中获取 UID
	uid, err := v.Redis().DoVar("GET", fmt.Sprintf(vars.RedisSessionMultiTokenUid, userToken))
	if err != nil {
		err = fmt.Errorf("获取用户 ID 失败： %s", err.Error())
		return
	}

	uid64 := uid.Int64()
	// 判断过期
	if uid64 == 0 {
		err = errors.New("登录已失效, 请重新登录")
		return
	}

	return UserInfo(uid64)
}

// addMultiToken 添加 TOKEN 与 UID 对应表
func addMultiToken(uid int64, token string) (err error) {
	// 以用户 ID 为字段，将令牌和到期时间戳分别储存到两个散列里面
	//_ = delMultiUidToken(uid)
	expiration := g.Cfg().GetInt64("server.web.token_expiration")
	// 若未设置,则默认 1 天
	if expiration == 0 {
		expiration = 86400
	}

	_, err = v.Redis().Do("SETEX", fmt.Sprintf(vars.RedisSessionMultiTokenUid, token), expiration, uid)
	if err != nil {
		return
	}

	_, err = v.Redis().Do("SETEX", fmt.Sprintf(vars.RedisSessionMultiUidToken, uid), expiration, token)
	return
}

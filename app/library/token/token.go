package token

import (
	"fmt"
	"github.com/gogf/gf/net/ghttp"
	"ccserver/app/library/v"
	"ccserver/app/vars"
	"strings"
)

// GetToken 获取用户 Token
func GetToken(r *ghttp.Request) string {
	return r.Header.Get("X-Token")
}

// UserInfo 从 Header token 中获取用户信息
func UserInfo(uid int64) (userInfo vars.UserInfo, err error) {
	uidStr := fmt.Sprintf("%d", uid)
	res, err := v.Redis().DoVar("HGETALL", vars.RedisUserInfof+uidStr)
	if err != nil {
		err = fmt.Errorf("用户信息(%v)获取失败, %s", uid, err.Error())
		return
	} else if res == nil {
		err = fmt.Errorf("数据不存在")
		return
	}

	err = res.Struct(&userInfo)
	// roles[0] = "r1,r2,r3"
	userInfo.Roles = strings.Split(userInfo.Roles[0], ",")
	return
}

func AddToken(uid int64, token string) error {
	//return addSingleToken(uid, token)
	return addMultiToken(uid, token)
}

// DelToken 删除 TOKEN
func DelToken(token string) error {
	//return delSingleToken(token)
	return delMultiToken(token)
}

// DelUidToken 根据 UID 删除 TOKEN
func DelUidToken(uid int64) error {
	//return delSingleUidToken(uid)
	return delMultiUidToken(uid)
}

// TokenUserInfo 用户 token 获取用户信息
func TokenUserInfo(r *ghttp.Request) (userInfo vars.UserInfo, err error) {
	//return tokenSingleUserInfo(r)
	return tokenMultiUserInfo(r)
}

package v_test

import (
	"ccserver/app/library/v"
	"testing"
)

// TestNewLicense TestNewLicense
func TestNewLicense(t *testing.T) {
	str := "ztsqVsXoWK/DE0D8FzBWTe634yOwHTbngLPOQfQtNh21FOhvfN0JapDodi7gon7gHuiMoJTb7ilIaKPIhLqCWi5QovHQZLSzQvFXMjIfXi3DAfc6nURk7KZdg2a/RUQBvtnJzxvFb4zcFW0WqEPJcQ=="
	if err := v.NewLicense(str); err != nil {
		t.Error(err)
	}
	license := v.GetLicense()
	if license.CpuID == "" || license.HddID == "" || license.Total == 0 || license.EndTime == "" {
		t.Logf("License invalid value: %+v\n", license)
		t.Fail()
	}
}

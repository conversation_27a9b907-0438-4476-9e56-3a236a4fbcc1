package v

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"ccserver/app/library/genstd"
	"ccserver/app/vars"
)

var (
	license          = &vars.License{}
	decryptionKeyStr = "YCWL:KEYGEN:2020"
	decryptionKey    = []byte(decryptionKeyStr)
)

// NewLicense New License
func NewLicense(base64String string) error {
	if base64String == "" {
		return errors.New("许可证不存在")
	}
	decCodeBytes, e := dec(base64String)
	if e != nil {
		return e
	}
	err := json.Unmarshal(decCodeBytes, license)
	if err != nil {
		return fmt.Errorf("许可证解析失败: %s", e)
	}
	return nil
}

// GetLicense Get License
func GetLicense() *vars.License {
	return license
}

// SetLicense Set License
func SetLicense(key *vars.License) {
	license = key
}

// dec license
func dec(str string) ([]byte, error) {
	var err error
	decBase64Bytes, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return nil, err
	}
	decCode, err := genstd.Decrypt(decBase64Bytes, decryptionKey)
	if err != nil {
		return nil, err
	}
	if decCode == nil {
		return nil, errors.New("许可证解密失败")
	}
	return decCode, nil
}

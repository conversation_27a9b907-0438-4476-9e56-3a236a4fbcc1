package rand

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"hash"
	"math/rand"
	"strconv"
	"time"
)

// RandomString 随机字符串
func RandomString(flag string, length ...int) string {
	var pool string

	switch flag {
	case "basic":
		seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))
		return strconv.Itoa(seededRand.Int())

	case "alnum", "numeric", "nozero", "alpha", "hex":
		switch flag {
		case "alpha":
			pool = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
			break
		case "alnum":
			pool = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
			break
		case "numeric":
			pool = "0123456789"
			break
		case "nozero":
			pool = "123456789"
			break
		case "hex":
			pool = "0123456789abcdefABCDEF"
			break
		}

		var result []byte
		bytes := []byte(pool)
		if len(length) == 0 {
			length[0] = 32
		}
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		for i := 0; i < length[0]; i++ {
			result = append(result, bytes[r.Intn(len(bytes))])
		}
		return string(result)

	case "md5", "sha1", "sha256":
		var h hash.Hash
		seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))

		switch flag {
		case "sha1":
			h = sha1.New()
			break

		case "sha256":
			h = sha256.New()
			break

		case "md5":
		default:
			h = md5.New()
		}

		if h != nil {
			h.Write([]byte(strconv.Itoa(seededRand.Int())))
			return hex.EncodeToString(h.Sum(nil))
		}
	}

	return ""
}

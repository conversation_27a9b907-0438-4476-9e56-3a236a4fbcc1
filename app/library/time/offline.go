// 离线时间判断

package time

import (
	cacheservice "ccserver/app/service/cache"
	"ccserver/app/vars"
	"time"
)

// Offline struct
type Offline struct {
	deviceId string
	now      time.Time
	nowTs    int64
}

// NewOffline Offline init
func NewOffline() *Offline {
	t := new(Offline)
	t.Time(time.Now())
	return t
}

func (t *Offline) SetDeviceId(deviceId string) *Offline {
	t.deviceId = deviceId
	return t
}

func (t *Offline) Time(now time.Time) *Offline {
	t.now = now
	t.nowTs = t.now.Unix()
	return t
}

func (t *Offline) TermOfflineTime() int64 {
	return cacheservice.GetRedisDeviceOfflineTs(t.deviceId)
}

// IsTermOffline 终端是否离线
func (t *Offline) IsTermOffline() bool {
	if t.TermOfflineTime() == -1 {
		return false
	} else {
		return t.nowTs-t.TermOfflineTime() > vars.Config.Business.OfflineInterval
	}
}

// Lan1OfflineTime LAN1断开时间
func (t *Offline) Lan1OfflineTime() int64 {
	return cacheservice.GetRedisLan1OfflineTs(t.deviceId)
}

// Lan2OfflineTime LAN2断开时间
func (t *Offline) Lan2OfflineTime() int64 {
	return cacheservice.GetRedisLan2OfflineTs(t.deviceId)
}

// Lan1FixedOfflineTime LAN1断开时间(修正)
func (t *Offline) Lan1FixedOfflineTime() int64 {
	data := t.Lan1OfflineTime()
	if data == -1 {
		return time.Now().Unix()
	}
	return data
}

// Lan2FixedOfflineTime LAN2断开时间(修正
// 若为 -1 则未断开, 则延后 5 分钟离线
func (t *Offline) Lan2FixedOfflineTime() int64 {
	data := t.Lan2OfflineTime()
	if data == -1 {
		return time.Now().Unix()
	}
	return data
}

// IsLan1Offline LAN1 口是否断开
func (t *Offline) IsLan1Offline() bool {
	return t.nowTs-t.Lan1FixedOfflineTime() > vars.Config.Business.LanOfflineInterval
}

// IsLan2Offline LAN2 口是否断开
func (t *Offline) IsLan2Offline() bool {
	return t.nowTs-t.Lan2FixedOfflineTime() > vars.Config.Business.LanOfflineInterval
}

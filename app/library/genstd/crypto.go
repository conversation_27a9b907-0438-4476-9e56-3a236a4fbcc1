package genstd

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"errors"
	"github.com/tjfoc/gmsm/sm4"
	"io"
	"log"
)

// Encrypt 加密
func Encrypt(data, key []byte) ([]byte, error) {
	return SM4Encrypt(data, key)
}

// Decrypt 解密
func Decrypt(code, key []byte) ([]byte, error) {
	return SM4Decrypt(code, key)
}

// SM4Encrypt SM4 加密
func SM4Encrypt(origBytes, key []byte) ([]byte, error) {
	block, err := sm4.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()

	defer func() {
		if e := recover(); e != nil {
			log.Fatalln(e)
		}
	}()

	origData := pkcs5Padding(origBytes, blockSize)
	iv := make([]byte, sm4.BlockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv)
	encrypted := make([]byte, len(origData))
	blockMode.CryptBlocks(encrypted, origData)
	return encrypted, nil
}

// SM4Decrypt 解密
func SM4Decrypt(encrypted, key []byte) ([]byte, error) {
	block, err := sm4.NewCipher(key)
	if err != nil {
		return nil, err
	}
	iv := make([]byte, sm4.BlockSize)
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(encrypted))
	blockMode.CryptBlocks(origData, encrypted)

	defer func() {
		if e := recover(); e != nil {
			log.Fatalln(e)
		}
	}()

	origData = pkcs5UnPadding(origData)
	return origData, nil
}

// pkcs5Padding pkcs5Padding fill
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padText...)
}

// pkcs5UnPadding pkcs5UnPadding
func pkcs5UnPadding(src []byte) []byte {
	length := len(src)
	if length == 0 {
		return nil
	}
	unPadding := int(src[length-1])
	return src[:(length - unPadding)]
}

// AesEncryptCFB AES-CFB 加密
func AesEncryptCFB(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	encrypted := make([]byte, aes.BlockSize+len(origData))
	iv := encrypted[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(encrypted[aes.BlockSize:], origData)
	return encrypted, nil
}

// AesDecryptCFB AES-CFB 解密
func AesDecryptCFB(encrypted, key []byte) ([]byte, error) {
	block, _ := aes.NewCipher(key)
	if len(encrypted) < aes.BlockSize {
		return nil, errors.New("ciphertext too short")
	}
	iv := encrypted[:aes.BlockSize]
	encrypted = encrypted[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(encrypted, encrypted)
	return encrypted, nil
}

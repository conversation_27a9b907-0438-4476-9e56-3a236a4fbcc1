package genstd

import (
	"errors"
	"os/exec"
)

// GetCPUID Get CPU ID
func GetCPUID() (string, error) {
	var cpuid string
	cmd := exec.Command("/bin/sh", "-c", `sudo dmidecode -t 4 | grep ID | sort -u | head -n 1 | awk -F ':' '{print $2}' | tr -d " \n"`)
	b, e := cmd.CombinedOutput()
	if e != nil {
		return "", e
	}
	if len(b) == 0 {
		return "", errors.New("CPU ID acquisition failed. ")
	}
	cpuid = string(b)
	return cpuid, nil
}

// GetHDDSN GET HDD Serial Number
func GetHDDSN() (string, error) {
	var hddinfo string
	cmd := exec.Command("/bin/sh", "-c", `sudo lshw -c disk | grep serial | sort -u | head -n 1 | awk -F ':' '{print $2}' | tr -d " \n"`)
	b, e := cmd.CombinedOutput()
	if e != nil {
		return "", e
	}
	if len(b) == 0 {
		return "", errors.New("HDD serial number acquisition failed. ")
	}
	hddinfo = string(b)
	return hddinfo, nil
}

// GetSSDSN GET SSD Serial Number
func GetSSDSN() (string, error) {
	var ssdinfo string
	cmd := exec.Command("/bin/sh", "-c", `sudo lsblk -r -o name,type,serial | grep disk | awk '{print $3}'`)
	b, e := cmd.CombinedOutput()
	if e != nil {
		return "", e
	}
	if len(b) == 0 {
		return "", errors.New("SSD serial number acquisition failed. ")
	}
	ssdinfo = string(b)
	return ssdinfo, nil
}

package model

type Notification struct {
	Id          int     `json:"id"`
	Content     string  `json:"content"`
	CreatedTime int     `json:"created_time"`
	DeviceId    int     `json:"device_id"`
	Type        int     `json:"type"`
	Lng         float64 `json:"lng"`
	Lat         float64 `json:"lat"`
	MessageTime int     `json:"message_time"`
	CompanyId   int     `json:"company_id"`
}

func (Notification) TableName() string {
	return "notification"
}

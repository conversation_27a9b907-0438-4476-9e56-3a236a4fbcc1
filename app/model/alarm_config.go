package model

type AlarmConfig struct {
	Id           uint   `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	Code         int    `json:"code" gorm:"column:code;not null"`
	FirstDomain  string `json:"first_domain" gorm:"column:first_domain;type:varchar(255)"`
	SecondDomain string `json:"second_domain" gorm:"column:second_domain;type:varchar(255)"`
	Level        uint8  `json:"level" gorm:"column:level;type:tinyint"`
	Type         uint8  `json:"type" gorm:"column:type;type:tinyint"`
	Description  string `json:"description" gorm:"column:description;type:text"`
	CreatedTime  string `json:"created_time" gorm:"column:created_time"`
	UpdatedTime  string `json:"updated_time" gorm:"column:updated_time"`
}

func (AlarmConfig) TableName() string {
	return "alarm_config"
}

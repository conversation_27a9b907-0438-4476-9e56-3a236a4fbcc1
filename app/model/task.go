package model

type Task struct {
	Id          int    `json:"id"`
	Name        string `json:"name"`
	Uid         int    `json:"uid"`
	Status      int    `json:"status"`
	Imsi        string `json:"imsi"`
	Type        int    `json:"type"`
	Cron        string `json:"cron"`
	Desp        string `json:"desp"`
	Year        int    `json:"year"`
	Month       int    `json:"month"`
	Day         int    `json:"day"`
	Hour        int    `json:"hour"`
	Minute      int    `json:"minute"`
	Second      int    `json:"second"`
	Week        string `json:"week"`
	CreatedTime int    `json:"created_time"`
	UpdatedTime int    `json:"updated_time"`
	IsActive    int    `json:"is_active"`
	MapId       int    `json:"map_id"`
	Times       int    `json:"times"`
}

func (Task) TableName() string {
	return "task"
}

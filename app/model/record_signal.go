package model

// RecordSignal 记录信号数据表模型
type RecordSignal struct {
	ID              uint64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                        // ID
	DeviceID        uint32  `gorm:"column:device_id;not null;default:0" json:"device_id"`                // 设备ID
	IMSI            string  `gorm:"column:imsi;not null;default:''" json:"imsi"`                         // IMSI
	UID             uint32  `gorm:"column:uid;not null;default:0" json:"uid"`                            // 归属
	Lan1Status      int8    `gorm:"column:lan1_status;not null;default:0" json:"lan1_status"`            // LAN1状态
	Lan2Status      int8    `gorm:"column:lan2_status;not null;default:0" json:"lan2_status"`            // LAN2状态
	UpgradeState    string  `gorm:"column:upgrade_state;not null;default:''" json:"upgrade_state"`       // 升级状态
	UpgradeProgress string  `gorm:"column:upgrade_progress;not null;default:''" json:"upgrade_progress"` // 升级进度
	Latitude        float64 `gorm:"column:lat;not null;default:0" json:"lat"`                            // 纬度
	Longitude       float64 `gorm:"column:lng;not null;default:0" json:"lng"`                            // 经度
	Located         int8    `gorm:"column:located;not null;default:0" json:"located"`                    // 是否定位(0.未定位,1.定位)
	Status          int8    `gorm:"column:sta;not null;default:0" json:"sta"`                            // 0:正常, 1:异常
	Mode            int32   `gorm:"column:mode;not null;default:0" json:"mode"`                          // 1:自动驾驶模式中, 2:遥控模式, 3:路径规划中
	ACC             int32   `gorm:"column:acc;not null;default:-1" json:"acc"`                           // ACC状态,即钥匙状态;0:ACC off, 1:ACC on, -1:未知
	Gear            int32   `gorm:"column:gear;not null;default:-1" json:"gear"`                         // 档位; 0:P档, 2:R档, 3:N档, 4:D档, -1:未知
	Door            int32   `gorm:"column:door;not null;default:-1" json:"door"`                         // 车门开关状态; 0:关, 1:开, -1:未知
	Light           int32   `gorm:"column:light;not null;default:-1" json:"light"`                       // 灯光开关状态;0:关, 1:开, -1:未知
	Window          int32   `gorm:"column:win;not null;default:-1" json:"win"`                           // 车窗开关状态;0:关, 1:开, -1:未知
	TotalMiles      int32   `gorm:"column:tm;not null;default:-1" json:"tm"`                             // 总里程, 单位km; -1:未知
	Tm              int32   `gorm:"column:tm;not null;default:-1" json:"tm"`                             // 总里程, 单位km; -1:未知 (兼容字段)
	RemainMiles     int32   `gorm:"column:rm;not null;default:-1" json:"rm"`                             // 当前电量可跑的续航里程; 单位km; -1:未知
	SteeringAngle   float64 `gorm:"column:ste;not null;default:-1" json:"ste"`                           // 方向盘转角; 单位0.1度; -1:未知
	BrakeStatus     float64 `gorm:"column:brk;not null;default:-1" json:"brk"`                           // 制动值; 单位0.1Mp; -1:未知
	HeadMode        int32   `gorm:"column:head_mode;not null;default:1" json:"head_mode"`                // 当前转向模式; 1:前后异向模式, 2:常规模式, 3：前后同向模式
	Signal          int32   `gorm:"column:sgn;not null;default:0" json:"sgn"`                            // 4G/5G网络信号强度百分比
	Speed           int32   `gorm:"column:spd;not null;default:-1" json:"spd"`                           // 车速; 单位km/h; -1:未知
	SpeedLimit      int32   `gorm:"column:spdL;not null;default:-1" json:"spdL"`                         // 最高限速; 单位km/h; -1:未知
	PowerLevel      int32   `gorm:"column:pL;not null;default:-1" json:"pL"`                             // 剩余电量百分比; -1:未知
	PowerVoltage    int32   `gorm:"column:pV;not null;default:-1" json:"pV"`                             // 大电瓶电压; 单位0.1V; -1:未知
	PowerCurrent    int32   `gorm:"column:pC;not null;default:-1" json:"pC"`                             // 总电流;单位0.1A; -1:未知
	PowerCharging   int32   `gorm:"column:pCh;not null;default:-1" json:"pCh"`                           // 充电状态; 0:未充电, 1:充电中, -1:未知
	BatteryStatus   int32   `gorm:"column:bat;not null;default:-1" json:"bat"`                           // 小电瓶电压; 单位0.1V; -1:未知
	Altitude        float64 `gorm:"column:alt;not null" json:"alt"`                                      // GPS海拔; 单位米; 保留1位小数
	Angle           float64 `gorm:"column:angle;not null" json:"angle"`                                  // GPS航向角度; 单位度; 保留1位小数
	SatCount        int32   `gorm:"column:sat_cnt;not null;default:0" json:"sat_cnt"`                    // 有效GPS卫星数
	Error           string  `gorm:"column:err;not null;default:''" json:"err"`                           // 车辆状态异常原因
	Event           int32   `gorm:"column:event;not null;default:0" json:"event"`                        // 触发此车辆实时状态消息上报的原因
	CreatedTime     uint32  `gorm:"column:created_time;not null;default:0" json:"created_time"`          // 添加时间
	SweeperStatus   string  `gorm:"column:sweeper_status;not null;default:''" json:"sweeper_status"`     // 清扫车状态
	LatestTask      string  `gorm:"column:latest_task;not null;type:text" json:"latest_task"`            // 车辆最近任务
	VehicleType     int32   `gorm:"column:v_type;not null;default:0" json:"v_type"`                      // 车辆类型，0：robobus，1：安防小车，2：无人清扫车，3：物流车
	PosQuality      float64 `gorm:"column:pos_q;default:-1" json:"pos_q"`                                // 定位质量; 单位米; 保留1位小数
	VehicleStatus   int32   `gorm:"column:vSta;not null;default:0" json:"vSta"`                          // 车辆状态; 0:正常, 1:异常
	IPCToChannel    int8    `gorm:"column:ipcToCha;not null;default:0" json:"ipcToCha"`                  // 工控机ipc与底盘chassis是否通讯; 1:通讯中, 0未通讯
	Throttle        float64 `gorm:"column:thr;not null;default:-1" json:"thr"`                           // 油门值; 百分比; 保留2位小数
	ADLocated       int8    `gorm:"column:adLocated;not null;default:0" json:"adLocated"`                // 自动驾驶是否定位,1:定位, 0未定位
	GPSLocated      int8    `gorm:"column:gpsLocated;not null;default:0" json:"gpsLocated"`              // GPS是否定位; 1定位, 0:未定位
	GPSStrength     int32   `gorm:"column:gpsStrength;not null;default:0" json:"gpsStrength"`            // GPS定位强度百分比
	Shutdown        int8    `gorm:"column:shutdown;not null;default:0" json:"shutdown"`                  // 底盘是否已做好关机准备; 0-否， 1-是
	EmergencyStatus int32   `gorm:"column:emgSta;not null;default:0" json:"emgSta"`                      // 急停状态反馈
	AirCondition    int32   `gorm:"column:airCon;not null;default:-1" json:"airCon"`                     // 空调开关状态; 0:关, 1:开, -1:未知
	InnerTemp       int32   `gorm:"column:inTemp;not null;default:-100" json:"inTemp"`                   // 车内温度; 单位度; -100:未知
	OuterTemp       int32   `gorm:"column:outTemp;not null;default:-100" json:"outTemp"`                 // 车外温度; 单位度; -100:未知
	Smoke           int32   `gorm:"column:smoke;not null;default:-1" json:"smoke"`                       // 烟雾浓度; 单位mV; -1:未知
	CO2             int32   `gorm:"column:co2;not null;default:-1" json:"co2"`                           // 二氧化碳浓度; 单位mV; -1:未知
	Seatbelt        int32   `gorm:"column:seatbelt;not null;default:-1" json:"seatbelt"`                 // 座椅安全带状态; 0:正常, 1:异常, -1:未知
	RetailBot       string  `gorm:"column:retail_bot;not null;type:text" json:"retail_bot"`              // 咖啡机器数据
	AlarmCount1     string  `gorm:"column:alarm_cnt1;not null;default:''" json:"alarm_cnt1"`             // 告警统计1
	AlarmCount2     string  `gorm:"column:alarm_cnt2;not null;default:''" json:"alarm_cnt2"`             // 告警统计2
	Alarm           string  `gorm:"column:alarm;not null;default:''" json:"alarm"`                       // 告警内容
	HeadLight       int32   `gorm:"column:head_light;not null;default:-1" json:"head_light"`             // 大灯开关状态; 0:关, 1:开, -1:未知
	RC              int32   `gorm:"column:rc;not null;default:-1" json:"rc"`                             // 无线遥控器开关的状态; -1:未知; 0:关; 1:开
}

// TableName 获取表名
func (r *RecordSignal) TableName() string {
	return "record_signal"
}

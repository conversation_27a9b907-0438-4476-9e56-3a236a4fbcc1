package model

import (
	"database/sql"
	"github.com/gogf/gf/database/gdb"
)

// Orm ORM
type Orm struct {
	base
}

// NewOrm init
func NewOrm(name string) *Orm {
	t := &Orm{}
	t.name = name
	t.connect()
	return t
}

// Data 获取一组数据 (扩展型)
func (t *Orm) Data(params Params) (result gdb.Result, err error) {
	return t.getDataExt(t.name, params)
}

// All 获取一组数据
func (t *Orm) All(where interface{}, sort string) (result gdb.Result, err error) {
	return t.getData(t.name, where, sort)
}

// One 获取一条数据
func (t *Orm) One(where interface{}, sort string) (record gdb.Record, err error) {
	return t.getDataOne(t.name, where, sort)
}

// Add 添加
func (t *Orm) Add(data interface{}) (result sql.Result, err error) {
	return t.addData(t.name, data)
}

// Mod 修改
func (t *Orm) Mod(where, data interface{}) (result sql.Result, err error) {
	return t.updateData(t.name, data, where)
}

// Del 删除
func (t *Orm) Del(where interface{}) (result sql.Result, err error) {
	return t.deleteData(t.name, where)
}

// Save 保存
func (t *Orm) Save(data interface{}) (result sql.Result, err error) {
	return t.saveData(t.name, data)
}

// Rows 是否获取数量
func (t *Orm) Rows(flag RowType) *Orm {
	t.rowsStyle = flag
	return t
}

// AddBatch 批量添加一组信息
func (t *Orm) AddBatch(data []interface{}, batch int) (result sql.Result, err error) {
	return t.addDataBatch(t.name, data, batch)
}

package model

type WrcMail struct {
	Id          int    `json:"id"`
	UserId      int    `json:"user_id"`
	ReceiveName string `json:"receive_name"`
	ReceiveTel  string `json:"receive_tel"`
	Park        string `json:"park"`
	Station     string `json:"station"`
	Device      string `json:"device"`
	Provider    string `json:"provider"`
	Number      string `json:"number"`
	ContainerId int    `json:"container_id"`
	Code        string `json:"code"`
	Status      int    `json:"status"`
	No          string `json:"no"`
	CellNo      int    `json:"cell_no"`
	SendTime    int    `json:"send_time"`
	HdMapId     int    `json:"hd_map_id"`
	ReceiveTime int    `json:"receive_time"`
	CreatedTime int    `json:"created_time"`
	UpdatedTime int    `json:"updated_time"`
	IsSend      int    `json:"is_send"`
}

func (WrcMail) TableName() string {
	return "wrc_mail"
}

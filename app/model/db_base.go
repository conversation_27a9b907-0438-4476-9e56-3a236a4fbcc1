package model

import (
	"database/sql"
	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/frame/g"
)

type base struct {
	db        gdb.DB
	name      string
	rowsStyle RowType // 获取数量的风格 (0,不获取数量, 1.仅获取数量, 2.数量和数据都获取)
	count     int
}

func (t *base) connect() {
	t.db = g.DB()
}

// getDataAll 获取一组信息
func (t *base) getDataAll(name string, where interface{}) (result gdb.Result, err error) {
	where = t.fixMapData(where)

	m := t.db.Table(name).Where(where)

	// 获取数量
	if isReturn, err := t.checkRows(m); err != nil {
		return nil, err
	} else if isReturn {
		return nil, nil
	}

	return m.All()
}

// GetDataOne 获取一条信息
func (t *base) getDataOne(name string, where interface{}, sort string) (record gdb.Record, err error) {
	where = t.fixMapData(where)

	m := t.db.Table(name).Where(where)
	if sort != "" {
		m = m.Order(sort)
	}
	return m.Limit(1).One()
}

// AddData 添加一条信息
func (t *base) addData(name string, data ...interface{}) (result sql.Result, err error) {
	return t.db.Table(name).Data(data).Insert()
}

// UpdateData 更新数据
func (t *base) updateData(name string, data, where interface{}) (result sql.Result, err error) {
	where = t.fixMapData(where)
	return t.db.Table(name).Where(where).Data(data).Update()
}

// DeleteData 删除数据
func (t *base) deleteData(name string, where interface{}) (result sql.Result, err error) {
	where = t.fixMapData(where)
	return t.db.Table(name).Delete(where)
}

// saveData 保存数据
func (t *base) saveData(name string, data interface{}) (result sql.Result, err error) {
	return t.db.Table(name).Data(data).Save()
}

// 获取数据
func (t *base) getData(name string, where interface{}, sort string) (result gdb.Result, err error) {
	if sort == "" {
		sort = "id DESC"
	}

	where = t.fixMapData(where)
	m := t.db.Table(name).Where(where)

	// 获取数量
	if isReturn, err := t.checkRows(m); err != nil {
		return nil, err
	} else if isReturn {
		return nil, nil
	}

	return m.Order(sort).All()
}

// GetDataExt 获取数据扩展方式
func (t *base) getDataExt(name string, params Params) (result gdb.Result, err error) {
	var m *gdb.Model
	m = t.db.Table(name)

	params.Where = t.fixMapData(params.Where)
	params.Where2 = t.fixMapData(params.Where2)
	m = m.Where(params.Where)
	m = m.Where(params.Where2)

	if params.LeftJoin != nil{
		m.LeftJoin(params.LeftJoin...)
	}
	if params.InnerJoin != nil{
		m.InnerJoin(params.InnerJoin...)
	}

	// 获取数量
	if isReturn, err := t.checkRows(m); err != nil {
		return nil, err
	} else if isReturn {
		return nil, nil
	}

	if params.Fields != "" {
		m = m.Fields(params.Fields)
	}

	if params.Sort != "" {
		m = m.Order(params.Sort)
	}

	if params.Limit > 0 {
		m = m.Offset(params.Offset).Limit(params.Limit)
	}

	result, err = m.All()
	return
}

// AddDataBatch 批量添加一组信息
func (t *base) addDataBatch(name string, data []interface{}, batch int) (result sql.Result, err error) {
	if batch == 0 {
		batch = len(data)
	}
	return t.db.Table(name).Data(data).Batch(batch).Insert()
}

func (t *base) checkRows(m *gdb.Model) (isReturn bool, err error) {
	// 获取数量
	if t.rowsStyle != RowsData {
		t.count, err = m.Count()

		// 重置获取数量为否
		t.rowsStyle = RowsData

		// 仅获取数量
		if t.rowsStyle == RowsOnly {
			isReturn = true
		}
	}

	return
}

// fixMapData gogf v1.15.3 不再支持 where = g.map{} 的解决方法
func (t *base) fixMapData(data interface{}) interface{} {
	if data == nil {
		return 1
	}
	if val, ok := data.(g.Map); ok && len(val) == 0 {
		return 1
	}
	return data
}

// Rows 是否获取数量
func (t *base) Rows(flag RowType) *base {
	t.rowsStyle = flag
	return t
}

// Count 获取数据总数
func (t *base) Count() int {
	return t.count
}

// Db 获取实例
func (t *base) Db() *gdb.Model {
	return t.db.Table(t.name)
}

// GetDB 获取 DB 单例
func (t *base) GetDB() gdb.DB {
	return t.db
}

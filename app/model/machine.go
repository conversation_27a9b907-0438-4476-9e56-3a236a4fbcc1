package model

type Machine struct {
	Id            int    `json:"id"`
	Imsi          string `json:"imsi"`
	BillNo        string `json:"bill_no"`
	ProductId     int    `json:"product_id"`
	TaskId        int    `json:"task_id"`
	DeviceStatus  int    `json:"device_status"`
	Action        int    `json:"action"`
	TotalTime     int    `json:"total_time"`
	RTime         int    `json:"r_time"`
	ActionTime    int    `json:"action_time"`
	ActionRTime   int    `json:"action_r_time"`
	ErrCode       string `json:"err_code"`
	ErrMsg        string `json:"err_msg"`
	CupHolders    int    `json:"cup_holders"`
	Liquid        int    `json:"liquid"`
	Solid         int    `json:"solid"`
	LiquidCup     int    `json:"liquid_cup"`
	SolidCup      int    `json:"solid_cup"`
	Ports         int    `json:"ports"`
	ArmPosX       int    `json:"arm_pos_x"`
	OtherStatus1  int    `json:"other_status_1" gorm:"column:other_status_1"`
	OtherStatus2  int    `json:"other_status_2" gorm:"column:other_status_2"`
	CarP          int    `json:"car_p"`
	CarT          int    `json:"car_t"`
	CarStatus     int    `json:"car_status"`
	InvV          int    `json:"inv_v"`
	InvF          int    `json:"inv_f"`
	InvC          int    `json:"inv_c"`
	InvT          int    `json:"inv_t"`
	Time          int    `json:"time"`
	RunTime       int    `json:"run_time"`
	BootCnt       int    `json:"boot_cnt"`
	CreatedTime   int64  `json:"created_time"`
	UpdatedTime   int64  `json:"updated_time"`
	Uid           int64  `json:"uid"`
	ActionListLen int    `json:"action_list_len"`
	ActionIndex   int    `json:"action_index"`
}

func (Machine) TableName() string {
	return "machine"
}

package model

type TaskChild struct {
	Id          int    `json:"id"`
	ParentId    int64  `json:"parent_id"`
	Type        int    `json:"type"`
	Content     string `json:"content"`
	Condition   string `json:"condition"`
	Status      int    `json:"status"`
	Order       int    `json:"order"`
	Action      string `json:"action"`
	CreatedTime int    `json:"created_time"`
	UpdatedTime int    `json:"updated_time"`
	IsNow       int    `json:"is_now"`
	Name        string `json:"name"`
}

func (TaskChild) TableName() string {
	return "task_child"
}

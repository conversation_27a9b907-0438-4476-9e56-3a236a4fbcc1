package model

type Device struct {
	Id             int     `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	IMEI           string  `json:"imei" gorm:"column:imei;type:varchar(32);not null"`
	IMSI           string  `json:"imsi" gorm:"column:imsi;type:varchar(64);not null"`
	Ip             string  `json:"ip" gorm:"column:ip;type:varchar(15)"`
	Model          string  `json:"model" gorm:"column:model;type:varchar(52)"`
	SoftwareVer    string  `json:"software_ver" gorm:"column:software_ver;type:varchar(255);default:''"`
	HardwareVer    string  `json:"hardware_ver" gorm:"column:hardware_ver;type:varchar(255);default:''"`
	Uid            int64   `json:"uid" gorm:"column:uid;index"`
	Vender         string  `json:"vender" gorm:"column:vender;type:varchar(52)"`
	Ci             string  `json:"ci" gorm:"column:ci;type:varchar(64)"`
	Pci            string  `json:"pci" gorm:"column:pci;type:varchar(64)"`
	ManageIp       string  `json:"manage_ip" gorm:"column:manage_ip;type:varchar(16)"`
	Position       string  `json:"position" gorm:"column:position;type:varchar(52)"`
	Longitude      string  `json:"longitude" gorm:"column:longitude;type:varchar(12);default:'0'"`
	Latitude       string  `json:"latitude" gorm:"column:latitude;type:varchar(12);default:'0'"`
	Status         int8    `json:"status" gorm:"column:status;type:tinyint(1);default:0"`
	MaintainState  int     `json:"maintain_state" gorm:"column:maintain_state;default:0"`
	TimeSpending   int     `json:"time_spending" gorm:"column:time_spending;default:0"`
	OnlineBigint   uint    `json:"online_bigint" gorm:"column:online_bigint;default:0"`
	OnlineTimes    int     `json:"online_times" gorm:"column:online_times;default:0"`
	OfflineTimes   int     `json:"offline_times" gorm:"column:offline_times;default:0"`
	Online         int     `json:"online" gorm:"column:online;type:tinyint(1);default:0"`
	Name           string  `json:"name" gorm:"column:name;type:varchar(256)"`
	Tz             string  `json:"tz" gorm:"column:tz;type:varchar(52)"`
	NtpServer      string  `json:"ntp_server" gorm:"column:ntp_server;type:varchar(255)"`
	Interval       int     `json:"interval" gorm:"column:interval"`
	MonitoringIp   string  `json:"monitoring_ip" gorm:"column:monitoring_ip;type:varchar(16)"`
	MonitoringPort int     `json:"monitoring_port" gorm:"column:monitoring_port"`
	UNMPIp         string  `json:"unmp_ip" gorm:"column:unmp_ip;type:varchar(16)"`
	UNMPPort       int     `json:"unmp_port" gorm:"column:unmp_port"`
	Baud232        int     `json:"baud_232" gorm:"column:baud_232"`
	DataBit232     int     `json:"data_bit_232" gorm:"column:data_bit_232"`
	Parity232      int     `json:"parity_232" gorm:"column:parity_232"`
	StopBit232     float64 `json:"stop_bit_232" gorm:"column:stop_bit_232"`
	Baud485        int     `json:"baud_485" gorm:"column:baud_485"`
	DataBit485     int     `json:"data_bit_485" gorm:"column:data_bit_485"`
	Parity485      int     `json:"parity_485" gorm:"column:parity_485"`
	StopBit485     float64 `json:"stop_bit_485" gorm:"column:stop_bit_485"`
	OnlineTime     string  `json:"online_time" gorm:"column:online_time"`
	OfflineTime    string  `json:"offline_time" gorm:"column:offline_time"`
	CreatedTime    string  `json:"created_time" gorm:"column:created_time"`
	UpdatedTime    string  `json:"updated_time" gorm:"column:updated_time"`
	ResetTime      int64   `json:"reset_time" gorm:"column:reset_time"`
	OperationNum   uint    `json:"operation_num" gorm:"column:operation_num;default:0"`
	OutTime        uint    `json:"out_time" gorm:"column:out_time;default:0"`
	CompanyId      int     `json:"company_id" gorm:"column:company_id;default:1"`
	Password       string  `json:"password" gorm:"column:password;type:varchar(255);default:''"`
	Advert         string  `json:"advert" gorm:"column:advert;type:text"`
	AdvertType     int     `json:"advert_type" gorm:"column:advert_type;default:0"`
	IsVcu          uint    `json:"is_vcu" gorm:"column:is_vcu;default:1"`
	TaskId         uint    `json:"task_id" gorm:"column:task_id;default:0"`
	AdvertSetId    uint    `json:"advert_set_id" gorm:"column:advert_set_id;default:0"`
	Type           int     `json:"type" gorm:"column:type;default:0"`
	Real           uint8   `json:"real" gorm:"column:real;type:tinyint(3);default:0"`
	ExpireTime     int64   `json:"expire_time" gorm:"column:expire_time;default:0"`
	MapId          int     `json:"map_id" gorm:"column:map_id;default:0"`
	IsTest         uint    `json:"is_test" gorm:"column:is_test;default:0"`
	Province       string  `json:"province" gorm:"column:province;type:varchar(255);default:''"`
	City           string  `json:"city" gorm:"column:city;type:varchar(255);default:''"`
	Area           string  `json:"area" gorm:"column:area;type:varchar(255);default:''"`
	RemoteDriving  uint8   `json:"remote_driving" gorm:"column:remote_driving;type:tinyint(1);default:0"`
	VinLogin       uint8   `json:"vin_login" gorm:"column:vin_login;type:tinyint(1);default:0"`
	VinCode        string  `json:"vin_code" gorm:"column:vin_code;type:varchar(255);default:''"`
	Push           uint8   `json:"push" gorm:"column:push;type:tinyint(1);default:0"`
	SendTime       uint    `json:"send_time" gorm:"column:send_time;default:0"`
}

func (Device) TableName() string {
	return "device"
}

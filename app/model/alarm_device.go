package model

type AlarmDevice struct {
	Id          uint   `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	DeviceId    int    `json:"device_id" gorm:"column:device_id;not null"`
	Imsi        string `json:"imsi" gorm:"column:imsi;type:varchar(64)"`
	AlarmCode   int    `json:"alarm_code" gorm:"column:alarm_code;not null"`
	Code        int    `json:"code" gorm:"column:code;not null"`
	Level       uint8  `json:"level" gorm:"column:level;type:tinyint"`
	Type        uint8  `json:"type" gorm:"column:type;type:tinyint"`
	Message     string `json:"message" gorm:"column:message;type:text"`
	Msg         string `json:"msg" gorm:"column:msg;type:text"`
	Status      int    `json:"status" gorm:"column:status;default:0"`
	Ts          int64  `json:"ts" gorm:"column:ts"`
	CreatedTime string `json:"created_time" gorm:"column:created_time"`
	UpdatedTime string `json:"updated_time" gorm:"column:updated_time"`
}

func (AlarmDevice) TableName() string {
	return "alarm_device"
}

package model

type User struct {
	Id            int64  `json:"id"`
	<PERSON><PERSON><PERSON>      string `json:"username"`
	Password      string `json:"password"`
	ParentId      int    `json:"parent_id"`
	Name          string `json:"name"`
	HdMapId       int    `json:"hd_map_id"`
	Tip           string `json:"tip"`
	Status        int    `json:"status"`
	RealName      string `json:"real_name"`
	Tel           string `json:"tel"`
	Email         string `json:"email"`
	Type          int    `json:"type"`
	CreatedTime   string `json:"created_time"`
	UpdatedTime   string `json:"updated_time"`
	Level         int    `json:"level"`
	IsDelete      int    `json:"is_delete"`
	MapIds        string `json:"map_ids"`
	Logo          string `json:"logo"`
	HeadImg       string `json:"head_img"`
	Check         int    `json:"check"`
	CoLastSend    int64  `json:"co_last_send"`
	SmokeLastSend int64  `json:"smoke_last_send"`
}

func (User) TableName() string {
	return "user"
}

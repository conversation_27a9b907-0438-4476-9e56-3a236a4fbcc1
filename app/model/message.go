package model

type Message struct {
	Id          int     `json:"id"`
	DeviceId    int     `json:"device_id"`
	DeviceImsi  string  `json:"device_imsi"`
	DeviceName  string  `json:"device_name"`
	UserId      int     `json:"user_id"`
	UserName    string  `json:"user_name"`
	Type        int     `json:"type"`
	Content     string  `json:"content"`
	Lng         float64 `json:"lng"`
	Lat         float64 `json:"lat"`
	MessageTime int     `json:"message_time"`
	CreatedTime int     `json:"created_time"`
	CompanyId   int     `json:"company_id"`
}

func (Message) TableName() string {
	return "message"
}

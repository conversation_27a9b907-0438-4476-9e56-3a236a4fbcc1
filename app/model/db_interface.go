package model

import (
	"database/sql"
	"github.com/gogf/gf/database/gdb"
)

// Model Model 基类
type Model interface {
	Data(params Params) (result gdb.Result, err error)

	All(w interface{}, s string) (result gdb.Result, err error)

	One(w interface{}, s string) (record gdb.Record, err error)

	Add(d interface{}) (result sql.Result, err error)

	Mod(w, d interface{}) (result sql.Result, err error)

	Del(w interface{}) (result sql.Result, err error)

	Save(data interface{}) (result sql.Result, err error)

	Rows(flag RowType) *Orm

	Count() int

	AddBatch(data []interface{}, batch int) (result sql.Result, err error)

	Db() *gdb.Model
}

package cache

import (
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/vars"
	"fmt"
	"github.com/gogf/gf/container/gvar"
	"github.com/gogf/gf/frame/g"
	"time"
)

// DelOwnDevices 删除redis中记录的指定用户所拥有的所有设备
// uid: 0 删除所有用户的，非0 删除指定用户的
//func DelOwnDevices(uid int64) {
//	if uid > 0 {
//		_, err := v.Redis().Do("DEL", fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid))
//		if err != nil {
//			v.Log().Errorf("del(%d), %s", uid, err)
//		}
//	} else {
//		keyVar, err := v.Redis().DoVar("KEYS", fmt.Sprintf("%s*", vars.RedisDeviceOwnerUID))
//		if err != nil {
//			v.Log().Errorf("keys, %s", err)
//		} else {
//			for _, key := range keyVar.Strings() {
//				_, err := v.Redis().Do("DEL", key)
//				if err != nil {
//					v.Log().Errorf("del(%s), %s", key, err)
//				}
//			}
//		}
//	}
//}

func DelOwnDevices(uid int64) {
	if uid > 0 {
		// 单个用户的设备删除
		_, err := v.Redis().Do("DEL", fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid))
		if err != nil {
			fmt.Printf("del(%d), %s\n", uid, err)
		}
	} else {
		// 使用 SCAN 代替 KEYS
		cursor := "0"
		pattern := fmt.Sprintf("%s*", vars.RedisDeviceOwnerUID)

		for {
			// 使用 SCAN 命令
			res, err := v.Redis().DoVar("SCAN", cursor, "MATCH", pattern, "COUNT", 100)
			if err != nil {
				fmt.Printf("scan error: %s\n", err)
				return
			}

			resArray := res.Interfaces()
			if len(resArray) != 2 {
				fmt.Println("invalid scan response")
				return
			}

			// 获取新的 cursor
			cursor = resArray[0].(string)

			// 获取 keys
			if keysArray, ok := resArray[1].([]interface{}); ok {
				// 逐个删除 keys
				for _, key := range keysArray {
					if keyStr, ok := key.(string); ok {
						_, err = v.Redis().Do("DEL", keyStr)
						if err != nil {
							fmt.Printf("delete key error: %s\n", err)
						}
					}
				}
			}

			// cursor 为 "0" 表示遍历完成
			if cursor == "0" {
				break
			}
		}
	}
}

func GetOwnDevices(uid int64) (*gvar.Var, error) {
	return v.Redis().DoVar("SMEMBERS", fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid))
}

// AddOwnDevice 在redis缓存中记录终端的归属者
func AddOwnDevice(uid int64, deviceId string) {
	if uid > 0 {
		v.Redis().Do("SADD", fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid), deviceId)
	}
}

// DelOwnDevice 删除redis中记录的指定用户所拥有某个设备
func DelOwnDevice(uid int64, deviceId string) {
	if uid > 0 {
		v.Redis().Do("SREM", fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid), deviceId)
	}
}

// AddOwnDevicesAndInitHeartbeatTs
// 根据从数据库中查到的用户拥有的设备，在redis初始化设备心跳时间戳和设备的拥有者
// uid: 0 所有用户的，非0 指定用户的
//func AddOwnDevicesAndInitHeartbeatTs(uid int64) {
//	where := g.Map{}
//	if uid > 0 {
//		where["uid"] = uid
//	}
//	deviceRes, err := model.NewOrm(vars.TableDevice).All(where, "")
//	if err != nil {
//		v.Log().Errorf("获取终端列表失败, %s", err)
//		return
//	}
//	if deviceRes.IsEmpty() {
//		return
//	}
//
//	var deviceResArr []struct {
//		ID   int64  `json:"id"`
//		UID  int64  `json:"uid"`
//		IMEI string `json:"imei"`
//		IMSI string `json:"imsi"`
//	}
//	if err := deviceRes.Structs(&deviceResArr); err != nil {
//		v.LogTcp().Errorf("终端列表转换失败, %s", err)
//		return
//	}
//	for _, device := range deviceResArr {
//		AddOwnDevice(device.UID, device.IMSI) // 修改终端归属
//		initDeviceHeartbeatTs(device.IMSI)    // 初始化终端心跳时间
//	}
//}

func AddOwnDevicesAndInitHeartbeatTs(uid int64) {
	start := time.Now()

	where := g.Map{}
	if uid > 0 {
		where["uid"] = uid
	}

	// 获取设备列表
	deviceRes, err := model.NewOrm(vars.TableDevice).All(where, "")
	if err != nil {
		fmt.Printf("获取终端列表失败: %s\n", err)
		return
	}
	if deviceRes.IsEmpty() {
		return
	}
	fmt.Printf("数据库查询耗时: %v\n", time.Since(start))

	startStruct := time.Now()
	var deviceResArr []struct {
		ID   int64  `json:"id"`
		UID  int64  `json:"uid"`
		IMEI string `json:"imei"`
		IMSI string `json:"imsi"`
	}
	if err := deviceRes.Structs(&deviceResArr); err != nil {
		fmt.Printf("终端列表转换失败: %s\n", err)
		return
	}
	fmt.Printf("结构体转换耗时: %v\n", time.Since(startStruct))

	startGroup := time.Now()
	// 按 UID 分组设备
	devicesByUID := make(map[int64][]string)
	var allIMSIs []string
	for _, device := range deviceResArr {
		devicesByUID[device.UID] = append(devicesByUID[device.UID], device.IMSI)
		allIMSIs = append(allIMSIs, device.IMSI)
	}
	fmt.Printf("数据分组耗时: %v\n", time.Since(startGroup))

	// 获取 Redis 连接
	conn := v.Redis().Conn()
	defer conn.Close()

	startRedis := time.Now()
	// 开始事务
	err = conn.Send("MULTI")
	if err != nil {
		fmt.Printf("开始事务失败: %s\n", err)
		return
	}

	// 批量设置心跳时间
	nowTs := time.Now().Unix()
	args := []interface{}{vars.RedisDeviceHeartbeatTS}
	for _, imsi := range allIMSIs {
		args = append(args, imsi, nowTs)
	}
	err = conn.Send("HMSET", args...)
	if err != nil {
		fmt.Printf("发送 HMSET 命令失败: %s\n", err)
		return
	}
	fmt.Printf("设置心跳时间耗时: %v\n", time.Since(startRedis))

	startOwner := time.Now()
	// 批量设置设备归属
	for uid, imsis := range devicesByUID {
		if uid > 0 {
			args := []interface{}{fmt.Sprintf("%s%d", vars.RedisDeviceOwnerUID, uid)}
			for _, imsi := range imsis {
				args = append(args, imsi)
			}
			err = conn.Send("SADD", args...)
			if err != nil {
				fmt.Printf("发送 SADD 命令失败 (uid=%d): %s\n", uid, err)
				return
			}
		}
	}

	// 执行事务
	_, err = conn.Do("EXEC")
	if err != nil {
		fmt.Printf("提交事务失败: %s\n", err)
		return
	}
	fmt.Printf("设置设备归属耗时: %v\n", time.Since(startOwner))

	fmt.Printf("总耗时: %v\n", time.Since(start))
}

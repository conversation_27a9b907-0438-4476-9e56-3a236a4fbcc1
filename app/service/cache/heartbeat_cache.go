package cache

import (
	"ccserver/app/library/redis_util"
	"ccserver/app/library/v"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"math"
	"time"
)

// HeartbeatCache 心跳缓存工具类
type HeartbeatCache struct {
	redis *redis_util.RedisUtil
}

// NewHeartbeatCache 创建心跳缓存实例
func NewHeartbeatCache() *HeartbeatCache {
	return &HeartbeatCache{
		redis: redis_util.NewRedisUtil("HEARTBEAT_CACHE"),
	}
}

// 默认实例
var HeartbeatCacheInstance = NewHeartbeatCache()

// ============================================================================
// 心跳数据操作
// ============================================================================

// SetHeartbeatData 保存设备的最后心跳数据（带GPS回退逻辑）
func (h *HeartbeatCache) SetHeartbeatData(deviceId string, heartbeatData *protocol.HeartbeatRedis) error {
	currentLat := heartbeatData.HeartbeatBase.Latitude
	currentLng := heartbeatData.HeartbeatBase.Longitude
	currentTs := time.Now().Unix()

	// 检查当前经纬度是否有效
	isCurrentGpsValid := !(currentLat == 0 || currentLng == 0 || math.IsNaN(currentLat) || math.IsNaN(currentLng) ||
		math.IsInf(currentLat, 0) || math.IsInf(currentLng, 0))

	if isCurrentGpsValid {
		// 当前GPS有效：更新当前经纬度和有效GPS时间戳
		heartbeatData.LastValidLatitude = currentLat
		heartbeatData.LastValidLongitude = currentLng
		heartbeatData.LastValidGpsTs = currentTs
		pix_log.InfoWithIMSI(deviceId, "GPS有效，更新位置: lat=%v, lng=%v", currentLat, currentLng)
	} else {
		// 当前GPS无效：尝试获取上一次的有效经纬度
		lastData, err := h.GetHeartbeatData(deviceId)
		if err == nil && lastData != nil {
			// 使用上一次的有效经纬度
			if lastData.LastValidLatitude != 0 && lastData.LastValidLongitude != 0 {
				heartbeatData.HeartbeatBase.Latitude = lastData.LastValidLatitude
				heartbeatData.HeartbeatBase.Longitude = lastData.LastValidLongitude
				heartbeatData.LastValidLatitude = lastData.LastValidLatitude
				heartbeatData.LastValidLongitude = lastData.LastValidLongitude
				heartbeatData.LastValidGpsTs = lastData.LastValidGpsTs
				pix_log.InfoWithIMSI(deviceId, "GPS无效(lat=%v, lng=%v)，使用上次有效位置: lat=%v, lng=%v, 有效时间=%v",
					currentLat, currentLng, lastData.LastValidLatitude, lastData.LastValidLongitude,
					time.Unix(lastData.LastValidGpsTs, 0).Format("2006-01-02 15:04:05"))
			} else {
				pix_log.WarningWithIMSI(deviceId, "GPS无效且无历史有效位置: lat=%v, lng=%v", currentLat, currentLng)
			}
		} else {
			pix_log.WarningWithIMSI(deviceId, "GPS无效且无历史数据: lat=%v, lng=%v", currentLat, currentLng)
		}
	}

	// 存储到Redis
	return h.redis.SetJSON(vars.RedisLastHeartbeat, deviceId, heartbeatData)
}

// GetHeartbeatData 获取设备的最后心跳数据
func (h *HeartbeatCache) GetHeartbeatData(deviceId string) (*protocol.HeartbeatRedis, error) {
	var heartbeatData protocol.HeartbeatRedis
	err := h.redis.GetJSON(vars.RedisLastHeartbeat, deviceId, &heartbeatData)
	if err != nil {
		return nil, err
	}
	return &heartbeatData, nil
}

// GetHeartbeatDataString 获取设备的最后心跳数据（字符串格式）
func (h *HeartbeatCache) GetHeartbeatDataString(deviceId string) (string, error) {
	return h.redis.HGet(vars.RedisLastHeartbeat, deviceId)
}

// ============================================================================
// 心跳时间戳操作
// ============================================================================

// SetHeartbeatTimestamp 设置终端心跳时间戳
// val: -1 从未上过线; 非-1: 最后一次心跳时间戳
func (h *HeartbeatCache) SetHeartbeatTimestamp(deviceId string, timestamp int64) error {
	return h.redis.HSet(vars.RedisDeviceHeartbeatTS, deviceId, timestamp)
}

// GetHeartbeatTimestamp 获取终端心跳时间戳
// 返回值: -1 从未上过线; 0: 初始值; 其他: 最后一次心跳时间戳
func (h *HeartbeatCache) GetHeartbeatTimestamp(deviceId string) (int64, error) {
	return h.redis.HGetInt64(vars.RedisDeviceHeartbeatTS, deviceId)
}

// GetAllHeartbeatTimestamps 获取所有设备的心跳时间戳
func (h *HeartbeatCache) GetAllHeartbeatTimestamps() (map[string]int64, error) {
	return h.redis.HGetAllInt64(vars.RedisDeviceHeartbeatTS)
}

// DeleteHeartbeatTimestamp 删除设备的心跳时间戳
func (h *HeartbeatCache) DeleteHeartbeatTimestamp(deviceId string) error {
	return h.redis.HDel(vars.RedisDeviceHeartbeatTS, deviceId)
}

// ============================================================================
// 设备离线时间操作
// ============================================================================

// SetDeviceOfflineTimestamp 设置设备离线时间戳
// 默认为0，表示设备在服务软件本次启动后从未上线
// 设置为-1，表示设备在线
// 设置为其他非负数，表示设备离线时间
func (h *HeartbeatCache) SetDeviceOfflineTimestamp(deviceId string, timestamp int64) error {
	return h.redis.HSet(vars.RedisDeviceOfflineTS, deviceId, timestamp)
}

// GetDeviceOfflineTimestamp 获取设备的离线时间戳
func (h *HeartbeatCache) GetDeviceOfflineTimestamp(deviceId string) (int64, error) {
	return h.redis.HGetInt64(vars.RedisDeviceOfflineTS, deviceId)
}

// ClearAllDeviceOfflineTimestamps 清空所有设备的离线时间记录
func (h *HeartbeatCache) ClearAllDeviceOfflineTimestamps() error {
	return h.redis.Del(vars.RedisDeviceOfflineTS)
}

// ============================================================================
// LAN状态操作
// ============================================================================

// SetLan1OfflineTimestamp 设置LAN1断开时间
func (h *HeartbeatCache) SetLan1OfflineTimestamp(deviceId string, timestamp int64) error {
	return h.redis.HSet(vars.RedisDeviceLanOfflineTS, deviceId, timestamp)
}

// GetLan1OfflineTimestamp 获取LAN1断开时间
func (h *HeartbeatCache) GetLan1OfflineTimestamp(deviceId string) (int64, error) {
	return h.redis.HGetInt64(vars.RedisDeviceLanOfflineTS, deviceId)
}

// SetLan2OfflineTimestamp 设置LAN2断开时间
func (h *HeartbeatCache) SetLan2OfflineTimestamp(deviceId string, timestamp int64) error {
	return h.redis.HSet(vars.RedisDeviceLan2OfflineTS, deviceId, timestamp)
}

// GetLan2OfflineTimestamp 获取LAN2断开时间
func (h *HeartbeatCache) GetLan2OfflineTimestamp(deviceId string) (int64, error) {
	return h.redis.HGetInt64(vars.RedisDeviceLan2OfflineTS, deviceId)
}

// ============================================================================
// 设备授权计数操作
// ============================================================================

// InitDeviceAuthorizedCount 初始化已授权设备计数
func (h *HeartbeatCache) InitDeviceAuthorizedCount(count int64) error {
	return h.redis.Set(vars.RedisDeviceAuthorizedCnt, count)
}

// GetDeviceAuthorizedCount 获取已授权设备计数
func (h *HeartbeatCache) GetDeviceAuthorizedCount() (int64, error) {
	return h.redis.GetInt64(vars.RedisDeviceAuthorizedCnt)
}

// IncreaseDeviceAuthorizedCount 增加已授权设备计数（线程安全）
func (h *HeartbeatCache) IncreaseDeviceAuthorizedCount(increment int64) (int64, error) {
	// 使用Redis的原子操作
	result, err := v.Redis().DoVar("INCRBY", vars.RedisDeviceAuthorizedCnt, increment)
	if err != nil {
		pix_log.Error("[HEARTBEAT_CACHE] IncreaseDeviceAuthorizedCount失败: increment=%d, error=%v", increment, err)
		return 0, err
	}
	return result.Int64(), nil
}

// ============================================================================
// GPS相关辅助方法
// ============================================================================

// GetLastValidGpsInfo 获取设备最后有效GPS信息
func (h *HeartbeatCache) GetLastValidGpsInfo(deviceId string) (lat, lng float64, ts int64, exists bool) {
	heartbeatData, err := h.GetHeartbeatData(deviceId)
	if err != nil {
		return 0, 0, 0, false
	}

	if heartbeatData.LastValidLatitude != 0 && heartbeatData.LastValidLongitude != 0 {
		return heartbeatData.LastValidLatitude, heartbeatData.LastValidLongitude, heartbeatData.LastValidGpsTs, true
	}

	return 0, 0, 0, false
}

// ============================================================================
// 设备信息操作
// ============================================================================

// SetAllDeviceInfo 设置所有设备信息
func (h *HeartbeatCache) SetAllDeviceInfo(deviceInfo string) error {
	return h.redis.Set(vars.RedisDeviceAllInfo, deviceInfo)
}

// GetAllDeviceInfo 获取所有设备信息
func (h *HeartbeatCache) GetAllDeviceInfo() (string, error) {
	return h.redis.Get(vars.RedisDeviceAllInfo)
}

// ============================================================================
// 心跳统计操作（用于监控）
// ============================================================================

// AddHeartbeatToQueue 在redis缓存中记录心跳（用于统计）
func (h *HeartbeatCache) AddHeartbeatToQueue(heartbeatData string) error {
	return h.redis.LPush("HEART", heartbeatData)
}

// GetHeartbeatCount 统计心跳数量
func (h *HeartbeatCache) GetHeartbeatCount() (int64, error) {
	return h.redis.SCard("HEART")
}

// PopHeartbeatFromQueue 随机弹出心跳数据
func (h *HeartbeatCache) PopHeartbeatFromQueue() (string, error) {
	return h.redis.RPop("HEART")
}

package cache

import (
	"ccserver/app/library/redis_util"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// 使用新的Redis工具类实例
var redisUtil = redis_util.NewRedisUtil("CACHE_SERVICE")

// 🗑️ 已删除 GetRedisHeartbeatRecordTs 和 SetRedisHeartbeatRecordTs 函数
// 原因：移除了 record_min_interval 配置，现在所有心跳都会记录到数据库

// GetRedisHeartbeatTs 获取终端心跳时间
// 返回值 val:-1 从未上过线; 0: 0:初始值，若为0，server启动时会统一更改为-1; 其他: 最后一次心跳时间戳
func GetRedisHeartbeatTs(deviceId string) int64 {
	result, _ := redisUtil.HGetInt64(vars.RedisDeviceHeartbeatTS, deviceId)
	return result
}

// SetRedisHeartbeatTs 设置终端心跳时间 val:-1 从未上过线;  非-1: 最后一次心跳时间戳
func SetRedisHeartbeatTs(deviceId string, val int64) {
	redisUtil.HSet(vars.RedisDeviceHeartbeatTS, deviceId, val)
}

// GetRedisDeviceOfflineTs 获取设备的离线时间
// 返回值: 默认为0，表示设备在服务软件本次启动后从未上线； 设置为-1，表示设备在线；设置为其他非负数，表示设备离线时间；
func GetRedisDeviceOfflineTs(deviceId string) int64 {
	result, _ := redisUtil.HGetInt64(vars.RedisDeviceOfflineTS, deviceId)
	return result
}

// SetRedisDeviceOfflineTs 默认为0，表示设备在服务软件本次启动后从未上线； 设置为-1，表示设备在线；设置为其他非负数，表示设备离线时间；
func SetRedisDeviceOfflineTs(deviceId string, val int64) {
	redisUtil.HSet(vars.RedisDeviceOfflineTS, deviceId, val)
}

// EmptyRedisDeviceOfflineTs 清空所有设备的离线时间记录,其实就是让所有的值为0（0表示设备离线）
func EmptyRedisDeviceOfflineTs() {
	redisUtil.Del(vars.RedisDeviceOfflineTS)
}

func InitRedisDeviceAuthorizedCnt(cnt int64) {
	redisUtil.Set(vars.RedisDeviceAuthorizedCnt, cnt)
}

func GetRedisDeviceAuthorizedCnt() int64 {
	result, _ := redisUtil.GetInt64(vars.RedisDeviceAuthorizedCnt)
	return result
}

var deviceAuthorizedCntMutex sync.Mutex

func IncreaseRRedisDeviceAuthorizedCnt(num int64) int64 {
	deviceAuthorizedCntMutex.Lock()
	defer deviceAuthorizedCntMutex.Unlock()

	current, _ := redisUtil.GetInt64(vars.RedisDeviceAuthorizedCnt)
	total := current + num
	redisUtil.Set(vars.RedisDeviceAuthorizedCnt, total)
	return total
}

// GetRedisDeviceHeartbeat 获取设备的最后心跳数据
func GetRedisDeviceHeartbeat(deviceId string) string {
	result, _ := redisUtil.HGet(vars.RedisLastHeartbeat, deviceId)
	return result
}

// SetRedisDeviceHeartbeat 保存设备的最后心跳数据（使用新的缓存工具类）
func SetRedisDeviceHeartbeat(deviceId string, val string) {
	// 反序列化当前心跳数据
	var currentData protocol.HeartbeatRedis
	if err := json.Unmarshal([]byte(val), &currentData); err != nil {
		pix_log.ErrorWithIMSI(deviceId, "心跳数据JSON解析失败: %v", err)
		return
	}

	// 使用新的心跳缓存工具类处理GPS回退逻辑
	if err := HeartbeatCacheInstance.SetHeartbeatData(deviceId, &currentData); err != nil {
		pix_log.ErrorWithIMSI(deviceId, "保存心跳数据到Redis失败: %v", err)
	}
}

// GetRedisLan1OfflineTs 获取LAN断开时间
func GetRedisLan1OfflineTs(deviceId string) int64 {
	result, _ := redisUtil.HGetInt64(vars.RedisDeviceLanOfflineTS, deviceId)
	return result
}

// SetRedisLan1OfflineTs 设置LAN断开时间
func SetRedisLan1OfflineTs(deviceId string, val int64) {
	redisUtil.HSet(vars.RedisDeviceLanOfflineTS, deviceId, val)
}

// GetRedisLan2OfflineTs 获取LAN2断开时间
func GetRedisLan2OfflineTs(deviceId string) int64 {
	result, _ := redisUtil.HGetInt64(vars.RedisDeviceLan2OfflineTS, deviceId)
	return result
}

// SetRedisLan2OfflineTs 设置LAN2断开时间
func SetRedisLan2OfflineTs(deviceId string, val int64) {
	redisUtil.HSet(vars.RedisDeviceLan2OfflineTS, deviceId, val)
}

// ResetRedisIPPool 重置 IP 缓存池
// func ResetRedisIPPool() error {
//	ipRes, err := model.NewOrm(vars.TableIP).All(nil, "")
//	if err != nil {
//		return err
//	}
//	var ipList []vars.TbIP
//	if err := ipRes.Structs(&ipList); err != nil {
//		return err
//	}
//	_, err = v.Redis().Do("DEL", vars.RedisNetworkIPPool)
//	if err != nil {
//		return err
//	}
//	for _, value := range ipList {
//		if nw := net.ParseIP(value.IP); nw != nil {
//			v.Redis().Do("SADD", vars.RedisNetworkIPPool, nw.String())
//		}
//	}
//	return nil
// }

func ResetRedisIPPool() error {
	start := time.Now()

	// 获取所有 IP
	ipRes, err := model.NewOrm(vars.TableIP).All(nil, "")
	if err != nil {
		return err
	}
	fmt.Printf("数据库查询耗时: %v\n", time.Since(start))

	startStruct := time.Now()
	var ipList []vars.TbIP
	if err := ipRes.Structs(&ipList); err != nil {
		return err
	}
	fmt.Printf("结构体转换耗时: %v\n", time.Since(startStruct))

	// 获取 Redis 连接
	conn := v.Redis().Conn()
	defer conn.Close()

	startRedis := time.Now()
	// 开始事务
	err = conn.Send("MULTI")
	if err != nil {
		fmt.Printf("开始事务失败: %s\n", err)
		return err
	}

	// 先删除旧的 IP 池
	err = conn.Send("DEL", vars.RedisNetworkIPPool)
	if err != nil {
		fmt.Printf("发送 DEL 命令失败: %s\n", err)
		return err
	}

	// 如果 IP 列表不为空，批量添加
	if len(ipList) > 0 {
		// 构建批量添加的参数
		args := []interface{}{vars.RedisNetworkIPPool}
		for _, ip := range ipList {
			if ip.IP != "" {
				args = append(args, ip.IP)
			}
		}

		// 一次性添加所有 IP
		if len(args) > 1 {
			err = conn.Send("SADD", args...)
			if err != nil {
				fmt.Printf("发送 SADD 命令失败: %s\n", err)
				return err
			}
		}
	}

	// 执行事务
	_, err = conn.Do("EXEC")
	if err != nil {
		fmt.Printf("提交事务失败: %s\n", err)
		return err
	}
	fmt.Printf("Redis操作耗时: %v\n", time.Since(startRedis))

	fmt.Printf("总耗时: %v\n", time.Since(start))
	return nil
}

// SetDeviceInfo 设置设备数据
func SetDeviceInfo(val string) {
	redisUtil.Set(vars.RedisDeviceAllInfo, val)
}

// GetDeviceInfo 获取设备数据
func GetDeviceInfo() string {
	result, _ := redisUtil.Get(vars.RedisDeviceAllInfo)
	return result
}

// AddHeart 在redis缓存中记录心跳
func AddHeart(heart string) {
	redisUtil.LPush("HEART", heart)
}

// CountHeart 统计心跳数量
func CountHeart() int64 {
	result, _ := redisUtil.SCard("HEART")
	return result
}

// GetHeart 随机弹出心跳数据
func GetHeart(num int64) string {
	result, _ := redisUtil.RPop("HEART")
	return result
}

// GetAllRedisHeartbeatTs 获取所有设备的心跳时间戳
func GetAllRedisHeartbeatTs() map[string]int64 {
	result, err := redisUtil.HGetAllInt64(vars.RedisDeviceHeartbeatTS)
	if err != nil {
		pix_log.Error("获取所有设备心跳时间戳失败: %v", err)
		return make(map[string]int64)
	}
	return result
}

// DelRedisHeartbeatTs 删除设备的心跳时间戳
func DelRedisHeartbeatTs(deviceId string) {
	redisUtil.HDel(vars.RedisDeviceHeartbeatTS, deviceId)
}

// GetDeviceLastValidGpsInfo 获取设备最后有效GPS信息（使用新的缓存工具类）
func GetDeviceLastValidGpsInfo(deviceId string) (lat, lng float64, ts int64, exists bool) {
	return HeartbeatCacheInstance.GetLastValidGpsInfo(deviceId)
}

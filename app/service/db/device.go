package db

import (
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/model"
	"ccserver/app/module/client"
	cacheservice "ccserver/app/service/cache"
	"ccserver/app/vars"
	"fmt"
	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/os/gtime"
	"time"
)

// GetAllDevicesIMSI 获取所有终端的
func GetAllDevicesIMSI() (gdb.Result, error) {
	return model.NewOrm(vars.TableDevice).Db().Fields("imsi").All()
}

// GetAllDevicesImsiAndResetTime 获取所有终端的
func GetAllDevicesImsiAndResetTime() (gdb.Result, error) {
	return model.NewOrm(vars.TableDevice).Db().Fields("imsi", "reset_time").All()
}

// RecordAllDevicesOffline
// 每次停止或启动时都将终端在数据库中的状态设置为离线
// isStarting: true:程序启动中  false:程序停止中
func RecordAllDevicesOffline(isStarting bool) {
	where := gdb.Map{
		"online": 1,
	}
	var defModel model.Model
	defModel = model.NewOrm(vars.TableDevice)
	deviceRes, err := defModel.All(where, "")
	if err != nil {
		v.Log().Errorf("(MySQL)服务器启动或停止, 将所有终端强制下线失败: %s", err)
		return
	}

	offlineReason := "服务启动时修正下线"
	if !isStarting {
		offlineReason = "服务停止时强制下线"
	}

	devices := make([]vars.TbDevice, 0)
	_ = deviceRes.Structs(&devices)

	for _, item := range devices {
		_, _ = AddLoginLog(item.IMSI, offlineReason, 0)
	}

	nowTime := gtime.Datetime()

	if !isStarting { // 程序停止中
		// nowTs := time.Now().Unix()
		// 本周一 0 点
		mondayTs := common.GetMondayTs()

		client.All().Iterator(func(key string, item interface{}) bool {
			// v.Log().Debugf("nowTs: %d, k: %s, v: %+v", nowTs, key, item)
			term := item.(*vars.Terminal)
			thisLoginTs := term.Prop.ConnectAt.Unix()
			if thisLoginTs < mondayTs { // 若终端在本周一前上线的，则以本周一 0 点时间作为起点时间
				thisLoginTs = mondayTs
			}
			// 本次在线时长
			currentOnlineTotalTime := time.Now().Unix() - thisLoginTs
			tsCounter := &gdb.Counter{
				Field: "time_spending",
				Value: float64(currentOnlineTotalTime),
			}
			otCounter := &gdb.Counter{
				Field: "offline_times",
				Value: 1,
			}

			GetCoffee().Table("device").Where("imsi = ?", term.Prop.IMSI).Updates(map[string]interface{}{
				"online":        0,
				"updated_time":  nowTime,
				"offline_time":  nowTime,
				"time_spending": currentOnlineTotalTime,
				"offline_times": 1,
			})

			d := gdb.Map{
				"online":        0,
				"updated_time":  nowTime,
				"offline_time":  nowTime,
				"time_spending": tsCounter,
				"offline_times": otCounter,
			}

			w := g.Map{
				"imsi": term.Prop.IMSI,
			}
			if _, e := model.NewOrm(vars.TableDevice).Mod(w, d); e != nil {
				v.LogTcp().Errorf("(MySQL)离线终端(%s)失败: %s", term.Prop.IMSI, e)
			}

			return true
		})
	} else { // 程序启动中
		otCounter := &gdb.Counter{
			Field: "offline_times",
			Value: 1,
		}

		GetCoffee().Table("device").Where("online = ?", 1).Updates(map[string]interface{}{
			"online":        0,
			"updated_time":  nowTime,
			"offline_time":  nowTime,
			"offline_times": 1,
		})

		data := gdb.Map{
			"online":        0,
			"updated_time":  nowTime,
			"offline_time":  nowTime,
			"offline_times": otCounter,
		}
		if _, e := defModel.Mod(where, data); e != nil {
			v.LogTcp().Errorf("%s, 批量修正终端下线状态失败:%s", offlineReason, err)
		}
	}
}

// RecordDeviceOffline 终端离线时将终端在数据库中的状态设置为离线
func RecordDeviceOffline(term *vars.Terminal, reason string) {
	// DebugOfOffline v.LogTcp().Debugf("RecordDeviceOffline, IMSI:%s, id:%d", term.Prop.IMSI, term.Prop.ID)
	if term.Prop.ID == 0 {
		return
	}

	cacheservice.SetRedisDeviceOfflineTs(term.Prop.IMSI, time.Now().Unix())

	// 本周一 0 点
	mondayTs := common.GetMondayTs()
	thisLoginTs := term.Prop.ConnectAt.Unix()
	if thisLoginTs < mondayTs { // 若终端在本周一前上线的，则以本周一 0 点时间作为起点时间
		thisLoginTs = mondayTs
	}
	// 本次在线时长
	currentOnlineTotalTime := time.Now().Unix() - thisLoginTs
	tsCounter := &gdb.Counter{
		Field: "time_spending",
		Value: float64(currentOnlineTotalTime),
	}
	otCounter := &gdb.Counter{
		Field: "offline_times",
		Value: 1,
	}

	nowTime := gtime.Datetime()
	data := gdb.Map{
		"online":        0,
		"updated_time":  nowTime,
		"offline_time":  nowTime,
		"time_spending": tsCounter,
		"offline_times": otCounter,
	}
	where := g.Map{
		"imsi": term.Prop.IMSI,
	}

	GetCoffee().Table("device").Where("imsi = ?", term.Prop.IMSI).Updates(map[string]interface{}{
		"online":        0,
		"updated_time":  nowTime,
		"offline_time":  nowTime,
		"time_spending": currentOnlineTotalTime,
		"offline_times": 1,
	})

	if _, e := model.NewOrm(vars.TableDevice).Mod(where, data); e != nil {
		v.LogTcp().Errorf("更新终端下线相关状态(IMSI:%s)失败:%s", term.Prop.IMSI, e)
	} else {
		// DebugOfOffline v.LogTcp().Debugf("更新设备的到离线状态成功, IMSI:%s, id:%d", term.Prop.IMSI, term.Prop.ID)
	}

	if _, e := AddLoginLog(term.Prop.IMSI, reason, 0); e != nil {
		v.LogTcp().Errorf("添加终端(IMSI:%s)下线日志失败:%s", term.Prop.IMSI, e)
	}
}

// GetDeviceCount 终端总数
func GetDeviceCount() (int, error) {
	var deviceM model.Model
	deviceM = model.NewOrm(vars.TableDevice)
	_, err := deviceM.Rows(model.RowsOnly).All(g.Map{}, "")
	if err != nil {
		return 0, fmt.Errorf("终端数量统计失败: %s", err)
	}
	return deviceM.Count(), nil
}

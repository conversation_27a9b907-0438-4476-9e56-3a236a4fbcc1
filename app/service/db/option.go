package db

import (
	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/util/gconv"
	"ccserver/app/model"
	"ccserver/app/vars"
)

var (
	OptionTerminalLogTimeout = "terminal_log_timeout"

	// UDP 广播选项
	OptionBroadcastServerStart  = "broadcast_server_start"
	OptionBroadcastLoopInterval = "broadcast_loop_interval"
	OptionBroadcastSendInterval = "broadcast_send_interval"
	OptionBroadcastUdpPort      = "broadcast_udp_port"
	OptionBroadcastMsgIP        = "broadcast_msg_ip"
	OptionBroadcastMsgPort      = "broadcast_msg_port"

	// 网络 IP 选项
	OptionManageIP   = "manage_ip"   // 网管网 IP
	OptionBusinessIP = "business_ip" // 业务网 IP
)

// GetAllOptions 获取所有配置选项
func InitOptions() error {
	options, err := GetAllOptions()
	if err != nil {
		return err
	}
	for _, option := range options {
		//log.Printf("option: %+v\n", option)
		switch option.OptionKey {
		case OptionBroadcastServerStart:
			vars.Option.BroadcastServerStart = gconv.Bool(option.OptionValue)

		case OptionTerminalLogTimeout:
			vars.Option.TerminalLogTimeout = gconv.Int64(option.OptionValue)

		case OptionBroadcastLoopInterval:
			vars.Option.BroadcastLoopInterval = gconv.Int64(option.OptionValue)

		case OptionBroadcastSendInterval:
			vars.Option.BroadcastSendInterval = gconv.Int64(option.OptionValue)

		case OptionBroadcastUdpPort:
			vars.Option.BroadcastUdpPort = gconv.Int(option.OptionValue)

		case OptionBroadcastMsgIP:
			vars.Option.BroadcastMsgIP = option.OptionValue

		case OptionBroadcastMsgPort:
			vars.Option.BroadcastMsgPort = gconv.Int(option.OptionValue)

		case OptionManageIP: // 网管网 IP
			vars.Option.ManageIP = option.OptionValue

		case OptionBusinessIP: // 业务网 IP
			vars.Option.BusinessIP = option.OptionValue
		}
	}
	return nil
}

// GetAllOptions 获取所有配置选项
func GetAllOptions() (options []vars.TbOption, err error) {
	optionRes, err := model.NewOrm(vars.TableOption).All(g.Map{}, "")
	if err != nil {
		return options, err
	}
	if e := optionRes.Structs(&options); e != nil {
		return options, e
	}
	return options, nil
}

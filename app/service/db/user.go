package db

import (
	"github.com/gogf/gf/database/gdb"
	"ccserver/app/model"
	"ccserver/app/vars"
	"time"
)

// XJUsers 获取市级管理员列表
// 	clear: 是否清除缓存
func XJUsers(clear bool) (gdb.Result, error) {

	cacheTime := 24 * time.Hour
	cacheKey := "xj-user-list"
	if clear {
		model.NewOrm("").GetDB().Table(vars.TableUser).Cache(-1, cacheKey).Update()
	}
	return model.NewOrm("").GetDB().Table(vars.TableUser).Cache(cacheTime, cacheKey).All()
}

func AllUser(clear bool) (gdb.Result, error) {
	cacheTime := 24 * time.Hour
	cacheKey := "all-user-list"
	if clear {
		model.NewOrm("").GetDB().Table(vars.TableUser).Where("status = 0").Where("is_delete = 0").Cache(-1, cacheKey).Update()
	}
	return model.NewOrm("").GetDB().Table(vars.TableUser).Where("status = 0").Where("is_delete = 0").Cache(cacheTime, cacheKey).All()
}

package db

import (
	"ccserver/app/vars"
	"database/sql"
	"fmt"
	"github.com/pelletier/go-toml"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"time"
)

var (
	dbPixmoving *gorm.DB
	dbWurenche  *gorm.DB
	dbJzy       *gorm.DB
	dbCoffee    *gorm.DB
	connDb      *sql.DB
	connDb2     *sql.DB
	connDb3     *sql.DB
	connDb4     *sql.DB
	err         error
	config      *toml.Tree
	config2     *toml.Tree
	config3     *toml.Tree
	config4     *toml.Tree
)

// func NewConnection() *gorm.DB {
//	// config, err = toml.LoadFile("config/config.toml") //加载toml文件
//	// if err != nil {
//	// 	log.Fatalf(err.Error())
//	// }
//
//	dbConfig := vars.Config.Database.Default
//	host := dbConfig.Host
//	port := dbConfig.Port
//	user := dbConfig.User
//	pass := dbConfig.Pass
//	name := dbConfig.Name
//
//	dsn := user + ":" + pass + "@tcp(" + host + ":" + fmt.Sprintf("%d", port) + ")/" + name + "?charset=utf8mb4&parseTime=True&loc=Local"
//	dbPixmoving, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
//		Logger: logger.Default.LogMode(logger.Silent),
//	})
//	if err != nil {
//		log.Fatalf(err.Error())
//	}
//
//	connDb, err = dbPixmoving.DB()
//	if err != nil {
//		log.Fatalf(err.Error())
//	}
//
//	connDb.SetMaxIdleConns(10)           // 最大空闲连接数
//	connDb.SetMaxOpenConns(5000)         // 最大连接数
//	connDb.SetConnMaxLifetime(time.Hour) // 设置连接空闲超时
//
//	return dbPixmoving
// }

func NewConnection() *gorm.DB {
	startTime := time.Now()
	fmt.Println("开始初始化数据库连接...")

	dbConfig := vars.Config.Database.Default
	host := dbConfig.Host
	port := dbConfig.Port
	user := dbConfig.User
	pass := dbConfig.Pass
	name := dbConfig.Name

	// 打印配置信息
	fmt.Printf("数据库配置: Host=%s, Port=%d, User=%s, Name=%s\n",
		host, port, user, name)

	// 添加连接超时参数
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=3s&readTimeout=2s&writeTimeout=2s",
		user, pass, host, port, name)

	fmt.Printf("使用 DSN: %s\n", dsn)

	// 尝试先用 database/sql 测试连接
	t1 := time.Now()
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("SQL Open failed: %v", err)
	}
	defer db.Close()

	// 设置连接参数
	db.SetConnMaxLifetime(time.Minute * 3)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)

	// 测试连接
	err = db.Ping()
	if err != nil {
		log.Fatalf("Database ping failed: %v", err)
	}
	fmt.Printf("原生 SQL 连接测试耗时: %v\n", time.Since(t1))

	// GORM 连接
	t2 := time.Now()
	dbPixmoving, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		// 添加慢查询日志
		QueryFields: true,
		PrepareStmt: true,
	})
	if err != nil {
		log.Fatalf("GORM Open failed: %v", err)
	}
	fmt.Printf("GORM 连接耗时: %v\n", time.Since(t2))

	// 获取底层的 *sql.DB
	connDb, err = dbPixmoving.DB()
	if err != nil {
		log.Fatalf("Get underlying db failed: %v", err)
	}

	// 设置连接池参数
	connDb.SetMaxIdleConns(10)
	connDb.SetMaxOpenConns(100)
	connDb.SetConnMaxLifetime(time.Hour)
	connDb.SetConnMaxIdleTime(time.Minute * 5)

	fmt.Printf("数据库连接总耗时: %v\n", time.Since(startTime))

	return dbPixmoving
}

func GetPixmoving() *gorm.DB {
	// if err = connDb.Ping(); err != nil {
	//	err = connDb.Close()
	//	if err != nil {
	//		return nil
	//	}
	//	dbPixmoving = NewConnection()
	// }
	return dbPixmoving
}

func NewConnectionWurenche() *gorm.DB {
	dbConfig := vars.Config.Database.Wurenche
	host := dbConfig.Host
	port := dbConfig.Port
	user := dbConfig.User
	pass := dbConfig.Pass
	name := dbConfig.Name

	dsn := user + ":" + pass + "@tcp(" + host + ":" + fmt.Sprintf("%d", port) + ")/" + name + "?charset=utf8mb4&parseTime=True&loc=Local"

	dbWurenche, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: nil,
	})
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb2, err = dbWurenche.DB()
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb2.SetMaxIdleConns(10)           // 最大空闲连接数
	connDb2.SetMaxOpenConns(100)          // 最大连接数
	connDb2.SetConnMaxLifetime(time.Hour) // 设置连接空闲超时
	return dbWurenche
}

func NewConnectionJzy() *gorm.DB {
	dbConfig := vars.Config.Database.Jzy
	host := dbConfig.Host
	port := dbConfig.Port
	user := dbConfig.User
	pass := dbConfig.Pass
	name := dbConfig.Name

	dsn := user + ":" + pass + "@tcp(" + host + ":" + fmt.Sprintf("%d", port) + ")/" + name + "?charset=utf8mb4&parseTime=True&loc=Local"
	fmt.Printf("数据库连接配置：%s", dsn)
	dbJzy, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		// Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb3, err = dbJzy.DB()
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb3.SetMaxIdleConns(10)           // 最大空闲连接数
	connDb3.SetMaxOpenConns(100)          // 最大连接数
	connDb3.SetConnMaxLifetime(time.Hour) // 设置连接空闲超时

	return dbJzy
}

func NewConnectionCoffee() *gorm.DB {
	dbConfig := vars.Config.Database.Coffee
	host := dbConfig.Host
	port := dbConfig.Port
	user := dbConfig.User
	pass := dbConfig.Pass
	name := dbConfig.Name

	dsn := user + ":" + pass + "@tcp(" + host + ":" + fmt.Sprintf("%d", port) + ")/" + name + "?charset=utf8mb4&parseTime=True&loc=Local"
	dbCoffee, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		// Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb4, err = dbCoffee.DB()
	if err != nil {
		log.Fatalf(err.Error())
	}

	connDb4.SetMaxIdleConns(10)           // 最大空闲连接数
	connDb4.SetMaxOpenConns(100)          // 最大连接数
	connDb4.SetConnMaxLifetime(time.Hour) // 设置连接空闲超时

	return dbCoffee
}

func GetDBWurenche() *gorm.DB {
	// if err = connDb.Ping(); err != nil {
	//	err = connDb.Close()
	//	if err != nil {
	//		return nil
	//	}
	//	dbPixmoving = NewConnection()
	// }
	return dbWurenche
}

func GetDBJzy() *gorm.DB {
	return dbJzy
}

func GetCoffee() *gorm.DB {
	return dbCoffee
}

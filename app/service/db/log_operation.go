package db

import (
	"database/sql"
	"github.com/gogf/gf/os/gtime"
	"ccserver/app/model"
	"ccserver/app/vars"
)

// AddOperationLog 记录用户操作日志
func AddOperationLog(user vars.UserInfo, message string, statusParam ...int64) (result sql.Result, err error) {
	var status int64 = 2
	if len(statusParam) > 0 {
		status = statusParam[0]
	}
	nowTime := gtime.Datetime()
	logData := &vars.TbOperationLog{
		UID:         user.ID,
		Username:    user.Username,
		Message:     message,
		Status:      status,
		CreatedTime: nowTime,
		UpdatedTime: nowTime,
	}
	return model.NewOrm(vars.TableOperationLog).Add(logData)
}

func AddSysAutoOperationLog(message string, statusParam ...int64) (result sql.Result, err error) {
	var status int64 = 2
	if len(statusParam) > 0 {
		status = statusParam[0]
	}
	nowTime := gtime.Datetime()
	logData := &vars.TbOperationLog{
		UID:         0,
		Username:    "[系统]",
		Message:     message,
		Status:      status,
		CreatedTime: nowTime,
		UpdatedTime: nowTime,
	}
	return model.NewOrm(vars.TableOperationLog).Add(logData)
}

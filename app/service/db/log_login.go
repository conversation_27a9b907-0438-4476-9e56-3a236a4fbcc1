package db

import (
	"database/sql"
	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/os/gtime"
	"ccserver/app/model"
	"ccserver/app/vars"
)

// AddLoginLog 添加上下线日志
// status 0:下线  1:上线
func AddLoginLog(imsi, reason string, status int64) (sql.Result, error) {
	logData := &vars.TbLoginLog{
		IMSI:      imsi,
		Status:    status,
		Reason:    reason,
		CreatedTs: gtime.Timestamp(),
	}
	return model.NewOrm(vars.TableLoginLog).Add(logData)
}

// GetLoginHistory 获取上下线日志
// status 0:下线  1:上线
func GetLoginHistory(where interface{}, order string) (gdb.Result, error) {
	return model.NewOrm(vars.TableLoginLog).All(where, order)
}

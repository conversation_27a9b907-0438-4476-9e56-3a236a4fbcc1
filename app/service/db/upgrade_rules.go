package db

import (
	"github.com/gogf/gf/database/gdb"
	"github.com/gogf/gf/frame/g"
	"ccserver/app/model"
	"ccserver/app/vars"
)

// ModUpgradeRulesDisable 将升级策略的状态全部改成未执行
func ModUpgradeRulesDisable() {
	model.NewOrm(vars.TableUpgradeRules).Mod(1, g.Map{"status": 0})
}

// GetUpgradeRulesEnable 获取正在执行的升级策略
func GetUpgradeRulesEnable() (result gdb.Result, err error) {
	return model.NewOrm(vars.TableUpgradeRules).All(g.Map{"status": 1}, "")
}

package db

import (
	"database/sql"
	"github.com/gogf/gf/os/gtime"
	"ccserver/app/model"
	"ccserver/app/vars"
)

func AddMessageLog(msg vars.MessageLog, operate []byte) (result sql.Result, err error) {
	var errMsg string
	if msg.ErrCode != 0 {
		errMsg = vars.ErrorMsg(msg.ErrCode)
	}
	nowTime := gtime.Datetime()
	logData := &vars.TbMessageLog{
		OperateId:   msg.OperateId,
		OperationId: msg.OperationId,
		IMSI:        msg.IMSI,
		Message:     string(operate),
		Content:     msg.Content,
		ErrorCode:   msg.ErrCode,
		ErrorMsg:    errMsg,
		Status:      msg.Status,
		CreatedTime: nowTime,
		UpdatedTime: nowTime,
	}
	return model.NewOrm(vars.TableMessageLog).Add(logData)
}

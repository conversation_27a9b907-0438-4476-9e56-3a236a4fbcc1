package tdengine

import (
	"ccserver/app/vars"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// TDengineClient TDengine REST API客户端
type TDengineClient struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	BaseURL  string
}

// DeviceStatus 设备状态结构
type DeviceStatus struct {
	IMSI             string    `json:"imsi"`
	DeviceName       string    `json:"device_name"`
	CurrentStatus    int       `json:"current_status"`    // 0=离线, 1=在线
	LastHeartbeatTime time.Time `json:"last_heartbeat_time"`
	OnlineDuration   int64     `json:"online_duration"`   // 在线时长（秒）
	Position         Position  `json:"position"`
	Speed            int       `json:"speed"`             // 速度 km/h
	Mileage          int       `json:"mileage"`           // 里程 km
	WorkMode         int       `json:"work_mode"`         // 工作模式
	TaskStatus       TaskInfo  `json:"task_status"`
	HeartbeatDelay   int       `json:"heartbeat_delay"`   // 心跳延迟（秒）
	DeviceType       int       `json:"device_type"`       // 设备类型
	BatteryLevel     int       `json:"battery_level"`     // 电池电量
	SignalStrength   int       `json:"signal_strength"`   // 信号强度
	Temperature      int       `json:"temperature"`       // 温度
	ErrorCount       int       `json:"error_count"`       // 错误计数
	LastMaintenanceTime time.Time `json:"last_maintenance_time"` // 最后维护时间
	FirmwareVersion  string    `json:"firmware_version"`  // 固件版本
}

// Position GPS位置信息
type Position struct {
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
	LocationName string  `json:"location_name"`
}

// TaskInfo 任务信息
type TaskInfo struct {
	TaskID         int    `json:"task_id"`
	Step           int    `json:"step"`           // 0=空闲, 1=执行中, 2=完成
	Progress       int    `json:"progress"`       // 进度百分比
	LeftTime       int    `json:"left_time"`      // 剩余时间（秒）
	CurrentStation string `json:"current_station"`
	TargetStation  string `json:"target_station"`
	TaskDistance   int    `json:"task_distance"`  // 任务距离（米）
	StartTime      int64  `json:"start_time"`     // 开始时间戳
}

// TDengineResponse TDengine REST API响应结构
type TDengineResponse struct {
	Status     string          `json:"status"`
	Head       []string        `json:"head"`
	ColumnMeta [][]interface{} `json:"column_meta"`
	Data       [][]interface{} `json:"data"`
	Rows       int             `json:"rows"`
}

// NewTDengineClient 创建TDengine客户端
func NewTDengineClient() *TDengineClient {
	config := vars.Config.Database.Tdengine
	client := &TDengineClient{
		Host:     config.Host,
		Port:     config.Port,
		User:     config.User,
		Password: config.Pass,
		Database: config.Name,
	}
	client.BaseURL = fmt.Sprintf("http://%s:%d/rest/sql", client.Host, client.Port)
	return client
}

// executeSQL 执行SQL查询
func (c *TDengineClient) executeSQL(sql string) (*TDengineResponse, error) {
	pix_log.Info("[TDENGINE] 执行SQL: %s", sql)

	// 直接发送SQL作为请求体
	req, err := http.NewRequest("POST", c.BaseURL, strings.NewReader(sql))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "text/plain")
	req.SetBasicAuth(c.User, c.Password)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	pix_log.Info("[TDENGINE] 响应内容: %s", string(body))

	// 解析响应
	var tdResp TDengineResponse
	if err := json.Unmarshal(body, &tdResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 响应内容: %s", err, string(body))
	}

	if tdResp.Status != "succ" {
		return nil, fmt.Errorf("TDengine查询失败: %s", string(body))
	}

	pix_log.Info("[TDENGINE] 查询成功，返回 %d 行数据", tdResp.Rows)
	return &tdResp, nil
}

// GetDeviceTables 获取所有设备心跳表
func (c *TDengineClient) GetDeviceTables() ([]string, error) {
	// 先切换数据库
	useSQL := fmt.Sprintf("USE %s", c.Database)
	_, err := c.executeSQL(useSQL)
	if err != nil {
		return nil, fmt.Errorf("切换数据库失败: %v", err)
	}

	// 再查询表
	showSQL := "SHOW TABLES LIKE 'device_heartbeat_%'"
	resp, err := c.executeSQL(showSQL)
	if err != nil {
		return nil, err
	}

	var tables []string
	for _, row := range resp.Data {
		if len(row) > 0 {
			if tableName, ok := row[0].(string); ok {
				tables = append(tables, tableName)
			}
		}
	}

	pix_log.Info("[TDENGINE] 找到 %d 个设备表", len(tables))
	return tables, nil
}

// GetLatestDeviceStatus 获取设备最新状态
func (c *TDengineClient) GetLatestDeviceStatus(tableName string) (*DeviceStatus, error) {
	// 提取IMSI（从表名中提取）
	imsi := strings.TrimPrefix(tableName, "device_heartbeat_")
	
	sql := fmt.Sprintf(`
		SELECT 
			ts, lat, lng, spd, tm, mode, latest_task
		FROM %s.%s 
		ORDER BY ts DESC 
		LIMIT 1
	`, c.Database, tableName)

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	if len(resp.Data) == 0 {
		return nil, fmt.Errorf("设备 %s 无数据", imsi)
	}

	row := resp.Data[0]
	if len(row) < 7 {
		return nil, fmt.Errorf("设备 %s 数据格式不正确", imsi)
	}

	// 解析时间戳
	var lastHeartbeat time.Time
	if tsStr, ok := row[0].(string); ok {
		if ts, err := time.Parse("2006-01-02 15:04:05.000", tsStr); err == nil {
			lastHeartbeat = ts
		}
	}

	// 解析GPS坐标
	var lat, lng float64
	if latVal, ok := row[1].(float64); ok {
		lat = latVal
	}
	if lngVal, ok := row[2].(float64); ok {
		lng = lngVal
	}

	// 解析其他字段
	var speed, mileage, mode int
	if spdVal, ok := row[3].(float64); ok {
		speed = int(spdVal)
	}
	if tmVal, ok := row[4].(float64); ok {
		mileage = int(tmVal)
	}
	if modeVal, ok := row[5].(float64); ok {
		mode = int(modeVal)
	}

	// 解析任务信息JSON
	var taskInfo TaskInfo
	if latestTaskStr, ok := row[6].(string); ok && latestTaskStr != "" {
		var taskData map[string]interface{}
		if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err == nil {
			if taskID, ok := taskData["task_id"].(float64); ok {
				taskInfo.TaskID = int(taskID)
			}
			if step, ok := taskData["step"].(float64); ok {
				taskInfo.Step = int(step)
			}
			if progress, ok := taskData["progress"].(float64); ok {
				taskInfo.Progress = int(progress)
			}
			if leftTime, ok := taskData["left_time"].(float64); ok {
				taskInfo.LeftTime = int(leftTime)
			}
			if currentStation, ok := taskData["current_station"].(string); ok {
				taskInfo.CurrentStation = currentStation
			}
			if targetStation, ok := taskData["target_station"].(string); ok {
				taskInfo.TargetStation = targetStation
			}
		}
	}

	// 判断在线状态（基于心跳时间）
	currentStatus := 0
	heartbeatTimeout := vars.Config.Business.HeartbeatTimeout
	if time.Since(lastHeartbeat) <= heartbeatTimeout {
		currentStatus = 1
	}

	// 计算在线时长
	onlineDuration := int64(0)
	if currentStatus == 1 {
		onlineDuration = int64(time.Since(lastHeartbeat).Seconds())
	}

	// 生成位置名称（简化版）
	locationName := fmt.Sprintf("位置(%.4f,%.4f)", lat, lng)

	device := &DeviceStatus{
		IMSI:              imsi,
		DeviceName:        fmt.Sprintf("设备-%s", imsi),
		CurrentStatus:     currentStatus,
		LastHeartbeatTime: lastHeartbeat,
		OnlineDuration:    onlineDuration,
		Position: Position{
			Latitude:     lat,
			Longitude:    lng,
			LocationName: locationName,
		},
		Speed:               speed,
		Mileage:             mileage,
		WorkMode:            mode,
		TaskStatus:          taskInfo,
		HeartbeatDelay:      int(time.Since(lastHeartbeat).Seconds()),
		DeviceType:          0,
		BatteryLevel:        85 + (int(imsi[len(imsi)-1:][0]) % 15), // 模拟电量
		SignalStrength:      3 + (int(imsi[len(imsi)-1:][0]) % 3),   // 模拟信号强度
		Temperature:         20 + (int(imsi[len(imsi)-1:][0]) % 20), // 模拟温度
		ErrorCount:          0,
		LastMaintenanceTime: time.Now().AddDate(0, 0, -7), // 7天前维护
		FirmwareVersion:     "v2.1.0",
	}

	return device, nil
}

// GetAllDevicesStatus 获取所有设备状态
func (c *TDengineClient) GetAllDevicesStatus() ([]*DeviceStatus, error) {
	tables, err := c.GetDeviceTables()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceStatus
	for _, table := range tables {
		device, err := c.GetLatestDeviceStatus(table)
		if err != nil {
			pix_log.Warning("[TDENGINE] 获取设备状态失败: %s, 错误: %v", table, err)
			continue
		}
		devices = append(devices, device)
	}

	pix_log.Info("[TDENGINE] 成功获取 %d 个设备状态", len(devices))
	return devices, nil
}

// GetDeviceHistory 获取设备历史数据
func (c *TDengineClient) GetDeviceHistory(imsi string, startTime, endTime time.Time, limit int) ([]*DeviceStatus, error) {
	tableName := fmt.Sprintf("device_heartbeat_%s", imsi)
	
	sql := fmt.Sprintf(`
		SELECT 
			ts, lat, lng, spd, tm, mode, latest_task
		FROM %s.%s 
		WHERE ts >= '%s' AND ts <= '%s'
		ORDER BY ts DESC 
		LIMIT %d
	`, c.Database, tableName, 
		startTime.Format("2006-01-02 15:04:05"), 
		endTime.Format("2006-01-02 15:04:05"), 
		limit)

	resp, err := c.executeSQL(sql)
	if err != nil {
		return nil, err
	}

	var devices []*DeviceStatus
	for _, row := range resp.Data {
		if len(row) < 7 {
			continue
		}

		// 解析数据（类似GetLatestDeviceStatus的逻辑）
		var lastHeartbeat time.Time
		if tsStr, ok := row[0].(string); ok {
			if ts, err := time.Parse("2006-01-02 15:04:05.000", tsStr); err == nil {
				lastHeartbeat = ts
			}
		}

		var lat, lng float64
		if latVal, ok := row[1].(float64); ok {
			lat = latVal
		}
		if lngVal, ok := row[2].(float64); ok {
			lng = lngVal
		}

		var speed, mileage, mode int
		if spdVal, ok := row[3].(float64); ok {
			speed = int(spdVal)
		}
		if tmVal, ok := row[4].(float64); ok {
			mileage = int(tmVal)
		}
		if modeVal, ok := row[5].(float64); ok {
			mode = int(modeVal)
		}

		var taskInfo TaskInfo
		if latestTaskStr, ok := row[6].(string); ok && latestTaskStr != "" {
			var taskData map[string]interface{}
			if err := json.Unmarshal([]byte(latestTaskStr), &taskData); err == nil {
				if taskID, ok := taskData["task_id"].(float64); ok {
					taskInfo.TaskID = int(taskID)
				}
				if step, ok := taskData["step"].(float64); ok {
					taskInfo.Step = int(step)
				}
			}
		}

		currentStatus := 0
		heartbeatTimeout := vars.Config.Business.HeartbeatTimeout
		if time.Since(lastHeartbeat) <= heartbeatTimeout {
			currentStatus = 1
		}

		device := &DeviceStatus{
			IMSI:              imsi,
			DeviceName:        fmt.Sprintf("设备-%s", imsi),
			CurrentStatus:     currentStatus,
			LastHeartbeatTime: lastHeartbeat,
			Position: Position{
				Latitude:     lat,
				Longitude:    lng,
				LocationName: fmt.Sprintf("位置(%.4f,%.4f)", lat, lng),
			},
			Speed:      speed,
			Mileage:    mileage,
			WorkMode:   mode,
			TaskStatus: taskInfo,
		}

		devices = append(devices, device)
	}

	return devices, nil
}

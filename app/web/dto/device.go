package dto

import model2 "ccserver/app/model"

type RespDeviceForm struct {
	ID          int64  `json:"id"`
	IMEI        string `json:"imei"`
	IMSI        string `json:"imsi"`
	IP          string `json:"ip"`
	Name        string `json:"name"`
	Vender      string `json:"vender"`
	CI          string `json:"ci"`
	PCI         string `json:"pci"`
	ManageIp    string `json:"manage_ip"`
	Model       string `json:"model"`
	SoftwareVer string `json:"software_ver"`
	HardwareVer string `json:"hardware_ver"`
	UID         string `json:"uid"`
	UserId      int    `json:"user_id"`
	RSRP        int64  `json:"rsrp"`
	SINR        int64  `json:"sinr"`
	RSSI        int64  `json:"rssi"`
	RSRQ        int64  `json:"rsrq"`
	Online      int    `json:"online"`
	// Warning         int    `json:"warning"`       // WAN 口告警状态
	OnlineBigint    int64  `json:"online_bigint"` // 本次在线时长
	TimeSpending    int64  `json:"time_spending"` // 本周在线时长
	OnlineTimes     int64  `json:"online_times"`  // 上线次数
	OfflineTimes    int64  `json:"offline_times"` // 下线次数
	OnlineTime      string `json:"online_time"`
	OfflineTime     string `json:"offline_time"`
	CreatedTime     string `json:"created_time"`
	UpdatedTime     string `json:"updated_time"`
	Longitude       string `json:"longitude"`
	Latitude        string `json:"latitude"`
	Position        string `json:"position"`
	WanRx           int64  `json:"wan_rx"`
	WanTx           int64  `json:"wan_tx"`
	UpgradeState    string `json:"upgrade_state"`
	UpgradeProgress string `json:"upgrade_progress"`

	/*
		Located bool  `json:"located"`
		Sta     uint8 `json:"sta"`
		Mode    uint8 `json:"mode"`
	*/
	Acc        int8             `json:"acc"`        /* ACC状态,即钥匙状态; 整型数S8; 0:ACC off, 1:ACC on, -1:未知; */
	Gear       int8             `json:"gear"`       /* 档位; 整型数S8; 0:P档, 2:R档, 3:N档, 4:D档, -1:未知; */
	Door       int8             `json:"door"`       /* 车门开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Light      int8             `json:"light"`      /* 灯光开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Win        int8             `json:"win"`        /* 车窗开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Tm         int32            `json:"tm"`         /* 总里程(total mileages); 整型数S32; 单位km; -1:未知; */
	Rm         int16            `json:"rm"`         /* 当前电量可跑的续航里程; 整型数S16; 单位km; -1:未知; */
	Ste        int16            `json:"ste"`        /* 方向盘(steering wheel)转角; 浮点数S16; 单位0.1度; -1:未知 */
	Seat       [6]int           `json:"seat"`       /* 座位信息 */
	Brk        int16            `json:"brk"`        /* 制动(braking)值; 整型数S16; 单位0.1Mp; 保留1位小数; -1:未知*/
	HeadMode   uint8            `json:"headMode"`   /* 当前转向模式; 整型数U8; 1:前后异向模式, 2:常规模式, 3：前后同向模式; */
	Sgn        uint8            `json:"sgn"`        /* 4G/5G网络信号强度百分比; 整型数U8; */
	Spd        int16            `json:"spd"`        /* 车速; 整型数S16; 单位km/h; -1:未知; */
	SpdL       int16            `json:"spdL"`       /* 最高限速; 整型数S16; 单位km/h; -1:未知; */
	PL         int8             `json:"pL"`         /* 剩余电量百分比; 整型数S8; -1:未知; */
	PV         int16            `json:"pV"`         /* 大电瓶电压; 整型数S16; 单位0.1V; -1:未知; */
	PC         int16            `json:"pC"`         /* 总电流; 整型数S16; 单位0.1A; -1:未知 */
	PCh        int8             `json:"pCh"`        /* 充电状态; 整型数S8; 0:未充电, 1:充电中, -1:未知; */
	Bat        int16            `json:"bat"`        /* 小电瓶电压; 整型数S16; 单位0.1V; -1:未知态; */
	Located    bool             `json:"located"`    /* GPS是否定位; bool型; true:定位, false:未定位; */
	Lng        float64          `json:"lng"`        /* GPS经度; 浮点数Float64; 单位度; 保留6位小数; */
	Lat        float64          `json:"lat"`        /* GPS纬度; 浮点数Float64; 单位度; 保留6位小数; */
	Alt        float32          `json:"alt"`        /* GPS海拔; 浮点数Float32; 单位米; 保留1位小数; */
	Angle      float32          `json:"angle"`      /* GPS航向角度; 浮点数Float32; 单位度; 保留1位小数; */
	SatCnt     uint8            `json:"satCnt"`     /* 有效GPS卫星数; 整型数; */
	Mode       uint8            `json:"mode"`       /* 车辆驾驶模式; 0:空闲状态(idle), 1:自动驾驶模式中, 2:遥控模式, 3:路径规划中; */
	Sta        uint8            `json:"sta"`        /* 车辆状态; 整型数U8; 0:正常, 1:异常; */
	Err        string           `json:"err"`        /* 车辆状态异常原因; "":无异常, 非空字符串:异常原因; */
	Event      int8             `json:"event"`      /* 触发此车辆实时状态消息上报的原因; 整型数; 取值见下表event取值对照表; */
	Warning    []int            `json:"warning"`    /* 无符号32位整型 报警标志位;取值详见报警预警标志位定义*/
	Ts         int64            `json:"ts"`         /* 消息时间戳Unix时间 单位秒 */
	LatestTask string           `json:"latestTask"` /* 车辆最近的任务 */
	VideoName  string           `json:"video_name"` /*视频名称*/
	VideoUrl   string           `json:"video_url"`  /*视频下载地址*/
	Advert     string           `json:"advert"`     /*广告地址*/
	AdvertType int              `json:"advert_type"`
	VSta       int              `json:"vSta"`
	MapId      int              `json:"map_id"`
	Task       model2.Task      `json:"task"`
	TaskChild  model2.TaskChild `json:"task_child"`
	Type       int              `json:"type"` // 车辆类型
	Thr        float32          `json:"thr"`
	ExpireTime int64            `json:"expire_time"`
	EmgSta     uint8            `json:"emgSta"`
	AirCon     uint8            `json:"airCon"`
	InTemp     uint8            `json:"inTemp"`
	OutTemp    uint8            `json:"outTemp"`
	Smoke      int32            `json:"smoke"`
	Co2        int32            `json:"co2"`
	AlarmCnt1  string           `json:"alarmCnt1"`
	AlarmCnt2  string           `json:"alarmCnt2"`
	Alarm      string           `json:"alarm"`
}

type AnalysisUser struct {
	No           int    `json:"no"`
	Uid          int64  `json:"uid"`
	Username     string `json:"username"`
	Total        int    `json:"total"`
	Online       int    `json:"online"`
	Offline      int    `json:"offline"`
	Notification int    `json:"notification"`
}

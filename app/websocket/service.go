package websocket

import (
	"ccserver/app/service/tdengine"
	"ccserver/app/vars"
	"ccserver/pkg/pix_log"
	"time"
)

// DeviceWebSocketService 设备WebSocket服务
type DeviceWebSocketService struct {
	hub           *Hub
	tdengineClient *tdengine.TDengineClient
	ticker        *time.Ticker
	stopChan      chan bool
}

// NewDeviceWebSocketService 创建新的设备WebSocket服务
func NewDeviceWebSocketService() *DeviceWebSocketService {
	hub := NewHub()
	
	// 创建TDengine客户端
	tdengineClient := tdengine.NewTDengineClient(
		vars.Config.TDengine.Host,
		vars.Config.TDengine.Port,
		vars.Config.TDengine.User,
		vars.Config.TDengine.Password,
		vars.Config.TDengine.Database,
	)

	return &DeviceWebSocketService{
		hub:           hub,
		tdengineClient: tdengineClient,
		stopChan:      make(chan bool),
	}
}

// Start 启动WebSocket服务
func (s *DeviceWebSocketService) Start() {
	pix_log.Info("[WebSocket] 启动设备WebSocket服务")
	
	// 启动Hub
	go s.hub.Run()
	
	// 启动定时推送设备数据
	s.startDeviceDataPush()
}

// Stop 停止WebSocket服务
func (s *DeviceWebSocketService) Stop() {
	pix_log.Info("[WebSocket] 停止设备WebSocket服务")
	
	if s.ticker != nil {
		s.ticker.Stop()
	}
	
	close(s.stopChan)
}

// GetHub 获取Hub实例
func (s *DeviceWebSocketService) GetHub() *Hub {
	return s.hub
}

// startDeviceDataPush 启动设备数据推送
func (s *DeviceWebSocketService) startDeviceDataPush() {
	// 使用配置中的推送间隔
	pushInterval := time.Duration(vars.Config.Business.PushInterval) * time.Second
	s.ticker = time.NewTicker(pushInterval)
	
	pix_log.Info("[WebSocket] 启动设备数据推送，间隔: %v", pushInterval)
	
	// 立即推送一次
	go s.pushDeviceData()
	
	go func() {
		for {
			select {
			case <-s.ticker.C:
				s.pushDeviceData()
			case <-s.stopChan:
				return
			}
		}
	}()
}

// pushDeviceData 推送设备数据
func (s *DeviceWebSocketService) pushDeviceData() {
	if s.hub.GetClientCount() == 0 {
		// 没有客户端连接，跳过推送
		return
	}

	pix_log.Info("[WebSocket] 推送设备数据到 %d 个客户端", s.hub.GetClientCount())
	
	// 获取设备列表
	devices, stats, err := s.tdengineClient.GetDeviceList()
	if err != nil {
		pix_log.Error("[WebSocket] 获取设备列表失败: %v", err)
		return
	}

	// 构造响应数据
	responseData := map[string]interface{}{
		"total":   len(devices),
		"devices": devices,
		"stats":   stats,
	}

	// 广播设备列表更新
	s.hub.BroadcastDeviceUpdate("device_list_update", responseData)
}

// PushDeviceDetail 推送单个设备详情
func (s *DeviceWebSocketService) PushDeviceDetail(imsi string) {
	if s.hub.GetClientCount() == 0 {
		return
	}

	device, err := s.tdengineClient.GetDeviceDetail(imsi)
	if err != nil {
		pix_log.Error("[WebSocket] 获取设备详情失败: %v", err)
		return
	}

	// 广播设备详情更新
	s.hub.BroadcastDeviceUpdate("device_detail_update", map[string]interface{}{
		"imsi":   imsi,
		"device": device,
	})
}

// PushDeviceHistory 推送设备历史数据
func (s *DeviceWebSocketService) PushDeviceHistory(imsi string, hours int) {
	if s.hub.GetClientCount() == 0 {
		return
	}

	history, err := s.tdengineClient.GetDeviceHistory(imsi, hours)
	if err != nil {
		pix_log.Error("[WebSocket] 获取设备历史失败: %v", err)
		return
	}

	// 广播设备历史更新
	s.hub.BroadcastDeviceUpdate("device_history_update", map[string]interface{}{
		"imsi":    imsi,
		"hours":   hours,
		"history": history,
	})
}

// BroadcastSystemStatus 广播系统状态
func (s *DeviceWebSocketService) BroadcastSystemStatus(status map[string]interface{}) {
	if s.hub.GetClientCount() == 0 {
		return
	}

	s.hub.BroadcastDeviceUpdate("system_status_update", status)
}

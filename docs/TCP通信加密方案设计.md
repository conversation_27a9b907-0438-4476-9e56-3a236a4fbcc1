# TCP通信加密协议

ccserver支持SM4-GCM加密的TCP通信协议。服务端自动识别协议版本，客户端可选择明文或加密传输。

## 协议格式

### 明文协议 (V1)

| 字段 | 字节数 | 说明 |
|------|--------|------|
| 消息头标识 | 2 | `@<` (客户端→服务端) / `@>` (服务端→客户端) |
| 长度 | 2 | 从流水号到CRC16的总长度 |
| 流水号 | 4 | 递增编号 |
| 消息体格式 | 2 | `0x0000`: JSON / `0x0001`: 字节流 |
| 协议版本号 | 1 | `0x01` |
| 加密方式 | 1 | `0x00`: 未加密 |
| 随机数 | 4 | 填0 |
| 时间戳 | 8 | UNIX时间戳 |
| 预留 | 24 | 全部填0 |
| 消息体 | N | JSON数据 |
| CRC16校验码 | 2 | 校验码 |

### 加密协议 (V2)

| 字段 | 字节数 | V1用途 | V2用途 |
|------|--------|--------|--------|
| 消息头标识 | 2 | 协议标识 | 协议标识 |
| 长度 | 2 | 数据长度 | 数据长度 |
| 流水号 | 4 | 递增编号 | 递增编号 |
| 消息体格式 | 2 | `0x0000`: JSON | `0x0000`: JSON |
| 协议版本号 | 1 | `0x01` | `0x02` |
| 加密方式 | 1 | `0x00`: 未加密 | `0x01`: SM4-GCM |
| 随机数 | 4 | 填0(未使用) | **Nonce前4字节** |
| 时间戳 | 8 | UNIX时间戳 | UNIX时间戳 |
| 预留 | 24 | 全部填0(未使用) | **Nonce后8字节 + 认证标签16字节** |
| 消息体 | N | JSON数据 | SM4-GCM密文 |
| CRC16校验码 | 2 | 校验码 | 校验码 |

**字段复用说明**：
- **Nonce (12字节)**：随机数字段(4字节) + 预留字段前8字节
- **认证标签 (16字节)**：预留字段后16字节
- **协议包长度**：V1和V2完全相同，保持向后兼容

## 加密流程

1. 生成12字节随机Nonce
2. 使用SM4-GCM加密JSON消息体，获得密文和认证标签
3. 构造V2协议包：
   - Nonce前4字节 → 随机数字段
   - Nonce后8字节 → 预留字段前8字节
   - 认证标签16字节 → 预留字段后16字节
   - 密文 → 消息体字段
4. 发送到服务端

## 客户端实现

### Python

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python客户端 - SM4-GCM加密实现
使用cryptography库的SM4-GCM模式，与Go服务端完全兼容
"""

import os
import time
import struct
import secrets
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

def calculate_crc16(data):
    """计算CRC16校验码"""
    crc = 0xFFFF
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 1:
                crc = (crc >> 1) ^ 0xA001
            else:
                crc >>= 1
    return crc & 0xFFFF

class PythonSM4GCMClient:
    def __init__(self, key):
        """初始化SM4-GCM客户端"""
        if len(key) != 16:
            raise ValueError(f"SM4密钥长度必须为16字节，当前长度: {len(key)}")
        self.key = key
        self.bill_no = 1

    def generate_nonce(self):
        """生成12字节随机Nonce"""
        return secrets.token_bytes(12)

    def sm4_gcm_encrypt(self, plaintext, nonce):
        """SM4-GCM加密"""
        cipher = Cipher(algorithms.SM4(self.key), modes.GCM(nonce))
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        auth_tag = encryptor.tag
        return ciphertext, auth_tag

    def build_encrypted_packet(self, message, is_response=False):
        """构造加密协议包"""
        # 1. 生成Nonce
        nonce = self.generate_nonce()

        # 2. 加密消息体
        msg_body = message.encode('utf-8')
        ciphertext, auth_tag = self.sm4_gcm_encrypt(msg_body, nonce)

        # 3. 构造协议包
        packet = bytearray()

        # 消息头标识
        packet.extend(b'@<' if not is_response else b'@>')

        # 长度占位符
        length_pos = len(packet)
        packet.extend(b'\x00\x00')

        # 流水号 (4字节大端序)
        packet.extend(struct.pack('>I', self.bill_no))
        self.bill_no += 1

        # 消息体格式 (0x0000 JSON格式)
        packet.extend(struct.pack('>H', 0x0000))

        # 协议版本号 (0x02 加密协议)
        packet.append(0x02)

        # 加密方式 (0x01 SM4-GCM)
        packet.append(0x01)

        # 随机数字段 (4字节) - 复用为Nonce前4字节
        packet.extend(nonce[:4])

        # 时间戳 (8字节)
        packet.extend(struct.pack('>Q', int(time.time())))

        # 预留字段 (24字节) - 复用为Nonce后8字节 + 认证标签16字节
        packet.extend(nonce[4:12])  # Nonce后8字节
        packet.extend(auth_tag)     # 认证标签16字节

        # 密文
        packet.extend(ciphertext)

        # 计算并填充长度字段
        total_len = len(packet)
        length_value = total_len - 2 - 2 + 2
        struct.pack_into('>H', packet, length_pos, length_value)

        # CRC16校验码
        crc16 = calculate_crc16(packet[2:])
        packet.extend(struct.pack('>H', crc16))

        return bytes(packet)

# 使用示例
if __name__ == "__main__":
    key = b"1234567890123456"  # 16字节密钥
    client = PythonSM4GCMClient(key)

    message = '{"cmd": 2, "imsi": "460110123456789", "data": {"lat": 39.9042, "lng": 116.4074}}'
    encrypted_packet = client.build_encrypted_packet(message)

    print(f"加密包长度: {len(encrypted_packet)} 字节")
    print(f"加密包(hex): {encrypted_packet.hex()}")
```

### Go

```go
package main

import (
    "bytes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/binary"
    "fmt"
    "time"

    "github.com/tjfoc/gmsm/sm4"
)

// 协议常量
const (
    // 消息头标识
    MSG_START_CHAR1      = '@'
    MSG_START_CHAR2      = '<'
    MSG_RESP_START_CHAR2 = '>'

    // 协议版本
    PROTOCOL_VERSION_V2 = 0x02 // 加密协议

    // 加密方式
    ENCRYPTION_SM4_GCM = 0x01 // SM4-GCM加密

    // 消息体格式
    MSG_FORMAT_JSON = 0x0000

    // GCM参数
    GCM_NONCE_SIZE = 12 // GCM Nonce长度
    GCM_TAG_SIZE   = 16 // GCM认证标签长度
)

// SM4GCMCrypto SM4-GCM加密器
type SM4GCMCrypto struct {
    key []byte // 16字节SM4密钥
}

// NewSM4GCMCrypto 创建SM4-GCM加密器
func NewSM4GCMCrypto(key []byte) (*SM4GCMCrypto, error) {
    if len(key) != 16 {
        return nil, fmt.Errorf("SM4密钥长度必须为16字节，当前长度: %d", len(key))
    }
    return &SM4GCMCrypto{key: key}, nil
}

// GenerateNonce 生成12字节随机Nonce
func (c *SM4GCMCrypto) GenerateNonce() ([]byte, error) {
    nonce := make([]byte, GCM_NONCE_SIZE)
    _, err := rand.Read(nonce)
    if err != nil {
        return nil, fmt.Errorf("生成Nonce失败: %v", err)
    }
    return nonce, nil
}

// Encrypt SM4-GCM加密
func (c *SM4GCMCrypto) Encrypt(plaintext, nonce []byte) ([]byte, []byte, error) {
    if len(nonce) != GCM_NONCE_SIZE {
        return nil, nil, fmt.Errorf("Nonce长度必须为%d字节", GCM_NONCE_SIZE)
    }

    // 创建SM4-GCM加密器
    block, err := sm4.NewCipher(c.key)
    if err != nil {
        return nil, nil, fmt.Errorf("创建SM4密码器失败: %v", err)
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return nil, nil, fmt.Errorf("创建GCM模式失败: %v", err)
    }

    // 加密并生成认证标签
    ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

    // 分离密文和认证标签
    if len(ciphertext) < GCM_TAG_SIZE {
        return nil, nil, fmt.Errorf("加密结果长度异常")
    }

    actualCiphertext := ciphertext[:len(ciphertext)-GCM_TAG_SIZE]
    authTag := ciphertext[len(ciphertext)-GCM_TAG_SIZE:]

    return actualCiphertext, authTag, nil
}

// PacketBuilder 协议包构造器
type PacketBuilder struct {
    crypto *SM4GCMCrypto
    billNo uint32
}

// NewPacketBuilder 创建协议包构造器
func NewPacketBuilder(crypto *SM4GCMCrypto) *PacketBuilder {
    return &PacketBuilder{
        crypto: crypto,
        billNo: 1,
    }
}

// BuildEncryptedPacket 构造加密协议包
func (pb *PacketBuilder) BuildEncryptedPacket(msgBody []byte, isResponse bool) ([]byte, error) {
    // 生成Nonce
    nonce, err := pb.crypto.GenerateNonce()
    if err != nil {
        return nil, fmt.Errorf("生成Nonce失败: %v", err)
    }

    // 加密消息体
    ciphertext, authTag, err := pb.crypto.Encrypt(msgBody, nonce)
    if err != nil {
        return nil, fmt.Errorf("加密失败: %v", err)
    }

    // 构造协议包
    var buffer bytes.Buffer

    // 1. 消息头标识
    buffer.WriteByte(MSG_START_CHAR1)
    if isResponse {
        buffer.WriteByte(MSG_RESP_START_CHAR2)
    } else {
        buffer.WriteByte(MSG_START_CHAR2)
    }

    // 2. 长度占位符
    lengthPos := buffer.Len()
    binary.Write(&buffer, binary.BigEndian, uint16(0))

    // 3. 流水号
    binary.Write(&buffer, binary.BigEndian, pb.billNo)
    pb.billNo++

    // 4. 消息体格式
    binary.Write(&buffer, binary.BigEndian, uint16(MSG_FORMAT_JSON))

    // 5. 协议版本号
    buffer.WriteByte(PROTOCOL_VERSION_V2)

    // 6. 加密方式
    buffer.WriteByte(ENCRYPTION_SM4_GCM)

    // 7. 随机数字段 (4字节) - 复用为Nonce前4字节
    buffer.Write(nonce[:4])

    // 8. 时间戳
    binary.Write(&buffer, binary.BigEndian, uint64(time.Now().Unix()))

    // 9. 预留字段 (24字节) - 复用为Nonce后8字节 + 认证标签16字节
    buffer.Write(nonce[4:12])  // Nonce后8字节
    buffer.Write(authTag)      // 认证标签16字节

    // 10. 密文
    buffer.Write(ciphertext)

    // 11. 计算并填充长度字段
    totalLen := buffer.Len()
    lengthValue := uint16(totalLen - 2 - 2 + 2) // 减去头部2字节，加上CRC16
    lengthBytes := buffer.Bytes()
    binary.BigEndian.PutUint16(lengthBytes[lengthPos:lengthPos+2], lengthValue)

    // 12. 计算CRC16校验码
    crc16 := calculateCRC16(lengthBytes[2:])
    binary.Write(&buffer, binary.BigEndian, crc16)

    return buffer.Bytes(), nil
}

// calculateCRC16 计算CRC16校验码
func calculateCRC16(data []byte) uint16 {
    var crc uint16 = 0xFFFF
    for _, b := range data {
        crc ^= uint16(b)
        for i := 0; i < 8; i++ {
            if crc&1 != 0 {
                crc = (crc >> 1) ^ 0xA001
            } else {
                crc >>= 1
            }
        }
    }
    return crc
}

// 使用示例
func main() {
    // 初始化加密器
    key := []byte("1234567890123456") // 16字节密钥
    crypto, err := NewSM4GCMCrypto(key)
    if err != nil {
        fmt.Printf("创建加密器失败: %v\n", err)
        return
    }

    // 创建协议包构造器
    builder := NewPacketBuilder(crypto)

    // 构造测试消息
    message := `{"cmd": 2, "imsi": "460110123456789", "data": {"lat": 39.9042, "lng": 116.4074}}`
    encryptedPacket, err := builder.BuildEncryptedPacket([]byte(message), false)
    if err != nil {
        fmt.Printf("构造加密包失败: %v\n", err)
        return
    }

    fmt.Printf("加密包长度: %d 字节\n", len(encryptedPacket))
    fmt.Printf("加密包(hex): %x\n", encryptedPacket)
}
```

## 依赖库

**Python**:
```bash
pip install cryptography>=35.0.0
```

**Go**:
```bash
go get github.com/tjfoc/gmsm
```
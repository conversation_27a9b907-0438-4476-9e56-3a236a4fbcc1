# ccserver 标准化日志系统使用指南

## 1. 日志格式规范

### 1.1 标准格式
```
[时间戳] [级别] [模块] [IMSI] 消息内容
```

### 1.2 格式示例
```
[2025-07-09 23:07:16.353] [INFO] [TCP] [IMSI:460123456789012] 设备注册成功
[2025-07-09 23:07:16.354] [WARN] [TCP] [IMSI:460123456789012] 连接超时，正在重试
[2025-07-09 23:07:16.355] [ERROR] [SERVER] [IMSI:N/A] 数据库连接失败
[2025-07-09 23:07:16.356] [DEBUG] [DATABASE] [IMSI:460987654321098] 执行SQL查询
```

### 1.3 字段说明
- **时间戳**: `YYYY-MM-DD HH:mm:ss.SSS` 格式，精确到毫秒
- **级别**: `DEBUG`/`INFO`/`WARN`/`ERROR`，固定宽度
- **模块**: `TCP`/`SERVER`/`SQL`/`DATABASE`/`TERMINAL`，大写
- **IMSI**: `IMSI:具体值` 或 `IMSI:N/A`（系统级日志）
- **消息**: 实际日志内容

## 2. 目录结构

```
logs/
├── tcp/                    # TCP 相关日志
│   └── tcp-2025-07-09_01.log
├── server/                 # 服务器日志
│   └── server-2025-07-09_01.log
├── sql/                    # SQL 日志
├── database/               # 数据库日志
└── terminal/               # 终端日志
```

## 3. 使用方法

### 3.1 基础日志函数（不带 IMSI）

```go
import "ccserver/pix_log"

// 系统级日志，IMSI 自动设置为 N/A
pix_log.Info("系统启动完成")
pix_log.Warning("配置文件版本较旧")
pix_log.Error("数据库连接失败")
pix_log.Debug("调试信息")

// 模块专用函数
pix_log.ServerInfo("HTTP 服务器启动")
pix_log.DatabaseError("数据库操作失败")
```

### 3.2 带 IMSI 的日志函数

```go
// 设备相关日志，需要提供 IMSI
imsi := "460123456789012"
pix_log.InfoWithIMSI(imsi, "设备注册成功")
pix_log.WarningWithIMSI(imsi, "设备电量低于 20%%")
pix_log.ErrorWithIMSI(imsi, "设备通信超时")

// 模块专用带 IMSI 函数
pix_log.TcpInfoWithIMSI(imsi, "TCP 连接建立成功")
pix_log.ServerErrorWithIMSI(imsi, "处理设备请求失败")
pix_log.DatabaseInfoWithIMSI(imsi, "设备数据更新成功")
```

### 3.3 GoFrame 兼容接口

```go
import "ccserver/app/library/v"

// 原有调用方式保持不变
v.LogTcp().Infof("TCP 服务器监听端口 %d", 2020)
v.LogTcp().Errorf("设备认证失败，错误码: %d", 401)
v.Log().Warningf("系统内存使用率达到 %d%%", 85)

// 新增带 IMSI 的兼容接口
v.LogTcp().InfofWithIMSI(imsi, "设备连接成功")
v.LogTcp().ErrorfWithIMSI(imsi, "设备操作失败")
```

### 3.4 业务场景专用函数

```go
// 设备注册
pix_log.LogRegistration(imsi, true, "设备型号: PIX-V2.0")
pix_log.LogRegistration(imsi, false, "设备证书验证失败")

// 心跳处理
pix_log.LogHeartbeat(imsi, step, taskId, "任务执行中")

// 任务执行
pix_log.LogTaskExecution(imsi, taskId, executionId, "RUNNING", "导航任务")

// 安全事件
pix_log.LogSecurityEvent(imsi, "SUSPICIOUS_LOGIN", "异常登录尝试")

// 设备事件
pix_log.LogDeviceEvent(imsi, "CONNECTION", "设备上线")
pix_log.LogDeviceError(imsi, "UPGRADE", "固件升级失败")

// Kafka 相关日志
pix_log.LogKafkaConnection(brokers, true, "连接建立成功")
pix_log.LogKafkaMessage(imsi, "device-cmd", 0, 12345, 0x0101, "消息详情")
pix_log.LogKafkaCommand(imsi, 0x0101, true, "GoPoint 命令处理成功")
pix_log.LogKafkaDeviceSend(imsi, 0x0101, true, "指令下发成功")
```

### 3.5 批量日志（性能优化）

```go
// 创建批量日志器
batchLogger := pix_log.NewBatchLogger("tcp", imsi)

// 添加日志条目
batchLogger.Add("INFO", "批量操作开始")
batchLogger.Add("INFO", "处理数据包 %d/%d", 1, 100)
batchLogger.Add("WARN", "数据包校验失败")

// 批量写入
batchLogger.Flush()
```

## 4. 日志筛选和分析

### 4.1 按 IMSI 筛选
```bash
# 查看特定设备的所有日志
grep 'IMSI:460123456789012' logs/*/*.log

# 查看特定设备的错误日志
grep 'IMSI:460123456789012' logs/*/*.log | grep '\[ERROR\]'
```

### 4.2 按级别筛选
```bash
# 查看所有错误日志
grep '\[ERROR\]' logs/*/*.log

# 查看警告和错误日志
grep '\[WARN\]\|\[ERROR\]' logs/*/*.log
```

### 4.3 按模块筛选
```bash
# 查看 TCP 模块日志
grep '\[TCP\]' logs/tcp/*.log

# 查看数据库相关日志
grep '\[DATABASE\]' logs/database/*.log
```

### 4.4 按时间筛选
```bash
# 查看特定时间段的日志
grep '2025-07-09 23:0[0-9]' logs/*/*.log

# 查看最近 1 小时的日志
find logs -name "*.log" -exec grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" {} \;
```

### 4.5 统计分析
```bash
# 统计各级别日志数量
grep -o '\[DEBUG\]\|\[INFO\]\|\[WARN\]\|\[ERROR\]' logs/*/*.log | sort | uniq -c

# 统计活跃设备数量（去重 IMSI）
grep -o 'IMSI:[^]]*' logs/*/*.log | grep -v 'IMSI:N/A' | sort | uniq | wc -l

# 提取所有 IMSI 列表
awk -F'[IMSI:' '{print $2}' logs/*/*.log | awk -F']' '{print $1}' | grep -v 'N/A' | sort | uniq
```

### 4.6 Kafka 专用筛选
```bash
# 按 IMSI 筛选 Kafka 相关日志
grep 'IMSI:460123456789012' logs/tcp/*.log | grep -i kafka

# 筛选 Kafka 连接相关日志
grep 'Kafka.*连接' logs/tcp/*.log

# 筛选 Kafka 命令处理日志
grep 'Kafka.*命令' logs/tcp/*.log

# 筛选 Kafka 错误日志
grep 'Kafka.*失败\|Kafka.*错误' logs/tcp/*.log

# 按命令类型筛选
grep 'cmd=0x0101' logs/tcp/*.log  # GoPoint 命令
grep 'cmd=0x0610' logs/tcp/*.log  # AddOrder 命令

# 筛选分区相关日志
grep 'partition=' logs/tcp/*.log

# 统计 Kafka 消息处理成功率
grep 'Kafka.*成功' logs/tcp/*.log | wc -l
grep 'Kafka.*失败' logs/tcp/*.log | wc -l

# 提取处理过的设备 IMSI
grep 'Kafka.*命令处理' logs/tcp/*.log | grep -o 'IMSI:[^]]*' | sort | uniq
```

## 5. 配置说明

### 5.1 日志配置文件 (config/config.toml)
```toml
[logging]
base_path = "logs"                 # 日志基础目录
rotate_size = "50M"                # 单文件最大大小
rotate_backup_limit = 7            # 保留天数
date_format = "2006-01-02"         # 日期格式
max_files_per_day = 99             # 每日最大文件数

[logging.modules]
server = { level = "all", stdout = true, filename = "server" }
tcp = { level = "all", stdout = true, filename = "tcp" }
terminal = { level = "all", stdout = true, filename = "terminal" }
sql = { level = "all", stdout = true, filename = "sql" }
database = { level = "all", stdout = true, filename = "database" }
```

### 5.2 配置参数说明
- `level`: 日志级别 (`all`/`debug`/`info`/`warn`/`error`)
- `stdout`: 是否输出到控制台
- `filename`: 日志文件名前缀

## 6. 最佳实践

### 6.1 IMSI 使用原则
- **设备相关操作**: 必须使用带 IMSI 的日志函数
- **系统级操作**: 使用不带 IMSI 的日志函数
- **批量操作**: 考虑使用批量日志器提高性能

### 6.2 日志级别选择
- **DEBUG**: 详细的调试信息，生产环境可关闭
- **INFO**: 正常的业务流程信息
- **WARN**: 警告信息，不影响正常功能
- **ERROR**: 错误信息，需要关注和处理

### 6.3 性能考虑
- 频繁日志记录使用批量日志器
- 避免在循环中直接调用日志函数
- 合理设置日志级别，减少不必要的日志输出

### 6.4 日志内容规范
- 使用清晰、简洁的描述
- 包含必要的上下文信息
- 避免记录敏感信息（密码、密钥等）
- 使用统一的术语和格式

# ccserver 多环境配置说明

## 概述

ccserver 项目已改造为使用 GoFrame 推荐的多环境配置机制，支持开发、测试、生产等多个环境的配置管理。

## 配置文件

项目包含以下配置文件：

- `config/config.toml` - 开发环境配置（默认配置文件）
- `config/config.test.toml` - 测试环境配置
- `config/config.prod.toml` - 生产环境配置

每个配置文件都是完整独立的，包含该环境所需的全部配置项。

## 环境切换方式

### 1. 环境变量方式（推荐）

```bash
# 开发环境（使用默认配置文件）
./ccserver

# 测试环境
export GF_GCFG_FILE=config.test.toml
./ccserver

# 生产环境
export GF_GCFG_FILE=config.prod.toml
./ccserver
```

### 2. 命令行参数方式

```bash
# 开发环境（使用默认配置文件）
./ccserver

# 测试环境
./ccserver --gf.gcfg.file=config.test.toml

# 生产环境
./ccserver --gf.gcfg.file=config.prod.toml
```

### 3. 使用启动脚本（推荐）

```bash
# 开发环境
ENV=dev ./scripts/start.sh

# 测试环境
ENV=test ./scripts/start.sh

# 生产环境
ENV=prod ./scripts/start.sh
```

## Docker 部署

### Dockerfile 示例

```dockerfile
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY .. .
RUN go build -o ccserver server.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/ccserver .
COPY --from=builder /app/config ./config
COPY --from=builder /app/scripts ./scripts

# 设置环境变量指定配置文件
ENV GF_GCFG_FILE=config.prod.toml

CMD ["./ccserver"]
```

### docker-compose 示例

```yaml
version: '3.8'
services:
  ccserver-dev:
    build: .
    environment:
      - GF_GCFG_FILE=config.dev.toml
    ports:
      - "2022:2022"
    
  ccserver-prod:
    build: .
    environment:
      - GF_GCFG_FILE=config.prod.toml
    ports:
      - "2022:2022"
```

## 配置文件差异

### 开发环境 (config.toml - 默认配置)
- 日志级别：all（详细日志）
- 日志输出：同时输出到控制台和文件
- 数据库：使用开发数据库
- 推送间隔：3秒（快速测试）

### 测试环境 (config.test.toml)
- 日志级别：all（详细日志）
- 日志输出：同时输出到控制台和文件
- 数据库：使用测试数据库
- 推送间隔：5秒
- 数据保留期：较短（节省存储）

### 生产环境 (config.prod.toml)
- 日志级别：prod（仅重要日志）
- 日志输出：仅输出到文件
- 数据库：使用生产数据库
- 推送间隔：10秒（稳定性优先）
- 数据保留期：较长

## 开发模式判断

系统会自动判断当前是否为开发模式：

1. 检查环境变量 `GF_GCFG_FILE` 是否包含 "dev"
2. 如果未指定配置文件，默认为开发模式

开发模式下：
- 证书认证会被跳过
- 许可证检查会被跳过
- 返回较大的授权数量

## 注意事项

1. **配置文件完整性**：每个环境的配置文件都必须是完整的，包含所有必需的配置项
2. **敏感信息**：生产环境配置文件中的密码等敏感信息需要根据实际情况修改
3. **端口配置**：确保不同环境使用的端口不冲突
4. **数据库配置**：不同环境应使用不同的数据库，避免数据混乱
5. **日志配置**：生产环境建议关闭控制台输出，仅输出到文件

## 迁移说明

## 重构内容

本次重构完成了以下改造：

### 配置文件结构优化
- **语义化分组**：将原有的 `[setting]` 配置节拆分为 `[app]`、`[business]`、`[schedule]` 三个语义清晰的配置节
- **日志配置简化**：将 `[logger]` 重构为 `[logging]`，消除重复配置，采用模块化日志配置
- **定时任务统一**：将 `[plugin]` 中的定时任务配置合并到 `[schedule]` 配置节
- **中间件配置整合**：保持 `[redis]` 符合GoFrame期望，将Kafka配置归入 `[middleware]`
- **数据维护语义化**：将 `[[tableAutoClearConfigList]]` 重命名为更语义化的 `[[data_maintenance]]`

### 代码结构改进
- **配置结构体重构**：更新了所有配置结构体定义，使其与新的配置文件结构匹配
- **配置引用更新**：修改了所有使用配置的代码文件，将配置引用路径更新为新的结构
- **时间单位修正**：在配置加载后正确处理时间单位转换

### 移除的内容
- 自定义的配置文件读取逻辑
- `run_mode` 配置项
- 基于 `run_mode` 的环境判断逻辑

改为使用 GoFrame 官方推荐的环境变量或命令行参数方式来指定配置文件。

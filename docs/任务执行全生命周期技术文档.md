# 任务执行全生命周期技术文档

**文档版本**: v2.1
**最后更新**: 2024年12月
**适用项目**: ccserver v1.4.0+

**📋 更新说明**:
- ✅ 修复所有字符串状态示例，统一使用数字状态值（0-7）
- ✅ 移除已删除的 `execution_status` 字段引用
- ✅ 添加实际代码文件位置引用
- ✅ 明确ccapiex和ccserver项目边界
- ✅ 基于数据库验证结果确认表结构一致性
- ✅ 所有代码示例均来自ccserver项目实际实现

## 📋 目录
- [1. 概述](#1-概述)
- [2. 任务下发阶段](#2-任务下发阶段)
- [3. ccserver消息处理阶段](#3-ccserver消息处理阶段)
- [4. 心跳数据处理阶段](#4-心跳数据处理阶段)
- [5. task_execution_report同步更新](#5-task_execution_report同步更新)
- [6. 数据流向和状态管理](#6-数据流向和状态管理)
- [7. 关键技术细节](#7-关键技术细节)

---

## 1. 概述

### 1.1 系统架构
任务执行系统采用微服务架构，通过Kafka消息队列实现ccapiex（运营平台）和ccserver（TCP服务器）之间的解耦通信。

```
Web界面 → ccapiex → Kafka → ccserver → 车辆终端 → 心跳反馈 → 状态更新
```

### 1.2 项目边界说明

**⚠️ 重要：项目独立部署**
- **ccapiex** 和 **ccserver** 是独立部署的项目，不能直接引用对方的代码
- 两个项目通过 **Kafka消息队列** 进行异步通信
- 本文档主要描述 **ccserver项目** 的实现细节

**ccapiex项目职责**：
- 任务创建和管理
- 执行实例创建（task_execution_report表的初始记录）
- 通过Kafka发送任务下发消息
- 提供Web界面和API服务

**ccserver项目职责**：
- TCP设备连接管理
- Kafka消息消费和协议转换
- 心跳数据处理和状态更新
- task_execution_report表的状态同步更新

### 1.3 核心组件
- **ccapiex**: 运营平台，负责任务管理和下发
- **ccserver**: TCP服务器，负责设备连接和协议转换
- **task_execution_report**: 执行实例表，记录任务执行的完整生命周期
- **Kafka**: 消息队列，主题为`device_cmd2`
- **TDengine**: 时序数据库，存储心跳数据

### 1.4 设备注册前置条件

**⚠️ 重要：设备必须预先注册**

在任务执行之前，设备必须满足以下前置条件：

**📋 device表记录要求**：
- 设备的IMSI必须预先存在于`device`表中
- 设备状态必须为有效（未过期）
- 设备必须关联到有效的用户（uid字段不为空）

**🔐 注册验证流程**：
```
1. TCP连接建立 → 2. 发送注册消息 → 3. device表查询验证 → 4. 设置Authorized=true → 5. 心跳处理启用
```

**❌ 常见注册失败原因**：
- `暂不允许新终端注册`：IMSI不存在于device表中
- `该终端已过期`：device.expire_time <= 当前时间
- `设备未授权，跳过心跳处理`：注册验证失败，心跳数据被忽略

**💡 解决方案**：
```sql
-- 创建设备记录示例
INSERT INTO device (imsi, imei, name, uid, status, created_time, updated_time)
VALUES ('460110123456789', '860123456789012', '测试设备', 1, 1, NOW(), NOW());
```

### 1.5 关键概念澄清

**📋 Task vs ExecutionReport 概念区分**：
- **Task结构体**：描述任务定义，包含任务的基本信息（路线、动作列表等）
- **task_execution_report表**：记录任务执行实例，一个Task可以有多个执行实例
- **TaskId字段**：Task和TaskInfo结构体中的TaskId字段现在直接是执行实例ID（语义已变更）

**🔗 ExecutionId关联关系（已简化）**：
```
Task.TaskId → task_execution_report.id (主键)
TaskInfo.TaskID → task_execution_report.id (主键)
```

**✅ 关键优化**：
- ✅ **executionId**: 引用task_execution_report表主键id，实现任务定义与执行实例的关联
- ❌ **executionNo**: 已从设备传输中移除，仅保留数据库存储用于前端显示

---

## 2. 任务下发阶段

### 2.1 完整流程概述

```mermaid
sequenceDiagram
    participant Web as Web前端
    participant API as ccapiex API
    participant DB as MySQL数据库
    participant Kafka as Kafka队列

    Web->>API: POST /api/task/do
    API->>DB: 检查设备状态
    API->>DB: 获取子任务信息
    API->>DB: 创建执行实例记录
    API->>DB: 更新任务状态
    API->>Kafka: 发送任务消息
    API-->>Web: 返回成功响应
```

### 2.2 核心代码实现

**文件位置**: `ccapiex/controllers/task.go`

```go
// Do 下发任务的核心逻辑
func (c *TaskController) Do() {
    // 1. 参数验证和设备状态检查
    exist := c.service.CheckDevice(0, imsi)
    if exist {
        c.Fail("该设备存在正在执行的任务，无法下发新任务")
        return
    }

    // 2. 获取子任务并更新状态
    child, err := c.service.Child(id)  // 设置 status=1, is_now=1

    // 3. 更新主任务状态
    err = c.service.Cancel(id)  // 设置 task.status=1, times+=1

    // 4. 创建执行实例记录
    err = c.createExecutionReport(&task, &child, &userInfo)

    // 5. 获取执行实例信息
    executionReport, err := c.executionReportService.GetExecutionReport(child.Id, task.Times)

    // 6. 构建Kafka消息（已优化移除executionNo）
    cmdData := map[string]interface{}{
        "imsi":        task.Imsi,
        "cmd":         protocol.CmdMap[9],  // 0x0018
        "taskId":      child.Id,
        "executionId": executionReport.Id,  // 🆕 执行实例ID
        "routeId":     task.MapId,
        "name":        task.Name,
        "desp":        task.Desp,
        "actionList":  actionList,
    }

    // 7. 发送到Kafka
    err = c.deviceService.SendMessage(cmdData)
}
```

### 2.3 task_execution_report记录创建

**创建时机**: 任务下发前，确保执行实例记录先于Kafka消息创建

```go
// createExecutionReport 创建任务执行实例记录
func (c *TaskController) createExecutionReport(task *pix.Task, child *pix.TaskChild, userInfo *dto.UserInfo) error {
    // 获取设备信息 - 通过All方法查询单个设备
    devices, err := c.deviceService.All(0, task.Imsi, -1, "", false, -1)
    var device pix.Device
    if err != nil || len(devices) == 0 {
        // 如果获取设备失败，使用默认值
        device = pix.Device{Type: 0} // 默认为RoboBus
    } else {
        device = devices[0]
    }

    // 创建执行实例记录
    executionReport := &pix.TaskExecutionReport{
        TaskId:          int64(task.Id),
        ChildId:         child.Id,
        ExecutionNo:     task.Times,           // 保留用于前端显示
        Imsi:            task.Imsi,
        RunningTask:     fmt.Sprintf("%d-%d", child.Id, task.Times),
        StartTime:       time.Now(),
        Status:          "将要执行",
        VehiclePlate:    task.Imsi,
        VehicleType:     pix.GetVehicleType(device.Type),
        TaskType:        pix.GetTaskType(task.Type),
        VehicleUsage:    pix.GetVehicleUsage(device.Type),
        Creator:         userInfo.Username,
        ExecutionStatus: "created",
        CreatedTime:     time.Now().Unix(),
    }

    // 解析任务内容，填充地点信息
    err = c.executionReportService.ParseTaskContent(executionReport, child.Content, task.MapId)
    if err != nil {
        return fmt.Errorf("解析任务内容失败: %v", err)
    }

    // 保存到数据库
    err = c.executionReportService.CreateExecutionReport(executionReport)
    if err != nil {
        return fmt.Errorf("保存执行实例失败: %v", err)
    }

    return nil
}
```

### 2.4 数据库事务处理

**事务范围**: 子任务状态更新 + 主任务状态更新 + 执行实例创建

**注意**: 实际代码中的事务处理是通过ORM的原子操作实现的，具体的Child方法实现包含了状态更新逻辑，确保数据一致性。

---

## 3. ccserver消息处理阶段

### 3.1 Kafka消费者处理流程

**文件位置**: `boot/kafka.go`

```mermaid
flowchart TD
    A[Kafka消息接收] --> B{消息格式验证}
    B -->|有效| C[JSON解析]
    B -->|无效| Z1[记录错误日志]

    C --> D[提取基础信息]
    D --> E[cmd: 命令码<br/>imsi: 设备ID]

    E --> F{命令类型判断}
    F -->|0x0018| G1[任务下发处理]
    F -->|其他| G2[其他命令处理]

    G1 --> H1[解析Task结构<br/>提取: taskId, executionId<br/>routeId, actionList]
    H1 --> I1[重组JSON数据]
    I1 --> J[调用MakeCmd函数]
    J --> K[协议封装处理]
    K --> L[TCP设备下发]
```

### 3.2 Task结构体解析（已优化）

```go
// Task结构体定义 - 任务定义信息（已完全移除ExecutionId字段）
// 实际代码位置：boot/kafka.go
// TaskId字段现在直接是执行实例ID（语义已变更）
type Task struct {
    Cmd         uint16       `json:"cmd"`
    Imsi        string       `json:"imsi"`
    RouteId     int          `json:"routeId"`
    TaskId      int64        `json:"taskId"`      // 🔄 执行实例ID（字段名保持兼容，语义已变更）
    Name        string       `json:"name"`
    Desp        string       `json:"desp"`
    ActionList  []ActionList `json:"actionList"`
    // ExecutionId 字段已完全移除，功能合并到TaskId
}

### 2.2 设备注册消息格式

**📤 注册消息结构**：
```go
// 设备注册消息格式要求
// 实际代码位置：cmd/simulator/main.go (sendRegister方法)
registerData := map[string]interface{}{
    "cmd":           CMD_REGISTER,        // 注册命令
    "imsi":          "460110123456789",   // 设备IMSI（必须在device表中存在）
    "imei":          "860123456789012",   // 设备IMEI
    "model":         "TestDevice",        // 设备型号
    "software_ver":  "1.0.0",            // 软件版本（注意：使用下划线格式）
    "hardware_ver":  "1.0.0",            // 硬件版本（注意：使用下划线格式）
    "vendor":        "TestVendor",        // 厂商信息
    "latitude":      22.487064,           // 纬度（注意：使用完整单词）
    "longitude":     113.910408,          // 经度（注意：使用完整单词）
    "position":      "深圳市南山区",        // 位置描述
    "manage_ip":     "127.0.0.1",         // 管理IP
    "ci":            "12345",             // 小区标识
    "pci":           "67890",             // 物理小区标识
}
```

**⚠️ 字段格式注意事项**：
- 使用 `software_ver` 而不是 `softwareVer`
- 使用 `hardware_ver` 而不是 `hardwareVer`
- 使用 `latitude`/`longitude` 而不是 `lat`/`lng`
- 所有字段名使用下划线命名法，与ccserver期望格式一致

## 3. Kafka消息处理

// 消息处理逻辑
case protocol.Task: // 0x0018 任务下发
    var task Task
    err = json.Unmarshal(msg.Value, &task)
    if err != nil {
        fmt.Println("json数据解析失败：" + err.Error())
    }

    // 重组数据（已移除executionNo）
    data, _ = gjson.Encode(g.Map{
        "cmd":         task.Cmd,
        "routeId":     task.RouteId,
        "taskId":      task.TaskId,
        "executionId": task.ExecutionId,  // 🆕 传递executionId到设备
        "name":        task.Name,
        "desp":        task.Desp,
        "actionList":  task.ActionList,
    })
```

### 3.3 MakeCmd协议封装细节

**协议封装过程**（11步标准化封装）：

```go
// MakeCmd函数协议封装步骤
func MakeCmd(cmd uint16, billNo uint32, data []byte) []byte {
    // 1. 添加消息头标识符(@>)
    // 2. 设置动态计算的消息长度
    // 3. 添加递增的流水号
    // 4. 设置JSON格式标识(0x0000)
    // 5. 设置协议版本号(0x01)
    // 6. 设置加密方式(0x00=未加密)
    // 7. 添加随机数字段(4字节)
    // 8. 添加Unix时间戳(8字节)
    // 9. 添加24字节预留字段
    // 10. 附加重组后的JSON消息体
    // 11. 计算并添加CRC16校验和
}
```

### 3.4 TCP设备下发

```go
// SendToDevice 发送数据到指定设备
func SendToDevice(imsi string, data []byte) error {
    // 1. 查找设备连接
    client := tcp.GetClient(imsi)
    if client == nil {
        return fmt.Errorf("设备 %s 未连接", imsi)
    }

    // 2. 检查连接状态
    if !client.IsConnected() {
        return fmt.Errorf("设备 %s 连接已断开", imsi)
    }

    // 3. 发送二进制数据
    _, err := client.Write(data)
    return err
}
```

---

## 4. 心跳数据处理阶段

### 4.1 心跳数据接收和解析

**文件位置**: `app/system/heartbeat.go`

```go
// GetReply 心跳处理主函数
func (t *Heartbeat) GetReply(billNo uint32) ([]byte, error) {
    // 1. 构建Redis缓存数据
    redis, err := t.ToHeartbeatRedis()
    cacheservice.SetRedisDeviceHeartbeat(t.term.Prop.IMSI, cacheJSON)

    // 2. MQTT数据推送
    payload, err := t.ToHeartbeatMqtt()
    mqtt.SharedClient.Publish(topic, 0, false, payloadJSON)

    // 3. 数据库记录处理
    if t.isNeededRecord() {
        t.processing()  // 执行数据入库和状态更新
    }

    return gjson.Encode(replyData)
}
```

### 4.2 TaskInfo结构体（已优化）

```go
// TaskInfo 心跳数据中的任务信息（已完全移除ExecutionId字段）
// 实际代码位置：app/vars/protocol/heartbeat_device.go
// TaskID字段现在直接是执行实例ID（语义已变更）
type TaskInfo struct {
    Step           int8          `json:"step"`           // 步骤
    RouteID        int           `json:"routeId"`        // 路线ID
    TaskID         int64         `json:"taskId"`         // 🔄 执行实例ID（字段名保持兼容，语义已变更）
    Progress       int8          `json:"progress"`       // 进度
    StartTime      int64         `json:"startTime"`      // 开始时间
    Name           string        `json:"name"`           // 名称
    Desp           string        `json:"desp"`           // 描述
    Point          string        `json:"point"`          // 当前位置点
    NextPoint      string        `json:"nextPoint"`      // 下一个位置点
    LeftTime       int           `json:"leftTime"`       // 剩余时间
    TargetDistance int           `json:"targetDistance"` // 目标距离
    TaskDistance   int           `json:"taskDistance"`   // 任务距离
    CurrentStation SimpleStation `json:"currentStation"` // 当前站点
    TargetStation  SimpleStation `json:"targetStation"`  // 目标站点
    ActionList     []ActionInfo  `json:"actionList"`     // 动作列表
    RetailBot      interface{}   `json:"retailBot"`      // 零售机器人信息
    Other          string        `json:"other"`          // 其他信息
    // ExecutionId 字段已完全移除，功能合并到TaskID
}
```

### 4.3 step字段值的业务含义

| Step值 | 业务含义     | 触发条件 | 对应状态值 | 更新方法 |
|-------|----------|----------|------------|----------|
| **0** | 设备空闲     | 设备当前无任务执行 | 无更新 | 不触发状态更新 |
| **1** | 将要执行     | 收到任务指令 | status=2 (正在执行) | `updateExecutionReportOnStart()` |
| **2** | 正在执行     | 任务执行完成 | status=3 (已完成) | `updateExecutionReportOnComplete()` |
| **3** | 已取消（废弃）  | 任务被取消 | status=5 (已取消) | `updateExecutionReportOnCancel()` |
| **6** | 已暂停（废弃）      | 任务暂停 | status=4 (已暂停) | `updateExecutionReportOnPause()` |
| **7** | 终止中（待确定） | 任务暂停 | | `` |
| **8** | 已终止      | 任务被强制终止 |  | `updateExecutionReportOnTerminate()` |
| **9** | 无法完成     | 任务执行失败 | status=7 (无法完成) | `updateExecutionReportOnFail()` |

**状态枚举定义**: 0=不执行, 1=将要执行, 2=正在执行, 3=已完成, 4=已暂停, 5=已取消, 6=已终止, 7=无法完成

**重要区别说明**：
- **step=0**: 设备空闲状态，表示当前无任务执行，不触发task_execution_report状态更新
- **TaskID=0**: 设备无任务，会调用`updateExecutionReportOnNoTask()`终止所有执行中的实例
- **step=8**: 任务被强制终止，会调用`updateExecutionReportOnTerminate()`更新特定实例状态

### 4.4 isNeededRecord判断逻辑

```go
// isNeededRecord 判断是否需要记录心跳数据
func (t *Heartbeat) isNeededRecord() bool {
    currentStep := t.heartbeatDevice.LatestTask.Step

    // 无任务状态，不记录
    if currentStep == 0 && t.heartbeatDevice.LatestTask.TaskID == 0 {
        return false
    }

    // 获取executionId
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return false
    }

    // 检查状态是否发生变化
    return t.hasExecutionStatusChanged(executionId, currentStep)
}
```

---

## 5. task_execution_report同步更新

### 5.1 各step值触发的更新操作

```mermaid
stateDiagram-v2
    [*] --> created: 任务下发
    created --> started: step=1
    started --> started: step=2(进行中)
    started --> completed: step=3
    started --> paused: step=4
    started --> cancelled: step=5
    started --> terminated: step=6
    started --> failed: step=7,9

    paused --> started: 恢复执行
    paused --> cancelled: step=5
    paused --> terminated: step=6

    completed --> [*]
    cancelled --> [*]
    terminated --> [*]
    failed --> [*]
```

### 5.2 核心更新方法详解

#### 5.2.1 任务开始更新（完整版）

```go
// updateExecutionReportOnStart 任务开始时更新执行实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnStart() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    now := time.Now()
    updates := map[string]interface{}{
        // 基础状态字段
        "departure_time": now,
        "status":         2, // TaskStatusExecuting = 2 (正在执行)
        "updated_time":   now.Unix(),
    }

    // 🆕 规划数据字段（从心跳数据获取）
    if t.heartbeatDevice.LatestTask.LeftTime > 0 {
        updates["planned_duration_min"] = t.heartbeatDevice.LatestTask.LeftTime
    }
    if t.heartbeatDevice.LatestTask.TaskDistance > 0 {
        updates["planned_distance_m"] = t.heartbeatDevice.LatestTask.TaskDistance
    }

    // 🆕 地点相关字段（从心跳数据更新）
    if t.heartbeatDevice.LatestTask.CurrentStation.Name != "" {
        updates["start_location"] = t.heartbeatDevice.LatestTask.CurrentStation.Name
    }
    if t.heartbeatDevice.LatestTask.TargetStation.Name != "" {
        updates["end_location"] = t.heartbeatDevice.LatestTask.TargetStation.Name
    }

    // 🆕 里程数据字段
    if t.heartbeatDevice.TotalMileage >= 0 {
        updates["start_mileage_m"] = int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
    }

    // 🆕 任务内容字段（从心跳数据获取最新信息）
    if t.heartbeatDevice.LatestTask.Name != "" {
        updates["task_name"] = t.heartbeatDevice.LatestTask.Name
    }
    if t.heartbeatDevice.LatestTask.Desp != "" {
        updates["task_description"] = t.heartbeatDevice.LatestTask.Desp
    }

    // 🆕 路线和站点ID信息
    if t.heartbeatDevice.LatestTask.RouteID > 0 {
        updates["route_id"] = t.heartbeatDevice.LatestTask.RouteID
    }
    if t.heartbeatDevice.LatestTask.CurrentStation.ID > 0 {
        updates["start_station_id"] = t.heartbeatDevice.LatestTask.CurrentStation.ID
    }
    if t.heartbeatDevice.LatestTask.TargetStation.ID > 0 {
        updates["end_station_id"] = t.heartbeatDevice.LatestTask.TargetStation.ID
    }

    pix_log.Info(fmt.Sprintf("🚀 更新执行实例 %d 为开始状态，设备: %s", executionId, t.term.Prop.IMSI))
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.2 任务完成更新

```go
// updateExecutionReportOnComplete 任务完成时更新执行实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnComplete() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    now := time.Now()
    updates := map[string]interface{}{
        "end_time":     now,
        "arrival_time": now,
        "status":       3, // TaskStatusCompleted = 3 (已完成)
        "updated_time": now.Unix(),
    }

    // 🆕 计算实际执行时长
    if t.heartbeatDevice.LatestTask.StartTime > 0 {
        actualDurationSec := now.Unix() - t.heartbeatDevice.LatestTask.StartTime
        actualDurationMin := int(actualDurationSec / 60)
        if actualDurationMin > 0 {
            updates["actual_duration_min"] = actualDurationMin
        }
    }

    // 处理里程数据
    if t.heartbeatDevice.TotalMileage >= 0 {
        endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
        updates["end_mileage_m"] = endMileage

        // 🆕 计算实际运行里程（需要从数据库获取开始里程）
        var startMileage int
        db.GetPixmoving().Table("task_execution_report").
            Where("id = ?", executionId).
            Select("start_mileage_m").
            Scan(&startMileage)

        if startMileage > 0 {
            actualMileage := endMileage - startMileage
            if actualMileage > 0 {
                updates["actual_mileage_m"] = actualMileage
            }
        }
    }

    pix_log.Info(fmt.Sprintf("✅ 更新执行实例 %d 为完成状态，设备: %s", executionId, t.term.Prop.IMSI))
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.3 任务取消更新

```go
// updateExecutionReportOnCancel 任务取消时更新执行实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnCancel() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    now := time.Now()
    updates := map[string]interface{}{
        "status":       5, // TaskStatusCancelled = 5 (已取消)
        "end_time":     now,
        "arrival_time": now,
        "updated_time": now.Unix(),
    }

    // 🆕 计算实际执行时长
    if t.heartbeatDevice.LatestTask.StartTime > 0 {
        actualDurationSec := now.Unix() - t.heartbeatDevice.LatestTask.StartTime
        actualDurationMin := int(actualDurationSec / 60)
        if actualDurationMin > 0 {
            updates["actual_duration_min"] = actualDurationMin
        }
    }

    // 🆕 处理里程数据
    if t.heartbeatDevice.TotalMileage >= 0 {
        endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
        updates["end_mileage_m"] = endMileage

        // 计算实际运行里程
        var startMileage int
        db.GetPixmoving().Table("task_execution_report").
            Where("id = ?", executionId).
            Select("start_mileage_m").
            Scan(&startMileage)

        if startMileage > 0 {
            actualMileage := endMileage - startMileage
            if actualMileage > 0 {
                updates["actual_mileage_m"] = actualMileage
            }
        }
    }

    pix_log.Info(fmt.Sprintf("🚫 更新执行实例 %d 为取消状态，设备: %s", executionId, t.term.Prop.IMSI))
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.4 任务暂停更新

**✅ 当前实现合理**: 暂停状态不需要记录结束数据，因为任务可能恢复执行

```go
// updateExecutionReportOnPause 任务暂停时更新执行实例（当前实现合理）
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnPause() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    updates := map[string]interface{}{
        "status":       4, // TaskStatusPaused = 4 (已暂停)
        "updated_time": time.Now().Unix(),
    }

    // 注意：暂停状态不记录结束时间和里程，因为任务可能恢复执行
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.5 任务终止更新

```go
// updateExecutionReportOnTerminate 任务终止时更新执行实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnTerminate() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    now := time.Now()
    updates := map[string]interface{}{
        "status":       6, // TaskStatusTerminated = 6 (已终止)
        "end_time":     now,
        "arrival_time": now,
        "updated_time": now.Unix(),
    }

    // 🆕 计算实际执行时长
    if t.heartbeatDevice.LatestTask.StartTime > 0 {
        actualDurationSec := now.Unix() - t.heartbeatDevice.LatestTask.StartTime
        actualDurationMin := int(actualDurationSec / 60)
        if actualDurationMin > 0 {
            updates["actual_duration_min"] = actualDurationMin
        }
    }

    // 🆕 处理里程数据
    if t.heartbeatDevice.TotalMileage >= 0 {
        endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
        updates["end_mileage_m"] = endMileage

        // 计算实际运行里程
        var startMileage int
        db.GetPixmoving().Table("task_execution_report").
            Where("id = ?", executionId).
            Select("start_mileage_m").
            Scan(&startMileage)

        if startMileage > 0 {
            actualMileage := endMileage - startMileage
            if actualMileage > 0 {
                updates["actual_mileage_m"] = actualMileage
            }
        }
    }

    pix_log.Info(fmt.Sprintf("🛑 更新执行实例 %d 为终止状态，设备: %s", executionId, t.term.Prop.IMSI))
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.6 任务失败更新

```go
// updateExecutionReportOnFail 任务失败时更新执行实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnFail() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    now := time.Now()
    updates := map[string]interface{}{
        "status":       7, // TaskStatusFailed = 7 (无法完成)
        "end_time":     now,
        "arrival_time": now,
        "updated_time": now.Unix(),
    }

    // 🆕 计算实际执行时长
    if t.heartbeatDevice.LatestTask.StartTime > 0 {
        actualDurationSec := now.Unix() - t.heartbeatDevice.LatestTask.StartTime
        actualDurationMin := int(actualDurationSec / 60)
        if actualDurationMin > 0 {
            updates["actual_duration_min"] = actualDurationMin
        }
    }

    // 🆕 处理里程数据
    if t.heartbeatDevice.TotalMileage >= 0 {
        endMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米
        updates["end_mileage_m"] = endMileage

        // 计算实际运行里程
        var startMileage int
        db.GetPixmoving().Table("task_execution_report").
            Where("id = ?", executionId).
            Select("start_mileage_m").
            Scan(&startMileage)

        if startMileage > 0 {
            actualMileage := endMileage - startMileage
            if actualMileage > 0 {
                updates["actual_mileage_m"] = actualMileage
            }
        }
    }

    pix_log.Info(fmt.Sprintf("❌ 更新执行实例 %d 为失败状态，设备: %s", executionId, t.term.Prop.IMSI))
    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

#### 5.2.7 无任务状态处理

```go
// updateExecutionReportOnNoTask 无任务时终止所有执行中的实例
func (t *Heartbeat) updateExecutionReportOnNoTask() {
    // 直接通过数据库更新，不依赖ccapiex代码
    db.GetPixmoving().Table("task_execution_report").
        Where("imsi = ? AND status IN (?)", t.term.Prop.IMSI, []int{1, 2, 4}). // 1=将要执行, 2=正在执行, 4=已暂停
        Updates(map[string]interface{}{
            "status":       6, // TaskStatusTerminated = 6 (已终止)
            "updated_time": time.Now().Unix(),
        })
}
```

### 5.3 心跳处理中的状态转换逻辑

**文件位置**: `app/system/heartbeat.go` - `processing()` 方法

```go
// 心跳处理的主要逻辑
if t.heartbeatDevice.LatestTask.TaskID == 0 {
    // 无任务时更新状态
    db.GetPixmoving().Table("task").Where("imsi = ? and status = 2", t.term.Prop.IMSI).Update("status", 6)
    db.GetPixmoving().Table("task_child").Where("imsi = ? and status = 2", t.term.Prop.IMSI).Update("status", 6)

    // 🆕 终止所有执行中的实例
    t.updateExecutionReportOnNoTask()
} else {
    // 有任务时根据step值更新状态
    step := t.heartbeatDevice.LatestTask.Step

    // 根据步骤更新状态
    switch step {
    case 1:
        status = 2
        // 🆕 更新执行实例 - 任务开始
        t.updateExecutionReportOnStart()
    case 2:
        status = 3
        isNow = 0
        // 任务完成处理
        t.handleTaskCompletion(&child, device)
        // 🆕 更新执行实例 - 任务完成
        t.updateExecutionReportOnComplete()
    case 3:
        status = 5
        isNow = 0
        // 🆕 更新执行实例 - 任务取消
        t.updateExecutionReportOnCancel()
    case 6:
        status = 4
        // 🆕 更新执行实例 - 任务暂停
        t.updateExecutionReportOnPause()
    case 8:
        status = 6
        isNow = 0
        // 🆕 更新执行实例 - 任务终止
        t.updateExecutionReportOnTerminate()
    case 9:
        status = 7
        isNow = 0
        // 🆕 更新执行实例 - 任务失败
        t.updateExecutionReportOnFail()
    // 注意：case 0 不存在，step=0时不触发任何状态更新
    }
}
```

**关键逻辑说明**：
- **TaskID == 0**: 设备无任务，终止所有执行中的实例
- **TaskID != 0 且 step == 0**: 设备空闲但有任务ID，不触发状态更新
- **TaskID != 0 且 step != 0**: 根据step值进行相应的状态更新

### 5.4 完整状态转换表

| 心跳Step | task_child.status | status字段（数字） | status含义 | 说明 |
|----------|-------------------|-------------------|------------|------|
| **0** | - | - | 无更新 | 设备空闲状态，不触发状态更新 |
| **1** | 2 | 2 | 正在执行 | 任务开始执行 |
| **2** | 3 | 3 | 已完成 | 任务正常完成 |
| **3** | 5 | 5 | 已取消 | 任务被取消 |
| **6** | 4 | 4 | 已暂停 | 任务暂停 |
| **8** | 6 | 6 | 已终止 | 任务被强制终止 |
| **9** | 7 | 7 | 无法完成 | 任务执行失败 |

**特殊情况说明**：
- **TaskID == 0**（设备无任务）：调用 `updateExecutionReportOnNoTask()` 终止所有执行中实例（status=6）
- **step == 0**（设备空闲）：不触发任何状态更新，保持现有状态

### 5.5 字段更新完整性分析

#### 5.5.1 字段更新完整性状态

通过代码改进，所有状态更新方法的字段更新已经完善：

| 状态更新方法 | 更新字段 | 状态 | 业务价值 |
|-------------|----------|------|----------|
| `updateExecutionReportOnCancel` | status(5), updated_time, **end_time, arrival_time, actual_duration_min, end_mileage_m, actual_mileage_m** | ✅ 已完善 | 完整统计取消任务的资源消耗 |
| `updateExecutionReportOnTerminate` | status(6), updated_time, **end_time, arrival_time, actual_duration_min, end_mileage_m, actual_mileage_m** | ✅ 已完善 | 完整分析异常终止的执行情况 |
| `updateExecutionReportOnFail` | status(7), updated_time, **end_time, arrival_time, actual_duration_min, end_mileage_m, actual_mileage_m** | ✅ 已完善 | 完整分析失败任务的执行数据 |
| `updateExecutionReportOnPause` | status(4), updated_time | ✅ 合理 | 暂停状态不需要结束数据 |

#### 5.5.2 改进成果分析

**数据完整性提升**：
- 📊 **统计准确**: 所有异常结束的任务都能统计实际执行时长和里程
- 🔍 **分析能力增强**: 可以完整分析取消/终止/失败任务的资源消耗情况
- 📈 **报表数据完整**: 导出的执行报表中异常状态任务包含完整的关键数据

**业务价值提升**：
- 💰 **成本分析**: 能够准确计算异常任务的运营成本
- 🛠️ **故障排查**: 提供异常状态的详细执行数据支持
- 📋 **优化依据**: 基于完整数据进行任务和路线优化

#### 5.5.3 字段更新对比表

| 字段名 | 任务开始 | 任务完成 | 任务取消 | 任务暂停 | 任务终止 | 任务失败 | 状态 |
|--------|----------|----------|----------|----------|----------|----------|------|
| **status** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 完整 |
| **updated_time** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 完整 |
| **departure_time** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | 合理 |
| **end_time** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | **已完善** |
| **arrival_time** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | **已完善** |
| **actual_duration_min** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | **已完善** |
| **end_mileage_m** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | **已完善** |
| **actual_mileage_m** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | **已完善** |

#### 5.5.4 改进实施结果

| 状态 | 改进内容 | 实施状态 | 业务价值 |
|------|----------|----------|----------|
| **任务取消** | 添加结束时间和里程数据 | ✅ 已完成 | 完整分析取消原因和资源消耗 |
| **任务终止** | 添加结束时间和里程数据 | ✅ 已完成 | 完整支持故障分析和异常排查 |
| **任务失败** | 添加结束时间和里程数据 | ✅ 已完成 | 完整支持失败原因分析和优化 |
| **任务暂停** | 保持现状 | ✅ 保持 | 暂停状态逻辑合理，符合业务需求 |

### 5.6 完整字段更新逻辑详解

#### 5.6.1 字段分类和更新时机

基于实际代码分析，task_execution_report表的字段按更新时机可分为以下几类：

| 字段类别 | 更新时机 | 负责方法 | 数据来源 |
|----------|----------|----------|----------|
| **基础标识字段** | 任务下发时 | `createExecutionReport` | ccapiex任务信息 |
| **车辆信息字段** | 任务下发时 | `createExecutionReport` | 设备信息查询 |
| **地点相关字段** | 任务下发时+任务开始时 | `ParseTaskContent`+`updateExecutionReportOnStart` | 任务内容+心跳数据 |
| **规划数据字段** | 任务开始时 | `updateExecutionReportOnStart` | 心跳数据TaskInfo |
| **执行状态字段** | 状态变更时 | `updateExecutionReportOn*` | 心跳数据+计算 |
| **时间里程字段** | 开始/结束时 | `updateExecutionReportOnStart/Complete` | 心跳数据+计算 |

#### 5.6.2 任务下发时的字段初始化

**文件位置**: `ccapiex/controllers/task.go` - `createExecutionReport` 方法

```go
// createExecutionReport 创建任务执行实例记录
func (c *TaskController) createExecutionReport(task *pix.Task, child *pix.TaskChild, userInfo *dto.UserInfo) error {
    // 获取设备信息
    devices, err := c.deviceService.All(0, task.Imsi, -1, "", false, -1)
    var device pix.Device
    if err != nil || len(devices) == 0 {
        device = pix.Device{Type: 0} // 默认为RoboBus
    } else {
        device = devices[0]
    }

    // 创建执行实例记录
    executionReport := &pix.TaskExecutionReport{
        // 基础标识字段
        TaskId:       int64(task.Id),
        ChildId:      child.Id,
        ExecutionNo:  task.Times,
        Imsi:         task.Imsi,
        RunningTask:  fmt.Sprintf("%d-%d", child.Id, task.Times),

        // 时间字段
        StartTime:    time.Now(),
        CreatedTime:  time.Now().Unix(),

        // 状态字段（数字类型）
        Status:       1, // TaskStatusWillExecute = 1 (将要执行)

        // 车辆信息字段
        VehiclePlate: task.Imsi,
        VehicleType:  pix.GetVehicleType(device.Type),
        VehicleUsage: pix.GetVehicleUsage(device.Type),

        // 任务信息字段（数字类型）
        TaskType:     task.Type, // 直接使用数字类型
        Creator:      userInfo.Username,
    }

    // 解析任务内容，填充地点信息
    err = c.executionReportService.ParseTaskContent(executionReport, child.Content, task.MapId)

    // 保存到数据库
    return c.executionReportService.CreateExecutionReport(executionReport)
}
```

#### 5.6.3 任务内容解析和地点信息填充

**文件位置**: `ccapiex/service/task_execution_report_service.go` - `ParseTaskContent` 方法

```go
// ParseTaskContent 解析任务内容并填充地点信息
func (s *TaskExecutionReportService) ParseTaskContent(report *pix.TaskExecutionReport, content string, mapId int) error {
    if content == "" {
        return nil
    }

    // 保存原始内容
    report.OriginalTaskContent = content

    // 解析JSON内容
    var taskContent map[string]interface{}
    if err := json.Unmarshal([]byte(content), &taskContent); err != nil {
        return err
    }

    // 解析站点信息
    if stations, ok := taskContent["stations"].([]interface{}); ok && len(stations) >= 2 {
        // 起点
        if startStation, ok := stations[0].(map[string]interface{}); ok {
            if name, exists := startStation["name"]; exists {
                report.StartLocation = fmt.Sprintf("%v", name)
            }
        }

        // 终点
        if endStation, ok := stations[len(stations)-1].(map[string]interface{}); ok {
            if name, exists := endStation["name"]; exists {
                report.EndLocation = fmt.Sprintf("%v", name)
            }
        }

        // 途径点
        if len(stations) > 2 {
            var waypoints []string
            for i := 1; i < len(stations)-1; i++ {
                if station, ok := stations[i].(map[string]interface{}); ok {
                    if name, exists := station["name"]; exists {
                        waypoints = append(waypoints, fmt.Sprintf("%v", name))
                    }
                }
            }
            if len(waypoints) > 0 {
                waypointsJson, _ := json.Marshal(waypoints)
                report.Waypoints = string(waypointsJson)
            }
        }
    }

    return nil
}
```

#### 5.6.4 完整字段更新时机表

| 字段名 | 中文名称 | 任务下发 | 任务开始 | 任务完成 | 异常结束 | 数据来源 |
|--------|----------|----------|----------|----------|----------|----------|
| **基础标识字段** |
| `task_id` | 主任务ID | ✅ | ❌ | ❌ | ❌ | task.Id |
| `child_id` | 子任务ID | ✅ | ❌ | ❌ | ❌ | child.Id |
| `execution_no` | 执行序号 | ✅ | ❌ | ❌ | ❌ | task.Times |
| `imsi` | 设备标识 | ✅ | ❌ | ❌ | ❌ | task.Imsi |
| `running_task` | 运行任务标识 | ✅ | ❌ | ❌ | ❌ | 格式化生成 |
| **车辆信息字段** |
| `vehicle_plate` | 车牌 | ✅ | ❌ | ❌ | ❌ | task.Imsi |
| `vehicle_type` | 车型 | ✅ | ❌ | ❌ | ❌ | device.Type映射 |
| `vehicle_usage` | 车辆用途 | ✅ | ❌ | ❌ | ❌ | device.Type映射 |
| **任务信息字段** |
| `task_type` | 任务类型 | ✅ | ❌ | ❌ | ❌ | task.Type映射 |
| `creator` | 创建人 | ✅ | ❌ | ❌ | ❌ | userInfo.Username |
| `task_name` | 任务名称 | ❌ | ✅ | ❌ | ❌ | 心跳LatestTask.Name |
| `task_description` | 任务描述 | ❌ | ✅ | ❌ | ❌ | 心跳LatestTask.Desp |
| `original_task_content` | 原始任务内容 | ✅ | ❌ | ❌ | ❌ | child.Content |
| **地点相关字段** |
| `start_location` | 开始站点 | ✅ | ✅ | ❌ | ❌ | 任务内容+心跳数据 |
| `end_location` | 结束站点 | ✅ | ✅ | ❌ | ❌ | 任务内容+心跳数据 |
| `waypoints` | 途径站点 | ✅ | ❌ | ❌ | ❌ | 任务内容解析 |
| **规划数据字段** |
| `planned_distance_m` | 规划里程(米) | ❌ | ✅ | ❌ | ❌ | 心跳TaskDistance |
| `planned_duration_min` | 计划时长(分钟) | ❌ | ✅ | ❌ | ❌ | 心跳LeftTime |
| **时间字段** |
| `start_time` | 开始时间 | ✅ | ❌ | ❌ | ❌ | 任务下发时间 |
| `departure_time` | 出发时间 | ❌ | ✅ | ❌ | ❌ | step=1时间 |
| `end_time` | 结束时间 | ❌ | ❌ | ✅ | ✅ | 任务结束时间 |
| `arrival_time` | 到达时间 | ❌ | ❌ | ✅ | ✅ | 任务结束时间 |
| **里程字段** |
| `start_mileage_m` | 开始里程(米) | ❌ | ✅ | ❌ | ❌ | 心跳TotalMileage |
| `end_mileage_m` | 结束里程(米) | ❌ | ❌ | ✅ | ✅ | 心跳TotalMileage |
| `actual_mileage_m` | 实际里程(米) | ❌ | ❌ | ✅ | ✅ | 计算得出 |
| **执行数据字段** |
| `actual_duration_min` | 实际时长(分钟) | ❌ | ❌ | ✅ | ✅ | 计算得出 |
| `actual_speed_kmh` | 实际速度(公里/小时) | ❌ | ❌ | ✅ | ❌ | 计算得出 |
| **状态字段** |
| `status` | 任务状态 | ✅ | ✅ | ✅ | ✅ | 状态映射 |
| **时间戳字段** |
| `created_time` | 创建时间戳 | ✅ | ❌ | ❌ | ❌ | Unix时间戳 |
| `updated_time` | 更新时间戳 | ❌ | ✅ | ✅ | ✅ | Unix时间戳 |

#### 5.6.5 字段数据来源详解

**任务下发时设置的字段**：
- **基础标识**: 来自ccapiex的任务和子任务信息
- **车辆信息**: 通过设备查询获取，包含类型映射
- **任务信息**: 来自任务定义和用户信息
- **地点信息**: 通过ParseTaskContent解析任务内容JSON

**任务开始时更新的字段**：
- **规划数据**: 来自心跳数据的TaskDistance和LeftTime字段
- **地点信息**: 来自心跳数据的CurrentStation和TargetStation
- **里程数据**: 来自心跳数据的TotalMileage字段
- **任务内容**: 来自心跳数据的最新Name和Desp信息

**任务结束时计算的字段**：
- **时间数据**: 基于StartTime计算实际执行时长
- **里程数据**: 基于开始和结束里程计算实际运行里程
- **速度数据**: 基于里程和时长计算平均速度

#### 5.6.6 字段映射逻辑详解

**车辆信息映射**（基于设备类型）:
```go
// VehicleTypeMap 车型映射
var VehicleTypeMap = map[int]string{
    0: "RoboBus",
    1: "清扫车",
    2: "物流车",
    3: "室内清洁车",
    4: "海森堡",
}

// VehicleUsageMap 车辆用途映射
var VehicleUsageMap = map[int]string{
    0: "公交运输",
    1: "清扫作业",
    2: "物流运输",
    3: "室内清洁",
    4: "特殊用途",
}

// GetVehicleType 获取车型文本
func GetVehicleType(deviceType int) string {
    if text, exists := VehicleTypeMap[deviceType]; exists {
        return text
    }
    return "未知类型"
}
```

**任务类型映射**:
```go
// TaskTypeMap 任务类型映射
var TaskTypeMap = map[int]string{
    1: "定时任务",
    2: "周期任务",
}

// GetTaskType 获取任务类型文本
func GetTaskType(taskType int) string {
    if text, exists := TaskTypeMap[taskType]; exists {
        return text
    }
    return "普通任务"
}
```

**状态映射逻辑**:
```go
// TaskStatusMap 任务状态映射
var TaskStatusMap = map[int]string{
    0: "不执行",
    1: "将要执行",
    2: "正在执行",
    3: "已完成",
    4: "已暂停",
    5: "已取消",
    6: "已终止",
    7: "无法完成",
}

// StepToExecutionStatusMap step到执行状态的映射
var StepToExecutionStatusMap = map[int]ExecutionStatus{
    1: ExecutionStatusStarted,    // "started"
    2: ExecutionStatusCompleted,  // "completed"
    3: ExecutionStatusCancelled,  // "cancelled"
    6: ExecutionStatusPaused,     // "paused"
    8: ExecutionStatusTerminated, // "terminated"
    9: ExecutionStatusFailed,     // "failed"
}
```

### 5.7 executionId获取逻辑（已简化）

```go
// getExecutionIdFromHeartbeat 从心跳数据中获取执行实例ID
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) getExecutionIdFromHeartbeat() int64 {
    // 🔄 简化逻辑：taskId 直接是执行实例ID
    executionId := t.heartbeatDevice.LatestTask.TaskID

    if executionId > 0 {
        // 🆕 验证是否为有效的执行实例ID
        if t.isValidExecutionId(executionId) {
            pix_log.Info(fmt.Sprintf("✅ 设备 %s 获取到执行实例ID: %d",
                t.term.Prop.IMSI, executionId))
            return executionId
        } else {
            pix_log.Warning(fmt.Sprintf("⚠️ 设备 %s 的taskId %d 不是有效的执行实例ID",
                t.term.Prop.IMSI, executionId))
        }
    }

    pix_log.Warning(fmt.Sprintf("❌ 设备 %s 心跳数据中未包含有效的执行实例ID", t.term.Prop.IMSI))
    return 0
}

// isValidExecutionId 验证执行实例ID的有效性
func (t *Heartbeat) isValidExecutionId(executionId int64) bool {
    var count int64
    err := db.GetPixmoving().Table("task_execution_report").
        Where("id = ? AND imsi = ?", executionId, t.term.Prop.IMSI).
        Count(&count).Error

    return err == nil && count > 0
}
```

---

## 6. 数据流向和状态管理

### 6.1 完整数据流向图

```mermaid
flowchart TD
    A[Web前端任务下发] --> B[ccapiex API处理]
    B --> C[创建execution_report记录]
    C --> D[发送Kafka消息]
    D --> E[ccserver消费消息]
    E --> F[TCP协议转换]
    F --> G[设备接收任务]

    G --> H[设备发送心跳数据]
    H --> I[ccserver心跳处理]
    I --> J{step值判断}

    J -->|step=0| K[终止执行实例]
    J -->|step=1| L[更新为started]
    J -->|step=2| M[更新进度信息]
    J -->|step=3| N[更新为completed]
    J -->|step=4| O[更新为paused]
    J -->|step=5| P[更新为cancelled]
    J -->|step=6| Q[更新为terminated]
    J -->|step=7,9| R[更新为failed]

    K --> S[数据库状态更新]
    L --> S
    M --> S
    N --> S
    O --> S
    P --> S
    Q --> S
    R --> S

    S --> T[前端状态展示]
    S --> U[报表数据导出]
```

### 6.2 任务状态转换（数字枚举）

```mermaid
stateDiagram-v2
    [*] --> 1: 任务下发

    1 --> 2: step=1(开始执行)

    2 --> 2: step=2(执行中)
    2 --> 3: step=2(正常完成)
    2 --> 4: step=6(暂停)
    2 --> 5: step=3(取消)
    2 --> 6: step=8(终止)
    2 --> 7: step=9(失败)

    4 --> 2: 恢复执行
    4 --> 5: step=3(取消)
    4 --> 6: step=8(终止)

    3 --> [*]: 任务结束
    5 --> [*]: 任务结束
    6 --> [*]: 任务结束
    7 --> [*]: 任务结束

    note right of 1
        初始状态
        status = 1 (将要执行)
    end note

    note right of 2
        执行状态
        status = 2 (正在执行)
    end note

    note right of 3
        完成状态
        status = 3 (已完成)
    end note
```

### 6.3 错误处理和异常状态

#### 6.3.1 设备离线处理

```go
// updateExecutionReportOnNoTask 无任务时终止所有执行中的实例
// 实际代码位置：app/system/heartbeat.go
func (t *Heartbeat) updateExecutionReportOnNoTask() {
    // 直接通过数据库更新，不依赖ccapiex代码
    db.GetPixmoving().Table("task_execution_report").
        Where("imsi = ? AND status IN (?)", t.term.Prop.IMSI, []int{1, 2, 4}). // 1=将要执行, 2=正在执行, 4=已暂停
        Updates(map[string]interface{}{
            "status":       6, // TaskStatusTerminated = 6 (已终止)
            "updated_time": time.Now().Unix(),
        })
}
```

#### 6.3.2 任务失败处理

```go
// updateExecutionReportOnFail 任务失败时更新执行实例
func (t *Heartbeat) updateExecutionReportOnFail() {
    executionId := t.getExecutionIdFromHeartbeat()
    if executionId == 0 {
        return
    }

    updates := map[string]interface{}{
        "status":       7, // TaskStatusFailed = 7 (无法完成)
        "updated_time": time.Now().Unix(),
    }

    db.GetPixmoving().Table("task_execution_report").Where("id = ?", executionId).Updates(updates)
}
```

### 6.4 向后兼容性保证

#### 6.4.1 老设备兼容机制

1. **心跳数据优先**: 新设备直接从心跳数据获取executionId
2. **数据库查询兜底**: 老设备通过child_id查询最新执行实例
3. **渐进式升级**: 不强制要求设备立即升级

#### 6.4.2 协议兼容性

```go
// 协议版本兼容处理
if t.heartbeatDevice.LatestTask.ExecutionId > 0 {
    // 新协议：使用executionId
    return t.heartbeatDevice.LatestTask.ExecutionId
} else {
    // 老协议：通过taskId查询
    return t.queryExecutionIdByTaskId(t.heartbeatDevice.LatestTask.TaskID)
}
```

---

## 7. 关键技术细节

### 7.1 数据库字段映射关系

| 心跳数据字段 | 数据库字段 | 数据类型 | 说明 |
|-------------|------------|----------|------|
| `LatestTask.ExecutionId` | `id` | int64 | 执行实例主键 |
| `LatestTask.TaskID` | `child_id` | int | 子任务ID |
| `LatestTask.Name` | `running_task` | string | 运行任务标识 |
| `LatestTask.StartTime` | `departure_time` | datetime | 出发时间 |
| `TotalMileage` | `start_mileage_m` | int | 开始里程(米) |
| `TotalMileage` | `end_mileage_m` | int | 结束里程(米) |
| `LatestTask.TaskDistance` | `planned_distance_m` | int | 规划里程(米) |
| `LatestTask.LeftTime` | `planned_duration_min` | int | 计划时长(分钟) |

### 7.2 时间戳处理和时区转换

```go
// 时间戳统一处理
func (t *Heartbeat) getStandardTime() time.Time {
    // 使用服务器本地时区
    return time.Now()
}

// Unix时间戳转换
func (t *Heartbeat) toUnixTimestamp(t time.Time) int64 {
    return t.Unix()
}
```

### 7.3 里程数据计算逻辑

```go
// 里程数据处理逻辑
func (t *Heartbeat) calculateMileage(executionId int64) {
    if t.heartbeatDevice.TotalMileage >= 0 {
        currentMileage := int(t.heartbeatDevice.TotalMileage * 1000) // 转换为米

        // 获取开始里程
        var startMileage int
        db.GetPixmoving().Table("task_execution_report").
            Where("id = ?", executionId).
            Select("start_mileage_m").
            Scan(&startMileage)

        // 计算实际运行里程
        if startMileage > 0 {
            actualMileage := currentMileage - startMileage
            if actualMileage > 0 {
                // 更新实际里程
                db.GetPixmoving().Table("task_execution_report").
                    Where("id = ?", executionId).
                    Updates(map[string]interface{}{
                        "end_mileage_m":    currentMileage,
                        "actual_mileage_m": actualMileage,
                    })
            }
        }
    }
}
```

### 7.4 站点信息解析和存储

```go
// ParseTaskContent 解析任务内容并填充地点信息
func (s *TaskExecutionReportService) ParseTaskContent(report *pix.TaskExecutionReport, content string, mapId int) error {
    if content == "" {
        return nil
    }

    // 保存原始内容
    report.OriginalTaskContent = content

    // 解析JSON内容
    var taskContent map[string]interface{}
    if err := json.Unmarshal([]byte(content), &taskContent); err != nil {
        return err
    }

    // 解析起点和终点信息
    if startPoint, ok := taskContent["startPoint"].(map[string]interface{}); ok {
        if stationId, ok := startPoint["stationId"].(float64); ok {
            // 查询站点信息
            station := s.getStationInfo(int(stationId), mapId)
            if station != nil {
                report.StartLocation = station.Name
            }
        }
    }

    if endPoint, ok := taskContent["endPoint"].(map[string]interface{}); ok {
        if stationId, ok := endPoint["stationId"].(float64); ok {
            station := s.getStationInfo(int(stationId), mapId)
            if station != nil {
                report.EndLocation = station.Name
            }
        }
    }

    return nil
}
```

### 7.5 性能优化要点

#### 7.5.1 数据库查询优化

```sql
-- 执行实例查询索引
CREATE INDEX idx_execution_report_imsi_status ON task_execution_report(imsi, status);
CREATE INDEX idx_execution_report_child_created ON task_execution_report(child_id, created_time);

-- 心跳数据查询优化
CREATE INDEX idx_heartbeat_imsi_ts ON device_heartbeat_data(imsi, ts);
```

#### 7.5.2 缓存策略

```go
// Redis缓存执行实例信息
func (t *Heartbeat) cacheExecutionInfo(executionId int64) {
    cacheKey := fmt.Sprintf("execution:%d", executionId)
    cacheData := map[string]interface{}{
        "id":     executionId,
        "status": "started",
        "ts":     time.Now().Unix(),
    }

    cacheJSON, _ := json.Marshal(cacheData)
    redis.Set(cacheKey, string(cacheJSON), 300*time.Second) // 5分钟缓存
}
```

### 7.6 监控和日志

#### 7.6.1 关键日志点

```go
// 关键操作日志
pix_log.Info(fmt.Sprintf("🚀 任务下发：IMSI=%s, TaskId=%d, ExecutionId=%d",
    imsi, taskId, executionId))

pix_log.Info(fmt.Sprintf("📡 心跳处理：IMSI=%s, Step=%d, ExecutionId=%d",
    imsi, step, executionId))

pix_log.Info(fmt.Sprintf("✅ 状态更新：ExecutionId=%d, %s → %s",
    executionId, oldStatus, newStatus))
```

#### 7.6.2 性能监控

```go
// 执行时长监控
func (t *Heartbeat) monitorExecutionDuration(executionId int64) {
    var startTime time.Time
    db.GetPixmoving().Table("task_execution_report").
        Where("id = ?", executionId).
        Select("departure_time").
        Scan(&startTime)

    if !startTime.IsZero() {
        duration := time.Since(startTime)
        if duration > 30*time.Minute {
            pix_log.Warning(fmt.Sprintf("⚠️ 任务执行时间过长：ExecutionId=%d, Duration=%v",
                executionId, duration))
        }
    }
}
```

---

## 8. 故障排查指南

### 8.1 设备注册问题

**问题1：设备连接后无法注册**
```
症状：TCP连接成功，但心跳数据不被处理
日志：❌ [Register] 设备注册失败：IMSI=xxx 不存在于device表中，暂不允许新终端注册
```
**解决方案**：
```sql
-- 在device表中创建设备记录
INSERT INTO device (imsi, imei, name, uid, status, created_time, updated_time)
VALUES ('设备IMSI', '设备IMEI', '设备名称', 1, 1, NOW(), NOW());
```

**问题2：设备已过期**
```
症状：设备存在于device表但注册失败
日志：❌ [Register] 设备注册失败：IMSI=xxx 已过期，过期时间=2024-01-01 00:00:00
```
**解决方案**：
```sql
-- 更新设备过期时间或设置为永不过期
UPDATE device SET expire_time = 0 WHERE imsi = '设备IMSI';
```

**问题3：注册消息格式错误**
```
症状：TCP连接建立但注册失败，IMSI显示为空
日志：终端下线, 终端未在缓存列表中, IMSI:, 原因:终端主动断开
```
**解决方案**：检查注册消息字段格式，确保使用下划线命名法（software_ver, hardware_ver等）

### 8.2 心跳处理问题

**问题1：心跳数据被忽略**
```
症状：设备已连接但状态不更新
日志：⚠️ [GetReply] 设备未授权，跳过心跳处理：IMSI=xxx
```
**解决方案**：确认设备已成功注册并设置Authorized=true

**问题2：ExecutionId不匹配**
```
症状：心跳数据处理但状态不更新
日志：查询执行实例失败或找不到对应记录
```
**解决方案**：
```sql
-- 检查task_execution_report表中是否存在对应的execution_id
SELECT * FROM task_execution_report WHERE id = 执行实例ID;
```

### 8.3 日志分析技巧

**启用详细日志**：
```toml
# config/config.toml
[logging.modules]
tcp = { level = "all", stdout = true }
terminal = { level = "all", stdout = true }
```

**关键日志标识**：
- `✅ [Register]`：设备注册成功
- `❌ [Register]`：设备注册失败
- `⚠️ [GetReply]`：心跳处理警告
- `🔄 [GetReply]`：状态更新处理

---

## 9. 总结

### 8.1 系统优势

1. **数据一致性**: 通过executionId确保任务执行数据的准确跟踪
2. **向后兼容**: 支持新老设备的平滑过渡
3. **实时性**: 心跳数据实时更新执行状态
4. **可扩展性**: 模块化设计便于功能扩展
5. **性能优化**: 移除冗余字段，减少网络传输

### 8.2 关键技术点

- ✅ **executionId**: 全局唯一执行实例标识
- ✅ **step状态机**: 完整的任务状态生命周期管理
- ✅ **兼容机制**: 新老设备的平滑过渡方案
- ✅ **实时同步**: 心跳数据驱动的状态更新
- ✅ **事务处理**: 确保数据一致性的事务机制

### 8.3 未来优化方向

1. **消息队列优化**: 考虑使用更高性能的消息队列
2. **数据库分片**: 大规模部署时的数据库分片策略
3. **缓存优化**: 更精细的缓存策略提升性能
4. **监控告警**: 完善的监控和告警机制
5. **自动化测试**: 完整的自动化测试覆盖

---

**文档版本**: v1.0
**最后更新**: 2025-01-02
**维护者**: 系统架构团队
```
```
```
```
```
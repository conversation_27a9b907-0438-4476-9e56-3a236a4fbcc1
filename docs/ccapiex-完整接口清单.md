# ccapiex 完整 Kafka 消息发送接口清单

## 接口总览

| 序号 | 控制器 | 接口路径 | HTTP方法 | 功能描述 | Kafka命令码 | 十六进制 | 十进制 |
|------|--------|----------|----------|----------|-------------|----------|--------|
| 1 | TaskController | /api/task/do | POST | 下发任务 | CmdMap[9] | 0x0018 | 24 |
| 2 | TaskController | /api/task/send_all | POST | 批量下发任务 | CmdMap[9] | 0x0018 | 24 |
| 3 | ActionController | /api/device/action/cmd | POST | 通用控制命令 | 多种 | 多种 | 多种 |
| 4 | ActionController | /api/device/action/go_point | POST | 移动到指定位置 | CmdMap[6] | 0x0032 | 50 |
| 5 | ActionController | /api/device/action/set_speed | POST | 设置最高限速 | CmdMap[22] | 0x0040 | 64 |
| 6 | ActionController | /api/device/action/set_map | POST | 设置自驾地图 | CmdMap[25] | 0x0042 | 66 |
| 7 | VehicleController | /api/vehicle/task/emergencyStop | POST | 紧急停车 | CmdMap[3] | 0x0034 | 52 |
| 8 | VehicleController | /api/vehicle/task/device_sync | POST | 车端地图同步 | CmdMap[27] | 0x0044 | 68 |
| 9 | VehicleController | /api/vehicle/task/cloud_sync | POST | 云端地图同步 | CmdMap[28] | 0x0046 | 70 |
| 10 | FirmwareController | /api/firmware/push | POST | 固件升级推送 | CmdMap[23] | 0xA001 | 40961 |
| 11 | FirmwareController | /api/firmware/push_vcu | POST | VCU固件升级推送 | CmdMap[12] | 0xA003 | 40963 |
| 12 | AlarmController | /api/alarm/select_alarm | GET | 查询实时告警 | CmdMap[29] | 0x0039 | 57 |

## 详细接口说明

### 1. 任务管理接口 (TaskController)

#### 1.1 下发任务 - POST /api/task/do
**功能**: 下发自动驾驶任务到指定设备
**请求参数**: 
- id: 任务ID
- imsi: 设备标识

**Kafka消息格式**:
```json
{
    "cmd": 24,
    "imsi": "设备ID",
    "taskId": 123,
    "routeId": 456,
    "name": "任务名称",
    "desp": "任务描述",
    "actionList": [
        {
            "action": 1,
            "lat": 39.9042,
            "lng": 116.4074,
            "speed": 30
        }
    ]
}
```

**业务逻辑**:
1. 检查设备是否有正在执行的任务
2. 获取子任务并更新状态为执行中
3. 构建Kafka消息并发送
4. 更新设备关联信息
5. 记录操作日志

#### 1.2 批量下发任务 - POST /api/task/send_all
**功能**: 批量下发任务到多个设备
**请求参数**: 
- id: 任务ID

**Kafka消息格式**: 为每个设备发送独立的任务消息

### 2. 车辆控制接口 (ActionController)

#### 2.1 通用控制命令 - POST /api/device/action/cmd
**功能**: 发送各种车辆控制命令
**请求参数**: 
- imsi: 设备标识
- cmd: 命令类型

**支持的命令类型**:

| cmd参数 | 功能 | Kafka命令码 | 十六进制 | 十进制 | 消息格式 |
|---------|------|-------------|----------|--------|----------|
| 1 | 开始自动驾驶 | CmdMap[1] | 0x0023 | 35 | `{"cmd": 35, "imsi": "设备ID"}` |
| 2 | 停止自动驾驶 | CmdMap[2] | 0x0026 | 38 | `{"cmd": 38, "imsi": "设备ID"}` |
| 3 | 紧急停车 | CmdMap[3] | 0x0034 | 52 | `{"cmd": 52, "imsi": "设备ID"}` |
| 4 | 开中控锁 | CmdMap[4] | 0x0004 | 4 | `{"cmd": 4, "imsi": "设备ID"}` |
| 5 | 关中控锁 | CmdMap[5] | 0x0005 | 5 | `{"cmd": 5, "imsi": "设备ID"}` |
| 7 | 鸣笛 | CmdMap[7] | 0x0006 | 6 | `{"cmd": 6, "imsi": "设备ID"}` |
| 15 | 重启 | CmdMap[15] | 0x0010 | 16 | `{"cmd": 16, "imsi": "设备ID"}` |
| 16 | 启动自动驾驶 | CmdMap[16] | 0x0023 | 35 | `{"cmd": 35, "imsi": "设备ID"}` |
| 17 | 取消自动驾驶 | CmdMap[17] | 0x0025 | 37 | `{"cmd": 37, "imsi": "设备ID"}` |
| 19 | 暂停自动驾驶 | CmdMap[19] | 0x0024 | 36 | `{"cmd": 36, "imsi": "设备ID"}` |
| 20 | 断点续扫 | CmdMap[20] | 0x0605 | 1541 | `{"cmd": 1541, "imsi": "设备ID"}` |
| 21 | 一键回家 | CmdMap[21] | 0x0036 | 54 | `{"cmd": 54, "imsi": "设备ID"}` |
| 24 | 解除紧急停车 | CmdMap[24] | 0x0035 | 53 | `{"cmd": 53, "imsi": "设备ID"}` |

#### 2.2 移动到指定位置 - POST /api/device/action/go_point
**功能**: 控制车辆移动到指定GPS坐标
**请求参数**: 
- imsi: 设备标识
- lat: 纬度
- lng: 经度
- raw: 原始坐标数据

**Kafka消息格式**:
```json
{
    "cmd": 50,
    "imsi": "设备ID",
    "lat": 39.9042,
    "lng": 116.4074,
    "raw": "原始坐标数据"
}
```

#### 2.3 设置最高限速 - POST /api/device/action/set_speed
**功能**: 设置车辆最高行驶速度
**请求参数**: 
- imsi: 设备标识
- speed: 速度值(km/h)

**Kafka消息格式**:
```json
{
    "cmd": 64,
    "imsi": "设备ID",
    "speed": 30
}
```

#### 2.4 设置自驾地图 - POST /api/device/action/set_map
**功能**: 设置车辆使用的自动驾驶地图
**请求参数**: 
- imsi: 设备标识
- name: 地图名称

**Kafka消息格式**:
```json
{
    "cmd": 66,
    "imsi": "设备ID",
    "map": {
        "name": "地图名称"
    }
}
```

### 3. 车辆任务接口 (VehicleController)

#### 3.1 紧急停车 - POST /api/vehicle/task/emergencyStop
**功能**: 立即停止车辆运行
**请求参数**: 
- imsi: 设备标识

**Kafka消息格式**:
```json
{
    "cmd": 52,
    "imsi": "设备ID"
}
```

#### 3.2 车端地图同步 - POST /api/vehicle/task/device_sync
**功能**: 车端地图同步到云端
**请求参数**: 
- imsi: 设备标识

**Kafka消息格式**:
```json
{
    "cmd": 68,
    "imsi": "设备ID"
}
```

#### 3.3 云端地图同步 - POST /api/vehicle/task/cloud_sync
**功能**: 云端地图同步到车端
**请求参数**: 
- imsi: 设备标识

**Kafka消息格式**:
```json
{
    "cmd": 70,
    "imsi": "设备ID"
}
```

### 4. 固件管理接口 (FirmwareController)

#### 4.1 固件升级推送 - POST /api/firmware/push
**功能**: 推送固件升级指令到设备
**请求参数**: 
- t: 推送类型 (0=普通升级, 1=强制升级)

**Kafka消息格式**:
```json
{
    "cmd": 40961,
    "imsi": "设备ID"
}
```

#### 4.2 VCU固件升级推送 - POST /api/firmware/push_vcu
**功能**: 推送VCU固件升级指令到设备
**请求参数**: 
- t: 推送类型 (0=普通升级, 1=强制升级)

**Kafka消息格式**:
```json
{
    "cmd": 40963,
    "imsi": "设备ID"
}
```

### 5. 告警管理接口 (AlarmController)

#### 5.1 查询实时告警 - GET /api/alarm/select_alarm
**功能**: 查询设备当前告警状态
**请求参数**: 
- imsi: 设备标识

**Kafka消息格式**:
```json
{
    "cmd": 57,
    "imsi": "设备ID",
    "level": [0, 1, 2, 3]
}
```

## 技术实现要点

### 统一消息发送
所有接口都通过 `DeviceService.SendMessage()` 发送到 Kafka 主题 `device_cmd2`

### 协议命令映射
通过 `CmdMap` 数组实现 HTTP 参数到协议命令码的统一映射

### 状态跟踪
- Redis 缓存: 120秒命令状态缓存
- 操作日志: 完整的操作审计记录
- 设备状态检查: 防止重复任务下发

---

**文档版本**: v1.0  
**更新时间**: 2025-06-27  
**维护团队**: 系统架构组

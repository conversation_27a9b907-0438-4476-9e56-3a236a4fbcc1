# 告警信息处理

## 1. 心跳告警 (heartbeatDevice.Alarm)
```go
// 1. 存储告警记录到 alarm_device 表
db.GetPixmoving().Create(&model.AlarmDevice{
    Imsi:  p.heartbeat.term.Prop.IMSI,    // 设备 IMSI
    Code:  p.heartbeat.heartbeatDevice.Alarm.Code,   // 告警代码
    Level: p.heartbeat.heartbeatDevice.Alarm.Level,  // 告警级别
    Type:  p.heartbeat.heartbeatDevice.Alarm.Type,   // 告警类型
    Msg:   p.heartbeat.heartbeatDevice.Alarm.Msg,    // 告警消息
    Ts:    p.heartbeat.heartbeatDevice.Alarm.Ts,     // 告警时间戳
})

// 2. 查询告警配置并通过 MQTT 发送
// 2.1 从 alarm_config 表查询告警配置
var alarmConfig model.AlarmConfig
db.GetPixmoving().Where("code = ?", alarm.Code).First(&alarmConfig)

// 2.2 构造 MQTT 消息并发送到 {prefix}/alarm/device/{IMSI}
result := protocol.ResultAlarm{
    Id:           alarmConfig.Id,          // 配置 ID
    Code:         alarm.Code,              // 告警代码
    Level:        alarm.Level,             // 告警级别
    Type:         alarm.Type,              // 告警类型
    Msg:          alarm.Msg,               // 告警消息
    Ts:           alarm.Ts,                // 告警时间戳
    FirstDomain:  alarmConfig.FirstDomain, // 一级域
    SecondDomain: alarmConfig.SecondDomain,// 二级域
}
mqtt.SharedClient.Publish(topic, 0, false, resultJSON)
```

涉及的表：
1. `alarm_device` 表：存储告警基本信息
2. `alarm_config` 表：辅助查询告警域表

## 2. CO2 告警 (CO2 <= 270 && CO2 > 0)
```go
// 1. 存储通知记录到 notification 表
notification := model.Notification{
    DeviceId:    int(p.heartbeat.term.Prop.ID),
    Type:        105,  // CO2 告警类型
    Content:     fmt.Sprintf("您的车辆%s当前车内二氧化碳浓度超过1000ppm", p.heartbeat.term.Prop.IMSI),
    Lng:         p.heartbeat.heartbeatDevice.Longitude,
    Lat:         p.heartbeat.heartbeatDevice.Latitude,
    MessageTime: int(p.heartbeat.ts),
    CreatedTime: int(p.heartbeat.ts),
    CompanyId:   p.device.CompanyId,
}
db.GetPixmoving().Create(&notification)

// 2. 计算 CO2 数值
num := float64(0)
if p.heartbeat.heartbeatDevice.CO2 > 258 && p.heartbeat.heartbeatDevice.CO2 <= 270 {
    num = float64(290-p.heartbeat.heartbeatDevice.CO2) / 0.02
} else if p.heartbeat.heartbeatDevice.CO2 > 236 && p.heartbeat.heartbeatDevice.CO2 <= 258 {
    num = 276.53 - float64(p.heartbeat.heartbeatDevice.CO2)/0.0116
} else if p.heartbeat.heartbeatDevice.CO2 > 224 && p.heartbeat.heartbeatDevice.CO2 <= 236 {
    num = 264 - float64(p.heartbeat.heartbeatDevice.CO2)/0.008
} else if p.heartbeat.heartbeatDevice.CO2 < 224 {
    num = 5000
}

// 3. 发送短信（如果满足条件）
if p.user.Check == 1 && time.Now().Unix()-p.user.CoLastSend >= 300 {
    common.SendSms(p.user.Tel, p.device.IMSI, "二氧化碳", lastNum, time.Now().Format("2006-01-02 15:04:05"))
    // 更新最后发送时间
    db.GetPixmoving().Model(&model.User{}).Where("id", p.user.Id).Update("co_last_send", time.Now().Unix())
}
```

涉及的表：
1. `notification` 表：存储 CO2 告警通知记录
2. `user` 表：更新短信发送时间

## 3. 烟雾告警 (Smoke > 1)
```go
// 1. 存储通知记录到 notification 表
notification := model.Notification{
    DeviceId:    int(p.heartbeat.term.Prop.ID),
    Type:        106,  // 烟雾告警类型
    Content:     fmt.Sprintf("您的车辆%s当前车内烟雾浓度过高", p.heartbeat.term.Prop.IMSI),
    Lng:         p.heartbeat.heartbeatDevice.Longitude,
    Lat:         p.heartbeat.heartbeatDevice.Latitude,
    MessageTime: int(p.heartbeat.ts),
    CreatedTime: int(p.heartbeat.ts),
    CompanyId:   p.device.CompanyId,
}
db.GetPixmoving().Create(&notification)

// 2. 发送短信（如果满足条件）
if p.user.Check == 1 && time.Now().Unix()-p.user.SmokeLastSend >= 300 {
    common.SendSms(p.user.Tel, p.device.IMSI, "烟雾", "超标范围", time.Now().Format("2006-01-02 15:04:05"))
    // 更新最后发送时间
    db.GetPixmoving().Model(&model.User{}).Where("id", p.user.Id).Update("smoke_last_send", time.Now().Unix())
}
```

涉及的表：
1. `notification` 表：存储烟雾告警通知记录
2. `user` 表：更新短信发送时间 
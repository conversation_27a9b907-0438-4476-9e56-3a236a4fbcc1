# 设备注册时的数据存储操作

## 一、Redis操作

### 1. 心跳相关
```go
// 初始化心跳记录时间戳
cacheservice.SetRedisHeartbeatRecordTs(register.imsi, 0)

// 清除离线时间戳
cacheservice.SetRedisDeviceOfflineTs(register.imsi, -1)
```

### 2. 设备状态缓存
```go
// 设置设备在线状态
cacheservice.SetDeviceOnlineStatus(register.imsi, true)

// 更新设备最后活跃时间
cacheservice.UpdateDeviceLastActiveTime(register.imsi)
```

## 二、数据库操作

### 1. 设备表更新
```go
// 组装更新数据
data := gdb.Map{
    "imei":         register.imei,
    "model":        register.model,
    "software_ver": register.softwareVer,
    "hardware_ver": register.hardwareVer,
    "vender":       register.vender,
    "ci":           register.ci,
    "pci":          register.pci,
    "manage_ip":    register.manageIp,
    "online":       1,                // 设置在线状态
    "name":         siteName,
    "ip":           register.term.Prop.IP,
    "online_time":  nowTime,
    "updated_time": nowTime,
}

// 更新设备信息
db2.GetPixmoving().Table("device").
    Where("imsi = ?", register.imsi).
    Updates(data)
```

### 2. 登录日志记录
```go
// 添加登录日志
dbservice.AddLoginLog(register.term.Prop.IMSI, "终端上线", 1)
```

## 三、数据更新时序

1. Redis操作顺序：
   - 设置心跳记录时间戳
   - 清除离线时间戳
   - 更新设备在线状态
   - 更新最后活跃时间

2. 数据库操作顺序：
   - 更新设备表信息
   - 记录登录日志

## 四、注意事项

### 1. Redis操作注意点
- 心跳时间戳必须在注册开始时就设置
- 离线时间戳必须在注册成功时清除
- 缓存操作失败不影响数据库操作

### 2. 数据库操作注意点
- 设备表更新是原子操作
- 必须更新的字段：
  1. online = 1
  2. online_time
  3. updated_time
- 登录日志写入失败不影响注册流程 
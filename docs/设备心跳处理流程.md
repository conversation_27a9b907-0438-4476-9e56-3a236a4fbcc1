# 设备心跳处理流程 - Redis和数据库操作

## 一、Redis操作

### 1. 心跳数据缓存

```go
// 1. 存储心跳数据
cacheservice.SetRedisDeviceHeartbeat(t.term.Prop.IMSI, string(cacheJSON))

// 2. 更新心跳时间戳（用于判断设备是否在线）
cacheservice.SetRedisHeartbeatTs(t.term.Prop.IMSI, time.Now().Unix())

// 3. 更新心跳记录时间戳（用于控制数据库记录频率）
cacheservice.SetRedisHeartbeatRecordTs(t.term.Prop.IMSI, time.Now().Unix())
```

### 2. LAN口状态缓存
```go
// 更新LAN口离线时间戳
cacheservice.SetRedisLan1OfflineTs(imsi, offlineTs)  // LAN1
cacheservice.SetRedisLan2OfflineTs(imsi, offlineTs)  // LAN2
```

## 二、数据库操作

### 1. 任务状态更新
```go
// 1. 无任务时更新状态
if t.heartbeatDevice.LatestTask.TaskID == 0 {
    // 将执行中的任务状态改为取消
    // status = 6 表示任务取消
    db.GetPixmoving().Table("task").
        Where("imsi = ? and status = 2", t.term.Prop.IMSI).
        Update("status", 6)
    db.GetPixmoving().Table("task_child").
        Where("imsi = ? and status = 2", t.term.Prop.IMSI).
        Update("status", 6)
}

// 2. 有任务时更新任务状态
if t.heartbeatDevice.LatestTask.TaskID != 0 {
    // status的值由心跳包中的step字段决定：
    // step = 1 -> status = 2 (执行中)
    // step = 2 -> status = 3 (已完成)
    // step = 3 -> status = 5 (终止)
    // step = 6 -> status = 4 (暂停)
    // step = 8 -> status = 6 (取消)
    // step = 9 -> status = 7 (失败)
    step := t.heartbeatDevice.LatestTask.Step
    switch step {
    case 1:
        status = 2  // 执行中
    case 2:
        status = 3  // 已完成
    case 3:
        status = 5  // 终止
    case 6:
        status = 4  // 暂停
    case 8:
        status = 6  // 取消
    case 9:
        status = 7  // 失败
    }
    
    // 更新子任务状态
    db.GetPixmoving().Model(&child).
        Where("id = ?", child.Id).
        Updates(map[string]interface{}{
            "status": status,
            "is_now": isNow,
        })
    // 同步更新父任务状态
    db.GetPixmoving().Model(&model2.Task{}).
        Where("id", child.ParentId).
        Update("status", status)
}
```

### 2. 告警记录
```go
// 1. 设备告警记录
if t.heartbeatDevice.Alarm.Code != 0 {
    db.GetPixmoving().Create(&model2.AlarmDevice{
        Imsi:  t.term.Prop.IMSI,
        Code:  t.heartbeatDevice.Alarm.Code,
        Level: t.heartbeatDevice.Alarm.Level,
        Type:  t.heartbeatDevice.Alarm.Type,
        Msg:   t.heartbeatDevice.Alarm.Msg,
        Ts:    t.heartbeatDevice.Alarm.Ts,
    })
}

// 2. CO2告警记录
if t.heartbeatDevice.CO2 <= 270 && t.heartbeatDevice.CO2 > 0 {
    db.GetPixmoving().Create(&model2.Notification{
        DeviceId:    int(t.term.Prop.ID),
        Type:        105,
        Content:     fmt.Sprintf("您的车辆%s当前车内二氧化碳浓度超过1000ppm", t.term.Prop.IMSI),
        Lng:         t.heartbeatDevice.Longitude,
        Lat:         t.heartbeatDevice.Latitude,
        MessageTime: int(t.ts),
        CreatedTime: int(t.ts),
        CompanyId:   device.CompanyId,
    })
}

// 3. 烟雾告警记录
if t.heartbeatDevice.Smoke > 1 {
    db.GetPixmoving().Create(&model2.Notification{
        DeviceId:    int(t.term.Prop.ID),
        Type:        106,
        Content:     fmt.Sprintf("您的车辆%s当前车内烟雾浓度过高", t.term.Prop.IMSI),
        Lng:         t.heartbeatDevice.Longitude,
        Lat:         t.heartbeatDevice.Latitude,
        MessageTime: int(t.ts),
        CreatedTime: int(t.ts),
        CompanyId:   device.CompanyId,
    })
}
```

### 3. 事件消息记录
```go
// 1. 紧急消息
if common.InArrayUint(t.heartbeatDevice.Event, protocol.EmergencyMessage) {
    // 创建通知记录
    db.GetPixmoving().Create(&notification)
    // 创建用户通知关联记录
    db.GetPixmoving().Create(&notificationUser)
} else {
    // 2. 普通消息
    db.GetPixmoving().Create(&message)
}
```

### 4. 特定位置计数
```go
// 特定位置操作计数
if t.heartbeatDevice.Latitude == 26.748894 {
    db.GetPixmoving().Table("device").
        Where("imsi = ?", t.term.Prop.IMSI).
        UpdateColumn("operation_num", gorm.Expr("operation_num + ?", 1))
}
```

## 三、数据记录控制逻辑

### 1. 心跳记录判断
```go
func isNeededRecord() bool {
    // 1. 未设置归属者不记录
    if t.term.Prop.UID == 0 {
        return false
    }

    lastHbRecordTs := cacheservice.GetRedisHeartbeatRecordTs(t.term.Prop.IMSI)

    // 2. 首次记录
    if lastHbRecordTs == 0 {
        return true
    }

    // 3. 超过最小记录间隔才记录
    return time.Now().Unix()-lastHbRecordTs >= vars.Config.Setting.RecordMinInterval
} 
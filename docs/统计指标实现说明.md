# 统计指标实现说明

## 一、设备在线统计

### 1. 在线时长统计
数据来源：数据库 `login_log` 表
```go
// 支持两种统计方式，通过 vars.Config.Setting.OfflineReportThreshold 配置决定
if vars.Config.Setting.OfflineReportThreshold == 0 {
    // 1. 按实际在线时间统计
    info = GetDeviceLoginReport(device.IMSI, startTime, endTime)
} else {
    // 2. 按离线时长统计（可能包含偏差）
    info = GetDeviceLoginReportEx(device.IMSI, startTime, endTime)
}

// 从数据库获取登录记录
where := g.Map{
    "created_ts >= ": startTs,
    "created_ts < ":  endTs,
    "imsi":           imsi,
}
loginRes, err := dbService.GetLoginHistory(where, "id asc")
```

#### 1.1 实际在线时间统计逻辑
1. 从数据库获取时间段内的所有登录记录
2. 计算方式：
   - 无记录时：检查数据库中上一条记录状态
     - 如果是在线：`totalOnlineTime = endTs - startTs`
     - 如果是离线：`totalOnlineTime = 0`
   - 有记录时：
     - 第一条是下线：`totalOnlineTime += loginLog.CreatedTs - startTs`
     - 最后一条是上线：`totalOnlineTime += endTs - loginLog.CreatedTs`
     - 其他情况：`totalOnlineTime += 下线时间 - 上线时间`

### 2. 上下线次数统计
数据来源：数据库 `login_log` 表
```go
// 遍历数据库登录记录统计
for _, loginLog := range logs {
    if loginLog.Status == 1 {
        onlineTimes++
    }
    if loginLog.Status == 0 {
        offlineTimes++
    }
}
```

### 3. 在线率计算
数据来源：基于在线时长计算
```go
onlineRatio = float64(totalOnlineTime) / float64(endTs-startTs)
```

## 二、设备状态统计

### 1. 实时状态统计
数据来源：Redis
```go
// 1. 通过Redis心跳时间戳判断设备在线状态
func IsTermOffline() bool {
    // 从Redis获取最后心跳时间
    lastHeartbeatTs := cacheservice.GetRedisHeartbeatTs(deviceId)
    return time.Now().Unix()-lastHeartbeatTs >= vars.Config.Setting.OfflineThreshold
}

// 2. 通过Redis LAN口时间戳判断网络状态
func IsLan1Offline() bool {
    // 从Redis获取LAN口状态时间戳
    lastLan1OfflineTs := cacheservice.GetRedisLan1OfflineTs(deviceId)
    return lastLan1OfflineTs > 0
}
```

### 2. 设备类型统计
数据来源：数据库 `device` 表
```go
// 从数据库device表获取设备信息并统计
var devices []model.Device
db.GetPixmoving().Find(&devices)

for _, device := range devices {
    switch device.Type {
    case 0:
        deviceType0++
    case 1:
        deviceType1++
    case 2:
        deviceType2++
    case 3:
        deviceType3++
    case 4:
        deviceType4++
    }
}
```

## 三、任务统计

### 1. 任务状态统计
数据来源：数据库 `task_log` 表
```go
// 从数据库获取任务日志
db.GetPixmoving().Raw(
    "select * from task_log where id in(?) GROUP BY child_id order by id desc;", 
    allTaskId
).Scan(&taskLogs)

// 统计各状态任务数量
for _, taskLog := range taskLogs {
    switch taskLog.Status {
    case 2:
        taskDoing++    // 执行中
    case 3:
        taskDone++     // 已完成
    }
}
taskUndo = taskTotal - taskDoing - taskDone  // 未完成数
```

### 2. 时间维度统计
数据来源：数据库 `task_log` 表
```go
// 基于task_log表的created_time字段统计
for _, taskLog := range taskLogs {
    // 月度任务统计
    if taskLog.CreatedTime >= firstMonth.Unix() {
        taskMonth++
    }
    // 日度任务统计
    if taskLog.CreatedTime >= firstDay.Unix() {
        taskDay++
    }
}
```

## 四、告警统计

### 1. 设备告警统计
数据来源：数据库 `notification` 表
```go
// 直接查询数据库统计告警数量
db.GetPixmoving().Raw(
    "select count(*) from notification where device_id = ?", 
    device.Id
).Scan(&num)
```

### 2. 环境告警统计
数据来源：
- 实时数据：Redis心跳数据
- 告警记录：数据库 `notification` 表
```go
// 1. 从Redis获取实时环境数据
heartbeatData := cacheservice.GetRedisDeviceHeartbeat(imsi)

// 2. 判断是否需要告警并记录到数据库
// CO2告警：浓度 <= 270 且 > 0
if t.heartbeatDevice.CO2 <= 270 && t.heartbeatDevice.CO2 > 0 {
    db.GetPixmoving().Create(&model2.Notification{
        Type: 105,
        // ... 其他字段
    })
}

// 烟雾告警：浓度 > 1
if t.heartbeatDevice.Smoke > 1 {
    db.GetPixmoving().Create(&model2.Notification{
        Type: 106,
        // ... 其他字段
    })
}
```

### 3. 时间维度告警统计
数据来源：数据库 `message` 表
```go
// 按小时统计告警数
db.GetPixmoving().Raw(
    "select count(*) from message where user_id in (?) and created_time >= ? and created_time < ?", 
    allId, lastHour, lastOne
).Scan(&countAlarm)
```

## 五、地理位置统计

### 1. 省份设备分布
数据来源：
- 省份数据：数据库 `china` 表
- 设备数据：数据库 `device` 表
```go
// 1. 获取省份数据
var china []model.China
db.GetPixmoving().Raw("select * from china where pid = 0 and id > 0").Scan(&china)

// 2. 获取设备数据
var devices []model.Device
db.GetPixmoving().Find(&devices)

// 3. 统计分布
for _, province := range china {
    num := 0
    for _, device := range devices {
        if device.Province == province.Name {
            num++
        }
    }
}
```

## 六、心跳数据统计

### 1. Redis心跳计数
数据来源：Redis Set集合
```go
// 统计Redis Set中的心跳数据总数
func CountHeart() int64 {
    data, _ := v.Redis().DoVar("SCARD", "HEART")
    return data.Int64()
}
```

### 2. 心跳记录控制
数据来源：Redis
```go
func isNeededRecord() bool {
    // 1. 检查设备归属（从内存中的term.Prop获取）
    if t.term.Prop.UID == 0 {
        return false
    }
    
    // 2. 从Redis获取上次记录时间戳
    lastHbRecordTs := cacheservice.GetRedisHeartbeatRecordTs(t.term.Prop.IMSI)
    
    // 3. 判断是否需要记录
    if lastHbRecordTs == 0 {
        return true
    }
    return time.Now().Unix()-lastHbRecordTs >= vars.Config.Setting.RecordMinInterval
}
``` 
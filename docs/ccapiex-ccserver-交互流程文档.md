# ccapiex 与 ccserver 系统交互流程完整技术文档

## 1. 系统架构概述

### 1.1 整体架构
本系统采用微服务架构，通过 Kafka 消息队列实现 ccapiex（运营平台）和 ccserver（TCP服务器）之间的解耦通信。

```
Web界面 → ccapiex → Kafka → ccserver → 车辆终端
```

### 1.2 核心组件
- **ccapiex**: 运营平台，提供 Web API 接口
- **ccserver**: TCP 服务器，管理车辆连接和指令转发
- **Kafka**: 消息队列，主题为 `device_cmd2`
- **MySQL**: 数据库，存储任务和设备信息
- **MQTT**: 实时状态反馈通道

### 1.3 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web管理界面]
        B[移动端APP]
    end

    subgraph "ccapiex 运营平台"
        C[HTTP API网关]
        D[业务逻辑层]
        E[数据访问层]
        F[Kafka生产者]

        subgraph "核心控制器"
            C1[TaskController<br/>任务管理]
            C2[ActionController<br/>车辆控制]
            C3[VehicleController<br/>车辆任务]
            C4[FirmwareController<br/>固件管理]
            C5[AlarmController<br/>告警管理]
        end
    end

    subgraph "消息中间件"
        G[Kafka集群<br/>Topic: device_cmd2]
        H[Redis缓存<br/>状态跟踪]
    end

    subgraph "ccserver TCP服务器"
        I[Kafka消费者]
        J[消息解析器]
        K[协议转换层]
        L[TCP连接管理器]
        M[设备连接池]

        subgraph "协议处理"
            K1[JSON解析]
            K2[数据重组]
            K3[MakeCmd封装]
            K4[二进制协议]
        end
    end

    subgraph "数据存储"
        N[MySQL数据库<br/>任务/设备/日志]
        O[TDengine<br/>心跳数据]
    end

    subgraph "车辆终端"
        P[TCP客户端]
        Q[指令解析器]
        R[执行引擎]
        S[传感器数据]
    end

    subgraph "状态反馈系统"
        T[MQTT Broker]
        U[WebSocket服务]
        V[实时监控]
    end

    %% 主要数据流
    A --> C
    B --> C
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    C --> C5

    C1 --> D
    C2 --> D
    C3 --> D
    C4 --> D
    C5 --> D

    D --> E
    E --> N
    D --> F
    F --> G
    F --> H

    G --> I
    I --> J
    J --> K
    K --> L
    L --> M
    M --> P

    K --> K1
    K1 --> K2
    K2 --> K3
    K3 --> K4

    P --> Q
    Q --> R
    R --> S
    S --> T
    T --> U
    U --> V

    %% 反馈流
    S --> O
    L --> N
    V --> A

    %% 样式定义
    classDef frontendBox fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef apiBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef middlewareBox fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef serverBox fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storageBox fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef vehicleBox fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef feedbackBox fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class A,B frontendBox
    class C,D,E,F,C1,C2,C3,C4,C5 apiBox
    class G,H middlewareBox
    class I,J,K,L,M,K1,K2,K3,K4 serverBox
    class N,O storageBox
    class P,Q,R,S vehicleBox
    class T,U,V feedbackBox
```

系统架构图展示了完整的技术栈和数据流向，包括：
- **前端层**: Web管理界面和移动端APP
- **ccapiex运营平台**: 5个核心控制器和业务逻辑层
- **消息中间件**: Kafka集群和Redis缓存
- **ccserver TCP服务器**: 协议转换和连接管理
- **数据存储**: MySQL和TDengine双存储
- **车辆终端**: TCP客户端和执行引擎
- **状态反馈**: MQTT和WebSocket实时推送

### 1.4 完整时序图

```mermaid
sequenceDiagram
    participant Web as Web前端
    participant API as ccapiex API
    participant DB as MySQL数据库
    participant Redis as Redis缓存
    participant Kafka as Kafka队列
    participant Consumer as ccserver消费者
    participant Protocol as 协议转换层
    participant TCP as TCP管理器
    participant Vehicle as 车辆终端
    participant MQTT as MQTT反馈
    participant TDengine as TDengine

    Note over Web,TDengine: 任务下发完整流程

    %% 1. 任务下发请求
    Web->>API: POST /api/task/do
    Note right of Web: 请求下发任务

    %% 2. 业务逻辑处理
    API->>DB: 检查设备状态
    Note right of API: CheckDevice(0, imsi)
    DB-->>API: 返回设备状态

    alt 设备空闲
        %% 3. 数据库操作
        API->>DB: 获取子任务信息
        Note right of API: Child(id) - 设置status=1
        DB-->>API: 返回子任务数据

        API->>DB: 更新主任务状态
        Note right of API: Cancel(id) - 设置status=1, times+=1
        DB-->>API: 更新成功

        %% 4. 构建并发送Kafka消息
        API->>API: 构建Kafka消息
        Note right of API: {"cmd":24,"imsi":"xxx","taskId":123}

        API->>Kafka: 发送消息到device_cmd2
        Kafka-->>API: 发送确认

        API->>Redis: 缓存命令状态
        Note right of API: 120秒缓存
        Redis-->>API: 缓存成功

        API->>DB: 记录操作日志
        Note right of API: AddLog(imsi, logType, logMsg)
        DB-->>API: 日志记录成功

        API-->>Web: 返回成功响应

        %% 5. ccserver消息处理
        Kafka->>Consumer: 推送消息
        Consumer->>Consumer: 解析JSON消息
        Note right of Consumer: json.Unmarshal(msg.Value, &kafkaMsg)

        alt 消息格式正确
            Consumer->>Protocol: 协议转换
            Note right of Protocol: 数据重组 + MakeCmd封装

            Protocol->>Protocol: JSON → 二进制协议
            Note right of Protocol: 添加消息头、时间戳、校验和

            Protocol->>TCP: 查找设备连接
            TCP->>TCP: 检查连接状态
            Note right of TCP: client.All().Contains(deviceId)

            alt 设备在线
                TCP->>Vehicle: 发送二进制指令
                Note right of TCP: 通过TCP长连接发送
                Vehicle-->>TCP: 确认接收

                TCP->>MQTT: 发布执行状态
                Note right of TCP: WebSocket状态推送
                MQTT-->>Web: 实时状态更新

                %% 6. 任务执行与反馈
                Vehicle->>Vehicle: 解析并执行任务
                Note right of Vehicle: 指令解析器 → 执行引擎

                loop 任务执行过程
                    Vehicle->>TCP: 发送心跳数据
                    Note right of Vehicle: step=1,2,3... 状态变化
                    TCP->>TDengine: 存储心跳数据
                    TCP->>DB: 更新任务执行状态
                    TCP->>MQTT: 推送实时状态
                    MQTT-->>Web: 前端状态更新
                end

                Vehicle->>TCP: 任务完成通知
                Note right of Vehicle: step=2 任务完成
                TCP->>DB: 更新最终状态
                TCP->>MQTT: 推送完成状态
                MQTT-->>Web: 任务完成通知

            else 设备离线
                TCP->>MQTT: 发布错误状态
                Note right of TCP: "设备连接已断开"
                MQTT-->>Web: 错误状态通知
            end

        else 消息格式错误
            Consumer->>Consumer: 记录错误日志
            Note right of Consumer: JSON解析失败
        end

    else 设备忙碌
        API-->>Web: 返回错误
        Note right of API: "设备存在正在执行的任务"
    end

    Note over Web,TDengine: 紧急停车流程

    Web->>API: POST /vehicle/task/emergencyStop
    API->>Kafka: {"cmd":52,"imsi":"xxx"}
    Kafka->>Consumer: 推送紧急停车指令
    Consumer->>Protocol: 立即协议转换
    Protocol->>TCP: 高优先级转发
    TCP->>Vehicle: 紧急停车指令
    Vehicle->>Vehicle: 立即停车
    Vehicle->>TCP: 停车确认
    TCP->>MQTT: 发布停车状态
    MQTT-->>Web: 紧急停车完成
```

时序图详细展示了从任务下发到执行完成的完整流程，包括：
- **请求处理**: API接收、业务验证、数据库操作
- **消息传递**: Kafka发送、ccserver消费、协议转换
- **设备交互**: TCP连接检查、指令发送、状态反馈
- **实时监控**: 心跳数据、状态推送、前端更新
- **错误处理**: 设备离线、消息格式错误等异常情况

## 2. 关键技术实现

### 2.1 Kafka 配置
```yaml
# 连接配置
kafka_host: "127.0.0.1:9092"
kafka_topic: "device_cmd2"
```

### 2.2 消息格式定义
```go
// 基础消息结构
type KafkaMsg struct {
    Cmd  uint16 `json:"cmd"`   // 命令码
    Imsi string `json:"imsi"`  // 设备标识
}

// 任务消息结构
type Task struct {
    Cmd         uint16       `json:"cmd"`
    Imsi        string       `json:"imsi"`
    RouteId     int          `json:"routeId"`
    TaskId      int          `json:"taskId"`
    ExecutionId int64        `json:"executionId"` // 执行实例ID
    Name        string       `json:"name"`
    Desp        string       `json:"desp"`
    ActionList  []ActionList `json:"actionList"`
}

// 移动指令消息结构
type GoPointMsg struct {
    Cmd  uint16  `json:"cmd"`
    Imsi string  `json:"imsi"`
    Lat  float64 `json:"lat"`
    Lng  float64 `json:"lng"`
    Raw  string  `json:"raw"`
}

// 设置速度消息结构
type SetSpeed struct {
    Cmd   uint16 `json:"cmd"`
    Imsi  string `json:"imsi"`
    Speed int    `json:"speed"`
}

// 设置地图消息结构
type SetMap struct {
    Cmd  uint16 `json:"cmd"`
    Imsi string `json:"imsi"`
    Map  Map    `json:"map"`
}

type Map struct {
    Name string `json:"name"`
}
```

### 2.3 协议命令映射
```go
const (
    StartAutoDriving     = 0x0023  // 开始自动驾驶任务
    EmergencyStop        = 0x0034  // 紧急停车
    StopAuto            = 0x0026  // 停止自动驾驶
    Task                = 0x0018  // 下发任务
    CancelAuto          = 0x0025  // 取消自动驾驶
    PauseAuto           = 0x0024  // 暂停自动驾驶
    GoPoint             = 0x0032  // 移动到指定位置
    SetSpeed            = 0x0040  // 设置最高限速
    SetMap              = 0x0042  // 设置自驾地图
    Reboot              = 0x0010  // 重启
    FindOta             = 0x9003  // 查询新固件版本
    FindVcuOta          = 0xA003  // 查询新VCU固件版本
    UpdateOta           = 0xA001  // 白名单方式OTA升级
    SelectAlarm         = 0x0039  // 查询告警详情
    RelieveEmergencyStop = 0x0035  // 解除紧急停车
    DeviceSync          = 0x0044  // 车端地图同步到云端
    CloudSync           = 0x0046  // 云端地图同步到车端
)
```

## 3. 核心技术架构

### 3.1 消息队列架构
- **Kafka 主题**: `device_cmd2`
- **连接地址**: `127.0.0.1:9092`
- **消息格式**: JSON 统一封装
- **可靠性**: 生产者确认机制

### 3.2 协议转换层
- **输入**: Kafka JSON 消息
- **处理**: 数据解析与重组
- **输出**: 二进制 TCP 协议
- **封装**: MakeCmd 协议包装

## 4. ccserver 消息处理流程

### 4.1 Kafka 消费者实现
```go
// 消费者启动
func kafkaConsumer() {
    brokers := strings.Split("127.0.0.1:9092", ",")
    consumer, err := sarama.NewConsumer(brokers, config)

    // 订阅 device_cmd2 主题
    partitionList, err := consumer.Partitions("device_cmd2")

    for partition := range partitionList {
        pc, err := consumer.ConsumePartition("device_cmd2", int32(partition), sarama.OffsetNewest)
        go func(pc sarama.PartitionConsumer) {
            for msg := range pc.Messages() {
                // 处理消息
                processKafkaMessage(msg)
            }
        }(pc)
    }
}
```

### 4.2 消息处理与协议转换

**重要**: ccserver 不是原封不动转发 Kafka 消息，而是进行重要的协议转换：

#### 4.2.1 消息处理流程
1. **JSON 解析**: 解析 Kafka 的 JSON 消息
2. **数据重组**: 根据命令类型重新组织数据结构
3. **协议封装**: 通过 `MakeCmd()` 转换为二进制 TCP 协议
4. **添加协议头**: 消息头、时间戳、校验和等

```go
func processKafkaMessage(msg *sarama.ConsumerMessage) {
    // 1. 解析 Kafka JSON 消息
    var kafkaMsg KafkaMsg
    json.Unmarshal(msg.Value, &kafkaMsg)

    // 2. 根据命令类型重组数据
    switch kafkaMsg.Cmd {
    case protocol.Task:        // 0x0018 任务下发
        var task Task
        json.Unmarshal(msg.Value, &task)
        data, _ = gjson.Encode(g.Map{
            "cmd":        task.Cmd,
            "routeId":    task.RouteId,
            "taskId":     task.TaskId,
            "actionList": task.ActionList,
        })
    case protocol.GoPoint:     // 0x0032 移动指令
        var goPoint GoPointMsg
        json.Unmarshal(msg.Value, &goPoint)
        data, _ = gjson.Encode(g.Map{
            "cmd": goPoint.Cmd,
            "lat": goPoint.Lat,
            "lng": goPoint.Lng,
            "raw": goPoint.Raw,
        })
    }

    // 3. 封装成二进制协议并发送
    sendData := common.MakeCmd(kafkaMsg.Cmd, 0, data)
    common.SendToDevice(kafkaMsg.Imsi, sendData)
}
```

#### 4.2.2 MakeCmd 协议封装
```go
func MakeCmd(cmd uint16, billNo uint32, msgBody []byte) []byte {
    // 构建二进制协议包含：
    // - 消息头标识: '@>'
    // - 消息长度、流水号
    // - 协议版本号、加密方式
    // - 时间戳、预留字段
    // - JSON消息体
    // - CRC16校验和
    return binaryProtocolData
}
```

### 4.3 协议转换详细流程图

```mermaid
flowchart TD
    A[Kafka消息接收] --> B{消息格式验证}
    B -->|有效| C[JSON解析]
    B -->|无效| Z1[记录错误日志]

    C --> D[提取基础信息]
    D --> E[cmd: 命令码<br/>imsi: 设备ID]

    E --> F{命令类型判断}

    F -->|0x0018| G1[任务下发处理]
    F -->|0x0032| G2[移动指令处理]
    F -->|0x0034| G3[紧急停车处理]
    F -->|0x0040| G4[设置速度处理]
    F -->|0x0042| G5[设置地图处理]
    F -->|其他| G6[通用命令处理]

    G1 --> H1[解析Task结构<br/>提取: taskId, routeId<br/>actionList, name, desp]
    G2 --> H2[解析GoPoint结构<br/>提取: lat, lng, raw]
    G3 --> H3[解析基础结构<br/>仅保留: cmd]
    G4 --> H4[解析SetSpeed结构<br/>提取: speed]
    G5 --> H5[解析SetMap结构<br/>提取: map.name]
    G6 --> H6[解析基础结构<br/>保留: cmd, 其他字段]

    H1 --> I1[重组JSON数据<br/>gjson.Encode]
    H2 --> I1
    H3 --> I1
    H4 --> I1
    H5 --> I1
    H6 --> I1

    I1 --> J[调用MakeCmd函数]
    J --> K[协议封装处理]

    K --> L1[添加消息头<br/>@> 标识符]
    L1 --> L2[设置消息长度<br/>动态计算]
    L2 --> L3[添加流水号<br/>递增计数]
    L3 --> L4[设置消息格式<br/>0x0000: JSON格式]
    L4 --> L5[设置协议版本<br/>0x01: 版本1]
    L5 --> L6[设置加密方式<br/>0x00: 未加密]
    L6 --> L7[添加随机数<br/>预留字段]
    L7 --> L8[添加时间戳<br/>Unix时间戳]
    L8 --> L9[添加预留字段<br/>24字节预留]
    L9 --> L10[附加JSON消息体<br/>重组后的数据]
    L10 --> L11[计算CRC16校验<br/>数据完整性]
    L11 --> M[生成二进制协议包]

    M --> N{设备连接检查}
    N -->|在线| O[发送到TCP连接]
    N -->|离线| Z2[返回连接错误]

    O --> P[设备接收确认]
    P --> Q[状态反馈处理]

    Q --> R1[MQTT状态推送]
    Q --> R2[WebSocket通知]
    Q --> R3[数据库状态更新]

    R1 --> S[前端实时更新]
    R2 --> S
    R3 --> S

    %% 错误处理路径
    Z1 --> END1[处理结束]
    Z2 --> END2[错误处理]
    S --> END3[成功完成]

    %% 样式定义
    classDef processBox fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decisionBox fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataBox fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef protocolBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef errorBox fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef successBox fill:#e8f5e8,stroke:#4caf50,stroke-width:2px

    class A,C,D,J,K,O,P,Q processBox
    class B,F,N decisionBox
    class E,H1,H2,H3,H4,H5,H6,I1 dataBox
    class L1,L2,L3,L4,L5,L6,L7,L8,L9,L10,L11,M protocolBox
    class Z1,Z2,END1,END2 errorBox
    class G1,G2,G3,G4,G5,G6,R1,R2,R3,S,END3 successBox
```

流程图详细展示了ccserver中协议转换的完整过程：

#### 4.3.1 消息处理阶段
1. **Kafka消息接收**: 从device_cmd2主题接收JSON消息
2. **格式验证**: 验证JSON格式的有效性
3. **基础解析**: 提取cmd命令码和imsi设备ID

#### 4.3.2 命令分类处理
根据不同的命令类型进行专门处理：
- **0x0018 任务下发**: 解析完整的任务结构（taskId, routeId, actionList等）
- **0x0032 移动指令**: 解析GPS坐标（lat, lng, raw）
- **0x0034 紧急停车**: 仅保留基础命令信息
- **0x0040 设置速度**: 解析速度参数
- **0x0042 设置地图**: 解析地图名称

#### 4.3.3 协议封装过程
通过MakeCmd函数进行11步协议封装：
1. 添加消息头标识符(@>)
2. 设置动态计算的消息长度
3. 添加递增的流水号
4. 设置JSON格式标识
5. 设置协议版本号
6. 设置加密方式(未加密)
7. 添加随机数字段
8. 添加Unix时间戳
9. 添加24字节预留字段
10. 附加重组后的JSON消息体
11. 计算并添加CRC16校验和

#### 4.3.4 设备交互
- **连接检查**: 验证设备TCP连接状态
- **指令发送**: 通过长连接发送二进制协议包
- **状态反馈**: 多通道实时状态推送

### 4.4 数据流向与状态管理

上面的数据流向图展示了完整的数据处理链路：

#### 4.4.1 数据流向分析
**输入层 → 处理层 → 队列层 → 协议层 → 连接层 → 终端层 → 反馈层 → 存储层 → 展示层**

1. **数据输入**: Web前端、移动端、API调用
2. **ccapiex处理**: HTTP解析、业务验证、状态检查、消息构建
3. **消息队列**: Kafka分区管理、消费者组处理
4. **协议转换**: JSON解析、命令路由、数据重组、协议封装
5. **设备连接**: TCP连接池、状态管理、心跳检测
6. **车辆终端**: 协议解析、指令执行、数据采集
7. **状态反馈**: 心跳数据、MQTT推送、WebSocket通信
8. **数据存储**: MySQL业务数据、TDengine时序数据、Redis缓存
9. **监控展示**: 实时界面、状态展示、告警信息

#### 4.4.2 关键数据流
- **指令下行**: Web → ccapiex → Kafka → ccserver → 车辆
- **状态上行**: 车辆 → ccserver → 存储 → 前端展示
- **实时反馈**: 车辆 → MQTT/WebSocket → 前端更新

### 4.5 协议命令映射
ccapiex 通过 `CmdMap` 数组将 HTTP 参数映射为协议命令码，支持 29 种不同的命令类型，涵盖：
- **基础控制**: 开锁、鸣笛、重启等
- **自动驾驶**: 启动、停止、暂停、取消等
- **任务管理**: 下发任务、移动指令等
- **固件升级**: OTA 升级、VCU 升级等
- **地图管理**: 设置地图、同步地图等

## 5. TCP 连接管理

### 5.1 设备连接维护
ccserver 维护与车辆终端的长连接，通过连接池管理多个设备：
```go
func SendToDevice(deviceId string, data []byte) error {
    if !client.All().Contains(deviceId) {
        return errors.New("终端与服务器的连接已断开")
    }
    client.Get(deviceId).(*vars.Terminal).Prop.Message <- data
    return nil
}
```

### 5.2 连接状态监控
- 心跳检测机制
- 连接超时处理
- 自动重连逻辑

## 6. 系统特性

### 6.1 高可用性
- Kafka 集群部署
- TCP 连接池管理
- 故障自动恢复

### 6.2 实时性
- 异步消息处理
- 长连接维护
- 即时状态反馈

### 6.3 可扩展性
- 微服务架构
- 水平扩展支持
- 统一消息格式

## 7. 部署配置

### 7.1 环境要求
- Kafka 2.8+
- MySQL 8.0+
- Go 1.19+
- Redis 6.0+

### 7.2 配置示例
```ini
# ccapiex 配置
kafka_host = "127.0.0.1:9092"
kafka_topic = "device_cmd2"

# ccserver 配置
kafka_host = "127.0.0.1:9092"
tcp_port = 8080
mqtt_broker = "wss://testpixconsole.pixmoving.city/wss"
```

---

**文档版本**: v3.0
**更新时间**: 2025-06-27
**维护团队**: 系统架构组

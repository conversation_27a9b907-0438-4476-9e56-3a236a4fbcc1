package boot

import (
	"ccserver/app/library/common"
	"ccserver/app/module/mqtt"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"github.com/gogf/gf/encoding/gjson"
	"github.com/gogf/gf/frame/g"
)

type KafkaMsg struct {
	Cmd  uint16 `json:"cmd"`
	Imsi string `json:"imsi"`
}

type GoPointMSg struct {
	Cmd  uint16  `json:"cmd"`
	Imsi string  `json:"imsi"`
	Lat  float64 `json:"lat"`
	Lng  float64 `json:"lng"`
	Raw  string  `json:"raw"`
}

type ActionList struct {
	Action  string `json:"action"`
	Content string `json:"content"`
	PST     int    `json:"pST"`
	PET     int    `json:"pET"`
}

type Task struct {
	Cmd        uint16       `json:"cmd"`
	Imsi       string       `json:"imsi"`
	RouteId    int          `json:"routeId"`
	TaskId     int64        `json:"taskId"` // 执行实例ID（字段名保持兼容，语义已变更）
	Name       string       `json:"name"`
	Desp       string       `json:"desp"`
	ActionList []ActionList `json:"actionList"`
	// ExecutionId 字段已完全移除，功能合并到TaskId
}

type Road struct {
	Cmd     uint16    `json:"cmd"`
	Imsi    string    `json:"imsi"`
	Station []Station `json:"station"`
}

type SetSpeed struct {
	Cmd   uint16 `json:"cmd"`
	Imsi  string `json:"imsi"`
	Speed int    `json:"speed"`
}

type SetMap struct {
	Cmd  uint16 `json:"cmd"`
	Imsi string `json:"imsi"`
	Map  Map    `json:"map"`
}

type Map struct {
	Name string `json:"name"`
}

type Station struct {
	Order   int    `json:"order"`
	Name    string `json:"name"`
	Content string `json:"content"`
}

// AddOrder 制作饮品指令
type AddOrder struct {
	Cmd         int                      `json:"cmd"`
	BillNo      string                   `json:"billNo"`
	BillTime    int64                    `json:"billTime"`
	TaskId      int                      `json:"taskId"`
	ProductId   int                      `json:"productId"`
	ProductType int                      `json:"productType"`
	ProductName string                   `json:"productName"`
	ProductImg  string                   `json:"productImg"`
	Code        string                   `json:"code"`
	ActionList  []map[string]interface{} `json:"actionList"`
}

type DeviceSync struct {
	Cmd       int    `json:"cmd"`
	Name      string `json:"name"`
	UploadUrl string `json:"uploadUrl"`
}

type CloudSync struct {
	Cmd         int    `json:"cmd"`
	Name        string `json:"name"`
	DownloadUrl string `json:"downloadUrl"`
}

type SelectAlarm struct {
	Cmd   int     `json:"cmd"`
	Level [4]int8 `json:"level"`
}

var (
	wg             sync.WaitGroup
	kafkaConnected bool // 添加 Kafka 连接状态标志
)

func kafkaConsumer() {
	// 从配置文件读取Kafka地址
	kafkaBrokers := vars.Config.Middleware.Kafka.Brokers
	brokers := strings.Split(kafkaBrokers, ",")

	// 记录开始连接日志（使用INFO级别）
	if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			logger.Info("Kafka 开始连接: brokers=%v", brokers)
		}
	}

	config := sarama.NewConfig()
	config.Consumer.Return.Errors = true

	consumer, err := sarama.NewConsumer(brokers, config)
	if err != nil {
		pix_log.LogKafkaConnection(brokers, false, fmt.Sprintf("连接失败: %s", err.Error()))
		return
	}
	defer consumer.Close()

	kafkaConnected = true
	pix_log.LogKafkaConnection(brokers, true, "连接建立成功")

	// 从配置文件读取topic
	kafkaTopic := vars.Config.Middleware.Kafka.Topic

	partitionList, err := consumer.Partitions(kafkaTopic)
	if err != nil {
		if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
			if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
				logger.Error("获取 Kafka 分区列表失败: topic=%s, error=%s", kafkaTopic, err.Error())
			}
		}
		kafkaConnected = false
		return
	}
	if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
		if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
			logger.Info("获取到 Kafka 分区数量: topic=%s, partitions=%d", kafkaTopic, len(partitionList))
		}
	}

	for partition := range partitionList {
		pc, err := consumer.ConsumePartition(kafkaTopic, int32(partition), sarama.OffsetNewest)
		if err != nil {
			pix_log.LogKafkaPartition(int32(partition), false, fmt.Sprintf("error: %s", err.Error()))
			continue
		}
		defer pc.AsyncClose()

		pix_log.LogKafkaPartition(int32(partition), true, "消费者启动成功")

		go func(pc sarama.PartitionConsumer) {
			wg.Add(1)
			for msg := range pc.Messages() {
				var kafkaMsg KafkaMsg

				err = json.Unmarshal(msg.Value, &kafkaMsg)
				if err != nil {
					pix_log.LogKafkaMessageError(msg.Topic, msg.Partition, msg.Offset, err.Error(), string(msg.Value))
					continue
				}

				// 记录消息接收日志（带 IMSI）
				pix_log.LogKafkaMessage(kafkaMsg.Imsi, msg.Topic, msg.Partition, msg.Offset, kafkaMsg.Cmd,
					fmt.Sprintf("timestamp=%s, key=%s", msg.Timestamp.Format("2006-01-02 15:04:05"), string(msg.Key)))

				data, _ := gjson.Encode(g.Map{
					"cmd": kafkaMsg.Cmd,
				})
				switch kafkaMsg.Cmd {
				case protocol.GoPoint: // 移动到指定目标点
					var goPointMsg GoPointMSg
					err = json.Unmarshal(msg.Value, &goPointMsg)
					if err != nil {
						pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, false, fmt.Sprintf("GoPoint 消息解析失败: %s", err.Error()))
						continue
					}

					data, _ = gjson.Encode(g.Map{
						"cmd": goPointMsg.Cmd,
						"lat": goPointMsg.Lat,
						"lng": goPointMsg.Lng,
						"raw": goPointMsg.Raw,
					})

					pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, true, fmt.Sprintf("GoPoint 命令处理: lat=%.6f, lng=%.6f", goPointMsg.Lat, goPointMsg.Lng))

				case protocol.Task: // 下发自动驾驶任务
					var task Task
					err = json.Unmarshal(msg.Value, &task)
					if err != nil {
						fmt.Println("json数据解析失败：" + err.Error())
					}

					// 🔄 简化：taskId 直接是执行实例ID，无需executionId字段
					data, _ = gjson.Encode(g.Map{
						"cmd":        task.Cmd,
						"routeId":    task.RouteId,
						"taskId":     task.TaskId, // 直接使用执行实例ID
						"name":       task.Name,
						"desp":       task.Desp,
						"actionList": task.ActionList,
					})

				case 0x0020: // 下发自动驾驶路线站点
					var road Road
					err = json.Unmarshal(msg.Value, &road)
					if err != nil {
						fmt.Println("json数据解析失败：" + err.Error())
					}

					data, _ = gjson.Encode(g.Map{
						"cmd":      road.Cmd,
						"stations": road.Station,
					})

				case 0x0610: // 下发制作饮品
					var test AddOrder
					err = json.Unmarshal(msg.Value, &test)
					if err != nil {
						pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, false, fmt.Sprintf("AddOrder 消息解析失败: %s", err.Error()))
						continue
					}

					data, _ = gjson.Encode(g.Map{
						"cmd":         test.Cmd,
						"billNo":      test.BillNo,
						"billTime":    test.BillTime,
						"taskId":      test.TaskId,
						"productId":   test.ProductId,
						"productType": test.ProductType,
						"productName": test.ProductName,
						"productImg":  test.ProductImg,
						"code":        test.Code,
						"actionList":  test.ActionList,
					})

					pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, true, fmt.Sprintf("AddOrder 命令处理: billNo=%s, productName=%s", test.BillNo, test.ProductName))

				case protocol.SetSpeed:
					var setSpeed SetSpeed
					err = json.Unmarshal(msg.Value, &setSpeed)
					if err != nil {
						pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, false, fmt.Sprintf("SetSpeed 消息解析失败: %s", err.Error()))
						continue
					}
					data, _ = gjson.Encode(g.Map{
						"cmd":   setSpeed.Cmd,
						"speed": setSpeed.Speed,
					})

					pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, true, fmt.Sprintf("SetSpeed 命令处理: speed=%.2f", setSpeed.Speed))

				case protocol.SetMap:
					var setMap SetMap
					err = json.Unmarshal(msg.Value, &setMap)
					if err != nil {
						pix_log.Error("json数据解析失败：" + err.Error())
					}
					data, _ = gjson.Encode(g.Map{
						"cmd": setMap.Cmd,
						"map": setMap.Map,
					})

				case protocol.DeviceSync:
					var deviceSync DeviceSync
					err = json.Unmarshal(msg.Value, &deviceSync)
					if err != nil {
						pix_log.Error("json数据解析失败：" + err.Error())
					}
					data, _ = gjson.Encode(g.Map{
						"cmd": deviceSync.Cmd,
						"map": map[string]interface{}{
							"name":      deviceSync.Name,
							"uploadUrl": deviceSync.UploadUrl,
						},
					})

				case protocol.CloudSync:
					var cloudSync CloudSync
					err = json.Unmarshal(msg.Value, &cloudSync)
					if err != nil {
						pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, false, fmt.Sprintf("CloudSync 消息解析失败: %s", err.Error()))
						continue
					}
					data, _ = gjson.Encode(g.Map{
						"cmd": cloudSync.Cmd,
						"map": map[string]interface{}{
							"name":        cloudSync.Name,
							"downloadUrl": cloudSync.DownloadUrl,
						},
					})

					pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, true, fmt.Sprintf("CloudSync 命令处理: name=%s, url=%s", cloudSync.Name, cloudSync.DownloadUrl))

				case protocol.SelectAlarm:
					var selectAlarm SelectAlarm
					err = json.Unmarshal(msg.Value, &selectAlarm)
					if err != nil {
						pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, false, fmt.Sprintf("SelectAlarm 消息解析失败: %s", err.Error()))
						continue
					}
					data, _ = gjson.Encode(g.Map{
						"cmd":   selectAlarm.Cmd,
						"level": selectAlarm.Level,
					})

					pix_log.LogKafkaCommand(kafkaMsg.Imsi, kafkaMsg.Cmd, true, fmt.Sprintf("SelectAlarm 命令处理: level=%d", selectAlarm.Level))
				}

				sendData := common.MakeCmd(kafkaMsg.Cmd, 0, data)

				// 格式化 JSON 用于日志记录
				var jsonData interface{}
				var prettyData []byte
				if err := json.Unmarshal(data, &jsonData); err != nil {
					if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
						if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
							logger.ErrorWithIMSI(kafkaMsg.Imsi, "Kafka 命令数据解析失败: cmd=0x%04X, error=%s", kafkaMsg.Cmd, err.Error())
						}
					}
					continue
				}
				prettyData, _ = json.MarshalIndent(jsonData, "", "  ")

				// 记录命令下发内容
				if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
					if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
						logger.InfoWithIMSI(kafkaMsg.Imsi, "Kafka 命令下发内容: cmd=0x%04X, data=%s", kafkaMsg.Cmd, string(prettyData))
					}
				}

				code := 0
				result := ""
				if err = common.SendToDevice(kafkaMsg.Imsi, sendData); err != nil {
					result = fmt.Sprintf("发送命令到终端失败: %s", err.Error())
					pix_log.LogKafkaDeviceSend(kafkaMsg.Imsi, kafkaMsg.Cmd, false, result)
					code = 1
				} else {
					pix_log.LogKafkaDeviceSend(kafkaMsg.Imsi, kafkaMsg.Cmd, true, "指令下发成功")
				}

				// 🆕 直接使用MQTT配置
				topic := fmt.Sprintf("%s/cmd/device",
					vars.Config.Server.Mqtt.TopicPrefix,
				)

				body := map[string]interface{}{
					"code": code,
					"imsi": kafkaMsg.Imsi,
					"cmd":  kafkaMsg.Cmd,
					"msg":  result,
				}

				if mqtt.SharedClient.IsConnected() {
					returnData, _ := json.Marshal(body)
					token := mqtt.SharedClient.Publish(topic, 0, false, returnData)
					token.Wait() // wait是个阻塞函数，只有操作完成时才能返回
					// token.WaitTimeout(15 * time.Second)
					if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
						if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
							if token.Error() != nil {
								logger.ErrorWithIMSI(kafkaMsg.Imsi, "Kafka 结果 MQTT 发布失败: topic=%s, error=%s", topic, token.Error().Error())
							} else {
								logger.InfoWithIMSI(kafkaMsg.Imsi, "Kafka 结果 MQTT 发布成功: topic=%s, code=%d", topic, code)
							}
						}
					}
				} else {
					if globalManager := pix_log.GetGlobalManager(); globalManager != nil {
						if logger := globalManager.GetModuleLogger("kafka"); logger != nil {
							logger.WarningWithIMSI(kafkaMsg.Imsi, "Kafka 结果发布跳过: MQTT 未连接, topic=%s", topic)
						}
					}
				}
			}
			wg.Done()
		}(pc)
	}
	time.Sleep(10 * time.Second)
	wg.Wait()
}

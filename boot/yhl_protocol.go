package boot

import (
	"bytes"
	"ccserver/app/library/common"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"crypto/cipher"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/tjfoc/gmsm/sm4"
)

const (
	YHL_MSG_HEADER_LEN = 46 // 不含标识头2字节的消息头长度(长度 + 流水号 + 消息体格式 + 协议版本号 + 是否加密 + 随机数 + 时间戳 + 预留)

	// 协议版本常量
	PROTOCOL_VERSION_V1 = 0x01 // 明文协议
	PROTOCOL_VERSION_V2 = 0x02 // 加密协议

	// 加密方式常量
	ENCRYPTION_NONE    = 0x00 // 未加密
	ENCRYPTION_SM4_GCM = 0x01 // SM4-GCM加密

	// GCM参数
	GCM_NONCE_SIZE = 12 // GCM Nonce长度
	GCM_TAG_SIZE   = 16 // GCM认证标签长度
)

const (
	YHL_MSG_PARSED_STATE_IDLE       = 0
	YHL_MSG_PARSED_STATE_START_CHAR = 1
)

const (
	YHL_MSG_START_CHAR1      = '@'
	YHL_MSG_START_CHAR2      = '<'
	YHL_MSG_RESP_START_CHAR2 = '>'
)

const (
	NORMAL_RESP = `{
	"cmd": %d,
	"res": "%d",
	"bn": "%d"
}`
)

type YhlProtocol struct {
	parsedState uint8
	dataBuf     []byte
	sm4Key      []byte // SM4-GCM解密密钥
}

// SM4GCMDecryptor SM4-GCM解密器
type SM4GCMDecryptor struct {
	key []byte
}

// NewSM4GCMDecryptor 创建SM4-GCM解密器
func NewSM4GCMDecryptor(key []byte) (*SM4GCMDecryptor, error) {
	if len(key) != 16 {
		return nil, fmt.Errorf("SM4密钥长度必须为16字节，当前长度: %d", len(key))
	}
	return &SM4GCMDecryptor{key: key}, nil
}

// Decrypt SM4-GCM解密
func (d *SM4GCMDecryptor) Decrypt(ciphertext, authTag, nonce []byte) ([]byte, error) {
	if len(nonce) != GCM_NONCE_SIZE {
		return nil, fmt.Errorf("Nonce长度必须为%d字节", GCM_NONCE_SIZE)
	}

	if len(authTag) != GCM_TAG_SIZE {
		return nil, fmt.Errorf("认证标签长度必须为%d字节", GCM_TAG_SIZE)
	}

	// 创建SM4-GCM解密器
	block, err := sm4.NewCipher(d.key)
	if err != nil {
		return nil, fmt.Errorf("创建SM4密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 重新组合密文和认证标签
	fullCiphertext := append(ciphertext, authTag...)

	// 解密并验证认证标签
	plaintext, err := gcm.Open(nil, nonce, fullCiphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密或认证失败: %v", err)
	}

	return plaintext, nil
}

// initSM4Key 初始化SM4密钥（增强错误处理）
func (t *YhlProtocol) initSM4Key() error {
	// 检查是否启用加密
	if !vars.Config.Encryption.SM4.Enabled {
		pix_log.Warning("⚠️ SM4加密未启用，将拒绝所有加密消息")
		return fmt.Errorf("SM4加密功能未启用")
	}

	// 从配置中获取密钥
	keyHex := vars.Config.Encryption.SM4.Key
	if keyHex == "" {
		// 使用默认密钥（仅开发环境）
		t.sm4Key = []byte("1234567890123456") // 16字节默认密钥
		pix_log.Warning("⚠️ 配置中未设置SM4密钥，使用默认密钥（仅用于开发测试，生产环境必须配置专用密钥）")
		return nil
	}

	// 验证hex格式
	if len(keyHex)%2 != 0 {
		return fmt.Errorf("SM4密钥hex格式错误: 长度必须为偶数，当前长度=%d", len(keyHex))
	}

	// 解码hex密钥
	key, err := hex.DecodeString(keyHex)
	if err != nil {
		return fmt.Errorf("SM4密钥hex解码失败: %v (请检查密钥格式是否为有效的十六进制字符串)", err)
	}

	// 验证密钥长度
	if len(key) != 16 {
		return fmt.Errorf("SM4密钥长度错误: 必须为16字节，当前长度=%d字节 (hex长度应为32字符)", len(key))
	}

	t.sm4Key = key
	pix_log.Info(fmt.Sprintf("✅ SM4-GCM加密已启用，密钥加载成功 (密钥长度: %d字节)", len(key)))
	return nil
}

// decryptMessage 智能解密消息（支持任意协议版本的加密消息）
func (t *YhlProtocol) decryptMessage(msgBody []byte, msgHeader YhlProtocolMsgHeader, term *vars.Terminal) ([]byte, error) {
	// 验证加密类型
	if msgHeader.encryptedType != ENCRYPTION_SM4_GCM {
		return nil, fmt.Errorf("不支持的加密类型: 0x%02x", msgHeader.encryptedType)
	}

	// 检查加密必需的字段
	if msgHeader.nonce == nil || msgHeader.authTag == nil {
		return nil, fmt.Errorf("加密消息缺少Nonce或认证标签")
	}

	if len(msgHeader.nonce) != GCM_NONCE_SIZE {
		return nil, fmt.Errorf("Nonce长度错误: 期望%d字节, 实际%d字节", GCM_NONCE_SIZE, len(msgHeader.nonce))
	}

	if len(msgHeader.authTag) != GCM_TAG_SIZE {
		return nil, fmt.Errorf("认证标签长度错误: 期望%d字节, 实际%d字节", GCM_TAG_SIZE, len(msgHeader.authTag))
	}

	pix_log.InfoWithIMSI(term.Prop.IMSI, "🔑 [智能解密] 开始解密：协议版本=0x%02x, Nonce=%x, AuthTag=%x",
		msgHeader.protocolVer, msgHeader.nonce, msgHeader.authTag)

	// 获取SM4密钥
	if t.sm4Key == nil {
		if err := t.initSM4Key(); err != nil {
			return nil, fmt.Errorf("初始化SM4密钥失败: %v", err)
		}
	}

	// 创建解密器
	decryptor, err := NewSM4GCMDecryptor(t.sm4Key)
	if err != nil {
		return nil, fmt.Errorf("创建解密器失败: %v", err)
	}

	// 执行解密
	plaintext, err := decryptor.Decrypt(msgBody, msgHeader.authTag, msgHeader.nonce)
	if err != nil {
		return nil, fmt.Errorf("SM4-GCM解密失败: %v", err)
	}

	pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [智能解密] 解密成功：原始长度=%d, 解密后长度=%d",
		len(msgBody), len(plaintext))

	return plaintext, nil
}

type YhlProtocolMsgHeader struct {
	msgLen        uint16
	billNo        uint32
	msgFormat     uint16
	protocolVer   uint8
	encryptedType uint8
	encryptedRand uint32
	ts            uint64 // timestamp
	reserved      [24]byte

	// V2协议专用字段
	nonce   []byte // 12字节Nonce（仅V2协议）
	authTag []byte // 16字节认证标签（仅V2协议）
}

var billNo uint32

func makeRespRegisterMsgBody(res int, billNo uint32) []byte {
	msg := fmt.Sprintf(NORMAL_RESP, protocol.Register, res, billNo)
	return []byte(msg)
}

// MakeRespMsg 当msgBody的长度不为0时，使用msgBody直接作为消息体而不用动态构建
func MakeRespMsg(cmd uint16, msgHeader YhlProtocolMsgHeader, res int, msgBody []byte) []byte {
	var outLen uint16 = 0
	var idx uint16 = 0

	var tempBuff []byte = make([]byte, 2056, 2056)

	tempBuff[outLen] = YHL_MSG_START_CHAR1
	outLen++

	tempBuff[outLen] = YHL_MSG_RESP_START_CHAR2
	outLen++

	// 长度
	tempBuff[outLen] = byte(0>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(0) & 0xff
	outLen++

	// 流水号从0开始递增，为发送信息的流水号，用于接收方检测是否有信息的丢失。程序开始运行时等于零，发送第一各消息时开始计数，到最大数后自动归零
	var billNo uint32 = msgHeader.billNo
	tempBuff[outLen] = byte(billNo>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(billNo) & 0xff
	outLen++
	billNo++

	// 消息体格式 0x0000: 消息体为UTF8编码的JSON格式的数据，或JSON格式的数据经过加密后得到的数据  0x0001: 消息体为字节序列，即任意数据
	tempBuff[outLen] = 0
	outLen++
	tempBuff[outLen] = 0
	outLen++

	// 协议版本号; 0x01: 表示版本1，0x02: 表示版本2，以此类推
	tempBuff[outLen] = 0x01
	outLen++

	// 加密方式; 0x00: 表示未加密 0x01:表示用国密SM4 128位加密
	tempBuff[outLen] = 0
	outLen++

	// 随机数
	tempBuff[outLen] = byte(0>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(0>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(0>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(0 & 0xff)
	outLen++

	// 时间戳
	ts := time.Now().Unix()
	tempBuff[outLen] = byte(ts>>56) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>48) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>40) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>32) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>24) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>16) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(ts & 0xff)
	outLen++

	// 预留
	for idx = 0; idx < 24; idx++ {
		tempBuff[outLen] = byte(0 & 0xff)
		outLen++
	}

	var createdMsgBody []byte

	if len(msgBody) != 0 {
		createdMsgBody = msgBody
	} else {
		switch cmd {
		case protocol.Register:
			createdMsgBody = makeRespRegisterMsgBody(res, msgHeader.billNo)
			break
		case protocol.Heartbeat:
			break
		}
	}
	for idx := 0; idx < len(createdMsgBody); idx++ {
		tempBuff[outLen] = createdMsgBody[idx]
		outLen++
	}

	// 长度
	tempBuff[2] = byte((outLen-2-2+2)>>8) & 0xff // 减去头@< 并加上两字节的crc16校验
	tempBuff[3] = byte(outLen-2-2+2) & 0xff

	// crc16校验和
	checksum := common.Crc16CheckSum(tempBuff[2:], uint32(outLen-2))
	tempBuff[outLen] = byte(checksum>>8) & 0xff
	outLen++
	tempBuff[outLen] = byte(checksum) & 0xff
	outLen++

	return tempBuff[0:outLen]
}

// func (t *YhlProtocol) parseMsgBody(msgBody []byte, msgHeader YhlProtocolMsgHeader, term *vars.Terminal, ts []byte) (bool, error) {
// 	parseDeviceData(msgBody, msgHeader, term, ts)
//
// 	/*
// 		j, err := gjson.DecodeToJson(msgBody)
// 		if err != nil {
// 			v.LogTcp().Errorf("[Parser]JSON数据解析出错\nerr:%s, term:%x, data str:%s, data:%v", term, err, string(msgBody), msgBody)
// 			return false, err
// 		}
// 		cmd := j.GetInt64("cmd")
// 		v.LogTcp().Printf("got cmd:%d", cmd)
// 	*/
//
// 	return true, nil
// }

// HandleRecv 解析协议并处理
func (t *YhlProtocol) HandleRecv(data []byte, term *vars.Terminal) {
	// 🆕 添加协议解析层入口调试日志
	pix_log.InfoWithIMSI(term.Prop.IMSI, "📡 [HandleRecv] 接收原始数据：数据长度=%d, 连接状态=%t",
		len(data), term.IsConnected)

	t.dataBuf = append(t.dataBuf, data...)
	var msgHeader YhlProtocolMsgHeader

	for {
		if t.parsedState == YHL_MSG_PARSED_STATE_IDLE {
			dataLen := len(t.dataBuf)
			isFoundStartChar := false

			if dataLen > YHL_MSG_HEADER_LEN {
				for idx := 0; idx < (dataLen - 1); idx++ {
					if t.dataBuf[idx] == YHL_MSG_START_CHAR1 && t.dataBuf[idx+1] == YHL_MSG_START_CHAR2 {
						t.parsedState = YHL_MSG_PARSED_STATE_START_CHAR
						t.dataBuf = t.dataBuf[idx+2:] // 去除消息头标识@<

						err := binary.Read(bytes.NewReader(t.dataBuf[2:6]), binary.BigEndian, &(msgHeader.billNo))
						if err != nil {
							pix_log.Error("billNo error:", err.Error())
						}

						err = binary.Read(bytes.NewReader(t.dataBuf[6:8]), binary.BigEndian, &(msgHeader.msgFormat))
						if err != nil {
							pix_log.Error("msgFormat error:", err.Error())
						}

						err = binary.Read(bytes.NewReader(t.dataBuf[8:9]), binary.BigEndian, &(msgHeader.protocolVer))
						if err != nil {
							pix_log.Error("protocolVer error :", err.Error())
						}

						err = binary.Read(bytes.NewReader(t.dataBuf[9:10]), binary.BigEndian, &(msgHeader.encryptedType))
						if err != nil {
							pix_log.Error("encryptedType error:", err.Error())
						}

						err = binary.Read(bytes.NewReader(t.dataBuf[10:14]), binary.BigEndian, &(msgHeader.encryptedRand))
						if err != nil {
							pix_log.Error("encryptedRand error:", err.Error())
						}

						err = binary.Read(bytes.NewReader(t.dataBuf[14:22]), binary.BigEndian, &(msgHeader.ts))
						if err != nil {
							pix_log.Error("ts error:", err.Error())
						}

						// 🚀 智能加密检测：以加密字段为主要判断依据
						if msgHeader.encryptedType == ENCRYPTION_SM4_GCM {
							// 检测到加密消息：解析Nonce和认证标签（支持任意协议版本）
							msgHeader.nonce = make([]byte, GCM_NONCE_SIZE)
							msgHeader.authTag = make([]byte, GCM_TAG_SIZE)

							// Nonce前4字节来自随机数字段
							binary.BigEndian.PutUint32(msgHeader.nonce[0:4], msgHeader.encryptedRand)

							// Nonce后8字节来自预留字段前8字节
							copy(msgHeader.nonce[4:12], t.dataBuf[22:30])

							// 认证标签来自预留字段后16字节
							copy(msgHeader.authTag, t.dataBuf[30:46])

							pix_log.InfoWithIMSI(term.Prop.IMSI, "🔐 [智能检测] 检测到加密消息：协议版本=0x%02x, Nonce=%x, AuthTag=%x",
								msgHeader.protocolVer, msgHeader.nonce, msgHeader.authTag)
						} else if msgHeader.encryptedType == ENCRYPTION_NONE {
							// 检测到明文消息：读取预留字段（支持任意协议版本）
							copy(msgHeader.reserved[:], t.dataBuf[22:46])
							pix_log.InfoWithIMSI(term.Prop.IMSI, "🔓 [智能检测] 检测到明文消息：协议版本=0x%02x",
								msgHeader.protocolVer)
						} else {
							// 未知加密类型
							pix_log.WarningWithIMSI(term.Prop.IMSI, "⚠️ [智能检测] 未知加密类型：协议版本=0x%02x, 加密类型=0x%02x",
								msgHeader.protocolVer, msgHeader.encryptedType)
							copy(msgHeader.reserved[:], t.dataBuf[22:46]) // 默认按明文处理
						}

						// 🎯 智能协议验证：支持灵活的协议版本和加密组合
						if msgHeader.msgFormat != 0x0000 {
							pix_log.WarningWithIMSI(term.Prop.IMSI, "❌ 不支持的消息体格式: 0x%04x", msgHeader.msgFormat)
							t.parsedState = YHL_MSG_PARSED_STATE_IDLE
						} else if msgHeader.encryptedType == ENCRYPTION_NONE {
							// 明文消息：支持任意协议版本
							isFoundStartChar = true
							pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [协议验证] 接受明文消息：协议版本=0x%02x", msgHeader.protocolVer)
						} else if msgHeader.encryptedType == ENCRYPTION_SM4_GCM {
							// 加密消息：支持任意协议版本
							isFoundStartChar = true
							pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [协议验证] 接受加密消息：协议版本=0x%02x", msgHeader.protocolVer)
						} else {
							// 未知加密类型：拒绝处理
							pix_log.WarningWithIMSI(term.Prop.IMSI, "❌ [协议验证] 不支持的加密方式: 协议版本=0x%02x, 加密类型=0x%02x",
								msgHeader.protocolVer, msgHeader.encryptedType)
							t.parsedState = YHL_MSG_PARSED_STATE_IDLE
						}
						break
					}
				}

				if !isFoundStartChar {
					pix_log.Info("not found YHL_MSG_PARSED_STATE_START_CHAR")
					break
				}
			} else {
				break
			}
		} else if t.parsedState == YHL_MSG_PARSED_STATE_START_CHAR {
			dataLen := len(t.dataBuf) // 此时的dataBuf已除去消息标识头@<

			var msgLen uint16

			binary.Read(bytes.NewReader(t.dataBuf[0:2]), binary.BigEndian, &msgLen)

			if dataLen >= (int)(msgLen) {
				var checkSum uint16
				binary.Read(bytes.NewReader(t.dataBuf[msgLen:msgLen+2]), binary.BigEndian, &checkSum)

				computedCheckSum := common.Crc16CheckSum(t.dataBuf[0:msgLen], (uint32)(msgLen))

				if computedCheckSum == checkSum {
					msgHeader.msgLen = msgLen

					// 🆕 添加协议解析成功后的调试日志
					msgBodyLen := msgLen - YHL_MSG_HEADER_LEN
					pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [HandleRecv] 协议解析成功：消息长度=%d, 消息体长度=%d, 流水号=%d",
						msgLen, msgBodyLen, msgHeader.billNo)

					// 获取原始消息体
					msgBody := t.dataBuf[YHL_MSG_HEADER_LEN:msgLen]

					// 🔄 智能消息体处理：基于加密类型动态处理
					var finalMsgBody []byte
					if msgHeader.encryptedType == ENCRYPTION_SM4_GCM {
						// 加密消息：需要解密（支持任意协议版本）
						decryptedBody, err := t.decryptMessage(msgBody, msgHeader, term)
						if err != nil {
							pix_log.ErrorWithIMSI(term.Prop.IMSI, "❌ [HandleRecv] 加密消息解密失败：协议版本=0x%02x, 错误=%v",
								msgHeader.protocolVer, err)
							t.parsedState = YHL_MSG_PARSED_STATE_IDLE
							t.dataBuf = t.dataBuf[msgLen+2:]
							continue
						}
						finalMsgBody = decryptedBody
						pix_log.InfoWithIMSI(term.Prop.IMSI, "🔓 [HandleRecv] 加密消息解密成功：协议版本=0x%02x, 解密后长度=%d",
							msgHeader.protocolVer, len(finalMsgBody))
					} else {
						// 明文消息：直接使用（支持任意协议版本）
						finalMsgBody = msgBody
						pix_log.InfoWithIMSI(term.Prop.IMSI, "📄 [HandleRecv] 明文消息：协议版本=0x%02x, 长度=%d",
							msgHeader.protocolVer, len(finalMsgBody))
					}

					// 🆕 添加JSON数据预览
					previewLen := len(finalMsgBody)
					if previewLen > 100 {
						previewLen = 100
					}
					pix_log.InfoWithIMSI(term.Prop.IMSI, "📄 [HandleRecv] 最终消息体预览：前%d字符=%s",
						previewLen, string(finalMsgBody[:previewLen]))

					// 🆕 添加调用parseDeviceData前的日志
					pix_log.InfoWithIMSI(term.Prop.IMSI, "🔄 [HandleRecv] 调用parseDeviceData")

					parseDeviceData(finalMsgBody, msgHeader, term, t.dataBuf[14:22])

					// 🆕 添加调用parseDeviceData后的日志
					pix_log.InfoWithIMSI(term.Prop.IMSI, "✅ [HandleRecv] parseDeviceData调用完成")

					// if !ok {
					// 	// v.LogTcp().Warningf("解析msg body失败, err:%d", err)
					// 	pix_log.Error("msg body parse error , err:%d", err.Error())
					// }

					t.dataBuf = t.dataBuf[msgLen:]
					t.parsedState = YHL_MSG_PARSED_STATE_IDLE
				} else {
					// v.LogTcp().Debugf("校验和不对, computed:%d, needed:%d", computedCheckSum, checkSum)
					pix_log.ErrorWithIMSI(term.Prop.IMSI, "crc check error: need=%d, receive=%d", computedCheckSum, checkSum)
					t.parsedState = YHL_MSG_PARSED_STATE_IDLE
					break
				}
			} else {
				t.parsedState = YHL_MSG_PARSED_STATE_IDLE
				break
			}
		} else {
			break
		}
	}
}

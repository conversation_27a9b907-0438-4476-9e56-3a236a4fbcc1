package boot

import (
	"bytes"
	"ccserver/app/library/common"
	"ccserver/app/library/v"
	"ccserver/app/system"
	"ccserver/app/vars"
	"ccserver/app/vars/protocol"
	"ccserver/pix_log"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
)

// BytesToInt 字节转换成整形
func BytesToInt(b []byte) int64 {
	bytesBuffer := bytes.NewBuffer(b)

	var x int64
	err := binary.Read(bytesBuffer, binary.BigEndian, &x)
	if err != nil {
		fmt.Println(err.Error())
		return 0
	}
	return x
}

// parseDeviceData 解析设备数据并执行相应操作
// 该方法接收设备数据、消息头、终端信息和时间戳，解析数据并根据不同的操作类型执行相应的逻辑。
// func parseDeviceData(data []byte, msgHeader YhlProtocolMsgHeader, term *vars.Terminal, ts []byte) {
// 	// 检查终端是否已连接
// 	if !term.IsConnected {
// 		// 如果终端未连接，直接返回
// 		return
// 	}
//
// 	// 将时间戳字节数组转换为整型
// 	timestamp := BytesToInt(ts)
//
// 	j, err := gjson.DecodeToJson(data)
// 	if err != nil {
// 		// 如果解析失败，记录错误日志并返回
// 		pix_log.Error("json解析失败, data/err:", string(data), err.Error())
// 		return
// 	}
//
// 	// 初始化需要回复的标识和回复内容
// 	var isNeedReply bool
// 	var resp []byte
// 	var res int
//
// 	// 获取操作 ID
// 	operate := j.GetInt64("cmd") // 操作 ID
// 	switch operate {
// 	// 注册操作
// 	case protocol.Register:
// 		term.Mutex.Lock()
// 		defer term.Mutex.Unlock()
// 		if !term.IsConnected {
// 			return
// 		}
// 		isNeedReply = true
// 		term.Prop.IMEI = j.GetString("imei")
// 		term.Prop.IMSI = j.GetString("imsi")
// 		// 用Register 结构体，然后调用用Register回复消息
// 		app := system.NewRegister(j, term, data)
// 		// registerInfo, _ := j.ToJsonString()
// 		// pix_log.Info("收到 %s 注册消息：", term.Prop.IMSI)
// 		resp, err = app.GetReply(msgHeader.billNo)
//
// 	// 心跳操作
// 	case protocol.Heartbeat, protocol.QueryHeartbeatCb:
// 		err, num := common.CheckKey()
// 		if err != nil || num == 0 {
// 			pix_log.Error("证书已过期，", err.Error())
// 			pid := os.Getpid()
// 			cmdStr := fmt.Sprintf("kill -9 %d", pid)
// 			cmd := exec.Command("/bin/bash", "-c", cmdStr)
// 			if err := cmd.Start(); err != nil {
// 				pix_log.Error("Linux命令执行失败，请检查命令输入是否有误", err.Error())
// 			}
// 			return
// 		} else {
// 			if operate == protocol.Heartbeat {
// 				isNeedReply = true
// 			} else {
// 				isNeedReply = false
// 			}
// 			// 用Heartbeat 结构体，然后调用用Heartbeat 回复消息
// 			app := system.NewHeartbeat(j, term, data, timestamp)
// 			// pix_log.Info("收到 %s 心跳消息", term.Prop.IMSI)
// 			resp, err = app.GetReply(msgHeader.billNo)
// 		}
//
// 	// 终端上载信息
// 	case protocol.CbAddrGet, protocol.CbMonitoringGet, protocol.CbUnmpGet, protocol.CbRs232Get, protocol.CbRs485Get, protocol.CbTzGet, protocol.CbLanGet, protocol.CbAccountGet, protocol.CbHeartbeatGet:
// 		app := system.NewReceiveDeviceGetInfo(j, term, data)
// 		app.DoReply()
//
// 	// 操作终端反馈
// 	case protocol.CbAddrSet, protocol.CbMonitoringSet, protocol.CbUnmpSet, protocol.CbRs232Set, protocol.CbRs485Set, protocol.CbTzSet, protocol.CbLanSet, protocol.CbAccountSet, protocol.CbHeartbeatSet:
// 		app := system.NewReceiveDeviceSetInfo(j, term, data)
// 		app.DoReply()
//
// 	// 制作饮品收到回复
// 	case protocol.MakeOrder:
// 		orderNo := j.GetString("billNo")
// 		taskId := j.GetInt("taskId")
// 		system.CheckOrder(orderNo, taskId)
//
// 	// 订单状态同步
// 	case protocol.CheckOrder:
// 		orders := j.GetArray("orders")
// 		system.SyncOrder(orders)
// 		pix_log.Info("pad order:", orders)
//
// 	// 选择地图
// 	case protocol.SelectMap:
// 		mapList := j.GetArray("mapList")
// 		system.SelectMap(term, mapList)
//
// 	// 设备同步信息
// 	case protocol.DeviceSyncInfo, protocol.CloudSyncInfo:
// 		mapInfo := j.GetJson("map")
// 		system.SelectMapInfo(term, mapInfo)
//
// 	// 设置地图
// 	case protocol.SetMap:
// 		system.SetMapInfo(term)
//
// 	// 选择警报
// 	case protocol.SelectAlarm:
// 		alarmList := j.GetArray("alarmList")
// 		str, _ := j.ToJsonString()
// 		fmt.Println("get alarmList:", str)
// 		system.SelectAlarm(term, alarmList)
//
// 	// 开中控锁
// 	case protocol.Unlock, protocol.Lock, protocol.FindCar, protocol.StartCar, protocol.StopCar:
// 		thisRes := j.GetInt("res")
// 		thisBn := j.GetInt("bn")
// 		system.Replay(term, operate, thisRes, thisBn)
// 	}
//
// 	// 如果不需要回复或回复内容为空，直接返回
// 	if !isNeedReply || resp == nil {
// 		return
// 	}
//
// 	// 如果构造应答消息过程中出现错误，记录错误日志并返回
// 	if err != nil {
// 		v.LogTcp().Errorf("[ERROR]构造应答消息过程中出现错误\nIMSI:%s\n%s\n", term.Prop.IMSI, err)
// 		return
// 	}
//
// 	// 构造回复消息并发送到终端的消息通道
// 	respMsg := MakeRespMsg(uint16(operate), msgHeader, res, resp)
// 	term.Prop.Msg = string(resp)
// 	term.Prop.Message <- respMsg
// }

// parseDeviceData 解析设备数据并执行相应操作
func parseDeviceData(data []byte, msgHeader YhlProtocolMsgHeader, term *vars.Terminal, ts []byte) {
	// 🆕 添加parseDeviceData入口调试日志
	pix_log.InfoWithIMSI(term.Prop.IMSI, "🔍 [parseDeviceData] 开始解析设备数据：数据长度=%d, 连接状态=%t, 授权状态=%t",
		len(data), term.IsConnected, term.Prop.Authorized)

	// 检查终端是否已连接
	if !term.IsConnected {
		pix_log.WarningWithIMSI(term.Prop.IMSI, "❌ [parseDeviceData] 设备未连接，跳过处理")
		return
	}

	// 将时间戳字节数组转换为整型
	timestamp := BytesToInt(ts)

	var prettyJSON bytes.Buffer
	indentErr := json.Indent(&prettyJSON, data, "", "    ")
	if indentErr == nil {
		// if strings.Contains(term.Prop.IMSI, "MS") {
		pix_log.InfoWithIMSI(term.Prop.IMSI, "\n\n原始设备数据解析\n:%s", prettyJSON.String())
		// }
	} else {
		pix_log.InfoWithIMSI(term.Prop.IMSI, "\n\n原始设备数据解析失败\n:%s", indentErr.Error())
	}

	// 🆕 添加JSON解析前的调试日志
	pix_log.InfoWithIMSI(term.Prop.IMSI, "📝 [parseDeviceData] 开始JSON解析：连接IP=%s", term.Prop.IP)

	// 直接解析为 HeartbeatDevice 结构体
	var device protocol.HeartbeatDevice
	if err := json.Unmarshal(data, &device); err != nil {
		// 🆕 添加JSON解析失败的详细日志
		pix_log.ErrorWithIMSI(term.Prop.IMSI, "❌ [parseDeviceData] JSON解析失败：错误=%v", err)
		// 先尝试解析到临时map，看看alarmList字段的内容
		var tempMap map[string]interface{}
		if tempErr := json.Unmarshal(data, &tempMap); tempErr == nil {
			if alarmList, exists := tempMap["alarmList"]; exists {
				fmt.Printf("原始alarmList数据: %+v", alarmList)
			}
		}

		// 检查HeartbeatBase是否能正确解析
		var baseDevice protocol.HeartbeatBase
		if baseErr := json.Unmarshal(data, &baseDevice); baseErr == nil {
			fmt.Printf("HeartbeatBase解析结果: %+v", baseDevice)
		}

		// 原有的错误处理代码...
		if indentErr := json.Indent(&prettyJSON, data, "", "    "); indentErr == nil {
			fmt.Printf("设备数据解析失败:\n原始数据:\n%s\n错误信息: %s",
				prettyJSON.String(),
				err.Error())
		}
		return
	}

	// 初始化需要回复的标识和回复内容
	var isNeedReply bool
	var resp []byte
	var res int
	var err error

	// 如果不是注册消息，那么设备标识从【终端维护结构体】中获取
	if term.Prop.IMEI != "" && term.Prop.IMSI != "" && device.IMEI == "" || device.IMSI == "" {
		device.IMSI = term.Prop.IMSI
		device.IMEI = term.Prop.IMSI
	}

	// if time.Now().Second() == 1 && device.Cmd == 2 {
	// 将 JSON 数据格式化
	// var prettyJSON bytes.Buffer
	// if err := json.Indent(&prettyJSON, data, "", "    "); err == nil {
	// 	pix_log.Info("收到设备数据:\n%s", prettyJSON.String())
	// }
	// }

	// 🆕 添加JSON解析成功后的调试日志
	pix_log.InfoWithIMSI(device.IMSI, "✅ [parseDeviceData] JSON解析成功：Cmd=%d, Step=%d, TaskId=%d",
		device.Cmd, device.LatestTask.Step, device.LatestTask.TaskID)

	operate := device.Cmd

	// 🆕 添加操作类型判断的调试日志
	pix_log.InfoWithIMSI(device.IMSI, "🔀 [parseDeviceData] 操作类型判断：operate=%d", operate)

	switch operate {
	case protocol.Register:
		// 🆕 添加注册分支日志
		pix_log.InfoWithIMSI(device.IMSI, "📝 [parseDeviceData] 进入注册处理分支")

		term.Mutex.Lock()
		defer term.Mutex.Unlock()
		if !term.IsConnected {
			pix_log.WarningWithIMSI(term.Prop.IMSI, "❌ [parseDeviceData] 注册时设备未连接")
			return
		}
		isNeedReply = true
		// 使用结构体字段
		term.Prop.IMEI = device.IMEI
		term.Prop.IMSI = device.IMSI
		// 使用新的 NewRegister 方法
		app := system.NewRegister(&device, term, data)
		resp, err = app.GetReply(msgHeader.billNo)

	case protocol.Heartbeat, protocol.QueryHeartbeatCb:
		// 🆕 添加心跳数据接收层调试日志
		pix_log.InfoWithIMSI(device.IMSI, "💓 [parseDeviceData] 进入心跳处理分支：Cmd=%d, Step=%d, TaskId=%d",
			device.Cmd, device.LatestTask.Step, device.LatestTask.TaskID)

		// 🆕 添加证书检查前的日志
		pix_log.InfoWithIMSI(device.IMSI, "🔐 [parseDeviceData] 开始证书检查")

		err, num := common.CheckKey()
		if err != nil || num == 0 {
			pix_log.ErrorWithIMSI(device.IMSI, "❌ [parseDeviceData] 证书已过期：%v", err)
			pid := os.Getpid()
			cmdStr := fmt.Sprintf("kill -9 %d", pid)
			cmd := exec.Command("/bin/bash", "-c", cmdStr)
			if err := cmd.Start(); err != nil {
				pix_log.ErrorWithIMSI(device.IMSI, "Linux命令执行失败，请检查命令输入是否有误：%v", err)
			}
			return
		}

		// 🆕 添加证书检查成功的日志
		pix_log.InfoWithIMSI(device.IMSI, "✅ [parseDeviceData] 证书检查通过")

		isNeedReply = operate == protocol.Heartbeat

		// 🆕 添加回复需求判断的日志
		pix_log.InfoWithIMSI(device.IMSI, "📤 [parseDeviceData] 回复需求判断：isNeedReply=%t", isNeedReply)

		// 🆕 添加心跳处理器创建前的日志
		pix_log.InfoWithIMSI(device.IMSI, "🔧 [Parser] 创建心跳处理器：isNeedReply=%t", isNeedReply)

		// 使用新的 NewHeartbeat 方法
		app := system.NewHeartbeat(&device, term, data, timestamp)

		// 🆕 添加心跳处理器创建后的日志
		pix_log.InfoWithIMSI(device.IMSI, "✅ [Parser] 心跳处理器创建成功，开始调用GetReply")

		resp, err = app.GetReply(msgHeader.billNo)

		// 🆕 添加GetReply调用完成后的日志
		if err != nil {
			pix_log.ErrorWithIMSI(device.IMSI, "❌ [Parser] GetReply调用失败：错误=%v", err)
		} else {
			pix_log.InfoWithIMSI(device.IMSI, "✅ [Parser] GetReply调用成功：响应长度=%d", len(resp))
		}

	case protocol.CbAddrGet, protocol.CbMonitoringGet, protocol.CbUnmpGet,
		protocol.CbRs232Get, protocol.CbRs485Get, protocol.CbTzGet,
		protocol.CbLanGet, protocol.CbAccountGet, protocol.CbHeartbeatGet:
		app := system.NewReceiveDeviceGetInfo(&device, term, data)
		app.DoReply()

	case protocol.CbAddrSet, protocol.CbMonitoringSet, protocol.CbUnmpSet,
		protocol.CbRs232Set, protocol.CbRs485Set, protocol.CbTzSet,
		protocol.CbLanSet, protocol.CbAccountSet, protocol.CbHeartbeatSet:
		// 使用新的 NewReceiveDeviceSetInfo 方法
		app := system.NewReceiveDeviceSetInfo(&device, term, data)
		app.DoReply()

	case protocol.MakeOrder:
		// 直接使用结构体字段
		system.CheckOrder(device.BillNo, device.RetailBot.TaskID)

	case protocol.CheckOrder:
		// 直接使用结构体字段
		system.SyncOrder(device.Orders)
		pix_log.InfoWithIMSI(device.IMSI, "pad order: %v", device.Orders)

	case protocol.SelectMap:
		// 直接使用结构体字段
		system.SelectMap(term, device.MapList)

	case protocol.DeviceSyncInfo, protocol.CloudSyncInfo:
		// 直接使用结构体字段
		system.SelectMapInfo(term, &device.MapInfo)

	case protocol.SetMap:
		system.SetMapInfo(term)

		// 查询故障
	case protocol.SelectAlarm:
		// 直接使用结构体字段
		system.SelectAlarm(term, device.AlarmList)
		str, _ := json.Marshal(device.AlarmList)
		fmt.Println("get alarmList:", string(str))

	case protocol.Unlock, protocol.Lock, protocol.FindCar, protocol.StartCar, protocol.StopCar:
		// 直接使用结构体字段
		system.Replay(term, operate, device.Response, device.Bn)
	}

	// 如果不需要回复或回复内容为空，直接返回
	if !isNeedReply || resp == nil {
		return
	}

	// 如果构造应答消息过程中出现错误，记录错误日志并返回
	if err != nil {
		v.LogTcp().Errorf("[ERROR]构造应答消息过程中出现错误\nIMSI:%s\n%s\n", term.Prop.IMSI, err)
		return
	}

	// 构造回复消息并发送到终端的消息通道
	respMsg := MakeRespMsg(uint16(operate), msgHeader, res, resp)
	term.Prop.Msg = string(resp)
	term.Prop.Message <- respMsg
}

# ============================================================================
# 生产环境配置文件
# ============================================================================

# ============================================================================
# 应用基础配置 (结构体)
# ============================================================================
[app]
name = "服务"
version = "1.4.0"

# ============================================================================
# 业务配置 (结构体)
# ============================================================================
[business]
# 设备离线相关配置
offline_interval = 1800                # 设备长时间离线判断阈值（秒），30分钟，用于IsTermOffline()判断设备是否长时间离线
lan_offline_interval = 300             # LAN口离线判断阈值（秒），5分钟，用于IsLan1Offline()/IsLan2Offline()判断网络连接状态
offline_report_threshold = 1800        # 离线报告统计阈值（秒），30分钟，生成设备登录报告时只统计超过此时长的离线时间，0表示按实际时间统计

# 数据推送相关配置
push_interval = 3                      # Dashboard数据推送间隔（秒），控制前端设备状态更新频率

# 心跳检测相关配置（核心在线状态判断）
heartbeat_check_interval = 2           # 心跳检测器运行间隔（秒），定时检查所有设备心跳状态的频率
heartbeat_timeout = 10                 # 心跳超时时间（秒），超过此时间未收到心跳则判定设备离线，统一在线状态判断标准

# 系统监控相关
disk_name = "/dev/vda"

# ============================================================================
# 定时任务配置 (结构体)
# ============================================================================
[schedule]
db_backup_cron = "0 0 3 * * 6"
cpu_check_cron = "*/10 * * * * *"
report_generate_cron = "0 0 1 * * 1"
report_reset_cron = "0 0 0 * * 1"
upgrade_heartbeat = 5
upgrade_timeout = 300

# ============================================================================
# 日志配置 (结构体，简化重复配置)
# ============================================================================
[logging]
# 全局日志配置
base_path = "logs"
rotate_size = "50M"                # 每个文件50MB
rotate_backup_limit = 7            # 保留7天
rotate_check_interval = "1h"
date_format = "2006-01-02"         # 日期格式
max_files_per_day = 99             # 每天最多99个文件（01-99序号）

# 各模块日志配置
[logging.modules]
server = { level = "prod", stdout = false, filename = "server" }
tcp = { level = "all", stdout = false, filename = "tcp" }
terminal = { level = "prod", stdout = false, filename = "terminal" }
sql = { level = "prod", stdout = false, filename = "sql" }
database = { level = "prod", stdout = false, filename = "database" }

# ============================================================================
# 数据库配置 (结构体 + 嵌套结构体)
# ============================================================================
[database]
# 全局数据库配置
default_charset = "utf8mb4"
default_timeout = "3s"
default_read_timeout = "2s"
default_write_timeout = "2s"

# GoFrame期望的数据库配置格式
[database.default]
host = "127.0.0.1"
port = 3306
user = "root"
pass = "qwer323w@8"
name = "pixmoving"
type = "mysql"

[database.wurenche]
host = "127.0.0.1"
port = 3306
user = "root"
pass = "qwer323w@8"
name = "wurenche"
type = "mysql"

[database.jzy]
host = "127.0.0.1"
port = 3306
user = "root"
pass = "qwer323w@8"
name = "jzy"
type = "mysql"

[database.coffee]
host = "127.0.0.1"
port = 3306
user = "root"
pass = "qwer323w@8"
name = "coffee"
type = "mysql"

# ============================================================================
# 服务器配置 (结构体 + 嵌套结构体)
# ============================================================================
[server]
# TCP服务器配置 (嵌套结构体)
[server.tcp]
host = "0.0.0.0"
port = 2020
keepalive_timeout = 1000

# 🆕 MQTT配置 (重构后的清晰配置)
[server.mqtt]
broker = "ws://localhost:8083/mqtt"            # MQTT broker WebSocket地址
topic_prefix = "/pix"                          # MQTT topic前缀
client_id = "ccserver"                         # MQTT客户端ID前缀
clean_session = true                           # MQTT clean session
connect_timeout = 30                           # 连接超时时间（秒）
keepalive_timeout = 120                        # 保活超时时间（秒）
ping_timeout = 45                              # Ping超时时间（秒）
auto_reconnect = true                          # 自动重连
max_reconnect_interval = 60                    # 最大重连间隔（秒）

# ============================================================================
# Redis配置 (GoFrame期望的格式)
# ============================================================================
[redis]
[redis.default]
host = "127.0.0.1"
port = 6379
db = 2
pass = "pix@6688"
idle_timeout = 600

# ============================================================================
# 中间件配置 (结构体)
# ============================================================================
[middleware]
[middleware.kafka]
brokers = "127.0.0.1:9092"
topic = "device-cmd"

# ============================================================================
# 加密配置 (结构体)
# ============================================================================
[encryption]
[encryption.sm4]
enabled = true
key = "31323334353637383930313233343536"  # 16字节密钥的hex编码 "1234567890123456"

# ============================================================================
# 安全防护配置 (结构体 + 嵌套结构体)
# ============================================================================
[security]
# 连接限制配置 (嵌套结构体)
[security.connection_limit]
enabled = true                    # 是否启用连接限制
initial_ban_duration = 300        # 首次禁止时长（秒）- 5分钟
max_ban_duration = 3600           # 最大禁止时长（秒）- 1小时
backoff_multiplier = 2.0          # 退避倍数
max_attempts = 5                  # 最大尝试次数
cleanup_interval = 600            # 清理间隔（秒）- 10分钟
cache_expiration = 7200           # 缓存过期时间（秒）- 2小时
log_suppression_duration = 60     # 日志抑制时长（秒）- 1分钟

# IP级别限制配置 (嵌套结构体) - 暂时禁用，专注IMSI级别防护
[security.ip_limit]
enabled = false                   # 是否启用IP限制（暂时禁用）
max_connections_per_ip = 10       # 每个IP最大连接数
ban_duration = 1800               # IP禁止时长（秒）- 30分钟

# ============================================================================
# 数据维护配置 (数组 - Array of Tables)
# 每个 [[data_maintenance]] 定义一个表的清理规则
# ============================================================================

# 信号记录表清理配置
[[data_maintenance]]
table = "record_signal"
retained_days = 14
cleanup_on_start = true
cron = "0 0 2 * * *"
time_column = "created_time"
time_type = "timestamp"

# 操作日志表清理配置
[[data_maintenance]]
table = "log_operation"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

# 消息日志表清理配置
[[data_maintenance]]
table = "log_message"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

# 登录日志表清理配置
[[data_maintenance]]
table = "log_login"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 * * 1"
time_column = "created_ts"
time_type = "timestamp"

# 登录报告表清理配置
[[data_maintenance]]
table = "report_login"
retained_days = 70
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

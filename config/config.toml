# 默认配置文件 - 指向开发环境
# 如果需要使用其他环境，请通过以下方式指定：
# 1. 环境变量：export GF_GCFG_FILE=config.prod.toml
# 2. 命令行参数：./ccserver --gf.gcfg.file=config.prod.toml
# 3. 启动脚本：ENV=prod ./scripts/start.sh

# ============================================================================
# 应用基础配置 (结构体)
# ============================================================================
[app]
name = "ccserver"
version = "1.4.0"

# ============================================================================
# 业务配置 (结构体)
# ============================================================================
[business]
# 设备离线相关配置
offline_interval = 1800                # 设备长时间离线判断阈值（秒），30分钟，用于IsTermOffline()判断设备是否长时间离线
lan_offline_interval = 300             # LAN口离线判断阈值（秒），5分钟，用于IsLan1Offline()/IsLan2Offline()判断网络连接状态
offline_report_threshold = 1800        # 离线报告统计阈值（秒），30分钟，生成设备登录报告时只统计超过此时长的离线时间，0表示按实际时间统计

# 数据推送相关配置
push_interval = 3                      # Dashboard数据推送间隔（秒），控制前端设备状态更新频率

# 心跳检测相关配置（核心在线状态判断）
heartbeat_check_interval = 2           # 心跳检测器运行间隔（秒），定时检查所有设备心跳状态的频率
heartbeat_timeout = 10                 # 心跳超时时间（秒），超过此时间未收到心跳则判定设备离线，统一在线状态判断标准

# 系统监控相关
disk_name = "/dev/nvme0n1"

# ============================================================================
# 定时任务配置 (结构体)
# ============================================================================
[schedule]
db_backup_cron = "0 0 3 * * 6"
cpu_check_cron = "*/10 * * * * *"
report_generate_cron = "0 0 1 * * 1"
report_reset_cron = "0 0 0 * * 1"
upgrade_heartbeat = 5
upgrade_timeout = 300

# ============================================================================
# 日志配置 (结构体，简化重复配置)
# ============================================================================
[logging]
# 全局日志配置
base_path = "logs"
rotate_size = "50M"                # 每个文件50MB
rotate_backup_limit = 7            # 保留7天
rotate_check_interval = "1h"
date_format = "2006-01-02"         # 日期格式
max_files_per_day = 99             # 每天最多99个文件（01-99序号）

# 各模块日志配置
[logging.modules]
server = { level = "all", stdout = true, filename = "server" }     # 保留服务器日志控制台输出
tcp = { level = "all", stdout = false, filename = "tcp" }          # 关闭TCP日志控制台输出（频繁）
terminal = { level = "all", stdout = false, filename = "terminal" } # 关闭终端日志控制台输出
database = { level = "all", stdout = false, filename = "database" } # 关闭数据库日志控制台输出
kafka = { level = "all", stdout = false, filename = "kafka" }       # 关闭Kafka日志控制台输出

# ============================================================================
# 数据库配置 (结构体 + 嵌套结构体)
# ============================================================================
[database]
# 全局数据库配置
default_charset = "utf8mb4"
default_timeout = "3s"
default_read_timeout = "2s"
default_write_timeout = "2s"

# GoFrame期望的数据库配置格式
[database.default]
host = "**************"
port = 3306
user = "root"
pass = "pix@6688"
name = "pixmoving-test"
type = "mysql"

[database.wurenche]
host = "**************"
port = 3306
user = "root"
pass = "pix@6688"
name = "wurenche"
type = "mysql"

[database.jzy]
host = "**************"
port = 3306
user = "root"
pass = "pix@6688"
name = "jzy"
type = "mysql"

[database.coffee]
host = "**************"
port = 3306
user = "root"
pass = "pix@6688"
name = "coffee"
type = "mysql"

# ============================================================================
# 服务器配置 (结构体 + 嵌套结构体)
# ============================================================================
[server]
# TCP服务器配置 (嵌套结构体)
[server.tcp]
host = "0.0.0.0"
port = 2020
keepalive_timeout = 1000

# 🆕 MQTT配置 (重构后的清晰配置)
[server.mqtt]
broker = "ws://**************:8083/mqtt"      # MQTT broker WebSocket地址
topic_prefix = "/pix"                          # MQTT topic前缀
client_id = "ccserver"                         # MQTT客户端ID前缀
clean_session = true                           # MQTT clean session
connect_timeout = 30                           # 连接超时时间（秒）
keepalive_timeout = 120                        # 保活超时时间（秒）
ping_timeout = 45                              # Ping超时时间（秒）
auto_reconnect = true                          # 自动重连
max_reconnect_interval = 60                    # 最大重连间隔（秒）

# �️ WebSocket配置已移除，完全使用MQTT配置

# ============================================================================
# Redis配置 (GoFrame期望的格式)
# ============================================================================
[redis]
[redis.default]
host = "**************"
port = 6379
db = 3
pass = "Pix@121cc"
idle_timeout = 600

# ============================================================================
# 中间件配置 (结构体)
# ============================================================================
[middleware]
[middleware.kafka]
brokers = "**************:9092"
topic = "device-cmd"

# ============================================================================
# 加密配置 (结构体)
# ============================================================================
[encryption]
[encryption.sm4]
enabled = true
key = "31323334353637383930313233343536"  # 16字节密钥的hex编码 "1234567890123456"

# ============================================================================
# 安全防护配置 (结构体 + 嵌套结构体)
# ============================================================================
[security]
# 连接限制配置 (嵌套结构体)
[security.connection_limit]
enabled = true                    # 是否启用连接限制
initial_ban_duration = 300        # 首次禁止时长（秒）- 5分钟
max_ban_duration = 3600           # 最大禁止时长（秒）- 1小时
backoff_multiplier = 2.0          # 退避倍数
max_attempts = 5                  # 最大尝试次数
cleanup_interval = 600            # 清理间隔（秒）- 10分钟
cache_expiration = 7200           # 缓存过期时间（秒）- 2小时
log_suppression_duration = 60     # 日志抑制时长（秒）- 1分钟

# IP级别限制配置 (嵌套结构体) - 暂时禁用，专注IMSI级别防护
[security.ip_limit]
enabled = false                   # 是否启用IP限制（暂时禁用）
max_connections_per_ip = 10       # 每个IP最大连接数
ban_duration = 1800               # IP禁止时长（秒）- 30分钟

# ============================================================================
# 数据维护配置 (数组 - Array of Tables)
# 每个 [[data_maintenance]] 定义一个表的清理规则
# ============================================================================

# 信号记录表清理配置
[[data_maintenance]]
table = "record_signal"
retained_days = 14
cleanup_on_start = true
cron = "0 0 2 * * *"
time_column = "created_time"
time_type = "timestamp"

# 操作日志表清理配置
[[data_maintenance]]
table = "log_operation"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

# 消息日志表清理配置
[[data_maintenance]]
table = "log_message"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

# 登录日志表清理配置
[[data_maintenance]]
table = "log_login"
retained_days = 30
cleanup_on_start = true
cron = "0 0 2 * * 1"
time_column = "created_ts"
time_type = "timestamp"

# 登录报告表清理配置
[[data_maintenance]]
table = "report_login"
retained_days = 70
cleanup_on_start = true
cron = "0 0 2 1 * *"
time_column = "created_time"
time_type = "datetime"

# ============================================================================
# TOML 语法规则说明和示例
# ============================================================================

# TOML (Tom's Obvious, Minimal Language) 是一种配置文件格式
# 以下是主要语法规则和本配置文件中的实际应用示例：

# 1. 基本数据类型
# ============================================================================
# 字符串：使用双引号
# name = "ccserver"
#
# 整数：直接写数值
# port = 2022
#
# 浮点数：包含小数点
# ratio = 3.14
#
# 布尔值：true 或 false
# enabled = true
#
# 日期时间：ISO 8601 格式
# created_at = 2024-01-01T00:00:00Z

# 2. 表格（Tables）- 对应 Go 结构体
# ============================================================================
# [section_name] 定义一个配置节，对应 Go 中的结构体
#
# 示例：[app] 对应 Go 代码中的：
# App struct {
#     Name    string `json:"name"`
#     Version string `json:"version"`
# }

# 3. 嵌套表格（Nested Tables）- 对应嵌套结构体
# ============================================================================
# [parent.child] 定义嵌套配置，对应 Go 中的嵌套结构体
#
# 本配置文件示例：
# [middleware]           # 定义顶级 Middleware 结构体
# [middleware.kafka]     # 定义 Middleware 下的 Kafka 子结构体
# brokers = "**************:9092"
# topic = "device-cmd"
#
# 对应的 Go 代码结构：
# Middleware struct {
#     Kafka struct {
#         Brokers string `json:"brokers"`
#         Topic   string `json:"topic"`
#     } `json:"kafka"`
# }

# 4. 更深层嵌套示例
# ============================================================================
# [server.tcp] 对应：
# Server struct {
#     Tcp struct {
#         Host string `json:"host"`
#         Port int    `json:"port"`
#     }
# }

# 5. 表格数组（Array of Tables）- 对应结构体切片
# ============================================================================
# [[array_name]] 定义数组中的一个元素，可以重复多次
#
# 本配置文件示例：
# [[data_maintenance]]   # 第一个数组元素
# table = "record_signal"
# retained_days = 14
#
# [[data_maintenance]]   # 第二个数组元素
# table = "log_operation"
# retained_days = 30
#
# 对应的 Go 代码：
# DataMaintenance []DataMaintenanceConfig

# 6. 内联表格（Inline Tables）- 简洁的对象表示
# ============================================================================
# 可以在一行内定义简单的对象
#
# 示例：
# server = { level = "prod", stdout = false }
#
# 等价于：
# [server]
# level = "prod"
# stdout = false

# 7. 数组（Arrays）
# ============================================================================
# 使用方括号定义数组
#
# 示例：
# hosts = ["***********", "***********", "***********"]
# ports = [8080, 8081, 8082]

# 8. 注释
# ============================================================================
# 使用 # 开头的行是注释，会被解析器忽略
# 注释可以单独一行，也可以在配置行的末尾

# 9. 配置文件组织建议
# ============================================================================
# - 使用注释分隔不同的配置区域
# - 相关配置放在一起
# - 使用有意义的节名称
# - 保持一致的命名风格（推荐 snake_case）

# 10. 本项目中的配置层次结构
# ============================================================================
# 顶级配置节：
# - [app]           应用基础信息
# - [business]      业务相关配置
# - [schedule]      定时任务配置
# - [logging]       日志配置
# - [database]      数据库配置
# - [server]        服务器配置
# - [redis]         Redis 配置
# - [middleware]    中间件配置
#
# 嵌套配置节：
# - [server.tcp]           TCP 服务器配置
# - [server.websocket]     WebSocket 配置
# - [database.default]     默认数据库配置
# - [redis.default]        默认 Redis 配置
# - [middleware.kafka]     Kafka 中间件配置
#
# 数组配置：
# - [[data_maintenance]]   数据维护规则数组

package pkg

import (
	"encoding/json"
	"fmt"
	"github.com/jinzhu/copier"
	"reflect"
)

// CreateBoolNumberConverter 创建布尔值转数字的转换器
func CreateBoolNumberConverter(dstType interface{}) copier.TypeConverter {
	return copier.TypeConverter{
		SrcType: false, // bool类型
		DstType: dstType,
		Fn: func(src interface{}) (interface{}, error) {
			b, ok := src.(bool)
			if !ok {
				return nil, fmt.Errorf("源值类型错误: 期望 bool, 得到 %T", src)
			}

			val := int64(0)
			if b {
				val = 1
			}

			// 根据目标类型进行转换
			switch reflect.TypeOf(dstType).Kind() {
			case reflect.Int8:
				return int8(val), nil
			case reflect.Int16:
				return int16(val), nil
			case reflect.Int32:
				return int32(val), nil
			case reflect.Int64:
				return val, nil
			case reflect.Int:
				return int(val), nil
			case reflect.Uint8:
				return uint8(val), nil
			case reflect.Uint16:
				return uint16(val), nil
			case reflect.Uint32:
				return uint32(val), nil
			case reflect.Uint64:
				return uint64(val), nil
			case reflect.Uint:
				return uint(val), nil
			default:
				return nil, fmt.Errorf("不支持的目标类型: %v", reflect.TypeOf(dstType))
			}
		},
	}
}

// GetCopierOption 获取配置了所有转换器的copier选项
func GetCopierOption() copier.Option {
	// 创建所有可能需要的转换器
	converters := []copier.TypeConverter{
		CreateBoolNumberConverter(int8(0)),
		CreateBoolNumberConverter(int16(0)),
		CreateBoolNumberConverter(int32(0)),
		CreateBoolNumberConverter(int64(0)),
		CreateBoolNumberConverter(int(0)),
		CreateBoolNumberConverter(uint8(0)),
		CreateBoolNumberConverter(uint16(0)),
		CreateBoolNumberConverter(uint32(0)),
		CreateBoolNumberConverter(uint64(0)),
		CreateBoolNumberConverter(uint(0)),
	}

	return copier.Option{
		Converters: converters,
		DeepCopy:   true,
	}
}

// 调试用的函数
func PrintConverterInfo(value interface{}) {
	t := reflect.TypeOf(value)
	fmt.Printf("类型: %v, Kind: %v\n", t, t.Kind())
}

func ToTDMap(v interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 获取值的反射信息
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	typ := val.Type()

	// 遍历所有字段
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 获取 json tag
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}

		// 获取字段值
		value := field.Interface()

		// 根据字段类型进行处理
		switch field.Kind() {
		case reflect.Struct:
			// 结构体类型，转换为 JSON 字符串
			if jsonData, err := json.Marshal(value); err != nil {
				return nil, err
			} else {
				result[jsonTag] = string(jsonData)
			}

		case reflect.Array, reflect.Slice:
			// 数组或切片类型，转换为 JSON 字符串
			if jsonData, err := json.Marshal(value); err != nil {
				return nil, err
			} else {
				result[jsonTag] = string(jsonData)
			}

		case reflect.Map:
			// Map 类型，转换为 JSON 字符串
			if jsonData, err := json.Marshal(value); err != nil {
				return nil, err
			} else {
				result[jsonTag] = string(jsonData)
			}

		default:
			// 基本类型，直接使用原值
			result[jsonTag] = value
		}
	}

	return result, nil
}

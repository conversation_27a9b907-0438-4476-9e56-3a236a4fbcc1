package protocol

import (
	"bytes"
	"crypto/cipher"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/tjfoc/gmsm/sm4"
)

const (
	// YHL协议常量
	YHL_MSG_HEADER_LEN = 46 // 消息头长度(不含标识头2字节)
	
	// 协议版本
	PROTOCOL_VERSION_V1 = 0x01 // 明文协议
	PROTOCOL_VERSION_V2 = 0x02 // 加密协议
	
	// 加密方式
	ENCRYPTION_NONE    = 0x00 // 未加密
	ENCRYPTION_SM4_GCM = 0x01 // SM4-GCM加密
	
	// GCM参数
	GCM_NONCE_SIZE = 12 // GCM Nonce长度
	GCM_TAG_SIZE   = 16 // GCM认证标签长度
)

const (
	// 消息起始标识
	YHL_MSG_START_CHAR1      = '@'
	YHL_MSG_START_CHAR2      = '<'
	YHL_MSG_RESP_START_CHAR2 = '>'
)

// 协议命令常量
const (
	CMD_REGISTER  = 0x0001 // 注册
	CMD_HEARTBEAT = 0x0002 // 心跳
	CMD_TASK      = 0x0018 // 任务
)

// YHL协议消息头结构
type YhlMsgHeader struct {
	MsgLen        uint16   // 消息长度
	BillNo        uint32   // 流水号
	MsgFormat     uint16   // 消息体格式
	ProtocolVer   uint8    // 协议版本
	EncryptedType uint8    // 加密类型
	EncryptedRand uint32   // 加密随机数
	Timestamp     uint64   // 时间戳
	Reserved      [24]byte // 预留字段
	
	// V2协议专用字段
	Nonce   []byte // 12字节Nonce（仅V2协议）
	AuthTag []byte // 16字节认证标签（仅V2协议）
}

// YHL协议客户端
type YhlClient struct {
	billNo     uint32 // 流水号计数器
	encryptKey []byte // 加密密钥
}

// 创建新的YHL客户端
func NewYhlClient() *YhlClient {
	return &YhlClient{
		billNo: 1,
	}
}

// 设置加密密钥
func (c *YhlClient) SetEncryptionKey(key string) error {
	keyBytes, err := hex.DecodeString(key)
	if err != nil {
		return fmt.Errorf("invalid encryption key: %v", err)
	}
	if len(keyBytes) != 16 {
		return fmt.Errorf("encryption key must be 16 bytes")
	}
	c.encryptKey = keyBytes
	return nil
}

// 构建消息
func (c *YhlClient) BuildMessage(cmd uint16, data interface{}, useEncryption bool) ([]byte, error) {
	// 序列化消息体
	msgBody, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message body: %v", err)
	}

	// 构建消息头
	header := YhlMsgHeader{
		BillNo:        c.billNo,
		MsgFormat:     0x0000, // JSON格式
		ProtocolVer:   PROTOCOL_VERSION_V1,
		EncryptedType: ENCRYPTION_NONE,
		EncryptedRand: 0,
		Timestamp:     uint64(time.Now().Unix()),
	}

	// 如果启用加密
	if useEncryption && c.encryptKey != nil {
		header.ProtocolVer = PROTOCOL_VERSION_V2
		header.EncryptedType = ENCRYPTION_SM4_GCM
		
		// 加密消息体
		encryptedBody, nonce, authTag, err := c.encryptMessage(msgBody)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt message: %v", err)
		}
		
		msgBody = encryptedBody
		header.Nonce = nonce
		header.AuthTag = authTag
	}

	// 计算消息长度
	msgLen := YHL_MSG_HEADER_LEN + len(msgBody)
	if useEncryption {
		msgLen += GCM_NONCE_SIZE + GCM_TAG_SIZE
	}
	header.MsgLen = uint16(msgLen)

	// 构建完整消息
	message := c.buildFullMessage(header, msgBody)
	
	// 递增流水号
	c.billNo++
	
	return message, nil
}

// 加密消息
func (c *YhlClient) encryptMessage(plaintext []byte) ([]byte, []byte, []byte, error) {
	block, err := sm4.NewCipher(c.encryptKey)
	if err != nil {
		return nil, nil, nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, nil, nil, err
	}

	// 生成随机nonce
	nonce := make([]byte, GCM_NONCE_SIZE)
	for i := range nonce {
		nonce[i] = byte(time.Now().UnixNano() % 256)
	}

	// 加密
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)
	
	// 分离密文和认证标签
	if len(ciphertext) < GCM_TAG_SIZE {
		return nil, nil, nil, fmt.Errorf("invalid ciphertext length")
	}
	
	encryptedData := ciphertext[:len(ciphertext)-GCM_TAG_SIZE]
	authTag := ciphertext[len(ciphertext)-GCM_TAG_SIZE:]

	return encryptedData, nonce, authTag, nil
}

// 构建完整消息
func (c *YhlClient) buildFullMessage(header YhlMsgHeader, msgBody []byte) []byte {
	var buffer bytes.Buffer

	// 写入起始标识
	buffer.WriteByte(YHL_MSG_START_CHAR1)
	buffer.WriteByte(YHL_MSG_START_CHAR2)

	// 写入消息头
	binary.Write(&buffer, binary.BigEndian, header.MsgLen)
	binary.Write(&buffer, binary.BigEndian, header.BillNo)
	binary.Write(&buffer, binary.BigEndian, header.MsgFormat)
	binary.Write(&buffer, binary.BigEndian, header.ProtocolVer)
	binary.Write(&buffer, binary.BigEndian, header.EncryptedType)
	binary.Write(&buffer, binary.BigEndian, header.EncryptedRand)
	binary.Write(&buffer, binary.BigEndian, header.Timestamp)
	buffer.Write(header.Reserved[:])

	// 如果是V2协议，写入nonce和authTag
	if header.ProtocolVer == PROTOCOL_VERSION_V2 {
		buffer.Write(header.Nonce)
		buffer.Write(header.AuthTag)
	}

	// 写入消息体
	buffer.Write(msgBody)

	// 计算并写入CRC16校验
	data := buffer.Bytes()[2:] // 除去起始标识
	checksum := calculateCRC16(data)
	binary.Write(&buffer, binary.BigEndian, checksum)

	return buffer.Bytes()
}

// CRC16校验计算
func calculateCRC16(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 解析响应消息
func (c *YhlClient) ParseResponse(data []byte) (*ResponseMessage, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("response too short")
	}

	// 检查起始标识
	if data[0] != YHL_MSG_START_CHAR1 || data[1] != YHL_MSG_RESP_START_CHAR2 {
		return nil, fmt.Errorf("invalid response header")
	}

	// 解析消息长度
	msgLen := binary.BigEndian.Uint16(data[2:4])
	if len(data) < int(msgLen)+4 {
		return nil, fmt.Errorf("incomplete response message")
	}

	// 解析消息头
	header, err := c.parseResponseHeader(data[2:])
	if err != nil {
		return nil, err
	}

	// 解析消息体
	bodyStart := 2 + YHL_MSG_HEADER_LEN
	bodyEnd := bodyStart + int(msgLen) - YHL_MSG_HEADER_LEN - 2 // 减去CRC
	
	if bodyEnd > len(data) {
		return nil, fmt.Errorf("invalid message body length")
	}

	msgBody := data[bodyStart:bodyEnd]

	// 解析JSON消息体
	var response ResponseMessage
	if err := json.Unmarshal(msgBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response body: %v", err)
	}

	response.Header = header
	return &response, nil
}

// 解析响应消息头
func (c *YhlClient) parseResponseHeader(data []byte) (*YhlMsgHeader, error) {
	if len(data) < YHL_MSG_HEADER_LEN {
		return nil, fmt.Errorf("header too short")
	}

	header := &YhlMsgHeader{}
	reader := bytes.NewReader(data)

	binary.Read(reader, binary.BigEndian, &header.MsgLen)
	binary.Read(reader, binary.BigEndian, &header.BillNo)
	binary.Read(reader, binary.BigEndian, &header.MsgFormat)
	binary.Read(reader, binary.BigEndian, &header.ProtocolVer)
	binary.Read(reader, binary.BigEndian, &header.EncryptedType)
	binary.Read(reader, binary.BigEndian, &header.EncryptedRand)
	binary.Read(reader, binary.BigEndian, &header.Timestamp)
	reader.Read(header.Reserved[:])

	return header, nil
}

// 响应消息结构
type ResponseMessage struct {
	Header *YhlMsgHeader `json:"-"`
	Cmd    uint16        `json:"cmd"`
	Res    int           `json:"res"`
	BillNo uint32        `json:"bn"`
}

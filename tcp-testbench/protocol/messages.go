package protocol

import (
	"math/rand"
	"time"
)

// 设备注册消息
type RegisterMessage struct {
	Cmd         uint16  `json:"cmd"`
	IMEI        string  `json:"imei"`
	IMSI        string  `json:"imsi"`
	Model       string  `json:"model"`
	SoftwareVer string  `json:"sw"`
	HardwareVer string  `json:"hd"`
	Vendor      string  `json:"vendor"`
	CI          string  `json:"ci"`
	PCI         string  `json:"pci"`
	ManageIP    string  `json:"manage_ip"`
	Position    string  `json:"position"`
	Longitude   float64 `json:"lng"`
	Latitude    float64 `json:"lat"`
}

// 心跳消息
type HeartbeatMessage struct {
	Cmd        uint16     `json:"cmd"`
	IMSI       string     `json:"imsi"`
	IMEI       string     `json:"imei"`
	UID        string     `json:"uid"`
	VType      int8       `json:"vType"`
	ACC        int8       `json:"acc"`
	Gear       int8       `json:"gear"`
	TM         int32      `json:"tm"`         // 总里程(km)
	RM         int16      `json:"rm"`         // 剩余里程(km)
	Lng        float64    `json:"lng"`        // GPS经度
	Lat        float64    `json:"lat"`        // GPS纬度
	Located    int8       `json:"located"`    // 定位状态
	Status     int8       `json:"sta"`        // 设备状态
	Mode       int8       `json:"mode"`       // 工作模式
	Speed      int16      `json:"spd"`        // 当前速度
	SpeedLimit int16      `json:"spdL"`       // 限速值
	PowerLevel int8       `json:"pL"`         // 电量等级
	PowerVoltage int16    `json:"pV"`         // 电压
	PowerCurrent int16    `json:"pC"`         // 电流
	PowerCharging int8    `json:"pCh"`        // 充电状态
	BatteryStatus int16   `json:"bat"`        // 电池状态
	Altitude   float32    `json:"alt"`        // 海拔高度
	Angle      float32    `json:"angle"`      // 方向角度
	SatCount   int8       `json:"sat_cnt"`    // 卫星数量
	Error      string     `json:"err"`        // 错误信息
	Event      uint8      `json:"event"`      // 事件类型
	
	// 车辆控制状态
	Door          int8    `json:"door"`       // 车门状态
	Light         int8    `json:"light"`      // 车灯状态
	Window        int8    `json:"win"`        // 车窗状态
	HeadLight     int     `json:"headLight"`  // 前照灯状态
	HeadMode      int8    `json:"head_mode"`  // 大灯模式
	Signal        int8    `json:"sgn"`        // 转向信号
	SteeringAngle float32 `json:"ste"`        // 转向角度
	BrakeStatus   float32 `json:"brk"`        // 刹车状态
	Throttle      float32 `json:"thr"`        // 油门状态
	EmergencyStatus int8  `json:"emgSta"`     // 紧急状态
	
	// 环境状态
	AirCondition     int8  `json:"airCon"`   // 空调状态
	InnerTemperature int32 `json:"inTemp"`   // 车内温度
	OuterTemperature int32 `json:"outTemp"`  // 车外温度
	Smoke            int32 `json:"smoke"`    // 烟雾浓度
	CO2              int32 `json:"co2"`      // 二氧化碳浓度
	Seatbelt         int   `json:"seatbelt"` // 安全带状态
	
	// 网络状态
	Lan             []LanStatus `json:"lan"`     // 网络连接状态列表
	UpgradeState    string      `json:"state"`   // 升级状态
	UpgradeProgress string      `json:"percent"` // 升级进度
	
	// 车辆状态
	IPCToChannel    bool    `json:"ipcToCha"`    // IPC通道状态
	ADLocated       bool    `json:"adLocated"`   // AD定位状态
	GPSLocated      bool    `json:"gpsLocated"`  // GPS定位状态
	GPSStrength     int8    `json:"gpsStrength"` // GPS信号强度
	Shutdown        int8    `json:"shutdown"`    // 关机状态
	
	// 任务相关
	LatestTask  TaskInfo    `json:"latestTask"` // 最新任务信息
	AlarmList   []AlarmInfo `json:"alarmList"`  // 警报信息
	
	// 其他状态
	PosQuality float32 `json:"pos_q"`   // 定位质量
	Warning    []int   `json:"warning"` // 警告信息列表
	Seat       [6]int  `json:"seat"`    // 座位状态数组
}

// LAN状态
type LanStatus struct {
	Name       string `json:"name"`       // LAN口名称
	LinkStatus int64  `json:"linkStatus"` // 连接状态
}

// 任务信息
type TaskInfo struct {
	Step           int8          `json:"step"`           // 任务执行状态
	TaskID         uint64        `json:"taskId"`         // 任务ID
	RouteID        uint64        `json:"routeId"`        // 路线ID
	ExecutionId    int64         `json:"executionId"`    // 执行实例ID
	Progress       int8          `json:"progress"`       // 任务完成百分比
	StartTime      int64         `json:"startTime"`      // 任务开始时间
	TaskDistance   int           `json:"taskDistance"`   // 任务总距离
	TargetDistance int           `json:"targetDistance"` // 目标剩余距离
	LeftTime       int           `json:"leftTime"`       // 到达下一站预计时间
	Name           string        `json:"name"`           // 任务名称
	Desp           string        `json:"desp"`           // 任务描述
	CurrentStation SimpleStation `json:"currentStation"` // 当前站点
	TargetStation  SimpleStation `json:"targetStation"`  // 目标站点
}

// 简化版站点信息
type SimpleStation struct {
	ID   int    `json:"id"`   // 站点ID
	Name string `json:"name"` // 站点名称
}

// 告警信息
type AlarmInfo struct {
	Code  int    `json:"code"`  // 告警代码
	Level uint8  `json:"level"` // 告警级别
	Type  uint8  `json:"type"`  // 告警类型
	Msg   string `json:"msg"`   // 告警消息
	Ts    int64  `json:"ts"`    // 时间戳
}

// 消息构建器
type MessageBuilder struct {
	deviceConfig *DeviceConfig
	rand         *rand.Rand
}

// 设备配置
type DeviceConfig struct {
	IMSI        string  `json:"imsi"`
	IMEI        string  `json:"imei"`
	Model       string  `json:"model"`
	SoftwareVer string  `json:"softwareVer"`
	HardwareVer string  `json:"hardwareVer"`
	Vendor      string  `json:"vendor"`
	CI          string  `json:"ci"`
	PCI         string  `json:"pci"`
	ManageIP    string  `json:"manageIp"`
	Position    string  `json:"position"`
	Longitude   float64 `json:"longitude"`
	Latitude    float64 `json:"latitude"`
}

// 创建消息构建器
func NewMessageBuilder(config *DeviceConfig) *MessageBuilder {
	return &MessageBuilder{
		deviceConfig: config,
		rand:         rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// 构建注册消息
func (mb *MessageBuilder) BuildRegisterMessage() *RegisterMessage {
	return &RegisterMessage{
		Cmd:         CMD_REGISTER,
		IMEI:        mb.deviceConfig.IMEI,
		IMSI:        mb.deviceConfig.IMSI,
		Model:       mb.deviceConfig.Model,
		SoftwareVer: mb.deviceConfig.SoftwareVer,
		HardwareVer: mb.deviceConfig.HardwareVer,
		Vendor:      mb.deviceConfig.Vendor,
		CI:          mb.deviceConfig.CI,
		PCI:         mb.deviceConfig.PCI,
		ManageIP:    mb.deviceConfig.ManageIP,
		Position:    mb.deviceConfig.Position,
		Longitude:   mb.deviceConfig.Longitude,
		Latitude:    mb.deviceConfig.Latitude,
	}
}

// 构建心跳消息
func (mb *MessageBuilder) BuildHeartbeatMessage(options *HeartbeatOptions) *HeartbeatMessage {
	if options == nil {
		options = &HeartbeatOptions{}
	}

	// 基础位置信息（可以添加随机偏移模拟移动）
	lng := mb.deviceConfig.Longitude
	lat := mb.deviceConfig.Latitude
	
	if options.SimulateMovement {
		// 添加小范围随机偏移模拟设备移动
		lng += (mb.rand.Float64() - 0.5) * 0.001 // 约100米范围
		lat += (mb.rand.Float64() - 0.5) * 0.001
	}

	// 构建LAN状态
	lanStatus := []LanStatus{
		{Name: "LAN1", LinkStatus: 1},
		{Name: "LAN2", LinkStatus: 1},
	}

	// 构建任务信息
	taskInfo := TaskInfo{
		Step:           options.TaskStep,
		TaskID:         options.TaskID,
		RouteID:        options.RouteID,
		ExecutionId:    options.ExecutionId,
		Progress:       options.Progress,
		StartTime:      options.StartTime,
		TaskDistance:   options.TaskDistance,
		TargetDistance: options.TargetDistance,
		LeftTime:       options.LeftTime,
		Name:           options.TaskName,
		Desp:           options.TaskDesp,
		CurrentStation: SimpleStation{
			ID:   options.CurrentStationID,
			Name: options.CurrentStationName,
		},
		TargetStation: SimpleStation{
			ID:   options.TargetStationID,
			Name: options.TargetStationName,
		},
	}

	return &HeartbeatMessage{
		Cmd:         CMD_HEARTBEAT,
		IMSI:        mb.deviceConfig.IMSI,
		IMEI:        mb.deviceConfig.IMEI,
		UID:         "1",
		VType:       options.VehicleType,
		ACC:         options.ACC,
		Gear:        options.Gear,
		TM:          options.TotalMileage,
		RM:          options.RemainMileage,
		Lng:         lng,
		Lat:         lat,
		Located:     1, // 已定位
		Status:      options.DeviceStatus,
		Mode:        options.WorkMode,
		Speed:       options.Speed,
		SpeedLimit:  options.SpeedLimit,
		PowerLevel:  options.PowerLevel,
		PowerVoltage: options.PowerVoltage,
		PowerCurrent: options.PowerCurrent,
		PowerCharging: options.PowerCharging,
		BatteryStatus: options.BatteryStatus,
		Altitude:    float32(options.Altitude),
		Angle:       options.Angle,
		SatCount:    options.SatCount,
		Error:       options.ErrorMsg,
		Event:       options.Event,
		
		// 车辆控制状态
		Door:          options.Door,
		Light:         options.Light,
		Window:        options.Window,
		HeadLight:     options.HeadLight,
		HeadMode:      options.HeadMode,
		Signal:        options.Signal,
		SteeringAngle: options.SteeringAngle,
		BrakeStatus:   options.BrakeStatus,
		Throttle:      options.Throttle,
		EmergencyStatus: options.EmergencyStatus,
		
		// 环境状态
		AirCondition:     options.AirCondition,
		InnerTemperature: options.InnerTemperature,
		OuterTemperature: options.OuterTemperature,
		Smoke:            options.Smoke,
		CO2:              options.CO2,
		Seatbelt:         options.Seatbelt,
		
		// 网络和升级状态
		Lan:             lanStatus,
		UpgradeState:    options.UpgradeState,
		UpgradeProgress: options.UpgradeProgress,
		
		// 车辆状态
		IPCToChannel: options.IPCToChannel,
		ADLocated:    options.ADLocated,
		GPSLocated:   options.GPSLocated,
		GPSStrength:  options.GPSStrength,
		Shutdown:     options.Shutdown,
		
		// 任务和告警
		LatestTask: taskInfo,
		AlarmList:  options.AlarmList,
		
		// 其他状态
		PosQuality: options.PosQuality,
		Warning:    options.Warning,
		Seat:       options.Seat,
	}
}

// 心跳选项
type HeartbeatOptions struct {
	// 基础选项
	SimulateMovement bool
	VehicleType      int8
	
	// 设备状态
	ACC          int8
	Gear         int8
	DeviceStatus int8
	WorkMode     int8
	
	// 行驶状态
	TotalMileage  int32
	RemainMileage int16
	Speed         int16
	SpeedLimit    int16
	Altitude      float64
	Angle         float32
	SatCount      int8
	
	// 电源状态
	PowerLevel    int8
	PowerVoltage  int16
	PowerCurrent  int16
	PowerCharging int8
	BatteryStatus int16
	
	// 车辆控制
	Door            int8
	Light           int8
	Window          int8
	HeadLight       int
	HeadMode        int8
	Signal          int8
	SteeringAngle   float32
	BrakeStatus     float32
	Throttle        float32
	EmergencyStatus int8
	
	// 环境状态
	AirCondition     int8
	InnerTemperature int32
	OuterTemperature int32
	Smoke            int32
	CO2              int32
	Seatbelt         int
	
	// 网络和升级
	UpgradeState    string
	UpgradeProgress string
	
	// 车辆状态
	IPCToChannel bool
	ADLocated    bool
	GPSLocated   bool
	GPSStrength  int8
	Shutdown     int8
	
	// 任务信息
	TaskStep            int8
	TaskID              uint64
	RouteID             uint64
	ExecutionId         int64
	Progress            int8
	StartTime           int64
	TaskDistance        int
	TargetDistance      int
	LeftTime            int
	TaskName            string
	TaskDesp            string
	CurrentStationID    int
	CurrentStationName  string
	TargetStationID     int
	TargetStationName   string
	
	// 告警和其他
	AlarmList  []AlarmInfo
	ErrorMsg   string
	Event      uint8
	PosQuality float32
	Warning    []int
	Seat       [6]int
}

// 获取默认心跳选项
func GetDefaultHeartbeatOptions() *HeartbeatOptions {
	return &HeartbeatOptions{
		SimulateMovement: false,
		VehicleType:      1, // 清扫机器人
		ACC:              1, // ACC开启
		Gear:             0, // 空档
		DeviceStatus:     0, // 正常
		WorkMode:         1, // 自动模式
		TotalMileage:     1000,
		RemainMileage:    500,
		Speed:            15,
		SpeedLimit:       25,
		Altitude:         10.5,
		Angle:            90.0,
		SatCount:         12,
		PowerLevel:       80,
		PowerVoltage:     48,
		PowerCurrent:     10,
		PowerCharging:    0, // 未充电
		BatteryStatus:    100,
		Door:             0, // 关闭
		Light:            0, // 关闭
		Window:           0, // 关闭
		HeadLight:        0, // 关闭
		HeadMode:         1, // 常规模式
		Signal:           0, // 无转向
		SteeringAngle:    0.0,
		BrakeStatus:      0.0,
		Throttle:         0.0,
		EmergencyStatus:  0, // 正常
		AirCondition:     0, // 关闭
		InnerTemperature: 25,
		OuterTemperature: 20,
		Smoke:            0,
		CO2:              400,
		Seatbelt:         0,
		UpgradeState:     "",
		UpgradeProgress:  "",
		IPCToChannel:     true,
		ADLocated:        true,
		GPSLocated:       true,
		GPSStrength:      5,
		Shutdown:         0,
		TaskStep:         0, // 无任务
		TaskID:           0,
		RouteID:          0,
		ExecutionId:      0,
		Progress:         0,
		StartTime:        0,
		TaskDistance:     0,
		TargetDistance:   0,
		LeftTime:         0,
		TaskName:         "",
		TaskDesp:         "",
		CurrentStationID: 0,
		CurrentStationName: "",
		TargetStationID:  0,
		TargetStationName: "",
		AlarmList:        []AlarmInfo{},
		ErrorMsg:         "",
		Event:            0,
		PosQuality:       0.95,
		Warning:          []int{},
		Seat:             [6]int{0, 0, 0, 0, 0, 0},
	}
}

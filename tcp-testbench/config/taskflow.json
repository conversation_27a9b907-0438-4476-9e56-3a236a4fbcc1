{"taskFlowConfig": {"id": "", "name": "默认任务流", "description": "", "heartbeatInterval": 5000, "triggerMode": "heartbeat_count"}, "stateNodes": [{"id": "node_0", "step": 0, "name": "未开始", "config": {"heartbeatCount": 2}, "position": {"x": 100, "y": 100}}, {"id": "node_1", "step": 1, "name": "正在执行", "config": {"heartbeatCount": 6}, "position": {"x": 300, "y": 100}}, {"id": "node_2", "step": 2, "name": "已完成", "config": {"heartbeatCount": 1}, "position": {"x": 500, "y": 100}}, {"id": "node_9", "step": 9, "name": "执行失败", "config": {"heartbeatCount": 1}, "position": {"x": 300, "y": 250}}], "stateTransitions": [{"id": "trans_0_1", "fromNodeId": "node_0", "toNodeId": "node_1", "description": "开始执行", "probability": 1, "triggerType": "", "triggerValue": 0}, {"id": "trans_1_2", "fromNodeId": "node_1", "toNodeId": "node_2", "description": "正常完成", "probability": 0.8, "triggerType": "", "triggerValue": 0}, {"id": "trans_1_9", "fromNodeId": "node_1", "toNodeId": "node_9", "description": "执行失败", "probability": 0.2, "triggerType": "", "triggerValue": 0}], "taskConfig": {"taskId": 1, "routeId": 1, "taskName": "默认任务", "taskDescription": "任务描述", "taskDistance": 360, "leftTime": 5, "currentStation": {"id": 1, "name": "起始站点"}, "targetStation": {"id": 2, "name": "目标站点"}}}
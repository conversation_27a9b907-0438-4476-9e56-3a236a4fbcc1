package taskflow

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// TaskFlowManager 任务流程管理器（简化版本）
type TaskFlowManager struct {
	// 配置数据
	configData TaskFlowData `json:"configData"`

	// 运行时状态
	runtime RuntimeState `json:"runtime"`

	// 内部状态
	mutex      sync.RWMutex
	configPath string
}

// NewTaskFlowManager 创建任务流程管理器
func NewTaskFlowManager(configPath string) *TaskFlowManager {
	manager := &TaskFlowManager{
		configPath: configPath,
		runtime: RuntimeState{
			IsRunning:     false,
			CurrentNodeID: "",
			CurrentStep:   0,
		},
	}

	// 加载配置（如果文件不存在会使用默认配置）
	manager.LoadConfig()

	return manager
}

// SaveTaskFlowData 保存前端传来的任务流程数据
func (m *TaskFlowManager) SaveTaskFlowData(data TaskFlowData) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 保存配置数据
	m.configData = data

	fmt.Printf("📁 保存任务流程配置: %s\n", data.TaskFlowConfig.Name)

	// 持久化到文件
	return m.saveToFile()
}

// GetTaskFlowData 获取任务流程数据（供前端使用）
func (m *TaskFlowManager) GetTaskFlowData() TaskFlowData {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.configData
}

// StartFlow 启动任务流程
func (m *TaskFlowManager) StartFlow() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.runtime.IsRunning {
		return fmt.Errorf("任务流程已在运行中")
	}

	// 检查是否有配置数据
	if len(m.configData.StateNodes) == 0 {
		return fmt.Errorf("没有配置任务流程节点")
	}

	// 找到初始节点（step=0）
	var initialNode *StateNode
	for i := range m.configData.StateNodes {
		if m.configData.StateNodes[i].Step == 0 {
			initialNode = &m.configData.StateNodes[i]
			break
		}
	}

	if initialNode == nil {
		return fmt.Errorf("未找到初始节点（step=0）")
	}

	// 初始化运行时状态
	m.runtime.IsRunning = true
	m.runtime.CurrentNodeID = initialNode.ID
	m.runtime.CurrentStep = initialNode.Step
	m.runtime.HeartbeatCount = 0
	m.runtime.TotalHeartbeats = 0
	m.runtime.StartTime = time.Now().Unix()
	m.runtime.LastTransitionTime = 0

	fmt.Printf("🚀 任务流程启动: 初始节点=%s, step=%d\n", initialNode.ID, initialNode.Step)

	return nil
}

// StopFlow 停止任务流程
func (m *TaskFlowManager) StopFlow() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.runtime.IsRunning = false
	fmt.Printf("⏹️ 任务流程已停止\n")
}

// OnHeartbeatSent 处理心跳发送事件
func (m *TaskFlowManager) OnHeartbeatSent() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.runtime.IsRunning {
		fmt.Printf("⚠️ 任务流程未运行，忽略心跳通知\n")
		return nil
	}

	// 增加心跳计数
	previousCount := m.runtime.HeartbeatCount
	m.runtime.HeartbeatCount++
	m.runtime.TotalHeartbeats++

	// 获取当前节点
	currentNode := m.getCurrentNode()
	if currentNode == nil {
		return fmt.Errorf("当前节点不存在: %s", m.runtime.CurrentNodeID)
	}

	fmt.Printf("🔄 处理心跳: %d → %d/%d (step=%d)\n",
		previousCount, m.runtime.HeartbeatCount, currentNode.Config.HeartbeatCount, currentNode.Step)

	// 检查是否达到转换条件
	if m.runtime.HeartbeatCount >= currentNode.Config.HeartbeatCount {
		fmt.Printf("🚀 达到心跳次数要求，执行状态转换...\n")

		// 重置心跳计数
		m.runtime.HeartbeatCount = 0

		// 执行状态转换
		return m.executeTransition()
	} else {
		remaining := currentNode.Config.HeartbeatCount - m.runtime.HeartbeatCount
		fmt.Printf("⏳ 还需要 %d 次心跳才能转换状态\n", remaining)
	}

	return nil
}

// getCurrentNode 获取当前节点
func (m *TaskFlowManager) getCurrentNode() *StateNode {
	for i := range m.configData.StateNodes {
		if m.configData.StateNodes[i].ID == m.runtime.CurrentNodeID {
			return &m.configData.StateNodes[i]
		}
	}
	return nil
}

// executeTransition 执行状态转换
func (m *TaskFlowManager) executeTransition() error {
	// 查找可用的转换
	var availableTransitions []StateTransition
	for _, transition := range m.configData.StateTransitions {
		if transition.FromNodeID == m.runtime.CurrentNodeID {
			availableTransitions = append(availableTransitions, transition)
		}
	}

	if len(availableTransitions) == 0 {
		fmt.Printf("⚠️ 没有找到可用的状态转换\n")
		return nil
	}

	// 选择第一个转换（简化版本，后续可以支持概率选择）
	selectedTransition := availableTransitions[0]

	// 查找目标节点
	var targetNode *StateNode
	for i := range m.configData.StateNodes {
		if m.configData.StateNodes[i].ID == selectedTransition.ToNodeID {
			targetNode = &m.configData.StateNodes[i]
			break
		}
	}

	if targetNode == nil {
		return fmt.Errorf("目标节点不存在: %s", selectedTransition.ToNodeID)
	}

	// 执行状态转换
	previousStep := m.runtime.CurrentStep
	m.runtime.CurrentNodeID = targetNode.ID
	m.runtime.CurrentStep = targetNode.Step
	m.runtime.LastTransitionTime = time.Now().Unix()

	fmt.Printf("🎯 执行状态转换: step %d → %d (%s)\n",
		previousStep, targetNode.Step, selectedTransition.Description)

	return nil
}

// GetCurrentTaskInfo 获取当前任务信息
func (m *TaskFlowManager) GetCurrentTaskInfo() TaskInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 从任务配置中获取信息
	taskID := 1
	taskDistance := 360
	if m.configData.TaskConfig.TaskID > 0 {
		taskID = m.configData.TaskConfig.TaskID
	}
	if m.configData.TaskConfig.TaskDistance > 0 {
		taskDistance = m.configData.TaskConfig.TaskDistance
	}

	return TaskInfo{
		Step:         m.runtime.CurrentStep,
		TaskID:       taskID,
		TaskDistance: taskDistance,
	}
}

// GetRuntimeState 获取运行时状态
func (m *TaskFlowManager) GetRuntimeState() RuntimeState {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.runtime
}

// saveToFile 保存配置到文件
func (m *TaskFlowManager) saveToFile() error {
	// 确保目录存在
	dir := filepath.Dir(m.configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 序列化配置数据
	data, err := json.MarshalIndent(m.configData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(m.configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	fmt.Printf("📁 配置已保存到: %s\n", m.configPath)
	return nil
}

// LoadConfig 从文件加载配置
func (m *TaskFlowManager) LoadConfig() error {
	// 检查文件是否存在
	if _, err := os.Stat(m.configPath); os.IsNotExist(err) {
		fmt.Printf("📁 配置文件不存在，使用默认配置: %s\n", m.configPath)
		// 使用默认配置
		m.configData = m.getDefaultConfig()
		return m.saveToFile()
	}

	// 读取文件
	data, err := os.ReadFile(m.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 反序列化
	if err := json.Unmarshal(data, &m.configData); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	fmt.Printf("📁 配置已从文件加载: %s\n", m.configPath)
	return nil
}

// getDefaultConfig 获取默认配置
func (m *TaskFlowManager) getDefaultConfig() TaskFlowData {
	return TaskFlowData{
		TaskFlowConfig: TaskFlowConfig{
			ID:               "default_flow",
			Name:             "默认任务流程",
			Description:      "默认的任务状态流程配置",
			HeartbeatInterval: 2000,
			TriggerMode:      "heartbeat_count",
		},
		StateNodes: []StateNode{
			{
				ID:   "node_0",
				Step: 0,
				Name: "待命状态",
				Config: struct {
					HeartbeatCount int `json:"heartbeatCount"`
				}{HeartbeatCount: 2},
				Position: struct {
					X float64 `json:"x"`
					Y float64 `json:"y"`
				}{X: 100, Y: 100},
			},
		},
		StateTransitions: []StateTransition{},
		TaskConfig: TaskConfig{
			TaskID:          1,
			RouteID:         1,
			TaskName:        "默认任务",
			TaskDescription: "默认任务描述",
			TaskDistance:    360,
			LeftTime:        5,
		},
	}
}

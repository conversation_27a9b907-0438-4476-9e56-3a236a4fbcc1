package taskflow

// TaskFlowData 前后端交换的任务流程数据格式
type TaskFlowData struct {
	// 基础配置
	TaskFlowConfig TaskFlowConfig `json:"taskFlowConfig"`
	
	// 状态节点
	StateNodes []StateNode `json:"stateNodes"`
	
	// 状态转换
	StateTransitions []StateTransition `json:"stateTransitions"`
	
	// 任务配置
	TaskConfig TaskConfig `json:"taskConfig"`
}

// TaskFlowConfig 任务流程配置
type TaskFlowConfig struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Description      string `json:"description"`
	HeartbeatInterval int   `json:"heartbeatInterval"` // 毫秒
	TriggerMode      string `json:"triggerMode"`       // "heartbeat_count"
}

// StateNode 状态节点
type StateNode struct {
	ID     string `json:"id"`
	Step   int    `json:"step"`
	Name   string `json:"name"`
	Config struct {
		HeartbeatCount int `json:"heartbeatCount"`
	} `json:"config"`
	Position struct {
		X float64 `json:"x"`
		Y float64 `json:"y"`
	} `json:"position"`
}

// StateTransition 状态转换
type StateTransition struct {
	ID           string  `json:"id"`
	FromNodeID   string  `json:"fromNodeId"`
	ToNodeID     string  `json:"toNodeId"`
	Description  string  `json:"description"`
	Probability  float64 `json:"probability"`
	TriggerType  string  `json:"triggerType"`
	TriggerValue int     `json:"triggerValue"`
}

// TaskConfig 任务配置
type TaskConfig struct {
	TaskID          int    `json:"taskId"`
	RouteID         int    `json:"routeId"`
	TaskName        string `json:"taskName"`
	TaskDescription string `json:"taskDescription"`
	TaskDistance    int    `json:"taskDistance"`
	LeftTime        int    `json:"leftTime"`
	CurrentStation  struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"currentStation"`
	TargetStation struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"targetStation"`
}

// RuntimeState 运行时状态
type RuntimeState struct {
	IsRunning           bool   `json:"isRunning"`
	CurrentNodeID       string `json:"currentNodeId"`
	CurrentStep         int    `json:"currentStep"`
	HeartbeatCount      int    `json:"heartbeatCount"`
	TotalHeartbeats     int    `json:"totalHeartbeats"`
	LastTransitionTime  int64  `json:"lastTransitionTime"`
	StartTime           int64  `json:"startTime"`
}

// TaskInfo 当前任务信息（用于心跳数据）
type TaskInfo struct {
	Step         int `json:"step"`
	TaskID       int `json:"taskId"`
	TaskDistance int `json:"taskDistance"`
}

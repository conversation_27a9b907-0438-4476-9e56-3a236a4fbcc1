# TCP客户端测试工具 (TCPTestBench)

## 项目概述

TCPTestBench是一个专为ccserver项目开发的TCP客户端测试工具，基于Wails + Vue 3架构，能够完整模拟设备与ccserver的真实交互。

## 功能特性

### ✅ 已实现功能

#### 1. 完整的YHL协议支持
- **协议版本**: 支持V1(明文)和V2(加密)协议
- **消息封装**: 完整的消息头构建和CRC16校验
- **加密支持**: SM4-GCM加密算法实现
- **消息解析**: 响应消息的完整解析

#### 2. TCP客户端功能
- **连接管理**: TCP连接建立、维护、断开
- **状态监控**: 实时连接状态和错误处理
- **消息队列**: 异步发送和接收消息处理
- **统计信息**: 详细的连接和消息统计
- **事件回调**: 完整的事件通知机制

#### 3. 设备模拟功能
- **设备注册**: 完整的设备注册流程模拟
- **心跳发送**: 支持所有心跳字段的动态配置
- **任务模拟**: 任务状态变化和生命周期模拟
- **自动心跳**: 可配置间隔的自动心跳发送
- **位置模拟**: 支持设备移动轨迹模拟

#### 4. 用户界面
- **现代化UI**: 基于Element Plus的暗色主题
- **快速测试**: 一体化的测试面板
- **实时监控**: 连接状态和统计信息实时显示
- **操作日志**: 详细的操作和错误日志记录

## 快速开始

### 1. 环境要求
- Go 1.19+
- Node.js 16+
- Wails v2.10.1+

### 2. 安装依赖
```bash
# 安装Go依赖
go mod tidy

# 安装前端依赖
cd frontend && npm install
```

### 3. 运行开发模式
```bash
wails dev
```

### 4. 构建发布版本
```bash
wails build
```

## 使用指南

### 快速测试模式
1. 启动应用后，点击顶部的"快速测试"按钮
2. 配置服务器连接参数（主机、端口等）
3. 设置设备信息（IMSI、IMEI、位置等）
4. 点击"创建连接"建立TCP客户端
5. 点击"连接"连接到ccserver
6. 发送注册消息进行设备注册
7. 发送心跳消息或启动自动心跳
8. 监控连接状态和统计信息

## 核心文件说明

### 后端文件
- `app.go`: Wails应用主文件，提供前后端API接口
- `protocol/yhl_client.go`: YHL协议客户端实现
- `protocol/messages.go`: 消息结构定义和构建器
- `client/tcp_client.go`: TCP客户端核心实现

### 前端文件
- `frontend/src/App.vue`: 主应用组件
- `frontend/src/components/TestPanel.vue`: 快速测试面板
- `frontend/src/stores/`: Pinia状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLoggerStore = defineStore('logger', () => {
  // 日志数据
  const logs = ref([])
  const maxLogs = ref(1000)
  const autoScroll = ref(true)
  const logFilter = ref({
    level: 'all', // all, info, warning, error, debug
    type: 'all',  // all, sent, received, system
    search: ''
  })

  // 日志级别定义
  const logLevels = ref({
    'debug': { name: '调试', color: '#909399', icon: 'bug' },
    'info': { name: '信息', color: '#409EFF', icon: 'info-filled' },
    'warning': { name: '警告', color: '#E6A23C', icon: 'warning-filled' },
    'error': { name: '错误', color: '#F56C6C', icon: 'circle-close-filled' },
    'success': { name: '成功', color: '#67C23A', icon: 'circle-check-filled' }
  })

  // 日志类型定义
  const logTypes = ref({
    'sent': { name: '发送', icon: 'top', color: '#409EFF' },
    'received': { name: '接收', icon: 'bottom', color: '#67C23A' },
    'system': { name: '系统', icon: 'setting', color: '#909399' },
    'connection': { name: '连接', icon: 'link', color: '#E6A23C' }
  })

  // 计算属性
  const filteredLogs = computed(() => {
    let filtered = logs.value

    // 按级别过滤
    if (logFilter.value.level !== 'all') {
      filtered = filtered.filter(log => log.level === logFilter.value.level)
    }

    // 按类型过滤
    if (logFilter.value.type !== 'all') {
      filtered = filtered.filter(log => log.type === logFilter.value.type)
    }

    // 按搜索关键词过滤
    if (logFilter.value.search) {
      const search = logFilter.value.search.toLowerCase()
      filtered = filtered.filter(log => 
        log.message.toLowerCase().includes(search) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(search))
      )
    }

    return filtered
  })

  const logStats = computed(() => {
    const stats = {
      total: logs.value.length,
      info: 0,
      warning: 0,
      error: 0,
      debug: 0,
      success: 0,
      sent: 0,
      received: 0,
      system: 0,
      connection: 0
    }

    logs.value.forEach(log => {
      stats[log.level]++
      stats[log.type]++
    })

    return stats
  })

  // 方法
  const addLog = (level, type, message, data = null) => {
    const log = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      level,
      type,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    }

    logs.value.push(log)

    // 限制日志数量
    if (logs.value.length > maxLogs.value) {
      logs.value = logs.value.slice(-maxLogs.value)
    }

    return log
  }

  // 便捷方法
  const info = (type, message, data) => addLog('info', type, message, data)
  const warning = (type, message, data) => addLog('warning', type, message, data)
  const error = (type, message, data) => addLog('error', type, message, data)
  const debug = (type, message, data) => addLog('debug', type, message, data)
  const success = (type, message, data) => addLog('success', type, message, data)

  // 特定类型的日志方法
  const logMessageSent = (command, data) => {
    const message = `发送指令: ${command.name || command} (cmd: 0x${command.cmd?.toString(16).toUpperCase() || 'unknown'})`
    return info('sent', message, data)
  }

  const logMessageReceived = (data) => {
    const message = `收到响应: ${data.res !== undefined ? (data.res === 0 ? '成功' : '失败') : '数据'}`
    return info('received', message, data)
  }

  const logConnectionEvent = (event, details) => {
    let level = 'info'
    let message = ''

    switch (event) {
      case 'connecting':
        message = `正在连接服务器 ${details.host}:${details.port}`
        break
      case 'connected':
        level = 'success'
        message = `已连接到服务器 ${details.host}:${details.port}`
        break
      case 'disconnected':
        level = 'warning'
        message = `与服务器断开连接`
        break
      case 'error':
        level = 'error'
        message = `连接错误: ${details.error}`
        break
      default:
        message = `连接事件: ${event}`
    }

    return addLog(level, 'connection', message, details)
  }

  const logSystemEvent = (event, details) => {
    return info('system', event, details)
  }

  // 过滤器方法
  const updateFilter = (filter) => {
    logFilter.value = { ...logFilter.value, ...filter }
  }

  const clearFilter = () => {
    logFilter.value = {
      level: 'all',
      type: 'all',
      search: ''
    }
  }

  // 日志管理
  const clearLogs = () => {
    logs.value = []
  }

  const exportLogs = (format = 'json') => {
    const exportData = {
      exportTime: new Date().toISOString(),
      totalLogs: logs.value.length,
      logs: filteredLogs.value
    }

    if (format === 'json') {
      return JSON.stringify(exportData, null, 2)
    } else if (format === 'csv') {
      const headers = ['时间', '级别', '类型', '消息', '数据']
      const rows = filteredLogs.value.map(log => [
        log.timestamp.toISOString(),
        log.level,
        log.type,
        log.message,
        log.data ? JSON.stringify(log.data) : ''
      ])
      
      return [headers, ...rows].map(row => 
        row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',')
      ).join('\n')
    }

    return exportData
  }

  const setMaxLogs = (max) => {
    maxLogs.value = max
    if (logs.value.length > max) {
      logs.value = logs.value.slice(-max)
    }
  }

  const toggleAutoScroll = () => {
    autoScroll.value = !autoScroll.value
  }

  // 格式化方法
  const formatLogData = (data) => {
    if (!data) return ''
    try {
      return JSON.stringify(data, null, 2)
    } catch (error) {
      return data.toString()
    }
  }

  const formatTimestamp = (timestamp) => {
    return timestamp.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    })
  }

  return {
    // 状态
    logs,
    maxLogs,
    autoScroll,
    logFilter,
    logLevels,
    logTypes,
    
    // 计算属性
    filteredLogs,
    logStats,
    
    // 基础方法
    addLog,
    info,
    warning,
    error,
    debug,
    success,
    
    // 特定日志方法
    logMessageSent,
    logMessageReceived,
    logConnectionEvent,
    logSystemEvent,
    
    // 过滤器方法
    updateFilter,
    clearFilter,
    
    // 管理方法
    clearLogs,
    exportLogs,
    setMaxLogs,
    toggleAutoScroll,
    
    // 格式化方法
    formatLogData,
    formatTimestamp
  }
})

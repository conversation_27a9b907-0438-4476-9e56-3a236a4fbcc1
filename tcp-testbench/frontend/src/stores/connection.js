import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useConnectionStore = defineStore('connection', () => {
  // 连接状态
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionError = ref('')
  const lastConnectedTime = ref(null)
  const connectionDuration = ref(0)
  const activeConnectionId = ref('')

  // 服务器配置
  const serverConfig = ref({
    host: '**************',
    port: 2020,
    timeout: 5000,
    keepaliveTimeout: 150000
  })

  // 连接统计
  const stats = ref({
    totalConnections: 0,
    successfulConnections: 0,
    failedConnections: 0,
    totalMessagesSent: 0,
    totalMessagesReceived: 0,
    averageLatency: 0
  })

  // 计算属性
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting'
    if (isConnected.value) return 'connected'
    if (connectionError.value) return 'error'
    return 'disconnected'
  })

  const connectionStatusText = computed(() => {
    switch (connectionStatus.value) {
      case 'connecting': return '连接中...'
      case 'connected': return '已连接'
      case 'error': return '连接失败'
      default: return '未连接'
    }
  })

  const successRate = computed(() => {
    if (stats.value.totalConnections === 0) return 0
    return ((stats.value.successfulConnections / stats.value.totalConnections) * 100).toFixed(1)
  })

  // 方法
  const updateServerConfig = (config) => {
    serverConfig.value = { ...serverConfig.value, ...config }
    saveToLocalStorage()
  }

  const setConnecting = (connecting) => {
    isConnecting.value = connecting
    if (connecting) {
      connectionError.value = ''
    }
  }

  const setConnected = (connected) => {
    isConnected.value = connected
    if (connected) {
      lastConnectedTime.value = new Date()
      stats.value.totalConnections++
      stats.value.successfulConnections++
      connectionError.value = ''
    }
    isConnecting.value = false
    saveToLocalStorage()
  }

  const setConnectionError = (error) => {
    connectionError.value = error
    isConnected.value = false
    isConnecting.value = false
    stats.value.totalConnections++
    stats.value.failedConnections++
    saveToLocalStorage()
  }

  const setActiveConnectionId = (connectionId) => {
    activeConnectionId.value = connectionId
    saveToLocalStorage()
  }

  const incrementMessageSent = () => {
    stats.value.totalMessagesSent++
  }

  const incrementMessageReceived = () => {
    stats.value.totalMessagesReceived++
  }

  const updateLatency = (latency) => {
    const total = stats.value.totalMessagesReceived
    if (total === 0) {
      stats.value.averageLatency = latency
    } else {
      stats.value.averageLatency = ((stats.value.averageLatency * (total - 1)) + latency) / total
    }
  }

  const resetStats = () => {
    stats.value = {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      totalMessagesSent: 0,
      totalMessagesReceived: 0,
      averageLatency: 0
    }
    saveToLocalStorage()
  }

  // 本地存储
  const saveToLocalStorage = () => {
    const data = {
      serverConfig: serverConfig.value,
      stats: stats.value
    }
    localStorage.setItem('tcp-debugger-connection', JSON.stringify(data))
  }

  const loadFromLocalStorage = () => {
    try {
      const data = localStorage.getItem('tcp-debugger-connection')
      if (data) {
        const parsed = JSON.parse(data)
        if (parsed.serverConfig) {
          serverConfig.value = { ...serverConfig.value, ...parsed.serverConfig }
        }
        if (parsed.stats) {
          stats.value = { ...stats.value, ...parsed.stats }
        }
      }
    } catch (error) {
      console.error('Failed to load connection data from localStorage:', error)
    }
  }

  // 延迟加载数据以提升性能
  setTimeout(() => {
    loadFromLocalStorage()
  }, 0)

  return {
    // 状态
    isConnected,
    isConnecting,
    connectionError,
    lastConnectedTime,
    connectionDuration,
    activeConnectionId,
    serverConfig,
    stats,

    // 计算属性
    connectionStatus,
    connectionStatusText,
    successRate,

    // 方法
    updateServerConfig,
    setConnecting,
    setConnected,
    setConnectionError,
    setActiveConnectionId,
    incrementMessageSent,
    incrementMessageReceived,
    updateLatency,
    resetStats,
    saveToLocalStorage,
    loadFromLocalStorage
  }
})

import { defineStore } from 'pinia'
import { ref, computed, nextTick } from 'vue'
import { useHeartbeatStore } from './heartbeat'
import backendLogger from '../utils/backendLogger'

export const useTaskFlowStore = defineStore('taskFlow', () => {
  // 任务流配置
  const taskFlowConfig = ref({
    id: '',
    name: '默认任务流',
    description: '',
    triggerMode: 'heartbeat_count', // 'heartbeat_count' | 'time_interval' | 'manual'
    heartbeatInterval: 5000, // 心跳间隔(ms)
    isActive: false,
    createdAt: null,
    updatedAt: null
  })

  // 状态节点定义
  const stateNodes = ref([
    {
      id: 'node_0',
      step: 0,
      name: '未开始',
      description: '任务准备阶段',
      color: '#909399',
      position: { x: 100, y: 100 },
      config: {
        heartbeatCount: 2,     // 心跳次数触发
        conditions: []         // 触发条件
      }
    },
    {
      id: 'node_1',
      step: 1,
      name: '正在执行',
      description: '任务执行中',
      color: '#409EFF',
      position: { x: 300, y: 100 },
      config: {
        heartbeatCount: 10,
        conditions: []
      }
    },
    {
      id: 'node_2',
      step: 2,
      name: '已完成',
      description: '任务成功完成',
      color: '#67C23A',
      position: { x: 500, y: 100 },
      config: {
        heartbeatCount: 1,
        conditions: []
      }
    },
    {
      id: 'node_9',
      step: 9,
      name: '执行失败',
      description: '任务执行失败',
      color: '#F56C6C',
      position: { x: 300, y: 250 },
      config: {
        heartbeatCount: 1,
        conditions: []
      }
    }
  ])

  // 状态转换连接
  const stateTransitions = ref([
    {
      id: 'trans_0_1',
      fromNodeId: 'node_0',
      toNodeId: 'node_1',
      probability: 1.0,        // 转换概率 (0-1)
      conditions: [],          // 转换条件
      delay: 0,               // 转换延迟(ms)
      description: '开始执行'
    },
    {
      id: 'trans_1_2',
      fromNodeId: 'node_1',
      toNodeId: 'node_2',
      probability: 0.8,
      conditions: [],
      delay: 1000,
      description: '正常完成'
    },
    {
      id: 'trans_1_9',
      fromNodeId: 'node_1',
      toNodeId: 'node_9',
      probability: 0.2,
      conditions: [],
      delay: 500,
      description: '执行失败'
    }
  ])

  // 运行时状态
  const runtimeState = ref({
    currentNodeId: 'node_0',
    currentStep: 0,
    startTime: null,
    lastTransitionTime: null,
    heartbeatCount: 0,
    totalHeartbeats: 0,
    executionHistory: [],
    nextTransitionTime: null,
    isRunning: false
  })

  // 统计信息
  const statistics = ref({
    totalExecutions: 0,
    successfulCompletions: 0,
    failures: 0,
    averageExecutionTime: 0,
    stateDistribution: {},
    transitionCounts: {}
  })

  // 计算属性
  const currentNode = computed(() => {
    return stateNodes.value.find(node => node.id === runtimeState.value.currentNodeId)
  })

  const availableTransitions = computed(() => {
    return stateTransitions.value.filter(trans => 
      trans.fromNodeId === runtimeState.value.currentNodeId
    )
  })

  const executionProgress = computed(() => {
    if (!runtimeState.value.startTime) return 0
    const elapsed = Date.now() - runtimeState.value.startTime
    const estimated = estimateExecutionTime()
    return Math.min(100, (elapsed / estimated) * 100)
  })

  // 核心方法
  const startTaskFlow = async () => {
    runtimeState.value.isRunning = true
    runtimeState.value.startTime = Date.now()
    runtimeState.value.heartbeatCount = 0
    runtimeState.value.totalHeartbeats = 0
    runtimeState.value.executionHistory = []
    
    // 重置到初始状态
    const initialNode = stateNodes.value[0]
    runtimeState.value.currentNodeId = initialNode.id
    runtimeState.value.currentStep = initialNode.step

    // 初始化心跳数据中的任务信息（强制重置，忽略localStorage缓存）
    try {
      const heartbeatStore = useHeartbeatStore()
      if (heartbeatStore && heartbeatStore.updateTaskInfo) {
        await backendLogger.taskFlow(`任务流程启动：强制重置step为${initialNode.step}`)
        heartbeatStore.updateTaskInfo({
          step: initialNode.step,
          taskId: taskFlowConfig.value.id ? parseInt(taskFlowConfig.value.id.replace(/\D/g, '')) || 1 : 1,
          taskDistance: 360
        })
        // 立即保存到localStorage，确保覆盖旧数据
        heartbeatStore.saveToLocalStorage()
        await backendLogger.taskFlow('心跳数据初始化完成')
      }
    } catch (error) {
      await backendLogger.error('初始化心跳任务信息失败', error.message)
    }

    scheduleNextTransition()
    logStateChange('任务流开始', initialNode.step)
  }

  const stopTaskFlow = () => {
    runtimeState.value.isRunning = false
    updateStatistics()

    // 重置心跳数据中的任务信息
    try {
      const heartbeatStore = useHeartbeatStore()
      if (heartbeatStore && heartbeatStore.updateTaskInfo) {
        heartbeatStore.updateTaskInfo({
          step: 0,
          taskId: 0,
          taskDistance: 360
        })
      }
    } catch (error) {
      console.warn('重置心跳任务信息失败:', error)
    }
  }

  const pauseTaskFlow = () => {
    runtimeState.value.isRunning = false
  }

  const resumeTaskFlow = () => {
    runtimeState.value.isRunning = true
    scheduleNextTransition()
  }

  // 状态转换核心逻辑

  const scheduleNextTransition = () => {
    if (!runtimeState.value.isRunning) return

    const currentNode = getCurrentNode()
    const transitions = getAvailableTransitions()

    if (transitions.length === 0) {
      // 没有可用转换，结束流程
      stopTaskFlow()
      return
    }

    // 状态转换完全基于心跳计数，不使用时间延迟
    // 转换将在onHeartbeatSent()中检查心跳计数后触发
    console.log(`等待 ${currentNode.config.heartbeatCount} 次心跳后转换状态`)
  }

  const executeTransition = async () => {
    const transitions = getAvailableTransitions()
    if (transitions.length === 0) return

    // 根据概率选择转换
    const selectedTransition = selectTransitionByProbability(transitions)
    if (!selectedTransition) return

    const targetNode = stateNodes.value.find(node => node.id === selectedTransition.toNodeId)
    if (!targetNode) return

    // 执行状态转换
    const previousStep = runtimeState.value.currentStep
    await backendLogger.taskFlow(`🎯 执行状态转换: step ${previousStep} → ${targetNode.step} (${selectedTransition.description})`)

    runtimeState.value.currentNodeId = targetNode.id
    runtimeState.value.currentStep = targetNode.step
    runtimeState.value.lastTransitionTime = Date.now()

    // 更新心跳数据中的任务信息
    try {
      const heartbeatStore = useHeartbeatStore()
      if (heartbeatStore && heartbeatStore.updateTaskInfo) {
        await backendLogger.taskFlow(`📝 更新心跳数据: step=${targetNode.step}, taskId=${taskFlowConfig.value.id}`)
        heartbeatStore.updateTaskInfo({
          step: targetNode.step,
          taskId: taskFlowConfig.value.id ? parseInt(taskFlowConfig.value.id.replace(/\D/g, '')) || 1 : 1,
          taskDistance: 360 // 可以根据实际需求调整
        })
        // 立即保存到localStorage
        heartbeatStore.saveToLocalStorage()
        await backendLogger.taskFlow('✅ 心跳数据更新完成')
      }
    } catch (error) {
      await backendLogger.error('更新心跳任务信息失败', error.message)
    }

    // 记录转换历史
    logStateChange(`${previousStep} → ${targetNode.step}`, targetNode.step, selectedTransition.description)

    // 更新统计
    updateTransitionCount(selectedTransition.id)

    // 调度下一次转换
    setTimeout(() => {
      scheduleNextTransition()
    }, selectedTransition.delay)
  }

  // 辅助方法
  const getCurrentNode = () => {
    return stateNodes.value.find(node => node.id === runtimeState.value.currentNodeId)
  }

  const getAvailableTransitions = () => {
    return stateTransitions.value.filter(trans => 
      trans.fromNodeId === runtimeState.value.currentNodeId
    )
  }

  const selectTransitionByProbability = (transitions) => {
    const random = Math.random()
    let cumulative = 0
    
    for (const transition of transitions) {
      cumulative += transition.probability
      if (random <= cumulative) {
        return transition
      }
    }
    
    return transitions[0] // 默认返回第一个
  }



  const logStateChange = (description, step, details = '') => {
    const logEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      description,
      step,
      details,
      heartbeatCount: runtimeState.value.heartbeatCount
    }
    
    runtimeState.value.executionHistory.push(logEntry)
    
    // 限制历史记录数量
    if (runtimeState.value.executionHistory.length > 100) {
      runtimeState.value.executionHistory.shift()
    }
  }

  const updateTransitionCount = (transitionId) => {
    if (!statistics.value.transitionCounts[transitionId]) {
      statistics.value.transitionCounts[transitionId] = 0
    }
    statistics.value.transitionCounts[transitionId]++
  }

  const updateStatistics = () => {
    statistics.value.totalExecutions++
    
    if (runtimeState.value.currentStep === 2) {
      statistics.value.successfulCompletions++
    } else if (runtimeState.value.currentStep === 9) {
      statistics.value.failures++
    }
    
    // 计算平均执行时间
    if (runtimeState.value.startTime) {
      const duration = Date.now() - runtimeState.value.startTime
      statistics.value.averageExecutionTime = 
        (statistics.value.averageExecutionTime * (statistics.value.totalExecutions - 1) + duration) / 
        statistics.value.totalExecutions
    }
  }

  const estimateExecutionTime = () => {
    // 基于心跳次数和心跳间隔估算总执行时间
    let totalHeartbeats = 0
    for (const node of stateNodes.value) {
      totalHeartbeats += node.config.heartbeatCount
    }
    return totalHeartbeats * taskFlowConfig.value.heartbeatInterval
  }

  // 心跳触发处理
  const onHeartbeatSent = async () => {
    if (!runtimeState.value.isRunning) {
      await backendLogger.warn('任务流程未运行，忽略心跳通知')
      return
    }

    const previousHeartbeatCount = runtimeState.value.heartbeatCount
    runtimeState.value.heartbeatCount++
    runtimeState.value.totalHeartbeats++

    // 检查是否达到当前节点的心跳次数要求
    const currentNode = getCurrentNode()
    await backendLogger.taskFlow(`处理心跳: ${previousHeartbeatCount} → ${runtimeState.value.heartbeatCount}/${currentNode.config.heartbeatCount} (step=${currentNode.step})`)

    if (runtimeState.value.heartbeatCount >= currentNode.config.heartbeatCount) {
      await backendLogger.taskFlow('🚀 达到心跳次数要求，执行状态转换...')
      // 重置心跳计数并执行状态转换
      runtimeState.value.heartbeatCount = 0
      await executeTransition()
    } else {
      await backendLogger.taskFlow(`⏳ 还需要 ${currentNode.config.heartbeatCount - runtimeState.value.heartbeatCount} 次心跳才能转换状态`)
    }
  }

  return {
    // 状态
    taskFlowConfig,
    stateNodes,
    stateTransitions,
    runtimeState,
    statistics,
    
    // 计算属性
    currentNode,
    availableTransitions,
    executionProgress,
    
    // 方法
    startTaskFlow,
    stopTaskFlow,
    pauseTaskFlow,
    resumeTaskFlow,
    onHeartbeatSent,
    
    // 配置方法
    updateTaskFlowConfig: (config) => {
      taskFlowConfig.value = { ...taskFlowConfig.value, ...config }
    },
    
    addStateNode: (node) => {
      console.log('addStateNode called with:', node)
      console.log('Current stateNodes before add:', stateNodes.value)
      stateNodes.value.push({ ...node, id: `node_${Date.now()}` })
      console.log('Current stateNodes after add:', stateNodes.value)
    },
    
    updateStateNode: (nodeId, updates) => {
      const index = stateNodes.value.findIndex(node => node.id === nodeId)
      if (index !== -1) {
        stateNodes.value[index] = { ...stateNodes.value[index], ...updates }
      }
    },
    
    removeStateNode: (nodeId) => {
      stateNodes.value = stateNodes.value.filter(node => node.id !== nodeId)
      stateTransitions.value = stateTransitions.value.filter(
        trans => trans.fromNodeId !== nodeId && trans.toNodeId !== nodeId
      )
    },
    
    addTransition: (transition) => {
      stateTransitions.value.push({ ...transition, id: `trans_${Date.now()}` })
    },
    
    updateTransition: (transitionId, updates) => {
      console.log('Store updateTransition called:', transitionId, updates)
      const index = stateTransitions.value.findIndex(trans => trans.id === transitionId)
      console.log('Found transition at index:', index)
      if (index !== -1) {
        const oldTransition = stateTransitions.value[index]
        console.log('Old transition:', oldTransition)
        stateTransitions.value[index] = { ...stateTransitions.value[index], ...updates }
        console.log('New transition:', stateTransitions.value[index])
      } else {
        console.log('Transition not found!')
      }
    },
    
    removeTransition: (transitionId) => {
      stateTransitions.value = stateTransitions.value.filter(trans => trans.id !== transitionId)
    },

    // 配置导入导出方法
    loadStateNodes: (nodes) => {
      stateNodes.value = nodes.map(node => ({ ...node }))
    },

    loadStateTransitions: (transitions) => {
      stateTransitions.value = transitions.map(trans => ({ ...trans }))
    },

    updateTaskFlowConfig: (config) => {
      Object.assign(taskFlowConfig.value, config)
    }
  }
})

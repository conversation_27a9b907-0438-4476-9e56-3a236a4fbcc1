import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useDeviceStore = defineStore('device', () => {
  // 设备配置
  const deviceConfig = ref({
    imsi: 'TEST001',
    imei: '123456789012345',
    model: 'PIX_ROBOT_V2',
    softwareVer: '1.3.6',
    hardwareVer: '2.1.0',
    vendor: 'PIXMOVING',
    ci: '12345',
    pci: '67890',
    manageIp: '*************',
    lng: 113.91040802001953,
    lat: 22.487064361572266
  })

  // 设备模板
  const deviceTemplates = ref({
    'cleaning_robot': {
      name: '清扫机器人',
      model: 'PIX_CLEAN_V2',
      vType: 1,
      defaultSpeed: 15,
      maxSpeed: 25,
      batteryCapacity: 100
    },
    'delivery_robot': {
      name: '配送机器人',
      model: 'PIX_DELIVERY_V2',
      vType: 2,
      defaultSpeed: 20,
      maxSpeed: 30,
      batteryCapacity: 80
    },
    'patrol_robot': {
      name: '巡逻机器人',
      model: 'PIX_PATROL_V2',
      vType: 3,
      defaultSpeed: 10,
      maxSpeed: 20,
      batteryCapacity: 120
    }
  })

  // 当前选择的模板
  const selectedTemplate = ref('cleaning_robot')

  // 计算属性
  const currentTemplate = computed(() => {
    return deviceTemplates.value[selectedTemplate.value] || deviceTemplates.value.cleaning_robot
  })

  const deviceInfo = computed(() => {
    return {
      ...deviceConfig.value,
      template: currentTemplate.value
    }
  })

  // 方法
  const updateDeviceConfig = (config) => {
    deviceConfig.value = { ...deviceConfig.value, ...config }
    saveToLocalStorage()
  }

  const applyTemplate = (templateKey) => {
    const template = deviceTemplates.value[templateKey]
    if (template) {
      selectedTemplate.value = templateKey
      updateDeviceConfig({
        model: template.model
      })
    }
  }

  const generateRandomDevice = () => {
    const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
    updateDeviceConfig({
      imsi: `TEST${randomSuffix}`,
      imei: `${Math.floor(Math.random() * 900000000000000) + 100000000000000}`,
      ci: Math.floor(Math.random() * 90000) + 10000,
      pci: Math.floor(Math.random() * 90000) + 10000
    })
  }

  const resetToDefaults = () => {
    deviceConfig.value = {
      imsi: 'TEST001',
      imei: '123456789012345',
      model: 'PIX_ROBOT_V2',
      softwareVer: '1.3.6',
      hardwareVer: '2.1.0',
      vendor: 'PIXMOVING',
      ci: '12345',
      pci: '67890',
      manageIp: '*************',
      lng: 113.91040802001953,
      lat: 22.487064361572266
    }
    selectedTemplate.value = 'cleaning_robot'
    saveToLocalStorage()
  }

  // 本地存储
  const saveToLocalStorage = () => {
    const data = {
      deviceConfig: deviceConfig.value,
      selectedTemplate: selectedTemplate.value
    }
    localStorage.setItem('tcp-debugger-device', JSON.stringify(data))
  }

  const loadFromLocalStorage = () => {
    try {
      const data = localStorage.getItem('tcp-debugger-device')
      if (data) {
        const parsed = JSON.parse(data)
        if (parsed.deviceConfig) {
          deviceConfig.value = { ...deviceConfig.value, ...parsed.deviceConfig }
        }
        if (parsed.selectedTemplate) {
          selectedTemplate.value = parsed.selectedTemplate
        }
      }
    } catch (error) {
      console.error('Failed to load device data from localStorage:', error)
    }
  }

  // 延迟加载数据以提升性能
  setTimeout(() => {
    loadFromLocalStorage()
  }, 0)

  return {
    // 状态
    deviceConfig,
    deviceTemplates,
    selectedTemplate,
    
    // 计算属性
    currentTemplate,
    deviceInfo,
    
    // 方法
    updateDeviceConfig,
    applyTemplate,
    generateRandomDevice,
    resetToDefaults,
    saveToLocalStorage,
    loadFromLocalStorage
  }
})

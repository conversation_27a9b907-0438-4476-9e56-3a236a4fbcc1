import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useTaskFlowStore } from './taskFlow'

export const useHeartbeatStore = defineStore('heartbeat', () => {
  // 心跳配置
  const heartbeatConfig = ref({
    interval: 2000, // 2秒
    autoSend: false,
    template: 'normal_driving'
  })

  // 心跳状态
  const isHeartbeatActive = ref(false)
  const heartbeatCount = ref(0)
  const lastHeartbeatTime = ref(null)

  // 心跳数据模板
  const heartbeatTemplates = ref({
    'normal_driving': {
      name: '正常行驶',
      data: {
        vType: 0,
        acc: 1,
        gear: 1,
        speed: 15,
        battery: 85,
        charging: 0,
        locked: 0,
        headLight: 1,
        located: 1,
        gpsLocated: 1,
        adLocated: 1
      }
    },
    'charging': {
      name: '充电中',
      data: {
        vType: 0,
        acc: 0,
        gear: 0,
        speed: 0,
        battery: 65,
        charging: 1,
        locked: 1,
        headLight: 0,
        located: 1,
        gpsLocated: 1,
        adLocated: 0
      }
    },
    'standby': {
      name: '待机状态',
      data: {
        vType: 0,
        acc: 0,
        gear: 0,
        speed: 0,
        battery: 95,
        charging: 0,
        locked: 1,
        headLight: 0,
        located: 1,
        gpsLocated: 1,
        adLocated: 0
      }
    },
    'fault': {
      name: '故障状态',
      data: {
        vType: 0,
        acc: 1,
        gear: 0,
        speed: 0,
        battery: 45,
        charging: 0,
        locked: 0,
        headLight: 1,
        located: 1,
        gpsLocated: 1,
        adLocated: 0,
        event: 3 // 设备断电故障
      }
    }
  })

  // 当前心跳数据（不包含位置信息，位置信息统一从设备配置获取）
  const currentHeartbeatData = ref({
    cmd: 2,
    uid: "213",
    vType: 0,
    acc: 1,
    gear: 0,
    tm: 190,
    speed: 15,
    battery: 85,
    charging: 0,
    locked: 0,
    headLight: 1,
    located: 1,
    gpsLocated: 1,
    adLocated: 1,
    ipcToCha: true,
    ste: 0.0,
    brk: 0.0,
    thr: 0.0,
    headMode: 1,
    latestTask: {
      step: 0,
      taskId: 0,
      taskDistance: 360
    },
    event: 0,
    rsrp: -85,
    sinr: 15,
    rssi: -70,
    rsrq: -10,
    wanRx: 1024000,
    wanTx: 512000
  })

  // 计算属性
  const currentTemplate = computed(() => {
    return heartbeatTemplates.value[heartbeatConfig.value.template] || heartbeatTemplates.value.normal_driving
  })

  const heartbeatRate = computed(() => {
    if (heartbeatCount.value === 0) return 0
    const now = Date.now()
    const duration = lastHeartbeatTime.value ? (now - lastHeartbeatTime.value) / 1000 : 0
    return duration > 0 ? (heartbeatCount.value / duration * 60).toFixed(1) : 0
  })

  // 方法
  const updateHeartbeatConfig = (config) => {
    heartbeatConfig.value = { ...heartbeatConfig.value, ...config }
    saveToLocalStorage()
  }

  const updateHeartbeatData = (data) => {
    currentHeartbeatData.value = { ...currentHeartbeatData.value, ...data }
  }

  const applyTemplate = (templateKey) => {
    const template = heartbeatTemplates.value[templateKey]
    if (template) {
      heartbeatConfig.value.template = templateKey
      updateHeartbeatData(template.data)
      saveToLocalStorage()
    }
  }

  const startHeartbeat = () => {
    isHeartbeatActive.value = true
    heartbeatCount.value = 0
    lastHeartbeatTime.value = Date.now()
  }

  const stopHeartbeat = () => {
    isHeartbeatActive.value = false
  }

  const incrementHeartbeatCount = () => {
    heartbeatCount.value++
    lastHeartbeatTime.value = Date.now()
  }

  const updateLastHeartbeatTime = () => {
    lastHeartbeatTime.value = Date.now()
  }

  const resetHeartbeatStats = () => {
    heartbeatCount.value = 0
    lastHeartbeatTime.value = null
  }

  // 模拟GPS轨迹移动
  const simulateMovement = () => {
    if (isHeartbeatActive.value && currentHeartbeatData.value.speed > 0) {
      // 简单的GPS坐标变化模拟
      const speedKmh = currentHeartbeatData.value.speed
      const speedMs = speedKmh / 3.6 // 转换为米/秒
      const deltaTime = heartbeatConfig.value.interval / 1000 // 秒
      const distance = speedMs * deltaTime // 移动距离（米）
      
      // 假设向东北方向移动（45度）
      const deltaLat = (distance / 111000) * Math.cos(Math.PI / 4) // 纬度变化
      const deltaLng = (distance / (111000 * Math.cos(currentHeartbeatData.value.lat * Math.PI / 180))) * Math.sin(Math.PI / 4) // 经度变化
      
      currentHeartbeatData.value.lat += deltaLat
      currentHeartbeatData.value.lng += deltaLng
      currentHeartbeatData.value.tm += distance / 1000 // 总里程增加
    }
  }

  // 本地存储
  const saveToLocalStorage = () => {
    const data = {
      heartbeatConfig: heartbeatConfig.value,
      currentHeartbeatData: currentHeartbeatData.value
    }
    localStorage.setItem('tcp-debugger-heartbeat', JSON.stringify(data))
  }

  const loadFromLocalStorage = () => {
    try {
      const data = localStorage.getItem('tcp-debugger-heartbeat')
      if (data) {
        const parsed = JSON.parse(data)
        if (parsed.heartbeatConfig) {
          heartbeatConfig.value = { ...heartbeatConfig.value, ...parsed.heartbeatConfig }
        }
        if (parsed.currentHeartbeatData) {
          currentHeartbeatData.value = { ...currentHeartbeatData.value, ...parsed.currentHeartbeatData }
        }
      }
    } catch (error) {
      console.error('Failed to load heartbeat data from localStorage:', error)
    }
  }

  // 任务流集成方法
  const onHeartbeatSent = () => {
    // 通知任务流系统
    try {
      const taskFlowStore = useTaskFlowStore()
      if (taskFlowStore && taskFlowStore.onHeartbeatSent) {
        taskFlowStore.onHeartbeatSent()
      }
    } catch (error) {
      console.warn('任务流系统通知失败:', error)
    }
  }

  // 更新任务信息
  const updateTaskInfo = (taskInfo) => {
    if (taskInfo) {
      currentHeartbeatData.value.latestTask = {
        step: taskInfo.step || 0,
        taskId: taskInfo.taskId || 0,
        taskDistance: taskInfo.taskDistance || 360
      }
      saveToLocalStorage()
    }
  }

  // 重置心跳计数器
  const resetHeartbeatCount = () => {
    heartbeatCount.value = 0
  }

  // 延迟加载数据以提升性能
  setTimeout(() => {
    loadFromLocalStorage()
  }, 0)

  return {
    // 状态
    heartbeatConfig,
    isHeartbeatActive,
    heartbeatCount,
    lastHeartbeatTime,
    heartbeatTemplates,
    currentHeartbeatData,
    
    // 计算属性
    currentTemplate,
    heartbeatRate,
    
    // 方法
    updateHeartbeatConfig,
    updateHeartbeatData,
    applyTemplate,
    startHeartbeat,
    stopHeartbeat,
    incrementHeartbeatCount,
    updateLastHeartbeatTime,
    resetHeartbeatStats,
    simulateMovement,
    saveToLocalStorage,
    loadFromLocalStorage,
    onHeartbeatSent,
    updateTaskInfo,
    resetHeartbeatCount
  }
})

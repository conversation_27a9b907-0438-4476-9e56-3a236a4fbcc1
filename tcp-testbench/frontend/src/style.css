/* TCP Debugger 专业深色主题样式 */
:root {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 深色主题色彩变量 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --bg-hover: #404040;

  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #808080;

  --border-color: #404040;
  --border-hover: #606060;

  --accent-primary: #409EFF;
  --accent-success: #67C23A;
  --accent-warning: #E6A23C;
  --accent-danger: #F56C6C;
  --accent-info: #909399;

  /* 状态指示器颜色 */
  --status-connected: #67C23A;
  --status-connecting: #E6A23C;
  --status-disconnected: #909399;
  --status-error: #F56C6C;

  /* 阴影效果 */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);

  /* 布局变量 */
  --panel-min-height: 300px;
  --layout-spacing: 12px;
  --device-type: desktop;

  color-scheme: dark;
  color: var(--text-primary);
  background-color: var(--bg-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

html {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

body {
  margin: 0;
  padding: 0;
  min-width: 1200px;
  min-height: 100vh;
  background-color: var(--bg-primary);
  overflow: hidden;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@font-face {
    font-family: "Nunito";
    font-style: normal;
    font-weight: 400;
    src: local(""),
    url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

#app {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

/* Element Plus 深色主题覆盖 */
.el-card {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-color) !important;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: auto;
}

.el-card__body {
  flex: 1;
  overflow: visible;
  padding: 16px;
  height: auto;
}

.el-card__header {
  background-color: var(--bg-tertiary) !important;
  border-bottom-color: var(--border-color) !important;
  padding: 12px 16px;
  flex-shrink: 0;
}

.el-input__wrapper {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-color) !important;
}

.el-input__wrapper:hover {
  border-color: var(--border-hover) !important;
}

.el-button {
  border-color: var(--border-color) !important;
}

.el-button:hover {
  border-color: var(--border-hover) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--bg-hover);
  border-radius: 6px;
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}

/* 主应用滚动条特殊样式 */
.app-main::-webkit-scrollbar {
  width: 14px;
}

.app-main::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 7px;
  border: 2px solid var(--bg-primary);
}

.app-main::-webkit-scrollbar-thumb:hover {
  background: var(--accent-success);
}

/* 状态指示器样式 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-connected .status-dot {
  background-color: var(--status-connected);
}

.status-connecting .status-dot {
  background-color: var(--status-connecting);
}

.status-disconnected .status-dot {
  background-color: var(--status-disconnected);
}

.status-error .status-dot {
  background-color: var(--status-error);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 代码/日志显示样式 */
.code-block {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
}

/* 面板布局样式 */
.panel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--layout-spacing);
  padding: var(--layout-spacing);
}

.panel-grid {
  display: grid;
  gap: var(--layout-spacing);
  height: 100%;
}

.panel-grid-2x2 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto 1fr;
}

.panel-grid-3x2 {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto 1fr;
}

/* 性能优化样式 */
.panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: var(--panel-min-height);
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
}

/* 硬件加速 */
.el-card,
.panel-stack,
.main-grid {
  transform: translateZ(0);
  will-change: transform;
}

/* 减少重绘 */
.status-dot {
  contain: layout style paint;
}

.loading-spinner {
  contain: layout style paint;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .panel-grid-3x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
  }
}

@media (max-width: 1000px) {
  .panel-grid,
  .panel-grid-2x2,
  .panel-grid-3x2 {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(auto-fit, minmax(300px, 1fr));
  }
}

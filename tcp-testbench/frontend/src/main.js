import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import App from './App.vue'
import './style.css'

// 移除Vue Flow CSS，使用自制流程图

// 按需导入 Element Plus 组件
import {
  ElConfigProvider,
  ElCard,
  ElButton,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElSwitch,
  ElRadioGroup,
  ElRadio,
  ElRadioButton,
  ElTabs,
  ElTabPane,
  ElTag,
  ElDialog,
  ElCollapse,
  ElCollapseItem,
  ElEmpty,
  ElMessage,
  ElMessageBox,
  ElIcon,
  ElButtonGroup,
  ElColorPicker,
  ElDivider,
  ElSlider,
  ElTimeline,
  ElTimelineItem
} from 'element-plus'

// 按需导入图标
import {
  Monitor, Link, Timer, Connection, Close, Promotion, Refresh,
  Cpu, Iphone, Cellphone, Location, RefreshLeft,
  Operation, DocumentCopy, Delete, View, List,
  Document, Download, Search, InfoFilled, WarningFilled,
  CircleCloseFilled, CircleCheckFilled, Tools, Top, Bottom,
  Setting, QuestionFilled, VideoPlay, VideoPause,
  Stopwatch, TrendCharts, Odometer, Lightning, Plus, Clock
} from '@element-plus/icons-vue'

const app = createApp(App)
const pinia = createPinia()

// 注册组件
const components = [
  ElConfigProvider, ElCard, ElButton, ElInput, ElInputNumber,
  ElSelect, ElOption, ElForm, ElFormItem, ElRow, ElCol,
  ElSwitch, ElRadioGroup, ElRadio, ElRadioButton, ElTabs,
  ElTabPane, ElTag, ElDialog, ElCollapse, ElCollapseItem,
  ElEmpty, ElIcon, ElButtonGroup, ElColorPicker, ElDivider, ElSlider,
  ElTimeline, ElTimelineItem
]

components.forEach(component => {
  app.component(component.name, component)
})

// 注册图标
const icons = {
  Monitor, Link, Timer, Connection, Close, Promotion, Refresh,
  Cpu, Iphone, Cellphone, Location, RefreshLeft,
  Operation, DocumentCopy, Delete, View, List,
  Document, Download, Search, InfoFilled, WarningFilled,
  CircleCloseFilled, CircleCheckFilled, Tools, Top, Bottom,
  Setting, QuestionFilled, VideoPlay, VideoPause,
  Stopwatch, TrendCharts, Odometer, Lightning, Plus, Clock
}

// 注册图标组件
Object.entries(icons).forEach(([key, component]) => {
  app.component(key, component)
})

// 导入统一消息管理器
import messageManager from './utils/message'

// 全局配置 Element Plus 消息组件 - 使用统一消息管理器
app.config.globalProperties.$message = {
  success: (message, duration) => messageManager.success(message, duration),
  error: (message, duration) => messageManager.error(message, duration),
  warning: (message, duration) => messageManager.warning(message, duration),
  info: (message, duration) => messageManager.info(message, duration),
  // 保持向后兼容
  closeAll: () => messageManager.closeCurrentMessage()
}
app.config.globalProperties.$messageBox = ElMessageBox

app.use(pinia)
app.mount('#app')

// 开发环境下导入消息管理器测试工具
if (import.meta.env.DEV) {
  import('./utils/messageTest.js')
}

// 性能监控工具
export class PerformanceMonitor {
  constructor() {
    this.marks = new Map()
    this.measures = new Map()
  }

  // 标记时间点
  mark(name) {
    const timestamp = performance.now()
    this.marks.set(name, timestamp)
    console.log(`[Performance] Mark: ${name} at ${timestamp.toFixed(2)}ms`)
    return timestamp
  }

  // 测量两个时间点之间的耗时
  measure(name, startMark, endMark) {
    const startTime = this.marks.get(startMark)
    const endTime = this.marks.get(endMark) || performance.now()
    
    if (!startTime) {
      console.warn(`[Performance] Start mark "${startMark}" not found`)
      return 0
    }

    const duration = endTime - startTime
    this.measures.set(name, duration)
    console.log(`[Performance] Measure: ${name} = ${duration.toFixed(2)}ms`)
    return duration
  }

  // 获取所有测量结果
  getAllMeasures() {
    return Object.fromEntries(this.measures)
  }

  // 清除所有标记和测量
  clear() {
    this.marks.clear()
    this.measures.clear()
  }

  // 监控组件加载时间
  monitorComponentLoad(componentName) {
    const startMark = `${componentName}-start`
    const endMark = `${componentName}-end`
    
    this.mark(startMark)
    
    return {
      finish: () => {
        this.mark(endMark)
        return this.measure(`${componentName}-load`, startMark, endMark)
      }
    }
  }

  // 监控页面加载性能
  monitorPageLoad() {
    // DOM 内容加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.mark('dom-content-loaded')
      })
    } else {
      this.mark('dom-content-loaded')
    }

    // 页面完全加载完成
    if (document.readyState === 'complete') {
      this.mark('page-loaded')
    } else {
      window.addEventListener('load', () => {
        this.mark('page-loaded')
      })
    }

    // Vue 应用挂载完成
    this.mark('vue-app-start')
  }

  // 生成性能报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      marks: Object.fromEntries(this.marks),
      measures: Object.fromEntries(this.measures),
      navigation: performance.getEntriesByType('navigation')[0],
      memory: performance.memory ? {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      } : null
    }

    console.log('[Performance Report]', report)
    return report
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 自动开始监控页面加载
performanceMonitor.monitorPageLoad()

/**
 * 任务流配置模板系统
 * 提供常用的任务状态流转模板
 */

export const taskFlowTemplates = {
  // 正常完成流程
  normal_completion: {
    name: '正常完成流程',
    description: '标准的任务执行流程：准备 → 执行 → 完成',
    config: {
      triggerMode: 'heartbeat_count',
      heartbeatInterval: 5000
    },
    nodes: [
      {
        id: 'node_0',
        step: 0,
        name: '任务准备',
        description: '设备接收任务，准备开始执行',
        color: '#909399',
        position: { x: 100, y: 100 },
        config: {
          minDuration: 2000,
          maxDuration: 5000,
          heartbeatCount: 2,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_1',
        step: 1,
        name: '正在执行',
        description: '设备正在执行任务',
        color: '#409EFF',
        position: { x: 350, y: 100 },
        config: {
          minDuration: 15000,
          maxDuration: 30000,
          heartbeatCount: 10,
          randomDelay: 2000,
          conditions: []
        }
      },
      {
        id: 'node_2',
        step: 2,
        name: '任务完成',
        description: '任务成功完成',
        color: '#67C23A',
        position: { x: 600, y: 100 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      }
    ],
    transitions: [
      {
        id: 'trans_0_1',
        fromNodeId: 'node_0',
        toNodeId: 'node_1',
        probability: 1.0,
        conditions: [],
        delay: 0,
        description: '开始执行任务'
      },
      {
        id: 'trans_1_2',
        fromNodeId: 'node_1',
        toNodeId: 'node_2',
        probability: 1.0,
        conditions: [],
        delay: 1000,
        description: '任务执行完成'
      }
    ]
  },

  // 暂停恢复流程
  with_pause: {
    name: '暂停恢复流程',
    description: '包含暂停和恢复的任务流程：准备 → 执行 → 暂停 → 恢复 → 完成',
    config: {
      triggerMode: 'heartbeat_count',
      heartbeatInterval: 5000
    },
    nodes: [
      {
        id: 'node_0',
        step: 0,
        name: '任务准备',
        description: '设备接收任务，准备开始执行',
        color: '#909399',
        position: { x: 100, y: 100 },
        config: {
          minDuration: 2000,
          maxDuration: 5000,
          heartbeatCount: 2,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_1',
        step: 1,
        name: '正在执行',
        description: '设备正在执行任务',
        color: '#409EFF',
        position: { x: 300, y: 100 },
        config: {
          minDuration: 10000,
          maxDuration: 15000,
          heartbeatCount: 6,
          randomDelay: 2000,
          conditions: []
        }
      },
      {
        id: 'node_6',
        step: 6,
        name: '任务暂停',
        description: '任务暂时停止执行',
        color: '#E6A23C',
        position: { x: 500, y: 200 },
        config: {
          minDuration: 5000,
          maxDuration: 10000,
          heartbeatCount: 3,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_1_resume',
        step: 1,
        name: '恢复执行',
        description: '任务恢复执行',
        color: '#409EFF',
        position: { x: 700, y: 100 },
        config: {
          minDuration: 8000,
          maxDuration: 12000,
          heartbeatCount: 5,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_2',
        step: 2,
        name: '任务完成',
        description: '任务成功完成',
        color: '#67C23A',
        position: { x: 900, y: 100 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      }
    ],
    transitions: [
      {
        id: 'trans_0_1',
        fromNodeId: 'node_0',
        toNodeId: 'node_1',
        probability: 1.0,
        conditions: [],
        delay: 0,
        description: '开始执行任务'
      },
      {
        id: 'trans_1_6',
        fromNodeId: 'node_1',
        toNodeId: 'node_6',
        probability: 0.7,
        conditions: [],
        delay: 500,
        description: '任务暂停'
      },
      {
        id: 'trans_1_2',
        fromNodeId: 'node_1',
        toNodeId: 'node_2',
        probability: 0.3,
        conditions: [],
        delay: 1000,
        description: '直接完成'
      },
      {
        id: 'trans_6_1',
        fromNodeId: 'node_6',
        toNodeId: 'node_1_resume',
        probability: 1.0,
        conditions: [],
        delay: 1000,
        description: '恢复执行'
      },
      {
        id: 'trans_1r_2',
        fromNodeId: 'node_1_resume',
        toNodeId: 'node_2',
        probability: 1.0,
        conditions: [],
        delay: 1000,
        description: '完成任务'
      }
    ]
  },

  // 失败场景
  failure_scenario: {
    name: '失败场景',
    description: '包含多种失败情况的任务流程',
    config: {
      triggerMode: 'heartbeat_count',
      heartbeatInterval: 5000
    },
    nodes: [
      {
        id: 'node_0',
        step: 0,
        name: '任务准备',
        description: '设备接收任务，准备开始执行',
        color: '#909399',
        position: { x: 100, y: 100 },
        config: {
          minDuration: 2000,
          maxDuration: 5000,
          heartbeatCount: 2,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_1',
        step: 1,
        name: '正在执行',
        description: '设备正在执行任务',
        color: '#409EFF',
        position: { x: 350, y: 100 },
        config: {
          minDuration: 8000,
          maxDuration: 15000,
          heartbeatCount: 6,
          randomDelay: 2000,
          conditions: []
        }
      },
      {
        id: 'node_2',
        step: 2,
        name: '任务完成',
        description: '任务成功完成',
        color: '#67C23A',
        position: { x: 600, y: 50 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      },
      {
        id: 'node_3',
        step: 3,
        name: '任务取消',
        description: '用户主动取消任务',
        color: '#E6A23C',
        position: { x: 600, y: 150 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      },
      {
        id: 'node_9',
        step: 9,
        name: '执行失败',
        description: '任务执行过程中发生错误',
        color: '#F56C6C',
        position: { x: 600, y: 250 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      }
    ],
    transitions: [
      {
        id: 'trans_0_1',
        fromNodeId: 'node_0',
        toNodeId: 'node_1',
        probability: 1.0,
        conditions: [],
        delay: 0,
        description: '开始执行任务'
      },
      {
        id: 'trans_1_2',
        fromNodeId: 'node_1',
        toNodeId: 'node_2',
        probability: 0.6,
        conditions: [],
        delay: 1000,
        description: '任务成功完成'
      },
      {
        id: 'trans_1_3',
        fromNodeId: 'node_1',
        toNodeId: 'node_3',
        probability: 0.2,
        conditions: [],
        delay: 500,
        description: '用户取消任务'
      },
      {
        id: 'trans_1_9',
        fromNodeId: 'node_1',
        toNodeId: 'node_9',
        probability: 0.2,
        conditions: [],
        delay: 500,
        description: '执行过程失败'
      }
    ]
  },

  // 循环执行
  loop_execution: {
    name: '循环执行',
    description: '任务完成后自动重新开始的循环流程',
    config: {
      triggerMode: 'heartbeat_count',
      heartbeatInterval: 3000
    },
    nodes: [
      {
        id: 'node_0',
        step: 0,
        name: '任务准备',
        description: '设备接收任务，准备开始执行',
        color: '#909399',
        position: { x: 100, y: 150 },
        config: {
          minDuration: 2000,
          maxDuration: 3000,
          heartbeatCount: 1,
          randomDelay: 500,
          conditions: []
        }
      },
      {
        id: 'node_1',
        step: 1,
        name: '正在执行',
        description: '设备正在执行任务',
        color: '#409EFF',
        position: { x: 300, y: 150 },
        config: {
          minDuration: 8000,
          maxDuration: 12000,
          heartbeatCount: 4,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_2',
        step: 2,
        name: '任务完成',
        description: '任务成功完成，准备下一轮',
        color: '#67C23A',
        position: { x: 500, y: 150 },
        config: {
          minDuration: 2000,
          maxDuration: 3000,
          heartbeatCount: 1,
          randomDelay: 500,
          conditions: []
        }
      }
    ],
    transitions: [
      {
        id: 'trans_0_1',
        fromNodeId: 'node_0',
        toNodeId: 'node_1',
        probability: 1.0,
        conditions: [],
        delay: 0,
        description: '开始执行任务'
      },
      {
        id: 'trans_1_2',
        fromNodeId: 'node_1',
        toNodeId: 'node_2',
        probability: 1.0,
        conditions: [],
        delay: 1000,
        description: '任务执行完成'
      },
      {
        id: 'trans_2_0',
        fromNodeId: 'node_2',
        toNodeId: 'node_0',
        probability: 1.0,
        conditions: [],
        delay: 2000,
        description: '重新开始任务'
      }
    ]
  },

  // 复杂流程
  complex_flow: {
    name: '复杂流程',
    description: '包含多种状态和转换的复杂任务流程',
    config: {
      triggerMode: 'time_interval',
      heartbeatInterval: 5000
    },
    nodes: [
      {
        id: 'node_0',
        step: 0,
        name: '任务准备',
        description: '设备接收任务，准备开始执行',
        color: '#909399',
        position: { x: 100, y: 200 },
        config: {
          minDuration: 3000,
          maxDuration: 5000,
          heartbeatCount: 2,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_1',
        step: 1,
        name: '正在执行',
        description: '设备正在执行任务',
        color: '#409EFF',
        position: { x: 300, y: 200 },
        config: {
          minDuration: 12000,
          maxDuration: 20000,
          heartbeatCount: 8,
          randomDelay: 3000,
          conditions: []
        }
      },
      {
        id: 'node_6',
        step: 6,
        name: '任务暂停',
        description: '任务暂时停止执行',
        color: '#E6A23C',
        position: { x: 500, y: 300 },
        config: {
          minDuration: 5000,
          maxDuration: 8000,
          heartbeatCount: 3,
          randomDelay: 1000,
          conditions: []
        }
      },
      {
        id: 'node_8',
        step: 8,
        name: '任务终止',
        description: '任务被强制终止',
        color: '#F56C6C',
        position: { x: 500, y: 100 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      },
      {
        id: 'node_2',
        step: 2,
        name: '任务完成',
        description: '任务成功完成',
        color: '#67C23A',
        position: { x: 700, y: 200 },
        config: {
          minDuration: 1000,
          maxDuration: 2000,
          heartbeatCount: 1,
          randomDelay: 0,
          conditions: []
        }
      }
    ],
    transitions: [
      {
        id: 'trans_0_1',
        fromNodeId: 'node_0',
        toNodeId: 'node_1',
        probability: 1.0,
        conditions: [],
        delay: 0,
        description: '开始执行任务'
      },
      {
        id: 'trans_1_2',
        fromNodeId: 'node_1',
        toNodeId: 'node_2',
        probability: 0.5,
        conditions: [],
        delay: 1000,
        description: '任务成功完成'
      },
      {
        id: 'trans_1_6',
        fromNodeId: 'node_1',
        toNodeId: 'node_6',
        probability: 0.3,
        conditions: [],
        delay: 500,
        description: '任务暂停'
      },
      {
        id: 'trans_1_8',
        fromNodeId: 'node_1',
        toNodeId: 'node_8',
        probability: 0.2,
        conditions: [],
        delay: 200,
        description: '任务终止'
      },
      {
        id: 'trans_6_1',
        fromNodeId: 'node_6',
        toNodeId: 'node_1',
        probability: 0.8,
        conditions: [],
        delay: 1000,
        description: '恢复执行'
      },
      {
        id: 'trans_6_8',
        fromNodeId: 'node_6',
        toNodeId: 'node_8',
        probability: 0.2,
        conditions: [],
        delay: 500,
        description: '暂停后终止'
      }
    ]
  }
}

/**
 * 应用模板到任务流配置
 * @param {string} templateKey - 模板键名
 * @returns {Object} 模板配置
 */
export function applyTaskFlowTemplate(templateKey) {
  const template = taskFlowTemplates[templateKey]
  if (!template) {
    throw new Error(`未找到模板: ${templateKey}`)
  }

  // 深拷贝模板配置，避免修改原始模板
  return JSON.parse(JSON.stringify(template))
}

/**
 * 获取所有可用模板的列表
 * @returns {Array} 模板列表
 */
export function getAvailableTemplates() {
  return Object.keys(taskFlowTemplates).map(key => ({
    key,
    name: taskFlowTemplates[key].name,
    description: taskFlowTemplates[key].description
  }))
}

/**
 * 验证模板配置的有效性
 * @param {Object} template - 模板配置
 * @returns {Object} 验证结果
 */
export function validateTemplate(template) {
  const errors = []
  const warnings = []

  // 检查基本结构
  if (!template.nodes || !Array.isArray(template.nodes)) {
    errors.push('模板必须包含nodes数组')
  }

  if (!template.transitions || !Array.isArray(template.transitions)) {
    errors.push('模板必须包含transitions数组')
  }

  if (template.nodes && template.transitions) {
    // 检查节点ID唯一性
    const nodeIds = template.nodes.map(n => n.id)
    const duplicateIds = nodeIds.filter((id, index) => nodeIds.indexOf(id) !== index)
    if (duplicateIds.length > 0) {
      errors.push(`重复的节点ID: ${duplicateIds.join(', ')}`)
    }

    // 检查转换引用的节点是否存在
    for (const transition of template.transitions) {
      if (!nodeIds.includes(transition.fromNodeId)) {
        errors.push(`转换 ${transition.id} 引用了不存在的源节点: ${transition.fromNodeId}`)
      }
      if (!nodeIds.includes(transition.toNodeId)) {
        errors.push(`转换 ${transition.id} 引用了不存在的目标节点: ${transition.toNodeId}`)
      }
    }

    // 检查概率总和
    const nodeTransitions = {}
    template.transitions.forEach(t => {
      if (!nodeTransitions[t.fromNodeId]) {
        nodeTransitions[t.fromNodeId] = []
      }
      nodeTransitions[t.fromNodeId].push(t)
    })

    Object.entries(nodeTransitions).forEach(([nodeId, transitions]) => {
      const totalProbability = transitions.reduce((sum, t) => sum + t.probability, 0)
      if (Math.abs(totalProbability - 1.0) > 0.01) {
        warnings.push(`节点 ${nodeId} 的转换概率总和不等于1.0: ${totalProbability}`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

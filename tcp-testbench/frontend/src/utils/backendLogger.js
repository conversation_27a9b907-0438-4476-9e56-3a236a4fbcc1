import { LogToConsole } from 'wailsjs/go/main/App'

/**
 * 后端日志工具 - 将前端日志打印到Wails开发窗口控制台
 */
class BackendLogger {
  constructor() {
    this.enabled = true
  }

  /**
   * 启用/禁用后端日志
   */
  setEnabled(enabled) {
    this.enabled = enabled
  }

  /**
   * 信息日志
   */
  async info(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('info', message, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 警告日志
   */
  async warn(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('warn', message, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 错误日志
   */
  async error(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('error', message, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 调试日志
   */
  async debug(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('debug', message, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 普通日志
   */
  async log(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('info', message, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 任务流程专用日志 - 带特殊格式
   */
  async taskFlow(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('info', `🔄 [任务流程] ${message}`, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 心跳专用日志 - 带特殊格式
   */
  async heartbeat(message, data = null) {
    if (!this.enabled) return
    
    try {
      await LogToConsole('info', `💓 [心跳] ${message}`, data)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }

  /**
   * 分隔线日志
   */
  async separator(title = '') {
    if (!this.enabled) return
    
    const line = '='.repeat(50)
    const message = title ? `${line} ${title} ${line}` : line
    
    try {
      await LogToConsole('info', message)
    } catch (error) {
      console.warn('后端日志发送失败:', error)
    }
  }
}

// 创建全局实例
const backendLogger = new BackendLogger()

export default backendLogger

import { ElMessage } from 'element-plus'

/**
 * 增强型消息管理器 - 确保绝对的单一消息显示
 * 使用多重保障机制防止消息堆叠
 */
class MessageManager {
  constructor() {
    this.currentMessage = null
    this.messageQueue = [] // 跟踪所有创建的消息实例
    this.interceptElMessage() // 拦截直接调用 ElMessage 的情况
  }

  /**
   * 拦截直接调用 ElMessage 的情况，强制使用消息管理器
   */
  interceptElMessage() {
    if (typeof window !== 'undefined') {
      // 保存原始的 ElMessage
      const originalElMessage = ElMessage

      // 创建拦截器
      const interceptedElMessage = (options) => {
        console.warn('⚠️ 检测到直接调用 ElMessage，建议使用 messageManager')
        console.warn('📍 调用栈:', new Error().stack)

        // 如果是对象形式的调用，转换为消息管理器调用
        if (typeof options === 'object' && options.type) {
          const { type, message, duration = 3000 } = options
          return this._showMessage(type, message, duration)
        }

        // 否则使用原始方法但记录警告
        return originalElMessage(options)
      }

      // 复制原始方法的静态属性
      Object.keys(originalElMessage).forEach(key => {
        interceptedElMessage[key] = originalElMessage[key]
      })

      // 替换全局 ElMessage（如果存在）
      if (window.ElMessage) {
        window.ElMessage = interceptedElMessage
      }
    }
  }

  /**
   * 立即强制关闭现有消息 - 无延迟，立即中断当前消息
   */
  closeExistingMessages() {
    console.log('🧹 立即强制清理现有消息...')

    try {
      // 1. 立即强制清理DOM中的消息元素（最优先）
      this.forceClearMessageDOM()

      // 2. 立即关闭当前跟踪的消息
      if (this.currentMessage) {
        console.log('📝 立即强制关闭当前消息')
        try {
          this.currentMessage.close()
        } catch (e) {
          console.warn('关闭当前消息失败:', e)
        }
        this.currentMessage = null
      }

      // 3. 立即关闭队列中的所有消息
      if (this.messageQueue.length > 0) {
        console.log(`📝 立即关闭队列中的 ${this.messageQueue.length} 条消息`)
        this.messageQueue.forEach((msg, index) => {
          try {
            if (msg && typeof msg.close === 'function') {
              msg.close()
              console.log(`✅ 已立即关闭队列消息 ${index + 1}`)
            }
          } catch (e) {
            console.warn(`关闭队列消息 ${index + 1} 失败:`, e)
          }
        })
        this.messageQueue = []
      }

      // 4. 使用ElMessage.closeAll()作为强制保障
      ElMessage.closeAll()

      // 5. 再次强制清理DOM确保完全清除
      this.forceClearMessageDOM()

      console.log('✅ 现有消息立即清理完成')

    } catch (e) {
      console.error('立即清理现有消息时出错:', e)
      // 备用强制清理
      try {
        ElMessage.closeAll()
        this.forceClearMessageDOM()
      } catch (e2) {
        console.error('备用强制清理也失败:', e2)
      }
    }
  }

  /**
   * 立即强制清理DOM中的消息元素 - 最强力的清理机制
   */
  forceClearMessageDOM() {
    try {
      // 查找并立即移除所有 ElMessage 相关的DOM元素
      const messageSelectors = [
        '.el-message',
        '.el-message--success',
        '.el-message--error',
        '.el-message--warning',
        '.el-message--info',
        '[class*="el-message"]'
      ]

      messageSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          console.log(`🧹 立即强制清理 ${elements.length} 个 ${selector} 元素`)
          elements.forEach((el, index) => {
            try {
              // 立即隐藏元素
              el.style.display = 'none'
              el.style.opacity = '0'
              el.style.visibility = 'hidden'

              // 立即移除元素
              el.remove()
              console.log(`✅ 已立即移除消息DOM元素 ${selector}[${index + 1}]`)
            } catch (e) {
              console.warn(`移除消息DOM元素 ${selector}[${index + 1}] 失败:`, e)
            }
          })
        }
      })

      // 额外清理：查找可能的消息容器
      const containers = document.querySelectorAll('[class*="message"], [id*="message"]')
      containers.forEach((container, index) => {
        if (container.textContent && container.textContent.trim()) {
          // 检查是否可能是消息容器
          const rect = container.getBoundingClientRect()
          if (rect.width > 0 && rect.height > 0) {
            try {
              container.style.display = 'none'
              console.log(`🧹 隐藏可能的消息容器 ${index + 1}`)
            } catch (e) {
              console.warn(`隐藏消息容器失败:`, e)
            }
          }
        }
      })

    } catch (e) {
      console.warn('立即强制清理DOM失败:', e)
    }
  }

  /**
   * 显示成功消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长，默认3000ms
   */
  success(message, duration = 3000) {
    return this._showMessage('success', message, duration)
  }

  /**
   * 显示错误消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长，默认3000ms
   */
  error(message, duration = 3000) {
    return this._showMessage('error', message, duration)
  }

  /**
   * 显示警告消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长，默认3000ms
   */
  warning(message, duration = 3000) {
    return this._showMessage('warning', message, duration)
  }

  /**
   * 显示信息消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长，默认3000ms
   */
  info(message, duration = 3000) {
    return this._showMessage('info', message, duration)
  }

  /**
   * 统一的消息显示方法 - 立即显示新消息，强制中断当前消息
   * @param {string} type 消息类型
   * @param {string} message 消息内容
   * @param {number} duration 显示时长
   */
  _showMessage(type, message, duration) {
    console.log(`🚀 准备立即显示 ${type} 消息: "${message}"`)

    try {
      // 步骤1: 立即强制清理现有消息，无延迟
      this.closeExistingMessages()

      // 步骤2: 立即创建新消息，无延迟等待
      console.log('📝 立即创建新消息...')
      const newMessage = ElMessage({
        type,
        message,
        duration,
        showClose: true,
        grouping: false, // 禁用消息分组
        onClose: () => {
          console.log('📝 消息关闭回调触发')
          // 清理当前消息引用
          if (this.currentMessage === newMessage) {
            this.currentMessage = null
            console.log('✅ 已清理当前消息引用')
          }
          // 从队列中移除
          const index = this.messageQueue.indexOf(newMessage)
          if (index > -1) {
            this.messageQueue.splice(index, 1)
            console.log(`✅ 已从队列中移除消息 (位置: ${index})`)
          }
        }
      })

      // 步骤3: 验证消息创建成功
      if (!newMessage) {
        throw new Error('ElMessage 返回了 null 或 undefined')
      }

      // 步骤4: 立即更新跟踪状态
      this.currentMessage = newMessage
      this.messageQueue.push(newMessage)

      console.log(`✅ 消息立即显示成功! 当前队列长度: ${this.messageQueue.length}`)
      console.log('📝 消息实例:', newMessage)

      return newMessage

    } catch (e) {
      console.error('❌ 显示消息失败:', e)

      // 备用显示方案 - 也是立即执行
      try {
        console.log('🔄 尝试备用消息显示方案...')
        const backupMessage = ElMessage({
          type,
          message,
          duration,
          grouping: false
        })
        console.log('✅ 备用消息显示成功:', backupMessage)
        return backupMessage
      } catch (e2) {
        console.error('❌ 备用消息显示也失败:', e2)
        return null
      }
    }
  }

  /**
   * 手动关闭当前消息（向后兼容）
   */
  closeCurrentMessage() {
    console.log('🔧 手动关闭当前消息')
    this.closeExistingMessages()
  }

  /**
   * 调试方法：检查消息管理器状态
   */
  getStatus() {
    const status = {
      hasCurrentMessage: !!this.currentMessage,
      queueLength: this.messageQueue.length,
      currentMessage: this.currentMessage,
      messageQueue: this.messageQueue
    }
    console.log('📊 消息管理器状态:', status)
    return status
  }

  /**
   * 调试方法：测试消息显示
   */
  testMessage(message = '测试消息') {
    console.log('🧪 开始测试消息显示...')
    const result = this.success(message)
    console.log('🧪 测试结果:', result)
    return result
  }
}

// 创建全局实例
const messageManager = new MessageManager()

// 开发环境下添加全局调试工具
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.messageManager = messageManager
  window.testMessage = (msg) => messageManager.testMessage(msg)
  window.getMessageStatus = () => messageManager.getStatus()
  console.log('🛠️ 消息管理器调试工具已加载:')
  console.log('- window.messageManager - 消息管理器实例')
  console.log('- window.testMessage(msg) - 测试消息显示')
  console.log('- window.getMessageStatus() - 查看状态')
}

export default messageManager

// 布局配置工具
export const layoutConfig = {
  // 面板最小高度配置
  panelMinHeight: {
    mobile: 200,
    tablet: 250,
    desktop: 300
  },
  
  // 响应式断点
  breakpoints: {
    mobile: 768,
    tablet: 1200,
    desktop: 1600
  },
  
  // 间距配置
  spacing: {
    mobile: 6,
    tablet: 8,
    desktop: 12
  },
  
  // 获取当前设备类型
  getDeviceType() {
    const width = window.innerWidth
    if (width <= this.breakpoints.mobile) return 'mobile'
    if (width <= this.breakpoints.tablet) return 'tablet'
    return 'desktop'
  },
  
  // 获取当前设备的配置
  getCurrentConfig() {
    const deviceType = this.getDeviceType()
    return {
      deviceType,
      minHeight: this.panelMinHeight[deviceType],
      spacing: this.spacing[deviceType]
    }
  },
  
  // 计算面板高度
  calculatePanelHeight(containerHeight, panelCount, spacing) {
    const totalSpacing = spacing * (panelCount - 1)
    const availableHeight = containerHeight - totalSpacing
    return Math.max(availableHeight / panelCount, this.panelMinHeight[this.getDeviceType()])
  }
}

// 响应式布局管理器
export class ResponsiveLayoutManager {
  constructor() {
    this.listeners = []
    this.currentConfig = layoutConfig.getCurrentConfig()
    this.init()
  }
  
  init() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this))
    
    // 初始化CSS变量
    this.updateCSSVariables()
  }
  
  handleResize() {
    const newConfig = layoutConfig.getCurrentConfig()
    
    // 如果设备类型发生变化，触发回调
    if (newConfig.deviceType !== this.currentConfig.deviceType) {
      this.currentConfig = newConfig
      this.updateCSSVariables()
      this.notifyListeners(newConfig)
    }
  }
  
  // 更新CSS变量
  updateCSSVariables() {
    const root = document.documentElement
    const config = this.currentConfig
    
    root.style.setProperty('--panel-min-height', `${config.minHeight}px`)
    root.style.setProperty('--layout-spacing', `${config.spacing}px`)
    root.style.setProperty('--device-type', config.deviceType)
  }
  
  // 添加监听器
  addListener(callback) {
    this.listeners.push(callback)
  }
  
  // 移除监听器
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  // 通知所有监听器
  notifyListeners(config) {
    this.listeners.forEach(callback => {
      try {
        callback(config)
      } catch (error) {
        console.error('Layout listener error:', error)
      }
    })
  }
  
  // 获取当前配置
  getCurrentConfig() {
    return this.currentConfig
  }
  
  // 销毁管理器
  destroy() {
    window.removeEventListener('resize', this.handleResize.bind(this))
    this.listeners = []
  }
}

// 全局布局管理器实例
export const layoutManager = new ResponsiveLayoutManager()

// Vue 组合式函数
export function useResponsiveLayout() {
  const config = ref(layoutManager.getCurrentConfig())
  
  const updateConfig = (newConfig) => {
    config.value = newConfig
  }
  
  onMounted(() => {
    layoutManager.addListener(updateConfig)
  })
  
  onUnmounted(() => {
    layoutManager.removeListener(updateConfig)
  })
  
  return {
    config: readonly(config),
    deviceType: computed(() => config.value.deviceType),
    minHeight: computed(() => config.value.minHeight),
    spacing: computed(() => config.value.spacing)
  }
}

// 增强型消息管理器测试工具
import messageManager from './message'

// 基础测试：快速连续消息
export const testMessageManager = () => {
  console.log('🧪 开始基础消息管理器测试...')
  console.log('📊 测试前状态:', messageManager.getStatus())

  // 快速连续显示多条消息
  console.log('1️⃣ 显示第一条消息...')
  const msg1 = messageManager.success('消息1 - 应该被替换')
  console.log('1️⃣ 第一条消息结果:', msg1)

  console.log('2️⃣ 显示第二条消息...')
  const msg2 = messageManager.warning('消息2 - 应该被替换')
  console.log('2️⃣ 第二条消息结果:', msg2)

  console.log('3️⃣ 显示第三条消息...')
  const msg3 = messageManager.error('消息3 - 应该被替换')
  console.log('3️⃣ 第三条消息结果:', msg3)

  console.log('4️⃣ 显示最终消息...')
  const msg4 = messageManager.info('消息4 - 最终显示')
  console.log('4️⃣ 最终消息结果:', msg4)

  console.log('📊 测试后状态:', messageManager.getStatus())
  console.log('✅ 如果只看到"消息4 - 最终显示"，说明基础功能正常')
}

// 极限测试：模拟用户疯狂点击
export const testExtremeClicking = () => {
  console.log('🚀 开始极限点击测试...')

  // 模拟用户在50毫秒内连续点击10次
  for (let i = 0; i < 10; i++) {
    setTimeout(() => {
      messageManager.success(`极限测试消息 ${i + 1}`)
    }, i * 5) // 每5毫秒一次
  }

  setTimeout(() => {
    messageManager.error('最终消息 - 应该只显示这一条')
    console.log('✅ 如果只看到"最终消息"，说明极限测试通过')
  }, 100)
}

// 异步操作测试
export const testAsyncOperations = async () => {
  console.log('⚡ 开始异步操作测试...')

  // 模拟异步操作中的消息
  const promises = []
  for (let i = 0; i < 5; i++) {
    promises.push(
      new Promise(resolve => {
        setTimeout(() => {
          messageManager.warning(`异步消息 ${i + 1}`)
          resolve()
        }, Math.random() * 100)
      })
    )
  }

  await Promise.all(promises)
  messageManager.success('异步测试完成 - 最终消息')
  console.log('✅ 如果只看到"异步测试完成"，说明异步测试通过')
}

// 设备模板切换模拟测试
export const testDeviceTemplateSwitch = () => {
  console.log('🤖 开始设备模板切换测试...')

  const templates = ['清扫机器人', '配送机器人', '巡逻机器人']

  // 快速切换模板
  templates.forEach((template, index) => {
    setTimeout(() => {
      messageManager.success(`已应用 ${template} 模板`)
    }, index * 10) // 每10毫秒切换一次
  })

  setTimeout(() => {
    console.log('✅ 如果只看到最后一个模板消息，说明模板切换测试通过')
  }, 100)
}

// 综合压力测试
export const testComprehensive = () => {
  console.log('💪 开始综合压力测试...')

  // 混合各种类型的消息
  const messageTypes = ['success', 'error', 'warning', 'info']

  for (let i = 0; i < 20; i++) {
    const type = messageTypes[i % messageTypes.length]
    setTimeout(() => {
      messageManager[type](`压力测试消息 ${i + 1} (${type})`)
    }, i * 3) // 每3毫秒一次
  }

  setTimeout(() => {
    messageManager.success('🎉 压力测试完成 - 如果只看到这条消息，说明测试通过！')
    console.log('✅ 综合压力测试完成')
  }, 100)
}

// 在浏览器控制台中注册所有测试函数
if (typeof window !== 'undefined') {
  window.testMessageManager = testMessageManager
  window.testExtremeClicking = testExtremeClicking
  window.testAsyncOperations = testAsyncOperations
  window.testDeviceTemplateSwitch = testDeviceTemplateSwitch
  window.testComprehensive = testComprehensive

  // 一键运行所有测试
  window.runAllMessageTests = () => {
    console.log('🎯 运行所有消息管理器测试...')
    testMessageManager()
    setTimeout(testExtremeClicking, 1000)
    setTimeout(testAsyncOperations, 2000)
    setTimeout(testDeviceTemplateSwitch, 3000)
    setTimeout(testComprehensive, 4000)
  }

  console.log('📋 消息管理器测试工具已加载，可用函数：')
  console.log('- testMessageManager() - 基础测试')
  console.log('- testExtremeClicking() - 极限点击测试')
  console.log('- testAsyncOperations() - 异步操作测试')
  console.log('- testDeviceTemplateSwitch() - 设备模板切换测试')
  console.log('- testComprehensive() - 综合压力测试')
  console.log('- runAllMessageTests() - 运行所有测试')
}

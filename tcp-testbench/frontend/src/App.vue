<script setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { useLoggerStore } from './stores/logger'
import { performanceMonitor } from './utils/performance'
import { EventsOn, EventsOff } from 'wailsjs/runtime/runtime'
import {
  Monitor,
  QuestionFilled,
  Setting,
  Share
} from '@element-plus/icons-vue'

// 异步加载组件以提升性能
const ConnectionPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('ConnectionPanel')
  return import('./components/ConnectionPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

const DevicePanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('DevicePanel')
  return import('./components/DevicePanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

const HeartbeatPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('HeartbeatPanel')
  return import('./components/HeartbeatPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

// CommandPanel 组件已移除，业务指令测试功能不再需要

const LoggerPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('LoggerPanel')
  return import('./components/LoggerPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

const TaskFlowPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('TaskFlowPanel')
  return import('./components/TaskFlowPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})



const MessageDebugPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('MessageDebugPanel')
  return import('./components/MessageDebugPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

// MessageTestSimple 组件已移除，功能已在快速测试面板中验证

const TestPanel = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('TestPanel')
  return import('./components/TestPanel.vue').then(module => {
    monitor.finish()
    return module
  })
})

const PaginationDots = defineAsyncComponent(() => {
  const monitor = performanceMonitor.monitorComponentLoad('PaginationDots')
  return import('./components/PaginationDots.vue').then(module => {
    monitor.finish()
    return module
  })
})

const loggerStore = useLoggerStore()

// 应用配置
const locale = ref('zh-cn')
const appTitle = 'TCPTestBench - TCP客户端测试平台'
const appVersion = '1.0.0'
const isLoading = ref(true)
const showTestPanel = ref(false)

// 分页系统
const currentPage = ref(0)
const pages = [
  {
    label: '主界面',
    title: '连接、设备、心跳、命令和日志面板',
    icon: Monitor
  },
  {
    label: '任务流程',
    title: '任务状态流程配置和监控',
    icon: Share
  }
]

// 分页切换函数
const changePage = (pageIndex) => {
  currentPage.value = pageIndex
}

onMounted(async () => {
  performanceMonitor.mark('app-mount-start')

  // 设置深色主题
  document.documentElement.classList.add('dark')

  // 🔍 检查Wails绑定状态
  console.log('🔍 App mounted - Checking Wails bindings...')
  console.log('🔍 window.go:', typeof window.go)
  console.log('🔍 window.runtime:', typeof window.runtime)

  // 等待Wails绑定初始化
  let attempts = 0
  const maxAttempts = 30 // 增加到3秒
  while (typeof window.go === 'undefined' && attempts < maxAttempts) {
    console.log(`🔍 Waiting for Wails bindings... (${attempts + 1}/${maxAttempts})`)
    await new Promise(resolve => setTimeout(resolve, 100))
    attempts++
  }

  if (typeof window.go !== 'undefined') {
    console.log('✅ Wails bindings loaded successfully!')
    console.log('🔍 Available methods:', Object.keys(window.go.main.App))
  } else {
    console.warn('⚠️ Wails bindings not detected via window.go, but functions may still work')
    console.log('🔍 This is common in development mode, continuing...')
  }

  // 优化初始化过程
  await new Promise(resolve => setTimeout(resolve, 50))

  // 设置全局事件监听器
  if (typeof window.runtime !== 'undefined') {
    console.log('🔔 设置全局事件监听器')

    // 监听连接状态变化事件（全局备份）
    EventsOn('connection-status-changed', (data) => {
      console.log('🌐 [全局] 收到连接状态变化事件:', data)
      // 这里可以添加全局的状态处理逻辑
    })
  }

  // 记录应用启动
  loggerStore.info('system', `${appTitle} v${appVersion} 启动成功`)

  performanceMonitor.mark('app-mount-end')
  performanceMonitor.measure('app-mount', 'app-mount-start', 'app-mount-end')

  isLoading.value = false

  // 生成性能报告
  setTimeout(() => {
    performanceMonitor.generateReport()
  }, 1000)
})
</script>

<template>
  <el-config-provider :locale="locale">
    <div id="app">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-screen">
        <div class="loading-content">
          <div class="app-logo loading-logo">
            <el-icon size="32"><Monitor /></el-icon>
          </div>
          <h2>{{ appTitle }}</h2>
          <div class="loading-spinner"></div>
          <p>正在初始化界面...</p>
        </div>
      </div>

      <!-- 主应用界面 -->
      <div v-else class="app-content">
        <!-- 应用头部 -->
        <header class="app-header">
          <div class="header-left">
            <div class="app-logo">
              <el-icon size="24"><Monitor /></el-icon>
            </div>
            <div class="app-info">
              <h1 class="app-title">{{ appTitle }}</h1>
              <span class="app-version">v{{ appVersion }}</span>
            </div>
          </div>

          <!-- 分页导航 - 直接嵌入标题栏，无容器 -->
          <Suspense v-if="!showTestPanel">
            <template #default>
              <PaginationDots
                :current-page="currentPage"
                :pages="pages"
                @page-change="changePage"
              />
            </template>
            <template #fallback>
              <div class="navigation-loading">加载导航...</div>
            </template>
          </Suspense>

          <div class="header-right">
            <div class="header-actions">
              <el-button size="small" type="primary" @click="showTestPanel = !showTestPanel">
                <el-icon><Monitor /></el-icon>
                {{ showTestPanel ? '返回主界面' : '快速测试' }}
              </el-button>
              <el-button size="small" text>
                <el-icon><QuestionFilled /></el-icon>
                帮助
              </el-button>
              <el-button size="small" text>
                <el-icon><Setting /></el-icon>
                设置
              </el-button>
            </div>
          </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
          <!-- 消息管理器调试面板 -->
          <div v-if="showTestPanel" class="test-panel-container">
            <Suspense>
              <template #default>
                <MessageDebugPanel />
              </template>
              <template #fallback>
                <div class="panel-loading">加载消息调试面板...</div>
              </template>
            </Suspense>
          </div>

          <!-- 分页内容区域 -->
          <div v-else class="paginated-content">
            <!-- 页面容器 -->
            <div class="page-container">
              <!-- 第一页：主要面板 -->
              <div v-show="currentPage === 0" class="page-content main-page">
                <div class="main-grid">
                  <!-- 左侧面板 -->
                  <div class="left-panel">
                    <div class="panel-stack">
                      <Suspense>
                        <template #default>
                          <ConnectionPanel />
                        </template>
                        <template #fallback>
                          <div class="panel-loading">加载中...</div>
                        </template>
                      </Suspense>
                      <Suspense>
                        <template #default>
                          <DevicePanel />
                        </template>
                        <template #fallback>
                          <div class="panel-loading">加载中...</div>
                        </template>
                      </Suspense>
                    </div>
                  </div>

                  <!-- 中间面板 -->
                  <div class="center-panel">
                    <div class="panel-stack">
                      <Suspense>
                        <template #default>
                          <HeartbeatPanel />
                        </template>
                        <template #fallback>
                          <div class="panel-loading">加载中...</div>
                        </template>
                      </Suspense>
                      <!-- CommandPanel 组件已移除，业务指令测试功能不再需要 -->
                    </div>
                  </div>

                  <!-- 右侧面板 -->
                  <div class="right-panel">
                    <div class="panel-stack">
                      <Suspense>
                        <template #default>
                          <LoggerPanel />
                        </template>
                        <template #fallback>
                          <div class="panel-loading">加载中...</div>
                        </template>
                      </Suspense>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二页：任务流程配置 -->
              <div v-show="currentPage === 1" class="page-content task-flow-page">
                <div class="task-flow-container">
                  <Suspense>
                    <template #default>
                      <TaskFlowPanel />
                    </template>
                    <template #fallback>
                      <div class="panel-loading">加载任务流配置...</div>
                    </template>
                  </Suspense>
                </div>
              </div>

              <!-- 消息测试页面已移除，功能已在快速测试面板中验证 -->
            </div>

            <!-- 分页导航已移动到标题栏 -->
          </div>
        </main>
      </div>
    </div>
  </el-config-provider>
</template>

<style scoped>
#app {
  width: 100vw;
  height: 100vh;
  background-color: var(--bg-primary);
}

.app-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 加载屏幕样式 */
.loading-screen {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
}

.loading-content {
  text-align: center;
  color: var(--text-primary);
}

.loading-logo {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 auto 20px;
  animation: pulse 2s infinite;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 面板加载样式 */
.panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  min-height: 70px; /* 增加高度以容纳导航标签 */
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

/* .header-center 容器已移除，导航按钮直接在标题栏中 */

/* .header-navigation 容器样式已移除，导航按钮直接嵌入标题栏 */

.navigation-loading {
  text-align: center;
  color: var(--text-secondary);
  font-size: 12px;
  padding: 8px 16px;
}

.app-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.app-version {
  font-size: 12px;
  color: var(--text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.app-main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: calc(100vh - 76px);
}

/* 分页系统样式 */
.paginated-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.page-container {
  flex: 1;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.page-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  opacity: 1;
  transition: all 0.3s ease-in-out;
  overflow-y: auto;
}

.page-content.main-page {
  z-index: 2;
}

.page-content.task-flow-page {
  z-index: 1;
}

/* .page-content.message-test-page 样式已移除 */

.task-flow-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* .message-test-container 样式已移除 */

/* 原pagination-container样式已移除，导航已移动到标题栏 */

.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  min-height: fit-content;
  align-items: start;
}

.left-panel,
.center-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.panel-stack {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.panel-stack > * {
  height: auto;
  min-height: auto;
  max-height: none;
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .main-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}

@media (max-width: 1200px) {
  .app-main {
    padding: 12px;
  }

  .main-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .panel-stack {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 8px 12px;
    min-height: 60px; /* 保持足够高度容纳导航 */
    flex-wrap: wrap;
  }

  .app-title {
    font-size: 16px;
  }

  /* .header-center 响应式样式已移除 */

  /* .header-navigation 响应式样式已移除 */

  .app-main {
    padding: 8px;
  }

  .main-grid {
    gap: 8px;
  }

  .panel-stack {
    gap: 8px;
  }
}

/* 测试面板样式 */
.test-panel-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>

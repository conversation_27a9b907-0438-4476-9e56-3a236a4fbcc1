<template>
  <el-card class="task-flow-panel">
    <template #header>
      <div class="card-header">
        <span class="header-title">
          <div class="title-icon">
            <el-icon><Share /></el-icon>
          </div>
          <span class="title-text">任务状态流程配置</span>
        </span>
        <div class="header-actions">
          <div class="flow-status">
            <div :class="['status-indicator', flowStatus.runtime?.isRunning ? 'status-running' : 'status-stopped']">
              <div class="status-dot"></div>
              <span>{{ flowStatus.runtime?.isRunning ? '流程激活' : '流程待机' }}</span>
            </div>
          </div>
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              @click="activateFlow"
              :disabled="flowStatus.runtime?.isRunning || isLoading"
              :loading="isLoading"
            >
              <el-icon><Setting /></el-icon>
              激活流程
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="deactivateFlow"
              :disabled="!flowStatus.runtime?.isRunning || isLoading"
              :loading="isLoading"
            >
              <el-icon><Close /></el-icon>
              停用流程
            </el-button>
          </el-button-group>
        </div>
      </div>
    </template>

    <div class="task-flow-content">
      <!-- 配置选项卡 -->
      <el-tabs v-model="activeTab" type="border-card" class="flow-tabs">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <div class="basic-config">
            <el-alert
              title="任务状态流程配置"
              type="info"
              :closable="false"
              show-icon
              style="margin-bottom: 20px;"
            >
              <template #default>
                此面板用于配置任务状态的流转逻辑。心跳的发送和配置请在主界面的"基础配置"面板操作。
                激活流程后，系统将监听心跳事件并根据配置的规则自动进行任务状态转换。
              </template>
            </el-alert>
            <el-form :model="flowConfig" label-width="120px" size="default">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="流程名称">
                    <el-input v-model="flowConfig.name" placeholder="输入流程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="触发模式">
                    <el-input value="心跳次数触发" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="任务ID">
                    <el-input-number
                      v-model="taskConfig.taskId"
                      :min="1"
                      :max="999999"
                      placeholder="输入任务ID"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="路线ID">
                    <el-input-number
                      v-model="taskConfig.routeId"
                      :min="1"
                      :max="999999"
                      placeholder="输入路线ID"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="任务名称">
                    <el-input
                      v-model="taskConfig.taskName"
                      placeholder="输入任务名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="任务描述">
                    <el-input
                      v-model="taskConfig.taskDescription"
                      placeholder="输入任务描述"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="当前站点ID">
                    <el-input-number
                      v-model="taskConfig.currentStation.id"
                      :min="1"
                      :max="999999"
                      placeholder="当前站点ID"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="当前站点名称">
                    <el-input
                      v-model="taskConfig.currentStation.name"
                      placeholder="输入当前站点名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="目标站点ID">
                    <el-input-number
                      v-model="taskConfig.targetStation.id"
                      :min="1"
                      :max="999999"
                      placeholder="目标站点ID"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="目标站点名称">
                    <el-input
                      v-model="taskConfig.targetStation.name"
                      placeholder="输入目标站点名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="任务距离(米)">
                    <el-input-number
                      v-model="taskConfig.taskDistance"
                      :min="0"
                      :max="999999"
                      placeholder="任务总距离"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="预计时间(分钟)">
                    <el-input-number
                      v-model="taskConfig.leftTime"
                      :min="1"
                      :max="999"
                      placeholder="预计完成时间"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="流程描述">
                    <el-input
                      v-model="flowConfig.description"
                      type="textarea"
                      :rows="2"
                      placeholder="描述此流程的用途和特点"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>


          </div>
        </el-tab-pane>

        <!-- 状态流程图 -->
        <el-tab-pane label="流程设计" name="flow">
          <TaskFlowDesigner
            :nodes="taskFlowStore.stateNodes"
            :transitions="taskFlowStore.stateTransitions"
            :current-node-id="taskFlowStore.runtimeState.currentNodeId"
            @node-updated="handleNodeUpdate"
            @transition-updated="handleTransitionUpdate"
            @node-added="handleNodeAdd"
            @transition-added="handleTransitionAdd"
            @node-deleted="handleNodeDelete"
            @transition-deleted="handleTransitionDelete"
          />
        </el-tab-pane>



      </el-tabs>

      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <div class="left-actions">
          <el-button @click="saveConfiguration" type="primary" size="small">
            <el-icon><DocumentAdd /></el-icon>
            保存配置
          </el-button>
          <el-button @click="loadConfiguration" size="small">
            <el-icon><FolderOpened /></el-icon>
            加载配置
          </el-button>
          <el-upload
            ref="uploadRef"
            :show-file-list="false"
            :before-upload="importConfiguration"
            accept=".json"
            style="display: inline-block;"
          >
            <el-button size="small">
              <el-icon><Upload /></el-icon>
              导入文件
            </el-button>
          </el-upload>
          <el-button @click="exportConfiguration" size="small">
            <el-icon><Download /></el-icon>
            导出配置
          </el-button>
        </div>

        <div class="right-actions">
          <el-button @click="validateConfiguration" type="success" size="small">
            <el-icon><CircleCheck /></el-icon>
            验证配置
          </el-button>
          <el-button @click="resetConfiguration" type="warning" size="small">
            <el-icon><RefreshLeft /></el-icon>
            重置配置
          </el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useLoggerStore } from '../stores/logger'
import { useTaskFlowStore } from '../stores/taskFlow'
import TaskFlowDesigner from './TaskFlowDesignerSimple.vue'

import messageManager from '../utils/message'
import {
  Share, Setting, Close, DocumentAdd,
  FolderOpened, Download, Upload, CircleCheck, RefreshLeft,
  Connection, CircleClose, User
} from '@element-plus/icons-vue'

// 导入后端API
import {
  StartTaskFlow,
  StopTaskFlow,
  GetTaskFlowData,
  GetTaskFlowRuntimeState,
  GetCurrentTaskInfo,
  SaveTaskFlowData
} from 'wailsjs/go/main/App'

const loggerStore = useLoggerStore()
const taskFlowStore = useTaskFlowStore()

// 本地状态
const activeTab = ref('basic')
const isLoading = ref(false)
const flowStatus = ref({
  config: {},
  nodes: [],
  transitions: [],
  runtime: {
    isRunning: false,
    currentNodeId: '',
    currentStep: 0,
    heartbeatCount: 0,
    totalHeartbeats: 0
  }
})

const currentTaskInfo = ref({
  step: 0,
  taskId: 1,
  taskDistance: 360
})

// 流程配置 - 绑定到表单
const flowConfig = computed({
  get: () => taskFlowStore.taskFlowConfig,
  set: (value) => taskFlowStore.updateTaskFlowConfig(value)
})

// 心跳间隔（秒）
const heartbeatInterval = ref(5)

// 任务配置
const taskConfig = ref({
  taskId: 1,
  routeId: 1,
  taskName: '默认任务',
  taskDescription: '任务描述',
  taskDistance: 360,
  leftTime: 5,
  currentStation: {
    id: 1,
    name: '起始站点'
  },
  targetStation: {
    id: 2,
    name: '目标站点'
  }
})

// 高级配置
const advancedConfig = ref({
  enableLogging: true,
  logLevel: 'info',
  maxRetries: 3,
  timeout: 30000
})

// 定时刷新状态
let statusTimer = null

// 方法
const activateFlow = async () => {
  isLoading.value = true
  try {
    const result = await StartTaskFlow()
    await refreshStatus()
    loggerStore.logSystemEvent('任务状态流程已激活')
    messageManager.success('任务状态流程已激活：' + result)
  } catch (error) {
    messageManager.error('激活任务流程失败：' + error.message)
  } finally {
    isLoading.value = false
  }
}

const deactivateFlow = async () => {
  isLoading.value = true
  try {
    const result = await StopTaskFlow()
    await refreshStatus()
    loggerStore.logSystemEvent('任务状态流程已停用')
    messageManager.info('任务状态流程已停用：' + result)
  } catch (error) {
    messageManager.error('停用任务流程失败：' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 刷新状态
const refreshStatus = async () => {
  try {
    console.log('🔄 开始刷新任务流程状态...')

    // 获取任务流程数据和运行时状态
    console.log('📡 调用 GetTaskFlowData...')
    const taskFlowData = await GetTaskFlowData()
    console.log('✅ GetTaskFlowData 成功:', taskFlowData)

    console.log('📡 调用 GetTaskFlowRuntimeState...')
    const runtimeState = await GetTaskFlowRuntimeState()
    console.log('✅ GetTaskFlowRuntimeState 成功:', runtimeState)

    // 组装状态数据
    flowStatus.value = {
      config: taskFlowData.taskFlowConfig,
      nodes: taskFlowData.stateNodes,
      transitions: taskFlowData.stateTransitions,
      runtime: runtimeState
    }

    console.log('📡 调用 GetCurrentTaskInfo...')
    const taskInfo = await GetCurrentTaskInfo()
    console.log('✅ GetCurrentTaskInfo 成功:', taskInfo)
    currentTaskInfo.value = taskInfo

    console.log('🎉 任务流程状态刷新完成')
  } catch (error) {
    console.error('❌ 刷新任务流程状态失败:', error)
    messageManager.error('加载任务流程配置失败: ' + error.message)
  }
}

// 保存配置 - 调用后端API保存前端配置数据
const saveConfiguration = async () => {
  try {
    // 从前端Store获取当前配置数据
    const { useTaskFlowStore } = await import('../stores/taskFlow')
    const taskFlowStore = useTaskFlowStore()

    // 构建要保存的数据
    const taskFlowData = {
      taskFlowConfig: taskFlowStore.taskFlowConfig,
      stateNodes: taskFlowStore.stateNodes,
      stateTransitions: taskFlowStore.stateTransitions,
      taskConfig: {
        taskId: taskFlowStore.taskFlowConfig.id ? parseInt(taskFlowStore.taskFlowConfig.id.replace(/\D/g, '')) || 1 : 1,
        routeId: 1,
        taskName: '默认任务',
        taskDescription: '任务描述',
        taskDistance: 360,
        leftTime: 5,
        currentStation: { id: 1, name: '起始站点' },
        targetStation: { id: 2, name: '目标站点' }
      }
    }

    // 调用后端API保存
    const { SaveTaskFlowData } = await import('wailsjs/go/main/App')
    const result = await SaveTaskFlowData(taskFlowData)

    messageManager.success('配置保存成功：' + result)
    loggerStore.logSystemEvent('任务流程配置已保存到后端', taskFlowData)
  } catch (error) {
    messageManager.error('保存配置失败：' + error.message)
    console.error('保存任务流程配置失败:', error)
  }
}

// 事件处理
const handleNodeUpdate = (nodeId, updates) => {
  taskFlowStore.updateStateNode(nodeId, updates)
}

const handleTransitionUpdate = (transitionId, updates) => {
  console.log('handleTransitionUpdate called:', transitionId, updates)
  taskFlowStore.updateTransition(transitionId, updates)
  console.log('Transition updated in store')
}

const handleNodeAdd = (node) => {
  console.log('handleNodeAdd called with node:', node)
  taskFlowStore.addStateNode(node)
  console.log('Node added to store, current nodes:', taskFlowStore.stateNodes)
}

const handleTransitionAdd = (transition) => {
  console.log('handleTransitionAdd called with transition:', transition)

  // 添加新连接到store
  taskFlowStore.addTransition(transition)

  // 检查源节点的所有出口连接，重新分配概率
  redistributeProbabilities(transition.fromNodeId)
}

// 重新分配节点的出口连接概率
const redistributeProbabilities = (fromNodeId) => {
  console.log('redistributeProbabilities called for nodeId:', fromNodeId)

  // 获取该节点的所有出口连接
  const outgoingTransitions = taskFlowStore.stateTransitions.filter(t => t.fromNodeId === fromNodeId)
  console.log('Found outgoing transitions:', outgoingTransitions)

  if (outgoingTransitions.length > 1) {
    // 多连接时，平均分配概率
    const equalProbability = 1.0 / outgoingTransitions.length
    console.log(`Redistributing probability to ${equalProbability} for ${outgoingTransitions.length} transitions`)

    outgoingTransitions.forEach(transition => {
      if (Math.abs(transition.probability - equalProbability) > 0.001) {
        console.log(`Updating transition ${transition.id} probability from ${transition.probability} to ${equalProbability}`)
        taskFlowStore.updateTransition(transition.id, { probability: equalProbability })
      }
    })
  } else if (outgoingTransitions.length === 1) {
    // 单一连接时，设为100%
    const transition = outgoingTransitions[0]
    if (transition.probability !== 1.0) {
      console.log(`Setting single transition ${transition.id} to 100%`)
      taskFlowStore.updateTransition(transition.id, { probability: 1.0 })
    }
  }
}

const handleNodeDelete = (nodeId) => {
  taskFlowStore.removeStateNode(nodeId)
}

const handleTransitionDelete = (transitionId) => {
  taskFlowStore.removeTransition(transitionId)
}









// 配置管理（已移动到上面的新版本）

// 加载配置 - 从后端加载配置数据
const loadConfiguration = async () => {
  try {
    // 调用后端API获取配置数据
    const { GetTaskFlowData } = await import('wailsjs/go/main/App')
    const taskFlowData = await GetTaskFlowData()

    if (!taskFlowData || !taskFlowData.taskFlowConfig) {
      messageManager.warning('后端没有找到保存的配置')
      return
    }

    // 加载到前端Store
    const { useTaskFlowStore } = await import('../stores/taskFlow')
    const taskFlowStore = useTaskFlowStore()

    // 加载任务流配置
    if (taskFlowData.taskFlowConfig) {
      taskFlowStore.updateTaskFlowConfig(taskFlowData.taskFlowConfig)
    }

    // 加载状态节点
    if (taskFlowData.stateNodes) {
      taskFlowStore.loadStateNodes(taskFlowData.stateNodes)
    }

    // 加载状态转换
    if (taskFlowData.stateTransitions) {
      taskFlowStore.loadStateTransitions(taskFlowData.stateTransitions)
    }

    messageManager.success('配置已从后端加载')
    loggerStore.logSystemEvent('任务流程配置已从后端加载', taskFlowData)
  } catch (error) {
    console.error('配置加载失败:', error)
    messageManager.error('配置加载失败: ' + error.message)
  }
}

const exportConfiguration = () => {
  const config = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    taskFlowConfig: taskFlowStore.taskFlowConfig,
    stateNodes: taskFlowStore.stateNodes,
    stateTransitions: taskFlowStore.stateTransitions,
    taskConfig: taskConfig.value
  }
  
  const blob = new Blob([JSON.stringify(config, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `task-flow-config-${Date.now()}.json`
  a.click()
  
  messageManager.success('配置已导出')
}

const importConfiguration = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target.result)

        // 验证配置文件格式
        if (!config.version || !config.taskFlowConfig) {
          throw new Error('无效的配置文件格式')
        }

        // 加载任务流配置
        if (config.taskFlowConfig) {
          Object.assign(flowConfig.value, config.taskFlowConfig)
          taskFlowStore.updateTaskFlowConfig(config.taskFlowConfig)
          heartbeatInterval.value = config.taskFlowConfig.heartbeatInterval / 1000
        }

        // 加载状态节点
        if (config.stateNodes) {
          taskFlowStore.loadStateNodes(config.stateNodes)
        }

        // 加载状态转换
        if (config.stateTransitions) {
          taskFlowStore.loadStateTransitions(config.stateTransitions)
        }

        // 加载高级配置
        if (config.advancedConfig) {
          Object.assign(advancedConfig.value, config.advancedConfig)
        }

        messageManager.success(`配置文件 ${file.name} 导入成功`)
        resolve(false) // 阻止上传
      } catch (error) {
        console.error('配置文件导入失败:', error)
        messageManager.error('配置文件导入失败: ' + error.message)
        reject(error)
      }
    }

    reader.onerror = () => {
      messageManager.error('文件读取失败')
      reject(new Error('文件读取失败'))
    }

    reader.readAsText(file)
  })
}

const validateConfiguration = () => {
  // 配置验证逻辑
  const errors = []
  
  // 检查节点配置
  if (taskFlowStore.stateNodes.length === 0) {
    errors.push('至少需要一个状态节点')
  }
  
  // 检查转换配置
  const nodeIds = taskFlowStore.stateNodes.map(n => n.id)
  for (const transition of taskFlowStore.stateTransitions) {
    if (!nodeIds.includes(transition.fromNodeId) || !nodeIds.includes(transition.toNodeId)) {
      errors.push(`转换 ${transition.id} 引用了不存在的节点`)
    }
  }
  
  if (errors.length === 0) {
    messageManager.success('配置验证通过')
  } else {
    messageManager.error(`配置验证失败：${errors.join(', ')}`)
  }
}

const resetConfiguration = () => {
  // 重置配置逻辑
  messageManager.warning('配置已重置')
}

onMounted(async () => {
  console.log('TaskFlowPanel mounted - 连接后端任务流程管理器')

  // 测试API连接
  try {
    console.log('🧪 测试API连接...')
    const testData = await GetTaskFlowData()
    console.log('✅ API连接成功，测试数据:', testData)
  } catch (error) {
    console.error('❌ API连接失败:', error)
    messageManager.error('无法连接到后端服务: ' + error.message)
    return
  }

  // 初始化状态
  await refreshStatus()

  // 启动定时刷新
  statusTimer = setInterval(refreshStatus, 2000) // 每2秒刷新一次状态
})

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
})
</script>

<style scoped>
.task-flow-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 基础配置面板样式 */
.basic-config {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0; /* 允许收缩 */
  overflow: visible; /* 让内容自然流动 */
}

/* 表单行间距 */
.basic-config .el-row {
  margin-bottom: 16px;
}

.basic-config .el-row:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: var(--accent-primary);
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flow-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-muted);
}

.status-running .status-dot {
  background: var(--accent-primary);
  animation: pulse 2s infinite;
}

.status-stopped .status-dot {
  background: var(--text-muted);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.task-flow-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.flow-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* 强制选项卡头部在顶部 - 关键修复 */
.flow-tabs :deep(.el-tabs__header) {
  order: -1; /* 强制头部排在最前面 */
  margin: 0;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
}



/* 确保选项卡内容区域填充剩余空间 */
.flow-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: auto;
  min-height: 0; /* 允许flex收缩 */
  background: var(--bg-primary);
  order: 1; /* 确保内容在头部之后 */
}

/* 确保每个选项卡面板填充可用空间 */
.flow-tabs :deep(.el-tab-pane) {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  overflow: visible; /* 改为visible，让内容自然流动 */
  min-height: 0; /* 允许flex收缩 */
}

/* 流程设计器选项卡特殊处理 */
.flow-tabs :deep(.el-tab-pane[aria-labelledby="tab-flow"]) {
  padding: 0;
  overflow: hidden;
}

/* Element Plus 选项卡布局修复 - 关键部分 */
.flow-tabs :deep(.el-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* 允许收缩 */
}

/* 强制选项卡头部在最顶部 */
.flow-tabs :deep(.el-tabs__header) {
  order: -2 !important; /* 最高优先级排序 */
  flex-shrink: 0 !important;
  margin-bottom: 0 !important;
}

/* 强制选项卡内容在头部之后 */
.flow-tabs :deep(.el-tabs__content) {
  order: 1 !important;
  flex: 1 !important;
  min-height: 0 !important; /* 关键：允许内容区域收缩 */
  overflow: auto !important; /* 确保内容可滚动 */
}

/* 确保导航包装器正确定位 */
.flow-tabs :deep(.el-tabs__nav-wrap) {
  position: relative;
  z-index: 10;
}

/* 修复选项卡项目样式 */
.flow-tabs :deep(.el-tabs__item) {
  position: relative;
}





.advanced-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-card {
  margin-bottom: 16px;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.event-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  flex-shrink: 0;
  margin-top: auto;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .bottom-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .left-actions,
  .right-actions {
    justify-content: center;
  }

  .basic-config .el-col {
    margin-bottom: 12px;
  }
}

/* 表单标签样式 */
.basic-config :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}

/* 输入框样式 */
.basic-config :deep(.el-input),
.basic-config :deep(.el-select),
.basic-config :deep(.el-input-number) {
  width: 100%;
}

/* 文本域样式 */
.basic-config :deep(.el-textarea) {
  width: 100%;
}
</style>

<template>
  <div class="pagination-dots">
    <div 
      v-for="(page, index) in pages" 
      :key="index"
      :class="['pagination-dot', { 'active': currentPage === index }]"
      @click="$emit('page-change', index)"
      :title="page.title"
    >
      <div class="dot-inner">
        <el-icon v-if="page.icon" :size="12">
          <component :is="page.icon" />
        </el-icon>
      </div>
      <span class="dot-label">{{ page.label }}</span>
    </div>
  </div>
</template>

<script setup>
import {
  Monitor,
  Share
} from '@element-plus/icons-vue'

defineProps({
  currentPage: {
    type: Number,
    default: 0
  },
  pages: {
    type: Array,
    default: () => [
      {
        label: '主界面',
        title: '连接、设备、心跳、命令和日志面板',
        icon: Monitor
      },
      {
        label: '任务流程',
        title: '任务状态流程配置和监控',
        icon: Share
      }
    ]
  }
})

defineEmits(['page-change'])
</script>

<style scoped>
.pagination-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  /* 移除所有装饰样式，让tab按钮直接显示在标题栏中 */
}

.pagination-dot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 12px;
  min-width: 60px;
}

.pagination-dot:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.pagination-dot.active {
  background: rgba(64, 158, 255, 0.2);
  border: 1px solid rgba(64, 158, 255, 0.4);
}

.pagination-dot.active .dot-inner {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  transform: scale(1.1);
}

.dot-inner {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.dot-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.pagination-dot.active .dot-label {
  color: var(--accent-primary);
  font-weight: 600;
}

.pagination-dot:hover .dot-label {
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-dots {
    gap: 12px;
    /* 移除padding，让按钮直接显示 */
  }
  
  .pagination-dot {
    min-width: 50px;
    padding: 6px 8px;
  }
  
  .dot-inner {
    width: 20px;
    height: 20px;
  }
  
  .dot-label {
    font-size: 10px;
  }
}
</style>

<template>
  <el-card class="connection-panel">
    <template #header>
      <div class="card-header connection-header">
        <span class="header-title">
          <div class="title-icon connection-icon">
            <el-icon><Link /></el-icon>
          </div>
          <span class="title-text">服务器连接配置</span>
        </span>
        <div class="header-actions">
          <el-button 
            size="small" 
            type="primary" 
            :icon="Refresh" 
            @click="resetToDefaults"
            :disabled="connectionStore.isConnecting"
          >
            重置
          </el-button>
        </div>
      </div>
    </template>

    <div class="connection-config">
      <!-- 服务器配置 -->
      <el-form :model="serverConfig" label-width="100px" size="default">
        <el-form-item label="服务器地址">
          <el-input
            v-model="serverConfig.host"
            placeholder="请输入服务器IP地址"
            :disabled="connectionStore.isConnected"
            @change="updateConfig"
          >
            <template #prepend>
              <el-icon><Monitor /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="端口号">
          <el-input-number
            v-model="serverConfig.port"
            :min="1"
            :max="65535"
            :disabled="connectionStore.isConnected"
            @change="updateConfig"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="连接超时">
          <el-input-number
            v-model="timeoutSeconds"
            :min="1"
            :max="60"
            :disabled="connectionStore.isConnected"
            @change="updateTimeoutConfig"
            style="width: 100%"
          >
            <template #append>秒</template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="保活超时">
          <el-input-number
            v-model="keepaliveSeconds"
            :min="30"
            :max="600"
            :disabled="connectionStore.isConnected"
            @change="updateKeepaliveConfig"
            style="width: 100%"
          >
            <template #append>秒</template>
          </el-input-number>
        </el-form-item>
      </el-form>

      <!-- 连接状态 -->
      <div class="connection-status">
        <div class="status-row">
          <span class="status-label">连接状态:</span>
          <div :class="['status-indicator', connectionStore.connectionStatus]">
            <div class="status-dot"></div>
            <span>{{ connectionStore.connectionStatusText }}</span>
          </div>
        </div>

        <div v-if="connectionStore.lastConnectedTime" class="status-row">
          <span class="status-label">连接时间:</span>
          <span class="status-value">{{ formatTime(connectionStore.lastConnectedTime) }}</span>
        </div>

        <div v-if="connectionStore.connectionError" class="status-row error">
          <span class="status-label">错误信息:</span>
          <span class="status-value">{{ connectionStore.connectionError }}</span>
        </div>
      </div>

      <!-- 连接操作按钮 -->
      <div class="connection-actions">
        <el-button
          v-if="!connectionStore.isConnected"
          type="primary"
          :loading="connectionStore.isConnecting"
          @click="handleConnect"
          :disabled="!isConfigValid"
        >
          <el-icon><Connection /></el-icon>
          {{ connectionStore.isConnecting ? '连接中...' : '连接' }}
        </el-button>

        <el-button
          v-else
          type="danger"
          @click="handleDisconnect"
        >
          <el-icon><Close /></el-icon>
          断开连接
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useConnectionStore } from '../stores/connection'
import { useDeviceStore } from '../stores/device'
import { useLoggerStore } from '../stores/logger'
import {
  Link, Monitor, Connection, Close, Refresh
} from '@element-plus/icons-vue'
import messageManager from '../utils/message'
import { EventsOn, EventsOff } from 'wailsjs/runtime/runtime'
// import { ElMessage } from 'element-plus' // 已替换为统一消息管理器
// 暂时不使用导入，而是手动创建包装函数
// import {
//   CreateConnection,
//   Connect,
//   SendRegister,
//   Disconnect
// } from '../../wailsjs/go/main/App'

// 🔧 手动创建Wails绑定包装函数
const waitForWails = async (maxAttempts = 50) => {
  let attempts = 0
  while (attempts < maxAttempts) {
    if (window.go && window.go.main && window.go.main.App) {
      return true
    }
    await new Promise(resolve => setTimeout(resolve, 100))
    attempts++
  }
  return false
}

const CreateConnection = async (arg1, arg2) => {
  const isReady = await waitForWails()
  if (!isReady) {
    throw new Error('Wails bindings not available after waiting')
  }
  return window.go.main.App.CreateConnection(arg1, arg2)
}

const Connect = async (arg1) => {
  const isReady = await waitForWails()
  if (!isReady) {
    throw new Error('Wails bindings not available after waiting')
  }
  return window.go.main.App.Connect(arg1)
}

const SendRegister = async (arg1) => {
  const isReady = await waitForWails()
  if (!isReady) {
    throw new Error('Wails bindings not available after waiting')
  }
  return window.go.main.App.SendRegister(arg1)
}

const Disconnect = async (arg1) => {
  const isReady = await waitForWails()
  if (!isReady) {
    throw new Error('Wails bindings not available after waiting')
  }
  return window.go.main.App.Disconnect(arg1)
}

const connectionStore = useConnectionStore()
const deviceStore = useDeviceStore()
const loggerStore = useLoggerStore()

// 使用统一的消息管理器
const showMessage = (type, content, duration = 3000) => {
  return messageManager[type](content, duration)
}

// 本地配置状态
const serverConfig = ref({ ...connectionStore.serverConfig })
const timeoutSeconds = ref(Math.floor(connectionStore.serverConfig.timeout / 1000))
const keepaliveSeconds = ref(Math.floor(connectionStore.serverConfig.keepaliveTimeout / 1000))

// 计算属性
const isConfigValid = computed(() => {
  return serverConfig.value.host && 
         serverConfig.value.port > 0 && 
         serverConfig.value.port <= 65535
})

// 监听配置变化
watch(() => connectionStore.serverConfig, (newConfig) => {
  serverConfig.value = { ...newConfig }
  timeoutSeconds.value = Math.floor(newConfig.timeout / 1000)
  keepaliveSeconds.value = Math.floor(newConfig.keepaliveTimeout / 1000)
}, { deep: true })

// 方法
const updateConfig = () => {
  connectionStore.updateServerConfig(serverConfig.value)
}

const updateTimeoutConfig = () => {
  connectionStore.updateServerConfig({
    timeout: timeoutSeconds.value * 1000
  })
}

const updateKeepaliveConfig = () => {
  connectionStore.updateServerConfig({
    keepaliveTimeout: keepaliveSeconds.value * 1000
  })
}

const resetToDefaults = () => {
  const defaultConfig = {
    host: '**************',
    port: 2020,
    timeout: 5000,
    keepaliveTimeout: 150000
  }
  serverConfig.value = { ...defaultConfig }
  timeoutSeconds.value = 5
  keepaliveSeconds.value = 150
  connectionStore.updateServerConfig(defaultConfig)
  showMessage('success', '配置已重置为默认值')
}



const handleConnect = async () => {
  try {
    // 🔍 使用新的包装函数，自动等待Wails绑定
    console.log('🔍 Starting connection with auto-waiting wrapper functions...')

    connectionStore.setConnecting(true)
    loggerStore.logConnectionEvent('connecting', serverConfig.value)

    // 第1步：创建连接配置
    const connectionId = `main_conn_${Date.now()}`
    const connectionConfig = {
      id: connectionId,
      host: serverConfig.value.host,
      port: serverConfig.value.port,
      timeout: Math.floor(serverConfig.value.timeout / 1000),
      keepalive: Math.floor(serverConfig.value.keepaliveTimeout / 1000),
      encryptionKey: ''
    }

    const deviceConfig = {
      imsi: deviceStore.deviceConfig.imsi,
      imei: deviceStore.deviceConfig.imei,
      model: deviceStore.deviceConfig.model,
      softwareVer: deviceStore.deviceConfig.softwareVer,
      hardwareVer: deviceStore.deviceConfig.hardwareVer,
      vendor: deviceStore.deviceConfig.vendor,
      ci: deviceStore.deviceConfig.ci,
      pci: deviceStore.deviceConfig.pci,
      manageIp: deviceStore.deviceConfig.manageIp,
      lng: deviceStore.deviceConfig.lng,
      lat: deviceStore.deviceConfig.lat
    }

    // 第2步：创建TCP连接配置
    showMessage('info', '正在创建连接配置...', 0) // 持续显示直到被替换
    console.log('🔧 Calling CreateConnection with configs:', { connectionConfig, deviceConfig })
    const createResult = await CreateConnection(connectionConfig, deviceConfig)
    console.log('✅ CreateConnection result:', createResult)
    loggerStore.logConnectionEvent('connection_created', { connectionId, result: createResult })

    // 第3步：建立TCP连接
    showMessage('info', '正在连接服务器...', 0) // 持续显示直到被替换
    const connectResult = await Connect(connectionId)

    // 检查连接结果
    if (connectResult.includes('失败') || connectResult.includes('错误')) {
      throw new Error(connectResult)
    }

    loggerStore.logConnectionEvent('tcp_connected', { connectionId, result: connectResult })
    showMessage('success', 'TCP连接建立成功', 1500) // 短暂显示

    // 等待一下让用户看到成功消息
    await new Promise(resolve => setTimeout(resolve, 800))

    // 第4步：发送设备注册
    showMessage('info', '正在注册设备...', 0) // 持续显示直到被替换
    const registerResult = await SendRegister(connectionId)

    // 检查注册结果
    if (registerResult.includes('失败') || registerResult.includes('错误')) {
      throw new Error(registerResult)
    }

    loggerStore.logConnectionEvent('device_registered', { connectionId, result: registerResult })

    // 保存连接ID到store
    connectionStore.setActiveConnectionId(connectionId)
    connectionStore.setConnected(true)
    connectionStore.setConnecting(false)

    loggerStore.logConnectionEvent('connected', {
      ...serverConfig.value,
      connectionId,
      deviceInfo: deviceConfig
    })

    // 状态检查功能暂时移除
    // startStatusCheck()

    showMessage('success', '设备连接并注册成功！', 3000)

  } catch (error) {
    connectionStore.setConnecting(false)
    connectionStore.setConnectionError(error.message || error)
    loggerStore.logConnectionEvent('error', {
      ...serverConfig.value,
      error: error.message || error
    })

    // 使用统一消息管理器显示错误消息
    messageManager.error(`连接失败: ${error.message || error}`, 5000)

    showMessage('error', `连接失败: ${error.message || error}`, 5000)
  }
}

const handleDisconnect = async () => {
  try {
    const connectionId = connectionStore.activeConnectionId
    if (connectionId) {
      showMessage('info', '正在断开连接...', 0)
      const result = await Disconnect(connectionId)
      loggerStore.logConnectionEvent('disconnected', { connectionId, result })
    }

    connectionStore.setConnected(false)
    connectionStore.setActiveConnectionId('')
    connectionStore.setConnectionError('')

    // 状态检查功能暂时移除
    // stopStatusCheck()

    showMessage('success', '已断开连接', 2000)
  } catch (error) {
    loggerStore.logConnectionEvent('disconnect_error', { error: error.message || error })

    // 使用统一消息管理器显示警告消息
    messageManager.warning(`断开连接失败: ${error.message || error}`, 5000)

    showMessage('error', `断开连接失败: ${error.message || error}`, 5000)
    // 即使断开失败，也要重置本地状态
    connectionStore.setConnected(false)
    connectionStore.setActiveConnectionId('')
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 🧪 测试Wails绑定的函数
const testWailsBindings = async () => {
  console.log('🧪 Testing Wails bindings...')
  console.log('🔍 window.go:', typeof window.go)
  console.log('🔍 window.go.main:', typeof window.go?.main)
  console.log('🔍 window.go.main.App:', typeof window.go?.main?.App)
  console.log('🔍 CreateConnection function:', typeof CreateConnection)

  try {
    console.log('🧪 Waiting for Wails bindings...')
    const isReady = await waitForWails()

    if (!isReady) {
      throw new Error('Wails bindings not available after waiting 5 seconds')
    }

    console.log('✅ Wails bindings are ready!')
    console.log('🔍 Available methods:', Object.keys(window.go.main.App))

    showMessage('success', 'Wails绑定测试成功！', 3000)
  } catch (error) {
    console.error('❌ Test failed:', error)
    showMessage('error', `Wails绑定测试失败: ${error.message}`, 5000)
  }
}

// 暴露测试函数到全局
if (typeof window !== 'undefined') {
  window.testWailsBindings = testWailsBindings
}

// 状态检查功能暂时移除，避免绑定问题
// TODO: 重新实现状态检查功能

// 🆕 组件挂载时初始化
onMounted(async () => {
  console.log('🚀 ConnectionPanel mounted')

  // 🔍 调试Wails绑定状态
  console.log('🔍 window.go:', typeof window.go)
  console.log('🔍 window.runtime:', typeof window.runtime)
  console.log('🔍 window object keys:', Object.keys(window).filter(k => k.includes('go') || k.includes('wails') || k.includes('runtime')))

  // 等待Vue完全渲染
  await nextTick()

  // 等待Wails运行时初始化
  let attempts = 0
  const maxAttempts = 50
  while (typeof window.runtime === 'undefined' && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100))
    attempts++
  }

  if (typeof window.runtime !== 'undefined') {
    console.log('✅ 设置连接状态变化事件监听器')

    // 监听连接状态变化事件
    EventsOn('connection-status-changed', (data) => {
      console.log('🔔 收到连接状态变化事件:', data)

      if (data.connectionID === connectionStore.activeConnectionId) {
        // 更新连接状态
        if (data.status === 'error' || data.status === 'disconnected') {
          connectionStore.isConnected = false
          connectionStore.isConnecting = false
          connectionStore.connectionError = data.error || '连接已断开'

          loggerStore.error('connection', `连接断开: ${data.error || '未知原因'}`)

          // 使用统一消息管理器显示错误消息
          messageManager.error(`连接已断开: ${data.error || '未知原因'}`, 5000)

          // 同时显示messageManager的错误提示
          messageManager.showError(`连接已断开: ${data.error || '未知原因'}`)
        } else if (data.status === 'connected') {
          connectionStore.isConnected = true
          connectionStore.isConnecting = false
          connectionStore.connectionError = ''

          loggerStore.success('connection', '连接已建立')

          // 使用统一消息管理器显示成功消息
          messageManager.success('与服务器的连接已成功建立', 3000)
        }
      }
    })
  } else {
    console.warn('⚠️ Wails运行时未初始化，无法设置事件监听器')
  }

  // 状态检查功能暂时移除
  console.log('📝 Status monitoring temporarily disabled')
})

// 组件卸载时清理
onUnmounted(() => {
  // 关闭当前消息
  messageManager.closeCurrentMessage()

  // 清理事件监听器
  if (typeof window.runtime !== 'undefined') {
    console.log('🧹 清理连接状态变化事件监听器')
    EventsOff('connection-status-changed')
  }

  // 状态检查功能暂时移除
  // stopStatusCheck()
})
</script>

<style scoped>
.connection-panel {
  height: auto;
}

.connection-panel :deep(.el-card__body) {
  overflow: visible;
  padding: 12px;
  height: auto;
  margin-top: 0 !important;
  border-top: none !important;
}

.connection-panel :deep(.el-card__header) {
  padding: 0 !important;
  border-bottom: none !important;
  margin-bottom: 0 !important;
}

/* 连接面板主题色 - 蓝色 */
.connection-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  margin: 0;
  padding: 12px;
  border-radius: 8px 8px 0 0;
}

.connection-header .header-title {
  color: #ffffff !important;
}

.connection-header .header-actions .el-button {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.connection-header .header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
}

.connection-icon {
  background: rgba(255, 255, 255, 0.15);
}

.connection-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.connection-status {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 13px;
}

.status-row:last-child {
  margin-bottom: 0;
}

.status-row.error .status-value {
  color: var(--accent-danger);
}

.status-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.status-value {
  color: var(--text-primary);
}

.connection-actions {
  display: flex;
  gap: 8px;
}

.connection-actions .el-button {
  flex: 1;
  font-size: 13px;
}


</style>

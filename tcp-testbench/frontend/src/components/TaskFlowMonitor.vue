<template>
  <div class="task-flow-monitor">
    <!-- 实时状态卡片 -->
    <div class="monitor-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="status-card current-state">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Position /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">当前状态</div>
                <div class="card-value">Step {{ currentNode?.step || 0 }}</div>
                <div class="card-subtitle">{{ currentNode?.name || '未知状态' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card execution-time">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">执行时间</div>
                <div class="card-value">{{ formatDuration(executionDuration) }}</div>
                <div class="card-subtitle">{{ runtimeState.isRunning ? '运行中' : '已停止' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card heartbeat-count">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">心跳次数</div>
                <div class="card-value">{{ runtimeState.totalHeartbeats || 0 }}</div>
                <div class="card-subtitle">总计 {{ runtimeState.heartbeatCount || 0 }} 次</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card next-transition">
            <div class="card-content">
              <div class="card-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">下次转换</div>
                <div class="card-value">{{ nextTransitionTime }}</div>
                <div class="card-subtitle">{{ nextTransitionDescription }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主监控区域 -->
    <div class="monitor-main">
      <el-row :gutter="16">
        <!-- 执行历史 -->
        <el-col :span="12">
          <el-card class="history-card">
            <template #header>
              <div class="card-header">
                <span>执行历史</span>
                <el-button @click="clearHistory" size="small" type="danger" text>
                  <el-icon><Delete /></el-icon>
                  清空
                </el-button>
              </div>
            </template>
            
            <div class="history-content">
              <el-timeline>
                <el-timeline-item
                  v-for="event in displayHistory"
                  :key="event.id"
                  :timestamp="formatTimestamp(event.timestamp)"
                  :type="getEventType(event.step)"
                  :size="event.step === runtimeState.currentStep ? 'large' : 'normal'"
                >
                  <div class="history-event">
                    <div class="event-title">{{ event.description }}</div>
                    <div class="event-details" v-if="event.details">{{ event.details }}</div>
                    <div class="event-meta">
                      <span class="event-step">Step {{ event.step }}</span>
                      <span class="event-heartbeat">心跳 #{{ event.heartbeatCount }}</span>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
              
              <div v-if="executionHistory.length === 0" class="empty-history">
                <el-empty description="暂无执行历史" />
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 统计图表 -->
        <el-col :span="12">
          <el-card class="charts-card">
            <template #header>
              <div class="card-header">
                <span>统计分析</span>
                <el-select v-model="chartType" size="small" style="width: 120px;">
                  <el-option label="状态分布" value="state_distribution" />
                  <el-option label="执行趋势" value="execution_trend" />
                  <el-option label="转换统计" value="transition_stats" />
                </el-select>
              </div>
            </template>
            
            <div class="charts-content">
              <!-- 状态分布饼图 -->
              <div v-if="chartType === 'state_distribution'" class="chart-container">
                <div class="chart-title">状态时间分布</div>
                <div class="distribution-chart">
                  <div 
                    v-for="(item, index) in stateDistribution" 
                    :key="item.step"
                    class="distribution-item"
                  >
                    <div class="item-bar">
                      <div 
                        class="bar-fill" 
                        :style="{ 
                          width: `${item.percentage}%`, 
                          backgroundColor: getStepColor(item.step) 
                        }"
                      ></div>
                    </div>
                    <div class="item-info">
                      <span class="item-label">Step {{ item.step }}</span>
                      <span class="item-value">{{ item.percentage.toFixed(1) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 执行趋势图 -->
              <div v-if="chartType === 'execution_trend'" class="chart-container">
                <div class="chart-title">执行趋势</div>
                <div class="trend-chart">
                  <div class="trend-stats">
                    <div class="stat-row">
                      <span class="stat-label">总执行次数:</span>
                      <span class="stat-value">{{ statistics.totalExecutions }}</span>
                    </div>
                    <div class="stat-row">
                      <span class="stat-label">成功完成:</span>
                      <span class="stat-value success">{{ statistics.successfulCompletions }}</span>
                    </div>
                    <div class="stat-row">
                      <span class="stat-label">执行失败:</span>
                      <span class="stat-value error">{{ statistics.failures }}</span>
                    </div>
                    <div class="stat-row">
                      <span class="stat-label">平均时长:</span>
                      <span class="stat-value">{{ formatDuration(statistics.averageExecutionTime) }}</span>
                    </div>
                  </div>
                  
                  <div class="success-rate">
                    <div class="rate-label">成功率</div>
                    <div class="rate-circle">
                      <div 
                        class="rate-progress" 
                        :style="{ 
                          background: `conic-gradient(var(--el-color-success) ${successRate}%, var(--el-color-info-light-8) 0%)` 
                        }"
                      >
                        <div class="rate-text">{{ successRate.toFixed(1) }}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 转换统计 -->
              <div v-if="chartType === 'transition_stats'" class="chart-container">
                <div class="chart-title">转换统计</div>
                <div class="transition-stats">
                  <div 
                    v-for="(count, transitionId) in statistics.transitionCounts" 
                    :key="transitionId"
                    class="transition-item"
                  >
                    <div class="transition-info">
                      <span class="transition-name">{{ getTransitionName(transitionId) }}</span>
                      <span class="transition-count">{{ count }} 次</span>
                    </div>
                    <div class="transition-bar">
                      <div 
                        class="bar-fill" 
                        :style="{ width: `${(count / maxTransitionCount) * 100}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时日志 -->
    <div class="monitor-logs">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>实时日志</span>
            <div class="log-controls">
              <el-switch 
                v-model="autoScroll" 
                size="small"
                active-text="自动滚动"
                inactive-text=""
              />
              <el-button @click="clearLogs" size="small" type="danger" text>
                <el-icon><Delete /></el-icon>
                清空日志
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="logs-content" ref="logsContainer">
          <div 
            v-for="log in realtimeLogs" 
            :key="log.id"
            :class="['log-entry', `log-${log.level}`]"
          >
            <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
            <span class="log-data" v-if="log.data">{{ JSON.stringify(log.data) }}</span>
          </div>
          
          <div v-if="realtimeLogs.length === 0" class="empty-logs">
            <el-empty description="暂无日志记录" />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import {
  Position, Timer, DataLine, Clock, Delete
} from '@element-plus/icons-vue'

const props = defineProps({
  runtimeState: {
    type: Object,
    default: () => ({})
  },
  statistics: {
    type: Object,
    default: () => ({})
  },
  executionHistory: {
    type: Array,
    default: () => []
  },
  currentNode: {
    type: Object,
    default: () => null
  }
})

// 本地状态
const chartType = ref('state_distribution')
const autoScroll = ref(true)
const logsContainer = ref(null)
const realtimeLogs = ref([])

// 计算属性
const executionDuration = computed(() => {
  if (!props.runtimeState.startTime) return 0
  return Date.now() - props.runtimeState.startTime
})

const nextTransitionTime = computed(() => {
  if (!props.runtimeState.nextTransitionTime) return '--'
  const remaining = props.runtimeState.nextTransitionTime - Date.now()
  return remaining > 0 ? formatDuration(remaining) : '即将转换'
})

const nextTransitionDescription = computed(() => {
  return '等待状态转换'
})

const displayHistory = computed(() => {
  return props.executionHistory.slice(-10).reverse()
})

const stateDistribution = computed(() => {
  const distribution = {}
  let total = 0
  
  props.executionHistory.forEach(event => {
    if (!distribution[event.step]) {
      distribution[event.step] = 0
    }
    distribution[event.step]++
    total++
  })
  
  return Object.entries(distribution).map(([step, count]) => ({
    step: parseInt(step),
    count,
    percentage: total > 0 ? (count / total) * 100 : 0
  }))
})

const successRate = computed(() => {
  const total = props.statistics.totalExecutions || 0
  const successful = props.statistics.successfulCompletions || 0
  return total > 0 ? (successful / total) * 100 : 0
})

const maxTransitionCount = computed(() => {
  const counts = Object.values(props.statistics.transitionCounts || {})
  return Math.max(...counts, 1)
})

// 方法
const formatDuration = (ms) => {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatLogTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

const getEventType = (step) => {
  const typeMap = {
    0: 'info',
    1: 'primary',
    2: 'success',
    3: 'warning',
    6: 'warning',
    8: 'danger',
    9: 'danger'
  }
  return typeMap[step] || 'info'
}

const getStepColor = (step) => {
  const colorMap = {
    0: '#909399',
    1: '#409EFF',
    2: '#67C23A',
    3: '#E6A23C',
    6: '#F56C6C',
    8: '#F56C6C',
    9: '#F56C6C'
  }
  return colorMap[step] || '#909399'
}

const getTransitionName = (transitionId) => {
  // 这里应该根据实际的转换ID获取名称
  return `转换 ${transitionId.split('_').pop()}`
}

const clearHistory = () => {
  // 触发父组件清空历史
  console.log('清空执行历史')
}

const clearLogs = () => {
  realtimeLogs.value = []
}

const addLog = (level, message, data = null) => {
  const log = {
    id: Date.now(),
    timestamp: Date.now(),
    level,
    message,
    data
  }
  
  realtimeLogs.value.push(log)
  
  // 限制日志数量
  if (realtimeLogs.value.length > 100) {
    realtimeLogs.value.shift()
  }
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      if (logsContainer.value) {
        logsContainer.value.scrollTop = logsContainer.value.scrollHeight
      }
    })
  }
}

// 监听运行状态变化
watch(() => props.runtimeState.currentStep, (newStep, oldStep) => {
  if (newStep !== oldStep) {
    addLog('info', `状态转换: ${oldStep} → ${newStep}`, { newStep, oldStep })
  }
})

watch(() => props.runtimeState.isRunning, (isRunning) => {
  if (isRunning) {
    addLog('success', '任务流程开始执行')
  } else {
    addLog('warning', '任务流程已停止')
  }
})

// 生命周期
onMounted(() => {
  addLog('info', '监控面板已初始化')
})
</script>

<style scoped>
.task-flow-monitor {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: var(--bg-secondary);
}

.monitor-cards {
  flex-shrink: 0;
}

.status-card {
  height: 100px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-card.current-state {
  background: linear-gradient(135deg, #409EFF, #66B3FF);
  color: white;
}

.status-card.execution-time {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  color: white;
}

.status-card.heartbeat-count {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  color: white;
}

.status-card.next-transition {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  color: white;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.card-icon {
  font-size: 32px;
  opacity: 0.8;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 2px;
}

.card-subtitle {
  font-size: 11px;
  opacity: 0.7;
}

.monitor-main {
  flex: 1;
  min-height: 0;
}

.history-card,
.charts-card {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-content,
.charts-content {
  flex: 1;
  overflow-y: auto;
}

.history-event {
  margin-bottom: 8px;
}

.event-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.event-details {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 4px 0;
}

.event-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: var(--text-muted);
}

.empty-history,
.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.chart-container {
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  text-align: center;
}

.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-bar {
  flex: 1;
  height: 20px;
  background: var(--bg-secondary);
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.item-info {
  display: flex;
  justify-content: space-between;
  width: 80px;
  font-size: 12px;
}

.trend-chart {
  display: flex;
  gap: 24px;
}

.trend-stats {
  flex: 1;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.stat-label {
  color: var(--text-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.success {
  color: var(--el-color-success);
}

.stat-value.error {
  color: var(--el-color-danger);
}

.success-rate {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.rate-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.rate-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.rate-progress {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rate-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.transition-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transition-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.transition-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.transition-name {
  color: var(--text-primary);
}

.transition-count {
  color: var(--text-secondary);
}

.transition-bar {
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.transition-bar .bar-fill {
  height: 100%;
  background: var(--accent-primary);
  border-radius: 4px;
}

.monitor-logs {
  flex-shrink: 0;
  height: 200px;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logs-content {
  height: 140px;
  overflow-y: auto;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  display: flex;
  gap: 8px;
  padding: 2px 0;
  border-bottom: 1px solid var(--border-color);
}

.log-time {
  color: var(--text-muted);
  width: 80px;
  flex-shrink: 0;
}

.log-level {
  width: 50px;
  flex-shrink: 0;
  font-weight: 600;
}

.log-entry.log-info .log-level {
  color: var(--el-color-info);
}

.log-entry.log-success .log-level {
  color: var(--el-color-success);
}

.log-entry.log-warning .log-level {
  color: var(--el-color-warning);
}

.log-entry.log-error .log-level {
  color: var(--el-color-danger);
}

.log-message {
  flex: 1;
  color: var(--text-primary);
}

.log-data {
  color: var(--text-secondary);
  font-style: italic;
}
</style>

<template>
  <div class="message-test-fixed">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <div class="title-icon">
              <el-icon><Bell /></el-icon>
            </div>
            <span class="title-text">消息管理器修复验证</span>
          </span>
        </div>
      </template>

      <div class="test-content">
        <!-- 测试说明 -->
        <el-alert
          title="修复验证测试"
          type="info"
          description="验证修复后的消息管理器能够确保界面上始终只显示一个弹窗消息，无论消息类型如何。"
          show-icon
          :closable="false"
          class="test-description"
        />

        <!-- 快速测试按钮 -->
        <div class="quick-test-section">
          <h4>快速测试（验证立即切换功能）</h4>
          <div class="button-group">
            <el-button type="success" @click="testSuccess">成功消息</el-button>
            <el-button type="danger" @click="testError">错误消息</el-button>
            <el-button type="warning" @click="testWarning">警告消息</el-button>
            <el-button type="info" @click="testInfo">信息消息</el-button>
          </div>
          <div class="button-group">
            <el-button @click="testRapidSwitch" type="primary">快速切换测试</el-button>
            <el-button @click="testInstantInterrupt" type="primary">立即中断测试</el-button>
          </div>
          <p class="test-tip">
            <el-icon><InfoFilled /></el-icon>
            快速点击不同类型的按钮，新消息应该立即中断当前消息并显示，无延迟
          </p>
        </div>

      <!-- 连续测试 -->
      <div class="continuous-test-section">
        <h4>连续测试（模拟实际使用场景）</h4>
        <div class="button-group">
          <el-button @click="startContinuousTest" :disabled="isContinuousTestRunning">
            开始连续测试
          </el-button>
          <el-button @click="stopContinuousTest" :disabled="!isContinuousTestRunning">
            停止测试
          </el-button>
        </div>
        <p class="test-tip">
          <el-icon><Timer /></el-icon>
          模拟快速连续的消息显示，验证消息管理器的稳定性
        </p>
      </div>

      <!-- 混合类型测试 -->
      <div class="mixed-test-section">
        <h4>混合类型测试（重点验证）</h4>
        <div class="button-group">
          <el-button @click="testMixedMessages">混合消息测试</el-button>
          <el-button @click="testErrorAfterSuccess">成功后显示错误</el-button>
          <el-button @click="testInfoAfterError">错误后显示信息</el-button>
        </div>
        <p class="test-tip">
          <el-icon><Warning /></el-icon>
          重点验证不同类型消息之间的切换，确保不会同时显示多个消息
        </p>
      </div>

      <!-- 状态监控 -->
      <div class="status-section">
        <h4>消息管理器状态</h4>
        <div class="status-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="当前消息">
              {{ status.hasCurrentMessage ? '有' : '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="队列长度">
              {{ status.queueLength }}
            </el-descriptions-item>
            <el-descriptions-item label="测试计数">
              {{ testCount }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="status-actions">
          <el-button size="small" @click="refreshStatus">刷新状态</el-button>
          <el-button size="small" @click="clearAllMessages">强制清理</el-button>
        </div>
      </div>

      <!-- 测试日志 -->
      <div class="log-section">
        <h4>测试日志</h4>
        <div class="log-container">
          <div v-for="(log, index) in testLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span :class="['log-type', `log-${log.type}`]">{{ log.type.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <div class="log-actions">
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Bell, InfoFilled, Timer, Warning } from '@element-plus/icons-vue'
import messageManager from '../utils/message'

// 响应式数据
const status = ref({
  hasCurrentMessage: false,
  queueLength: 0
})
const testCount = ref(0)
const isContinuousTestRunning = ref(false)
const testLogs = ref([])
let continuousTestTimer = null

// 添加测试日志
const addLog = (type, message) => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 简化的测试方法
const testSuccess = () => {
  try {
    testCount.value++
    addLog('success', `测试成功消息 #${testCount.value}`)
    messageManager.success(`这是成功消息 #${testCount.value}`)
    refreshStatus()
  } catch (error) {
    console.error('testSuccess 错误:', error)
    addLog('error', `testSuccess 错误: ${error.message}`)
  }
}

const testError = () => {
  try {
    testCount.value++
    addLog('error', `测试错误消息 #${testCount.value}`)
    messageManager.error(`这是错误消息 #${testCount.value}`)
    refreshStatus()
  } catch (error) {
    console.error('testError 错误:', error)
    addLog('error', `testError 错误: ${error.message}`)
  }
}

const testWarning = () => {
  try {
    testCount.value++
    addLog('warning', `测试警告消息 #${testCount.value}`)
    messageManager.warning(`这是警告消息 #${testCount.value}`)
    refreshStatus()
  } catch (error) {
    console.error('testWarning 错误:', error)
    addLog('error', `testWarning 错误: ${error.message}`)
  }
}

const testInfo = () => {
  try {
    testCount.value++
    addLog('info', `测试信息消息 #${testCount.value}`)
    messageManager.info(`这是信息消息 #${testCount.value}`)
    refreshStatus()
  } catch (error) {
    console.error('testInfo 错误:', error)
    addLog('error', `testInfo 错误: ${error.message}`)
  }
}

// 混合测试
const testMixedMessages = () => {
  addLog('test', '开始混合消息测试')
  testSuccess()
  setTimeout(() => testError(), 100)
  setTimeout(() => testWarning(), 200)
  setTimeout(() => testInfo(), 300)
}

const testErrorAfterSuccess = () => {
  addLog('test', '测试：成功后显示错误')
  testSuccess()
  setTimeout(() => testError(), 500)
}

const testInfoAfterError = () => {
  addLog('test', '测试：错误后显示信息')
  testError()
  setTimeout(() => testInfo(), 500)
}

// 简化的快速切换测试
const testRapidSwitch = () => {
  try {
    addLog('test', '开始快速切换测试')

    // 快速连续触发不同类型的消息
    testSuccess()
    setTimeout(() => testError(), 100)
    setTimeout(() => testWarning(), 200)
    setTimeout(() => testInfo(), 300)

    addLog('test', '快速切换测试完成')
  } catch (error) {
    console.error('testRapidSwitch 错误:', error)
    addLog('error', `testRapidSwitch 错误: ${error.message}`)
  }
}

// 简化的立即中断测试
const testInstantInterrupt = () => {
  try {
    addLog('test', '开始立即中断测试')

    // 显示一个长时间的消息
    messageManager.info('这是一个长时间显示的消息，应该被立即中断', 10000)
    addLog('info', '显示长时间消息（10秒）')

    // 立即用新消息中断
    setTimeout(() => {
      messageManager.error('立即中断！这个错误消息应该立即替换上面的信息消息', 3000)
      addLog('error', '立即中断并显示错误消息')
    }, 500)
  } catch (error) {
    console.error('testInstantInterrupt 错误:', error)
    addLog('error', `testInstantInterrupt 错误: ${error.message}`)
  }
}

// 连续测试
const startContinuousTest = () => {
  isContinuousTestRunning.value = true
  addLog('test', '开始连续测试')
  
  const testTypes = ['success', 'error', 'warning', 'info']
  let index = 0
  
  continuousTestTimer = setInterval(() => {
    const type = testTypes[index % testTypes.length]
    switch (type) {
      case 'success': testSuccess(); break
      case 'error': testError(); break
      case 'warning': testWarning(); break
      case 'info': testInfo(); break
    }
    index++
  }, 800)
}

const stopContinuousTest = () => {
  if (continuousTestTimer) {
    clearInterval(continuousTestTimer)
    continuousTestTimer = null
  }
  isContinuousTestRunning.value = false
  addLog('test', '停止连续测试')
}

// 状态管理
const refreshStatus = () => {
  try {
    const currentStatus = messageManager.getStatus()
    status.value = {
      hasCurrentMessage: currentStatus.hasCurrentMessage,
      queueLength: currentStatus.queueLength
    }
  } catch (error) {
    console.error('refreshStatus 错误:', error)
    addLog('error', `refreshStatus 错误: ${error.message}`)
  }
}

const clearAllMessages = () => {
  try {
    messageManager.closeCurrentMessage()
    refreshStatus()
    addLog('system', '强制清理所有消息')
  } catch (error) {
    console.error('clearAllMessages 错误:', error)
    addLog('error', `clearAllMessages 错误: ${error.message}`)
  }
}

const clearLogs = () => {
  testLogs.value = []
  addLog('system', '日志已清空')
}

// 移除了复杂的性能监控方法以避免错误

// 生命周期
onMounted(() => {
  refreshStatus()
  addLog('system', '消息测试组件已加载')
})

onUnmounted(() => {
  stopContinuousTest()
})
</script>

<style scoped>
.message-test-fixed {
  margin: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #409EFF;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-description {
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.test-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
  margin: 0;
}

.status-info {
  margin-bottom: 12px;
}

.status-actions {
  display: flex;
  gap: 8px;
}

/* 移除了性能监控相关的CSS样式 */

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
  margin-bottom: 12px;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-type {
  font-weight: bold;
  min-width: 60px;
}

.log-success { color: #67C23A; }
.log-error { color: #F56C6C; }
.log-warning { color: #E6A23C; }
.log-info { color: #409EFF; }
.log-test { color: #909399; }
.log-system { color: #606266; }
.log-performance { color: #E6A23C; font-weight: bold; }

.log-message {
  flex: 1;
  color: #303133;
}

.log-actions {
  display: flex;
  gap: 8px;
}

h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}
</style>

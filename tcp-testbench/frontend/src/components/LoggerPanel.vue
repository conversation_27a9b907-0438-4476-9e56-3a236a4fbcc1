<template>
  <el-card class="logger-panel">
    <template #header>
      <div class="card-header logger-header">
        <span class="header-title">
          <div class="title-icon logger-icon">
            <el-icon><Document /></el-icon>
          </div>
          <span class="title-text">实时消息日志</span>
        </span>
        <div class="header-actions">
          <el-button 
            size="small" 
            type="info" 
            :icon="Download" 
            @click="exportLogs"
          >
            导出日志
          </el-button>
          <el-button 
            size="small" 
            type="warning" 
            :icon="Delete" 
            @click="clearLogs"
          >
            清空日志
          </el-button>
        </div>
      </div>
    </template>

    <div class="logger-content">
      <!-- 过滤器 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="logFilter.level" @change="updateFilter" placeholder="日志级别" size="small">
              <el-option label="全部级别" value="all" />
              <el-option 
                v-for="(level, key) in loggerStore.logLevels" 
                :key="key"
                :label="level.name" 
                :value="key"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="logFilter.type" @change="updateFilter" placeholder="消息类型" size="small">
              <el-option label="全部类型" value="all" />
              <el-option 
                v-for="(type, key) in loggerStore.logTypes" 
                :key="key"
                :label="type.name" 
                :value="key"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="logFilter.search"
              @input="updateFilter"
              placeholder="搜索日志内容..."
              size="small"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-button 
              size="small" 
              @click="clearFilter"
              :disabled="isFilterEmpty"
            >
              清空过滤
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 日志统计 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ loggerStore.logStats.total }}</div>
            <div class="stat-label">总日志数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ loggerStore.logStats.sent }}</div>
            <div class="stat-label">发送消息</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ loggerStore.logStats.received }}</div>
            <div class="stat-label">接收消息</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ loggerStore.logStats.error }}</div>
            <div class="stat-label">错误数</div>
          </div>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="logs-section">
        <div class="logs-header">
          <span>显示 {{ filteredLogs.length }} / {{ loggerStore.logs.length }} 条日志</span>
          <div class="logs-controls">
            <el-switch
              v-model="loggerStore.autoScroll"
              @change="loggerStore.toggleAutoScroll"
              active-text="自动滚动"
              size="small"
            />
          </div>
        </div>

        <div 
          ref="logsContainer"
          class="logs-container"
          @scroll="onScroll"
        >
          <div 
            v-for="log in visibleLogs" 
            :key="log.id"
            :class="['log-item', `log-${log.level}`, `log-${log.type}`]"
          >
            <div class="log-header">
              <div class="log-meta">
                <el-icon :class="`icon-${log.level}`">
                  <component :is="getLogIcon(log)" />
                </el-icon>
                <span class="log-time">{{ loggerStore.formatTimestamp(log.timestamp) }}</span>
                <el-tag 
                  :type="getLogTagType(log.level)" 
                  size="small"
                  class="log-level-tag"
                >
                  {{ loggerStore.logLevels[log.level]?.name }}
                </el-tag>
                <el-tag 
                  :type="getTypeTagType(log.type)" 
                  size="small"
                  class="log-type-tag"
                >
                  {{ loggerStore.logTypes[log.type]?.name }}
                </el-tag>
              </div>
              <div class="log-actions">
                <el-button 
                  size="small" 
                  text 
                  @click="copyLogData(log)"
                  :icon="DocumentCopy"
                />
              </div>
            </div>
            
            <div class="log-message">{{ log.message }}</div>
            
            <div v-if="log.data" class="log-data">
              <el-collapse>
                <el-collapse-item title="详细数据" :name="log.id">
                  <div class="code-block">{{ loggerStore.formatLogData(log.data) }}</div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>

          <div v-if="filteredLogs.length === 0" class="empty-logs">
            <el-empty description="暂无日志数据" />
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useLoggerStore } from '../stores/logger'
import {
  Document, Download, Delete, Search, DocumentCopy,
  InfoFilled, WarningFilled, CircleCloseFilled,
  CircleCheckFilled, Tools, Top, Bottom, Setting, Link
} from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import messageManager from '../utils/message'

const loggerStore = useLoggerStore()

// 本地状态
const logFilter = ref({ ...loggerStore.logFilter })
const logsContainer = ref(null)
const visibleCount = ref(50) // 虚拟滚动，只显示部分日志

// 计算属性
const filteredLogs = computed(() => {
  return loggerStore.filteredLogs
})

const visibleLogs = computed(() => {
  return filteredLogs.value.slice(0, visibleCount.value)
})

const isFilterEmpty = computed(() => {
  return logFilter.value.level === 'all' && 
         logFilter.value.type === 'all' && 
         !logFilter.value.search
})

// 监听日志变化，自动滚动
watch(() => loggerStore.logs.length, () => {
  if (loggerStore.autoScroll) {
    nextTick(() => {
      scrollToBottom()
    })
  }
})

// 方法
const updateFilter = () => {
  loggerStore.updateFilter(logFilter.value)
}

const clearFilter = () => {
  logFilter.value = {
    level: 'all',
    type: 'all',
    search: ''
  }
  loggerStore.clearFilter()
}

const onScroll = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target
  
  // 接近底部时加载更多日志
  if (scrollHeight - scrollTop - clientHeight < 100) {
    if (visibleCount.value < filteredLogs.value.length) {
      visibleCount.value = Math.min(visibleCount.value + 50, filteredLogs.value.length)
    }
  }
}

const scrollToBottom = () => {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

const getLogIcon = (log) => {
  const iconMap = {
    'info': InfoFilled,
    'warning': WarningFilled,
    'error': CircleCloseFilled,
    'success': CircleCheckFilled,
    'debug': Tools
  }
  return iconMap[log.level] || InfoFilled
}

const getLogTagType = (level) => {
  const typeMap = {
    'info': 'primary',
    'warning': 'warning',
    'error': 'danger',
    'success': 'success',
    'debug': 'info'
  }
  return typeMap[level] || 'info'
}

const getTypeTagType = (type) => {
  const typeMap = {
    'sent': 'primary',
    'received': 'success',
    'system': 'info',
    'connection': 'warning'
  }
  return typeMap[type] || 'info'
}

const copyLogData = async (log) => {
  try {
    const data = {
      timestamp: log.timestamp,
      level: log.level,
      type: log.type,
      message: log.message,
      data: log.data
    }
    await navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    messageManager.success('日志数据已复制到剪贴板')
  } catch (error) {
    messageManager.error('复制失败')
  }
}

const exportLogs = () => {
  try {
    const format = 'json' // 可以扩展为用户选择
    const data = loggerStore.exportLogs(format)
    const blob = new Blob([data], {
      type: format === 'json' ? 'application/json' : 'text/csv'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `tcp-debugger-logs-${new Date().toISOString().slice(0, 10)}.${format}`
    a.click()
    URL.revokeObjectURL(url)
    messageManager.success('日志已导出')
  } catch (error) {
    messageManager.error('导出失败')
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有日志吗？', '确认操作', {
      type: 'warning'
    })
    loggerStore.clearLogs()
    visibleCount.value = 50
    messageManager.success('日志已清空')
  } catch {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  // 添加一些示例日志
  loggerStore.info('system', '应用程序启动')
  loggerStore.info('system', '界面组件初始化完成')
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped>
.logger-panel {
  height: auto;
}

.logger-panel :deep(.el-card__body) {
  overflow: visible;
  padding: 12px;
  height: auto;
  margin-top: 0 !important;
  border-top: none !important;
}

.logger-panel :deep(.el-card__header) {
  padding: 0 !important;
  border-bottom: none !important;
  margin-bottom: 0 !important;
}

/* 日志面板主题色 - 青色 */
.logger-header {
  background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
  margin: 0;
  padding: 12px;
  border-radius: 8px 8px 0 0;
}

.logger-header .header-title {
  color: #ffffff !important;
}

.logger-header .header-actions .el-button {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.logger-header .header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
}

.logger-icon {
  background: rgba(255, 255, 255, 0.15);
}

.logger-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.filter-section {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.stats-section {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.logs-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px 6px 0 0;
  border: 1px solid var(--border-color);
  border-bottom: none;
  font-size: 12px;
  color: var(--text-secondary);
}

.logs-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logs-container {
  height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 6px 6px;
  background-color: var(--bg-secondary);
}

.log-item {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.log-item:hover {
  background-color: var(--bg-hover);
}

.log-item:last-child {
  border-bottom: none;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-time {
  font-family: monospace;
  font-size: 12px;
  color: var(--text-secondary);
}

.log-level-tag,
.log-type-tag {
  font-size: 11px;
}

.log-message {
  color: var(--text-primary);
  margin-bottom: 8px;
  word-break: break-word;
}

.log-data {
  margin-top: 8px;
}

.icon-info { color: var(--accent-primary); }
.icon-warning { color: var(--accent-warning); }
.icon-error { color: var(--accent-danger); }
.icon-success { color: var(--accent-success); }
.icon-debug { color: var(--accent-info); }

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

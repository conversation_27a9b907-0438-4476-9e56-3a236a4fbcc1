<template>
  <div class="task-flow-timeline">
    <!-- 时间轴控制栏 -->
    <div class="timeline-controls">
      <div class="controls-left">
        <el-button-group>
          <el-button @click="playTimeline" type="primary" size="small" :disabled="isPlaying">
            <el-icon><VideoPlay /></el-icon>
            播放预览
          </el-button>
          <el-button @click="pauseTimeline" type="warning" size="small" :disabled="!isPlaying">
            <el-icon><VideoPause /></el-icon>
            暂停
          </el-button>
          <el-button @click="stopTimeline" type="danger" size="small">
            <el-icon><VideoPause /></el-icon>
            停止
          </el-button>
        </el-button-group>
      </div>
      
      <div class="controls-center">
        <div class="timeline-info">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="separator">/</span>
          <span class="total-time">{{ formatTime(totalDuration) }}</span>
        </div>
      </div>
      
      <div class="controls-right">
        <el-select v-model="timeScale" size="small" style="width: 120px;">
          <el-option label="1x 速度" value="1" />
          <el-option label="2x 速度" value="2" />
          <el-option label="5x 速度" value="5" />
          <el-option label="10x 速度" value="10" />
        </el-select>
      </div>
    </div>

    <!-- 时间轴主体 -->
    <div class="timeline-main">
      <!-- 时间刻度 -->
      <div class="timeline-ruler">
        <div class="ruler-track">
          <div 
            v-for="mark in timeMarks" 
            :key="mark.time"
            class="time-mark"
            :style="{ left: `${(mark.time / totalDuration) * 100}%` }"
          >
            <div class="mark-line"></div>
            <div class="mark-label">{{ formatTime(mark.time) }}</div>
          </div>
        </div>
        
        <!-- 播放进度指示器 -->
        <div 
          class="playhead"
          :style="{ left: `${(currentTime / totalDuration) * 100}%` }"
        >
          <div class="playhead-line"></div>
          <div class="playhead-handle"></div>
        </div>
      </div>

      <!-- 状态轨道 -->
      <div class="timeline-tracks">
        <div 
          v-for="(track, index) in stateTracks" 
          :key="track.step"
          class="state-track"
        >
          <div class="track-header">
            <div class="track-step" :style="{ backgroundColor: track.color }">
              {{ track.step }}
            </div>
            <div class="track-info">
              <div class="track-name">{{ track.name }}</div>
              <div class="track-description">{{ track.description }}</div>
            </div>
          </div>
          
          <div class="track-content">
            <!-- 状态持续时间块 -->
            <div 
              v-for="segment in track.segments" 
              :key="segment.id"
              class="time-segment"
              :style="getSegmentStyle(segment)"
              @click="selectSegment(segment)"
              :class="{ 'selected': selectedSegment?.id === segment.id }"
            >
              <div class="segment-content">
                <div class="segment-label">{{ segment.label }}</div>
                <div class="segment-duration">{{ formatDuration(segment.duration) }}</div>
              </div>
              
              <!-- 调整手柄 -->
              <div class="resize-handle left" @mousedown="startResize(segment, 'left', $event)"></div>
              <div class="resize-handle right" @mousedown="startResize(segment, 'right', $event)"></div>
            </div>
            
            <!-- 转换箭头 -->
            <div 
              v-for="transition in track.transitions" 
              :key="transition.id"
              class="transition-arrow"
              :style="getTransitionStyle(transition)"
            >
              <div class="arrow-line"></div>
              <div class="arrow-head"></div>
              <div class="transition-label">{{ transition.probability * 100 }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置面板 */
    <div class="timeline-config" v-if="selectedSegment">
      <div class="config-header">
        <h4>时间段配置</h4>
        <el-button @click="closeConfig" size="small" text>
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="config-content">
        <el-form :model="segmentConfig" label-width="100px" size="small">
          <el-form-item label="开始时间">
            <el-input-number 
              v-model="segmentConfig.startTime" 
              :min="0" 
              :step="1000"
              @change="updateSegmentConfig"
            >
              <template #append>ms</template>
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="持续时间">
            <el-input-number 
              v-model="segmentConfig.duration" 
              :min="1000" 
              :step="1000"
              @change="updateSegmentConfig"
            >
              <template #append>ms</template>
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="触发模式">
            <el-select v-model="segmentConfig.triggerMode" @change="updateSegmentConfig">
              <el-option label="时间触发" value="time" />
              <el-option label="心跳次数" value="heartbeat" />
              <el-option label="条件触发" value="condition" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="心跳次数" v-if="segmentConfig.triggerMode === 'heartbeat'">
            <el-input-number 
              v-model="segmentConfig.heartbeatCount" 
              :min="1" 
              @change="updateSegmentConfig"
            />
          </el-form-item>
          
          <el-form-item label="随机延迟">
            <el-slider 
              v-model="segmentConfig.randomDelay" 
              :min="0" 
              :max="5000" 
              :step="100"
              @change="updateSegmentConfig"
            />
            <span style="margin-left: 8px;">{{ segmentConfig.randomDelay }}ms</span>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="timeline-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">总时长</div>
          <div class="stat-value">{{ formatTime(totalDuration) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">状态数量</div>
          <div class="stat-value">{{ stateTracks.length }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">转换数量</div>
          <div class="stat-value">{{ totalTransitions }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">预计心跳</div>
          <div class="stat-value">{{ estimatedHeartbeats }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, Close } from '@element-plus/icons-vue'

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  transitions: {
    type: Array,
    default: () => []
  },
  runtimeState: {
    type: Object,
    default: () => ({})
  },
  triggerMode: {
    type: String,
    default: 'time_interval'
  }
})

const emit = defineEmits(['timeline-updated'])

// 时间轴状态
const isPlaying = ref(false)
const currentTime = ref(0)
const timeScale = ref('1')
const selectedSegment = ref(null)
const segmentConfig = ref({})

// 调整大小状态
const isResizing = ref(false)
const resizeTarget = ref(null)
const resizeDirection = ref('')
const resizeStartX = ref(0)
const resizeStartTime = ref(0)

// 计算属性
const totalDuration = computed(() => {
  let maxDuration = 0
  for (const node of props.nodes) {
    const duration = node.config.maxDuration || 5000
    maxDuration += duration
  }
  return Math.max(maxDuration, 30000) // 最少30秒
})

const timeMarks = computed(() => {
  const marks = []
  const interval = Math.max(1000, totalDuration.value / 20) // 20个刻度
  
  for (let time = 0; time <= totalDuration.value; time += interval) {
    marks.push({ time })
  }
  
  return marks
})

const stateTracks = computed(() => {
  const tracks = []
  let currentTime = 0
  
  // 按step排序节点
  const sortedNodes = [...props.nodes].sort((a, b) => a.step - b.step)
  
  for (const node of sortedNodes) {
    const duration = node.config.maxDuration || 5000
    const segments = [{
      id: `segment_${node.id}`,
      startTime: currentTime,
      duration: duration,
      label: node.name,
      nodeId: node.id,
      triggerMode: props.triggerMode,
      heartbeatCount: node.config.heartbeatCount || 1,
      randomDelay: node.config.randomDelay || 0
    }]
    
    // 查找从此节点出发的转换
    const nodeTransitions = props.transitions.filter(t => t.fromNodeId === node.id)
    
    tracks.push({
      step: node.step,
      name: node.name,
      description: node.description,
      color: node.color,
      segments,
      transitions: nodeTransitions.map(t => ({
        ...t,
        startTime: currentTime + duration,
        duration: t.delay || 500
      }))
    })
    
    currentTime += duration
  }
  
  return tracks
})

const totalTransitions = computed(() => {
  return props.transitions.length
})

const estimatedHeartbeats = computed(() => {
  if (props.triggerMode === 'heartbeat_count') {
    return props.nodes.reduce((total, node) => total + (node.config.heartbeatCount || 1), 0)
  } else {
    // 基于时间估算，假设每5秒一次心跳
    return Math.ceil(totalDuration.value / 5000)
  }
})

// 方法
const formatTime = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  return `${remainingSeconds}s`
}

const formatDuration = (ms) => {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

const getSegmentStyle = (segment) => {
  const left = (segment.startTime / totalDuration.value) * 100
  const width = (segment.duration / totalDuration.value) * 100
  
  return {
    left: `${left}%`,
    width: `${width}%`
  }
}

const getTransitionStyle = (transition) => {
  const left = (transition.startTime / totalDuration.value) * 100
  const width = (transition.duration / totalDuration.value) * 100
  
  return {
    left: `${left}%`,
    width: `${width}%`
  }
}

// 播放控制
let playTimer = null

const playTimeline = () => {
  isPlaying.value = true
  const startTime = Date.now()
  const initialTime = currentTime.value
  
  playTimer = setInterval(() => {
    const elapsed = (Date.now() - startTime) * parseInt(timeScale.value)
    currentTime.value = initialTime + elapsed
    
    if (currentTime.value >= totalDuration.value) {
      stopTimeline()
    }
  }, 50)
}

const pauseTimeline = () => {
  isPlaying.value = false
  if (playTimer) {
    clearInterval(playTimer)
    playTimer = null
  }
}

const stopTimeline = () => {
  isPlaying.value = false
  currentTime.value = 0
  if (playTimer) {
    clearInterval(playTimer)
    playTimer = null
  }
}

// 选择和配置
const selectSegment = (segment) => {
  selectedSegment.value = segment
  segmentConfig.value = { ...segment }
}

const closeConfig = () => {
  selectedSegment.value = null
  segmentConfig.value = {}
}

const updateSegmentConfig = () => {
  if (selectedSegment.value) {
    emit('timeline-updated', {
      type: 'segment',
      id: selectedSegment.value.id,
      config: segmentConfig.value
    })
  }
}

// 调整大小
const startResize = (segment, direction, event) => {
  event.stopPropagation()
  
  isResizing.value = true
  resizeTarget.value = segment
  resizeDirection.value = direction
  resizeStartX.value = event.clientX
  resizeStartTime.value = segment.startTime
  
  document.addEventListener('mousemove', onResizeMove)
  document.addEventListener('mouseup', onResizeEnd)
}

const onResizeMove = (event) => {
  if (!isResizing.value || !resizeTarget.value) return
  
  const deltaX = event.clientX - resizeStartX.value
  const deltaTime = (deltaX / window.innerWidth) * totalDuration.value
  
  if (resizeDirection.value === 'left') {
    const newStartTime = Math.max(0, resizeStartTime.value + deltaTime)
    resizeTarget.value.startTime = newStartTime
    resizeTarget.value.duration = resizeTarget.value.duration - deltaTime
  } else {
    resizeTarget.value.duration = Math.max(1000, resizeTarget.value.duration + deltaTime)
  }
}

const onResizeEnd = () => {
  isResizing.value = false
  resizeTarget.value = null
  resizeDirection.value = ''
  
  document.removeEventListener('mousemove', onResizeMove)
  document.removeEventListener('mouseup', onResizeEnd)
}

// 生命周期
onMounted(() => {
  // 初始化
})

onUnmounted(() => {
  if (playTimer) {
    clearInterval(playTimer)
  }
  
  document.removeEventListener('mousemove', onResizeMove)
  document.removeEventListener('mouseup', onResizeEnd)
})
</script>

<style scoped>
.task-flow-timeline {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
  border-radius: 8px;
  overflow: hidden;
}

.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
}

.controls-left,
.controls-center,
.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timeline-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 14px;
}

.current-time {
  color: var(--accent-primary);
  font-weight: 600;
}

.separator {
  color: var(--text-muted);
}

.total-time {
  color: var(--text-secondary);
}

.timeline-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.timeline-ruler {
  position: relative;
  height: 40px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
}

.ruler-track {
  position: relative;
  height: 100%;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mark-line {
  width: 1px;
  height: 20px;
  background: var(--border-color);
}

.mark-label {
  font-size: 11px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.playhead {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.playhead-line {
  width: 2px;
  height: 100%;
  background: var(--accent-primary);
}

.playhead-handle {
  position: absolute;
  top: -4px;
  left: -6px;
  width: 14px;
  height: 14px;
  background: var(--accent-primary);
  border-radius: 50%;
  border: 2px solid white;
}

.timeline-tracks {
  flex: 1;
  overflow-y: auto;
}

.state-track {
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid var(--border-color);
}

.track-header {
  width: 200px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
}

.track-step {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.track-info {
  flex: 1;
}

.track-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.track-description {
  font-size: 12px;
  color: var(--text-secondary);
}

.track-content {
  flex: 1;
  position: relative;
  background: var(--bg-secondary);
}

.time-segment {
  position: absolute;
  top: 8px;
  height: 44px;
  background: var(--accent-primary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
}

.time-segment:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.time-segment.selected {
  border: 2px solid var(--accent-secondary);
}

.segment-content {
  flex: 1;
  overflow: hidden;
}

.segment-label {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.segment-duration {
  font-size: 10px;
  opacity: 0.8;
}

.resize-handle {
  position: absolute;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
  background: rgba(255, 255, 255, 0.3);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.time-segment:hover .resize-handle {
  opacity: 1;
}

.resize-handle.left {
  left: 0;
}

.resize-handle.right {
  right: 0;
}

.transition-arrow {
  position: absolute;
  top: 26px;
  height: 8px;
  display: flex;
  align-items: center;
}

.arrow-line {
  flex: 1;
  height: 2px;
  background: var(--text-muted);
}

.arrow-head {
  width: 0;
  height: 0;
  border-left: 6px solid var(--text-muted);
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.transition-label {
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: var(--text-secondary);
  background: var(--bg-primary);
  padding: 2px 4px;
  border-radius: 2px;
}

.timeline-config {
  position: absolute;
  top: 60px;
  right: 16px;
  width: 280px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.config-header h4 {
  margin: 0;
  font-size: 14px;
  color: var(--text-primary);
}

.config-content {
  padding: 16px;
}

.timeline-stats {
  padding: 12px 16px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}
</style>

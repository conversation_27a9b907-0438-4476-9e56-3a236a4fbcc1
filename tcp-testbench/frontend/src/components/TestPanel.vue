<template>
  <div class="test-panel">
    <div class="panel-header">
      <h3>TCP客户端测试</h3>
    </div>
    
    <div class="panel-content">
      <!-- 服务器配置 -->
      <div class="config-section">
        <h4>服务器配置</h4>
        <div class="form-row">
          <label>主机:</label>
          <input v-model="serverConfig.host" type="text" placeholder="127.0.0.1" />
        </div>
        <div class="form-row">
          <label>端口:</label>
          <input v-model.number="serverConfig.port" type="number" placeholder="2022" />
        </div>
        <div class="form-row">
          <label>超时(秒):</label>
          <input v-model.number="serverConfig.timeout" type="number" placeholder="30" />
        </div>
        <div class="form-row">
          <label>保活(秒):</label>
          <input v-model.number="serverConfig.keepalive" type="number" placeholder="60" />
        </div>
      </div>

      <!-- 设备配置 -->
      <div class="config-section">
        <h4>设备配置</h4>
        <div class="form-row">
          <label>IMSI:</label>
          <input v-model="deviceConfig.imsi" type="text" placeholder="460001234567890" />
        </div>
        <div class="form-row">
          <label>IMEI:</label>
          <input v-model="deviceConfig.imei" type="text" placeholder="123456789012345" />
        </div>
        <div class="form-row">
          <label>设备型号:</label>
          <input v-model="deviceConfig.model" type="text" placeholder="TestDevice" />
        </div>
        <div class="form-row">
          <label>软件版本:</label>
          <input v-model="deviceConfig.softwareVer" type="text" placeholder="1.0.0" />
        </div>
        <div class="form-row">
          <label>硬件版本:</label>
          <input v-model="deviceConfig.hardwareVer" type="text" placeholder="1.0.0" />
        </div>
        <div class="form-row">
          <label>厂商:</label>
          <input v-model="deviceConfig.vendor" type="text" placeholder="TestVendor" />
        </div>
        <div class="form-row">
          <label>经度:</label>
          <input v-model.number="deviceConfig.longitude" type="number" step="0.000001" placeholder="116.397128" />
        </div>
        <div class="form-row">
          <label>纬度:</label>
          <input v-model.number="deviceConfig.latitude" type="number" step="0.000001" placeholder="39.916527" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <button @click="createConnection" :disabled="isLoading" class="btn btn-primary">
          {{ isLoading ? '创建中...' : '创建连接' }}
        </button>
        <button @click="connect" :disabled="!connectionId || isLoading" class="btn btn-success">
          连接
        </button>
        <button @click="sendRegister" :disabled="!isConnected || isLoading" class="btn btn-info">
          发送注册
        </button>
        <button @click="sendHeartbeat" :disabled="!isConnected || isLoading" class="btn btn-warning">
          发送心跳
        </button>
        <button @click="startAutoHeartbeat" :disabled="!isConnected || autoHeartbeatRunning" class="btn btn-secondary">
          {{ autoHeartbeatRunning ? '自动心跳运行中' : '启动自动心跳' }}
        </button>
        <button @click="disconnect" :disabled="!isConnected || isLoading" class="btn btn-danger">
          断开连接
        </button>
      </div>

      <!-- 状态显示 -->
      <div class="status-section">
        <h4>连接状态</h4>
        <div class="status-item">
          <span class="label">连接ID:</span>
          <span class="value">{{ connectionId || '未创建' }}</span>
        </div>
        <div class="status-item">
          <span class="label">状态:</span>
          <span class="value" :class="statusClass">{{ connectionStatus }}</span>
        </div>
        <div class="status-item">
          <span class="label">错误:</span>
          <span class="value error">{{ connectionError || '无' }}</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section" v-if="stats">
        <h4>统计信息</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="label">连接时间:</span>
            <span class="value">{{ formatTime(stats.connectTime) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">最后消息:</span>
            <span class="value">{{ formatTime(stats.lastMessageTime) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">发送消息:</span>
            <span class="value">{{ stats.messagesSent }}</span>
          </div>
          <div class="stat-item">
            <span class="label">接收消息:</span>
            <span class="value">{{ stats.messagesReceived }}</span>
          </div>
          <div class="stat-item">
            <span class="label">发送字节:</span>
            <span class="value">{{ stats.bytesSent }}</span>
          </div>
          <div class="stat-item">
            <span class="label">接收字节:</span>
            <span class="value">{{ stats.bytesReceived }}</span>
          </div>
          <div class="stat-item">
            <span class="label">错误次数:</span>
            <span class="value">{{ stats.errors }}</span>
          </div>
        </div>
      </div>

      <!-- 日志显示 -->
      <div class="log-section">
        <h4>操作日志</h4>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
            <span class="message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  CreateConnection,
  Connect,
  Disconnect,
  SendRegister,
  SendHeartbeat,
  StartAutoHeartbeat,
  GetConnectionStatus,
  GetStats
} from 'wailsjs/go/main/App'

// 响应式数据
const isLoading = ref(false)
const connectionId = ref('')
const connectionStatus = ref('未创建')
const connectionError = ref('')
const autoHeartbeatRunning = ref(false)
const stats = ref(null)
const logs = ref([])

// 配置数据
const serverConfig = ref({
  host: '127.0.0.1',
  port: 2022,
  timeout: 30,
  keepalive: 60
})

const deviceConfig = ref({
  imsi: '460001234567890',
  imei: '123456789012345',
  model: 'TestDevice',
  softwareVer: '1.0.0',
  hardwareVer: '1.0.0',
  vendor: 'TestVendor',
  ci: '12345',
  pci: '67890',
  manageIp: '*************',
  lng: 116.397128,
  lat: 39.916527
})

// 计算属性
const isConnected = computed(() => connectionStatus.value === 'connected')

const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'status-connected'
    case 'connecting': return 'status-connecting'
    case 'error': return 'status-error'
    default: return 'status-disconnected'
  }
})

// 方法
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    timestamp: new Date(),
    message,
    type
  })
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const createConnection = async () => {
  isLoading.value = true
  try {
    const connConfig = {
      id: `conn_${Date.now()}`,
      host: serverConfig.value.host,
      port: serverConfig.value.port,
      timeout: serverConfig.value.timeout,
      keepalive: serverConfig.value.keepalive,
      encryptionKey: ''
    }
    
    const result = await CreateConnection(connConfig, deviceConfig.value)
    connectionId.value = connConfig.id
    connectionStatus.value = 'disconnected'
    addLog(`连接创建成功: ${result}`, 'success')
  } catch (error) {
    addLog(`创建连接失败: ${error}`, 'error')
    connectionError.value = error
  } finally {
    isLoading.value = false
  }
}

const connect = async () => {
  if (!connectionId.value) return
  
  isLoading.value = true
  connectionStatus.value = 'connecting'
  try {
    const result = await Connect(connectionId.value)
    connectionStatus.value = 'connected'
    connectionError.value = ''
    addLog(`连接成功: ${result}`, 'success')
    startStatsUpdate()
  } catch (error) {
    connectionStatus.value = 'error'
    connectionError.value = error
    addLog(`连接失败: ${error}`, 'error')
  } finally {
    isLoading.value = false
  }
}

const disconnect = async () => {
  if (!connectionId.value) return
  
  isLoading.value = true
  try {
    const result = await Disconnect(connectionId.value)
    connectionStatus.value = 'disconnected'
    connectionError.value = ''
    autoHeartbeatRunning.value = false
    addLog(`断开连接: ${result}`, 'info')
    stopStatsUpdate()
  } catch (error) {
    addLog(`断开连接失败: ${error}`, 'error')
  } finally {
    isLoading.value = false
  }
}

const sendRegister = async () => {
  if (!connectionId.value) return
  
  isLoading.value = true
  try {
    const result = await SendRegister(connectionId.value)
    addLog(`注册消息发送: ${result}`, 'success')
  } catch (error) {
    addLog(`发送注册失败: ${error}`, 'error')
  } finally {
    isLoading.value = false
  }
}

const sendHeartbeat = async () => {
  if (!connectionId.value) return

  isLoading.value = true
  try {
    // 🔍 发送心跳前：打印任务流程配置信息
    try {
      const { useTaskFlowStore } = await import('../stores/taskFlow')
      const taskFlowStore = useTaskFlowStore()

      console.log('📋 ===== TestPanel发送心跳前的任务流程状态 =====')
      console.log('任务流程运行状态:', taskFlowStore.runtimeState.isRunning)
      console.log('当前节点ID:', taskFlowStore.runtimeState.currentNodeId)
      console.log('当前step:', taskFlowStore.runtimeState.currentStep)
      console.log('心跳计数:', taskFlowStore.runtimeState.heartbeatCount)
      console.log('总心跳数:', taskFlowStore.runtimeState.totalHeartbeats)

      const currentNode = taskFlowStore.currentNode
      if (currentNode) {
        console.log('当前节点配置:', {
          step: currentNode.step,
          name: currentNode.name,
          heartbeatCount: currentNode.config.heartbeatCount
        })
      }
      console.log('==========================================')
    } catch (error) {
      console.warn('获取任务流程状态失败:', error)
    }

    // 🔧 获取任务流程的当前状态，构建包含latestTask的心跳数据
    let latestTaskInfo = { step: 0, taskId: 0, taskDistance: 360 }
    try {
      const { useTaskFlowStore } = await import('../stores/taskFlow')
      const taskFlowStore = useTaskFlowStore()

      if (taskFlowStore.runtimeState.isRunning) {
        const currentNode = taskFlowStore.currentNode
        if (currentNode) {
          latestTaskInfo = {
            step: currentNode.step,
            taskId: taskFlowStore.taskFlowConfig.id ? parseInt(taskFlowStore.taskFlowConfig.id.replace(/\D/g, '')) || 1 : 1,
            taskDistance: 360
          }
        }
      }
    } catch (error) {
      console.warn('获取任务流程状态失败，使用默认值:', error)
    }

    const options = {
      simulateMovement: false,
      vehicleType: 1,
      acc: 1,
      gear: 0,
      deviceStatus: 0,
      workMode: 1,
      speed: 15,
      powerLevel: 80,
      // 🆕 添加任务信息
      latestTask: latestTaskInfo
    }

    console.log('💓 TestPanel发送心跳数据:', options)
    const result = await SendHeartbeat(connectionId.value, options)
    addLog(`心跳消息发送: ${result}`, 'success')

    // 🔍 发送心跳后：通知任务流程系统
    try {
      const { useTaskFlowStore } = await import('../stores/taskFlow')
      const taskFlowStore = useTaskFlowStore()

      console.log('📈 ===== TestPanel发送心跳后通知任务流程 =====')
      if (taskFlowStore.onHeartbeatSent) {
        taskFlowStore.onHeartbeatSent()
      }

      console.log('当前节点ID:', taskFlowStore.runtimeState.currentNodeId)
      console.log('当前step:', taskFlowStore.runtimeState.currentStep)
      console.log('心跳计数:', taskFlowStore.runtimeState.heartbeatCount)
      console.log('总心跳数:', taskFlowStore.runtimeState.totalHeartbeats)
      console.log('==========================================')
    } catch (error) {
      console.warn('通知任务流程失败:', error)
    }

  } catch (error) {
    addLog(`发送心跳失败: ${error}`, 'error')
  } finally {
    isLoading.value = false
  }
}

const startAutoHeartbeat = async () => {
  if (!connectionId.value) return
  
  try {
    const options = {
      simulateMovement: true,
      vehicleType: 1,
      acc: 1,
      gear: 0,
      deviceStatus: 0,
      workMode: 1,
      speed: 15,
      powerLevel: 80
    }
    const result = await StartAutoHeartbeat(connectionId.value, 10, options) // 10秒间隔
    autoHeartbeatRunning.value = true
    addLog(`自动心跳启动: ${result}`, 'success')
  } catch (error) {
    addLog(`启动自动心跳失败: ${error}`, 'error')
  }
}

// 统计信息更新
let statsInterval = null

const startStatsUpdate = () => {
  statsInterval = setInterval(async () => {
    if (connectionId.value) {
      try {
        const newStats = await GetStats(connectionId.value)
        stats.value = newStats
        
        const status = await GetConnectionStatus(connectionId.value)
        connectionStatus.value = status.status
        if (status.error) {
          connectionError.value = status.error
        }
      } catch (error) {
        console.error('更新统计信息失败:', error)
      }
    }
  }, 2000) // 每2秒更新一次
}

const stopStatsUpdate = () => {
  if (statsInterval) {
    clearInterval(statsInterval)
    statsInterval = null
  }
}

const formatTime = (time) => {
  if (!time) return '未知'
  const date = new Date(time)
  return date.toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  addLog('TCP客户端测试工具已启动', 'info')
})

onUnmounted(() => {
  stopStatsUpdate()
})
</script>

<style scoped>
.test-panel {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.panel-header h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.config-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.config-section h4 {
  color: #495057;
  margin-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 5px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.form-row label {
  width: 120px;
  font-weight: 500;
  color: #495057;
}

.form-row input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-secondary { background: #6c757d; color: white; }
.btn-danger { background: #dc3545; color: white; }

.status-section, .stats-section {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.status-item, .stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
  color: #495057;
}

.value {
  color: #212529;
}

.status-connected { color: #28a745; font-weight: bold; }
.status-connecting { color: #ffc107; font-weight: bold; }
.status-error { color: #dc3545; font-weight: bold; }
.status-disconnected { color: #6c757d; }

.error { color: #dc3545; }

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.log-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 13px;
}

.timestamp {
  width: 100px;
  color: #6c757d;
  margin-right: 10px;
}

.message {
  flex: 1;
}

.log-item.success .message { color: #28a745; }
.log-item.error .message { color: #dc3545; }
.log-item.warning .message { color: #ffc107; }
.log-item.info .message { color: #17a2b8; }
</style>

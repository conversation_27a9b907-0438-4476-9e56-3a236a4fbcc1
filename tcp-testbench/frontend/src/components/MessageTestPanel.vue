<template>
  <el-card class="message-test-panel">
    <template #header>
      <div class="card-header">
        <span class="header-title">
          <el-icon><Tools /></el-icon>
          消息管理器测试面板
        </span>
      </div>
    </template>

    <div class="test-section">
      <h4>基础测试</h4>
      <div class="button-group">
        <el-button type="success" @click="testBasic">基础测试</el-button>
        <el-button type="warning" @click="testExtreme">极限点击测试</el-button>
        <el-button type="danger" @click="testAsync">异步测试</el-button>
        <el-button type="info" @click="testDeviceSwitch">设备切换测试</el-button>
      </div>
    </div>

    <div class="test-section">
      <h4>单一消息测试</h4>
      <div class="button-group">
        <el-button type="success" @click="showSuccess">成功消息</el-button>
        <el-button type="danger" @click="showError">错误消息</el-button>
        <el-button type="warning" @click="showWarning">警告消息</el-button>
        <el-button type="info" @click="showInfo">信息消息</el-button>
      </div>
    </div>

    <div class="test-section">
      <h4>快速连续测试</h4>
      <div class="button-group">
        <el-button @click="rapidFire">快速连发 (10次)</el-button>
        <el-button @click="mixedMessages">混合消息类型</el-button>
        <el-button @click="stressTest">压力测试 (50次)</el-button>
      </div>
    </div>

    <div class="test-section">
      <h4>测试说明</h4>
      <el-alert 
        title="测试目标" 
        type="info" 
        :closable="false"
        description="无论点击多快，界面上应该只显示一条ElMessage弹窗。新消息会立即替换旧消息。"
      />
    </div>
  </el-card>
</template>

<script setup>
import { Tools } from '@element-plus/icons-vue'
import messageManager from '../utils/message'
import {
  testMessageManager,
  testExtremeClicking,
  testAsyncOperations,
  testDeviceTemplateSwitch,
  testComprehensive
} from '../utils/messageTest'

// 基础测试方法
const testBasic = () => {
  testMessageManager()
}

const testExtreme = () => {
  testExtremeClicking()
}

const testAsync = () => {
  testAsyncOperations()
}

const testDeviceSwitch = () => {
  testDeviceTemplateSwitch()
}

// 单一消息测试
const showSuccess = () => {
  messageManager.success('这是一条成功消息')
}

const showError = () => {
  messageManager.error('这是一条错误消息')
}

const showWarning = () => {
  messageManager.warning('这是一条警告消息')
}

const showInfo = () => {
  messageManager.info('这是一条信息消息')
}

// 快速连续测试
const rapidFire = () => {
  console.log('🚀 开始快速连发测试...')
  for (let i = 0; i < 10; i++) {
    setTimeout(() => {
      messageManager.success(`快速消息 ${i + 1}/10`)
    }, i * 50) // 每50毫秒一次
  }
}

const mixedMessages = () => {
  console.log('🎨 开始混合消息测试...')
  const types = ['success', 'error', 'warning', 'info']
  types.forEach((type, index) => {
    setTimeout(() => {
      messageManager[type](`${type.toUpperCase()} 消息 ${index + 1}`)
    }, index * 100)
  })
}

const stressTest = () => {
  console.log('💪 开始压力测试...')
  for (let i = 0; i < 50; i++) {
    const types = ['success', 'error', 'warning', 'info']
    const type = types[i % types.length]
    setTimeout(() => {
      messageManager[type](`压力测试 ${i + 1}/50`)
    }, i * 20) // 每20毫秒一次
  }
}
</script>

<style scoped>
.message-test-panel {
  margin: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.button-group .el-button {
  min-width: 120px;
}
</style>

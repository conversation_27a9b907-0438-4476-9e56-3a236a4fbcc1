<template>
  <el-card class="device-panel">
    <template #header>
      <div class="card-header device-header">
        <span class="header-title">
          <div class="title-icon device-icon">
            <el-icon><Cpu /></el-icon>
          </div>
          <span class="title-text">设备模拟配置</span>
        </span>
        <div class="header-actions">
          <el-button 
            size="small" 
            type="warning" 
            :icon="Refresh" 
            @click="generateRandomDevice"
          >
            随机生成
          </el-button>
          <el-button 
            size="small" 
            type="info" 
            :icon="RefreshLeft" 
            @click="resetToDefaults"
          >
            重置
          </el-button>
        </div>
      </div>
    </template>

    <div class="device-config">
      <!-- 设备模板选择 -->
      <div class="template-section">
        <div class="section-title">设备模板</div>
        <el-radio-group v-model="selectedTemplate" @change="applyTemplate">
          <el-radio-button 
            v-for="(template, key) in deviceStore.deviceTemplates" 
            :key="key" 
            :value="key"
          >
            {{ template.name }}
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 设备基础信息 -->
      <div class="section">
        <div class="section-title">基础信息</div>
        <el-form :model="deviceConfig" label-width="100px" size="default">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="IMSI">
                <el-input
                  v-model="deviceConfig.imsi"
                  placeholder="设备IMSI"
                  @change="updateConfig"
                >
                  <template #prepend>
                    <el-icon><Iphone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IMEI">
                <el-input
                  v-model="deviceConfig.imei"
                  placeholder="设备IMEI"
                  @change="updateConfig"
                >
                  <template #prepend>
                    <el-icon><Cellphone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="设备型号">
                <el-input
                  v-model="deviceConfig.model"
                  placeholder="设备型号"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="厂商">
                <el-input
                  v-model="deviceConfig.vendor"
                  placeholder="设备厂商"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="软件版本">
                <el-input
                  v-model="deviceConfig.softwareVer"
                  placeholder="软件版本"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="硬件版本">
                <el-input
                  v-model="deviceConfig.hardwareVer"
                  placeholder="硬件版本"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 网络信息 -->
      <div class="section">
        <div class="section-title">网络信息</div>
        <el-form :model="deviceConfig" label-width="100px" size="default">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="CI">
                <el-input
                  v-model="deviceConfig.ci"
                  placeholder="小区标识"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="PCI">
                <el-input
                  v-model="deviceConfig.pci"
                  placeholder="物理小区标识"
                  @change="updateConfig"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="管理IP">
            <el-input
              v-model="deviceConfig.manageIp"
              placeholder="设备管理IP地址"
              @change="updateConfig"
            >
              <template #prepend>
                <el-icon><Monitor /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- GPS配置 -->
      <div class="section gps-section">
        <div class="section-title">
          <el-icon class="section-icon"><Location /></el-icon>
          GPS配置
        </div>
        <el-form :model="deviceConfig" label-width="0" size="default" class="gps-form">
          <!-- 经纬度坐标 -->
          <el-row :gutter="16" class="gps-coordinates">
            <el-col :span="12">
              <div class="input-group">
                <label class="input-label">经度</label>
                <el-input
                  v-model="deviceConfig.lng"
                  @change="updateConfig"
                  @blur="validateLongitude"
                  placeholder="113.910000"
                  class="coordinate-input"
                  size="default"
                />
                <span class="input-unit">°E</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="input-group">
                <label class="input-label">纬度</label>
                <el-input
                  v-model="deviceConfig.lat"
                  @change="updateConfig"
                  @blur="validateLatitude"
                  placeholder="22.487000"
                  class="coordinate-input"
                  size="default"
                />
                <span class="input-unit">°N</span>
              </div>
            </el-col>
          </el-row>

          <!-- 里程和定位状态 -->
          <el-row :gutter="16" class="gps-status-row">
            <el-col :span="8">
              <div class="input-group">
                <label class="input-label">总里程</label>
                <el-input
                  v-model="gpsConfig.totalMileage"
                  @change="updateGpsConfig"
                  @blur="validateMileage"
                  placeholder="0.0"
                  class="mileage-input"
                  size="default"
                />
                <span class="input-unit">km</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="switch-group">
                <label class="input-label">GPS定位</label>
                <el-switch
                  v-model="gpsConfig.gpsLocated"
                  active-text="开启"
                  inactive-text="关闭"
                  @change="updateGpsConfig"
                  class="status-switch"
                  size="default"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="switch-group">
                <label class="input-label">自动驾驶定位</label>
                <el-switch
                  v-model="gpsConfig.adLocated"
                  active-text="开启"
                  inactive-text="关闭"
                  @change="updateGpsConfig"
                  class="status-switch"
                  size="default"
                />
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 当前模板信息 -->
      <div class="template-info">
        <div class="info-title">当前模板信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">设备类型:</span>
            <span class="info-value">{{ currentTemplate.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">默认速度:</span>
            <span class="info-value">{{ currentTemplate.defaultSpeed }} km/h</span>
          </div>
          <div class="info-item">
            <span class="info-label">最大速度:</span>
            <span class="info-value">{{ currentTemplate.maxSpeed }} km/h</span>
          </div>
          <div class="info-item">
            <span class="info-label">电池容量:</span>
            <span class="info-value">{{ currentTemplate.batteryCapacity }}%</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useDeviceStore } from '../stores/device'
import { useHeartbeatStore } from '../stores/heartbeat'
import { useLoggerStore } from '../stores/logger'
import {
  Cpu, Iphone, Cellphone, Monitor, Location, Operation,
  Refresh, RefreshLeft
} from '@element-plus/icons-vue'
import messageManager from '../utils/message'

const deviceStore = useDeviceStore()
const heartbeatStore = useHeartbeatStore()
const loggerStore = useLoggerStore()

// 本地状态
const deviceConfig = ref({ ...deviceStore.deviceConfig })
const selectedTemplate = ref(deviceStore.selectedTemplate)

// GPS配置状态（从心跳store获取）
const gpsConfig = ref({
  totalMileage: heartbeatStore.currentHeartbeatData.tm || 190,
  gpsLocated: Boolean(heartbeatStore.currentHeartbeatData.gpsLocated),
  adLocated: Boolean(heartbeatStore.currentHeartbeatData.adLocated)
})

// 计算属性
const currentTemplate = computed(() => {
  return deviceStore.deviceTemplates[selectedTemplate.value] || deviceStore.deviceTemplates.cleaning_robot
})

// 监听 store 变化
watch(() => deviceStore.deviceConfig, (newConfig) => {
  deviceConfig.value = { ...newConfig }
}, { deep: true })

watch(() => deviceStore.selectedTemplate, (newTemplate) => {
  selectedTemplate.value = newTemplate
})

// 监听心跳store变化，同步GPS配置（避免循环更新）
let isUpdatingFromStore = false
watch(() => heartbeatStore.currentHeartbeatData, (newData) => {
  if (isUpdatingFromStore) return

  console.log('从store同步GPS配置:', newData)
  gpsConfig.value = {
    totalMileage: newData.tm || 190,
    gpsLocated: Boolean(newData.gpsLocated),
    adLocated: Boolean(newData.adLocated)
  }
}, { deep: true })

// 方法
const updateConfig = () => {
  deviceStore.updateDeviceConfig(deviceConfig.value)
  loggerStore.logSystemEvent('设备配置已更新', deviceConfig.value)
}

const applyTemplate = (templateKey) => {
  deviceStore.applyTemplate(templateKey)
  selectedTemplate.value = templateKey
  deviceConfig.value = { ...deviceStore.deviceConfig }

  const template = deviceStore.deviceTemplates[templateKey]
  loggerStore.logSystemEvent(`应用设备模板: ${template.name}`, template)
  messageManager.success(`已应用 ${template.name} 模板`)
}

const generateRandomDevice = () => {
  deviceStore.generateRandomDevice()
  deviceConfig.value = { ...deviceStore.deviceConfig }
  loggerStore.logSystemEvent('生成随机设备信息', deviceConfig.value)
  messageManager.success('已生成随机设备信息')
}

const resetToDefaults = () => {
  deviceStore.resetToDefaults()
  deviceConfig.value = { ...deviceStore.deviceConfig }
  selectedTemplate.value = deviceStore.selectedTemplate
  loggerStore.logSystemEvent('重置设备配置为默认值')
  messageManager.success('设备配置已重置为默认值')
}

const updateGpsConfig = () => {
  // 更新心跳store中的GPS相关数据
  const heartbeatStore = useHeartbeatStore()
  console.log('更新GPS配置:', gpsConfig.value)

  isUpdatingFromStore = true
  heartbeatStore.updateHeartbeatData({
    tm: Number(gpsConfig.value.totalMileage),
    gpsLocated: gpsConfig.value.gpsLocated ? 1 : 0,
    adLocated: gpsConfig.value.adLocated ? 1 : 0
  })

  // 延迟重置标志位，确保store更新完成
  nextTick(() => {
    isUpdatingFromStore = false
  })

  loggerStore.logSystemEvent('GPS配置已更新', gpsConfig.value)
  messageManager.success('GPS配置已更新')
}

// 经纬度验证方法
const validateLongitude = () => {
  let value = parseFloat(deviceConfig.value.lng)
  if (isNaN(value)) {
    value = 113.910000
  } else {
    value = Math.max(-180, Math.min(180, value))
    value = parseFloat(value.toFixed(6))
  }
  deviceConfig.value.lng = value
  updateConfig()
}

const validateLatitude = () => {
  let value = parseFloat(deviceConfig.value.lat)
  if (isNaN(value)) {
    value = 22.487000
  } else {
    value = Math.max(-90, Math.min(90, value))
    value = parseFloat(value.toFixed(6))
  }
  deviceConfig.value.lat = value
  updateConfig()
}

// 里程验证方法
const validateMileage = () => {
  let value = parseFloat(gpsConfig.value.totalMileage)
  if (isNaN(value)) {
    value = 0.0
  } else {
    value = Math.max(0, Math.min(999999, value))
    value = parseFloat(value.toFixed(1))
  }
  gpsConfig.value.totalMileage = value
  updateGpsConfig()
}
</script>

<style scoped>
.device-panel {
  height: auto;
}

.device-panel :deep(.el-card__body) {
  overflow: visible;
  padding: 12px;
  height: auto;
  margin-top: 0 !important;
  border-top: none !important;
}

.device-panel :deep(.el-card__header) {
  padding: 0 !important;
  border-bottom: none !important;
  margin-bottom: 0 !important;
}

/* 设备面板主题色 - 橙色 */
.device-header {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
  margin: 0;
  padding: 12px;
  border-radius: 8px 8px 0 0;
}

.device-header .header-title {
  color: #ffffff !important;
}

.device-header .header-actions .el-button {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.device-header .header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
}

.device-icon {
  background: rgba(255, 255, 255, 0.15);
}

.device-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-section {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.section {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.section-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}



.template-info {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.info-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: var(--text-secondary);
  font-size: 14px;
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
}

.el-radio-group {
  width: 100%;
}

.el-radio-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* GPS配置专用样式 - 简化设计 */
.gps-section .section-title {
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.section-icon {
  color: var(--accent-primary);
  font-size: 16px;
}

.gps-form {
  width: 100%;
}

/* GPS布局样式 */
.gps-coordinates {
  margin-bottom: 16px;
}

.gps-status-row {
  margin-bottom: 0;
}

/* 输入组样式 - 简化设计 */
.input-group {
  margin-bottom: 12px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
  display: block;
}

.input-unit {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 11px;
  color: var(--text-muted);
  font-weight: 500;
  pointer-events: none;
  z-index: 10;
  background: var(--bg-primary);
  padding: 0 2px;
}

/* 坐标输入框样式 - 使用普通输入框最大化输入区域 */
.coordinate-input {
  width: 100% !important;
  position: relative;
}

.coordinate-input :deep(.el-input__wrapper) {
  padding-right: 25px !important; /* 只为单位标识留出空间 */
}

.coordinate-input :deep(.el-input__inner) {
  text-align: left !important;
  padding-left: 8px !important;
  padding-right: 30px !important; /* 为单位标识留出空间 */
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  letter-spacing: 0.5px;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 里程输入框样式 - 使用普通输入框最大化输入区域 */
.mileage-input {
  width: 100% !important;
  max-width: 300px;
  position: relative;
}

.mileage-input :deep(.el-input__wrapper) {
  padding-right: 25px !important; /* 只为单位标识留出空间 */
}

.mileage-input :deep(.el-input__inner) {
  text-align: left !important;
  padding-left: 8px !important;
  padding-right: 30px !important; /* 为单位标识留出空间 */
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 开关组样式 - 简化设计 */
.switch-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-switch {
  align-self: flex-start;
}

.status-switch :deep(.el-switch__label) {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.status-switch :deep(.el-switch__label.is-active) {
  color: var(--accent-primary);
  font-weight: 600;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .gps-coordinates .el-col {
    margin-bottom: 16px;
  }

  .gps-status-row .el-col {
    margin-bottom: 16px;
  }

  .coordinate-input :deep(.el-input__inner),
  .mileage-input :deep(.el-input__inner) {
    font-size: 14px;
    padding-right: 25px !important;
  }

  .input-unit {
    right: 3px;
  }
}

@media (max-width: 480px) {
  .gps-coordinates .el-col,
  .gps-status-row .el-col {
    span: 24;
    margin-bottom: 12px;
  }

  .mileage-input {
    max-width: 100%;
  }
}

/* 确保数值完整显示 */
.coordinate-input :deep(.el-input__inner),
.mileage-input :deep(.el-input__inner) {
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
  min-width: 0;
  box-sizing: border-box;
}

/* 确保单位标识不会干扰输入 */
.input-group {
  position: relative;
  overflow: visible;
}
</style>

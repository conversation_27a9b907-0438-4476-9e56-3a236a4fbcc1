<template>
  <el-card class="debug-panel">
    <template #header>
      <div class="card-header">
        <span class="header-title">
          <el-icon><Tools /></el-icon>
          消息管理器调试面板
        </span>
      </div>
    </template>

    <div class="debug-section">
      <h4>快速测试</h4>
      <div class="button-group">
        <el-button type="success" @click="testSuccess">成功消息</el-button>
        <el-button type="danger" @click="testError">错误消息</el-button>
        <el-button type="warning" @click="testWarning">警告消息</el-button>
        <el-button type="info" @click="testInfo">信息消息</el-button>
      </div>
    </div>

    <div class="debug-section">
      <h4>调试信息</h4>
      <div class="debug-info">
        <el-button @click="checkStatus">检查状态</el-button>
        <el-button @click="clearConsole">清空控制台</el-button>
      </div>
      <div class="status-display">
        <pre>{{ statusText }}</pre>
      </div>
    </div>

    <div class="debug-section">
      <h4>说明</h4>
      <el-alert 
        title="调试说明" 
        type="info" 
        :closable="false"
        description="1. 点击任意消息按钮应该能看到对应的ElMessage弹窗
2. 打开浏览器控制台查看详细的调试日志
3. 使用'检查状态'按钮查看消息管理器内部状态"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref } from 'vue'
import { Tools } from '@element-plus/icons-vue'
import messageManager from '../utils/message'

const statusText = ref('点击"检查状态"查看消息管理器状态')

const testSuccess = () => {
  console.log('🧪 测试成功消息')
  messageManager.success('这是一条成功消息！')
}

const testError = () => {
  console.log('🧪 测试错误消息')
  messageManager.error('这是一条错误消息！')
}

const testWarning = () => {
  console.log('🧪 测试警告消息')
  messageManager.warning('这是一条警告消息！')
}

const testInfo = () => {
  console.log('🧪 测试信息消息')
  messageManager.info('这是一条信息消息！')
}

const checkStatus = () => {
  console.log('🔍 检查消息管理器状态')
  const status = messageManager.getStatus()
  statusText.value = JSON.stringify(status, null, 2)
}

const clearConsole = () => {
  console.clear()
  console.log('🧹 控制台已清空')
}
</script>

<style scoped>
.debug-panel {
  margin: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.debug-section {
  margin-bottom: 24px;
}

.debug-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.debug-info {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.status-display {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.status-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

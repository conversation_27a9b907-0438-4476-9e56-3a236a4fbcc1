<template>
  <div class="performance-test">
    <h3>性能测试结果</h3>
    
    <div class="test-section">
      <h4>加载时间</h4>
      <div class="metrics">
        <div class="metric">
          <span class="label">应用启动:</span>
          <span class="value">{{ loadTimes.appMount || '--' }}ms</span>
        </div>
        <div class="metric">
          <span class="label">组件加载:</span>
          <span class="value">{{ loadTimes.totalComponents || '--' }}ms</span>
        </div>
        <div class="metric">
          <span class="label">首次渲染:</span>
          <span class="value">{{ loadTimes.firstPaint || '--' }}ms</span>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h4>内存使用</h4>
      <div class="metrics" v-if="memoryInfo">
        <div class="metric">
          <span class="label">已使用:</span>
          <span class="value">{{ formatBytes(memoryInfo.usedJSHeapSize) }}</span>
        </div>
        <div class="metric">
          <span class="label">总计:</span>
          <span class="value">{{ formatBytes(memoryInfo.totalJSHeapSize) }}</span>
        </div>
        <div class="metric">
          <span class="label">限制:</span>
          <span class="value">{{ formatBytes(memoryInfo.jsHeapSizeLimit) }}</span>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h4>响应式测试</h4>
      <div class="responsive-test">
        <div class="current-breakpoint">
          当前断点: <strong>{{ currentBreakpoint }}</strong>
        </div>
        <div class="window-size">
          窗口大小: {{ windowSize.width }} x {{ windowSize.height }}
        </div>
        <button @click="testResize" class="test-button">
          测试响应式布局
        </button>
      </div>
    </div>
    
    <div class="test-section">
      <h4>组件加载详情</h4>
      <div class="component-loads">
        <div 
          v-for="(time, component) in componentLoadTimes" 
          :key="component"
          class="component-load"
        >
          <span class="component-name">{{ component }}:</span>
          <span class="load-time">{{ time }}ms</span>
          <div class="load-bar">
            <div 
              class="load-progress" 
              :style="{ width: `${(time / maxLoadTime) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-actions">
      <button @click="runPerformanceTest" class="test-button primary">
        运行性能测试
      </button>
      <button @click="clearResults" class="test-button">
        清除结果
      </button>
      <button @click="exportResults" class="test-button">
        导出结果
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { performanceMonitor } from '../utils/performance'
import { layoutConfig } from '../utils/layout'

const loadTimes = ref({})
const memoryInfo = ref(null)
const componentLoadTimes = ref({})
const windowSize = ref({ width: 0, height: 0 })

const currentBreakpoint = computed(() => {
  return layoutConfig.getDeviceType()
})

const maxLoadTime = computed(() => {
  const times = Object.values(componentLoadTimes.value)
  return times.length > 0 ? Math.max(...times) : 100
})

const updateWindowSize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const runPerformanceTest = () => {
  // 获取性能报告
  const report = performanceMonitor.generateReport()
  
  // 更新加载时间
  loadTimes.value = {
    appMount: report.measures['app-mount']?.toFixed(2),
    firstPaint: report.navigation?.loadEventEnd?.toFixed(2),
    totalComponents: Object.values(report.measures)
      .filter(time => typeof time === 'number')
      .reduce((sum, time) => sum + time, 0)
      .toFixed(2)
  }
  
  // 更新内存信息
  memoryInfo.value = report.memory
  
  // 更新组件加载时间
  const componentTimes = {}
  Object.entries(report.measures).forEach(([key, value]) => {
    if (key.endsWith('-load')) {
      const componentName = key.replace('-load', '')
      componentTimes[componentName] = parseFloat(value.toFixed(2))
    }
  })
  componentLoadTimes.value = componentTimes
}

const testResize = () => {
  // 模拟窗口大小变化
  const sizes = [
    { width: 1920, height: 1080 }, // Desktop
    { width: 1200, height: 800 },  // Tablet
    { width: 768, height: 1024 },  // Mobile
  ]
  
  let index = 0
  const interval = setInterval(() => {
    if (index >= sizes.size) {
      clearInterval(interval)
      return
    }
    
    const size = sizes[index]
    // 注意：实际上无法通过JS改变窗口大小，这里只是模拟
    console.log(`模拟窗口大小变化: ${size.width}x${size.height}`)
    index++
  }, 1000)
}

const clearResults = () => {
  loadTimes.value = {}
  memoryInfo.value = null
  componentLoadTimes.value = {}
  performanceMonitor.clear()
}

const exportResults = () => {
  const results = {
    timestamp: new Date().toISOString(),
    loadTimes: loadTimes.value,
    memoryInfo: memoryInfo.value,
    componentLoadTimes: componentLoadTimes.value,
    windowSize: windowSize.value,
    breakpoint: currentBreakpoint.value
  }
  
  const blob = new Blob([JSON.stringify(results, null, 2)], { 
    type: 'application/json' 
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-test-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

onMounted(() => {
  updateWindowSize()
  window.addEventListener('resize', updateWindowSize)
  
  // 自动运行一次性能测试
  setTimeout(runPerformanceTest, 1000)
})
</script>

<style scoped>
.performance-test {
  padding: 20px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
}

.test-section h4 {
  margin: 0 0 10px 0;
  color: var(--accent-primary);
}

.metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.metric {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
}

.label {
  color: var(--text-secondary);
}

.value {
  font-weight: 600;
  color: var(--text-primary);
}

.responsive-test {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.current-breakpoint,
.window-size {
  padding: 8px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
}

.component-loads {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-load {
  display: grid;
  grid-template-columns: 150px 80px 1fr;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
}

.component-name {
  font-weight: 500;
}

.load-time {
  text-align: right;
  font-family: monospace;
}

.load-bar {
  height: 6px;
  background-color: var(--bg-primary);
  border-radius: 3px;
  overflow: hidden;
}

.load-progress {
  height: 100%;
  background-color: var(--accent-primary);
  transition: width 0.3s ease;
}

.test-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.test-button {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s;
}

.test-button:hover {
  background-color: var(--bg-hover);
}

.test-button.primary {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.test-button.primary:hover {
  background-color: var(--accent-primary);
  opacity: 0.9;
}
</style>

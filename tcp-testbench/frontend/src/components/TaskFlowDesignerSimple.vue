<template>
  <div class="task-flow-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button @click="addNode" type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加节点
          </el-button>
          <el-button @click="connectNodes" type="success" size="small" :disabled="selectedNodes.length !== 2">
            <el-icon><Connection /></el-icon>
            连接节点 ({{ selectedNodes.length }}/2)
          </el-button>
          <el-button @click="deleteSelected" type="danger" size="small" :disabled="selectedNodes.length === 0 && selectedTransitions.length === 0">
            <el-icon><Delete /></el-icon>
            删除选中 ({{ selectedNodes.length + selectedTransitions.length }})
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <div class="operation-tips">
          <span v-if="selectedNodes.length === 0" class="tip-text">
            点击节点选择，选择2个节点后可创建连接
          </span>
          <span v-else-if="selectedNodes.length === 1" class="tip-text">
            已选中1个节点，再选择一个节点进行连接
          </span>
          <span v-else-if="selectedNodes.length === 2" class="tip-text">
            已选中2个节点，点击"连接节点"按钮
          </span>
          <span v-else class="tip-text">
            已选中{{ selectedNodes.length }}个节点，请重新选择
          </span>
        </div>
        <el-button @click="autoLayout" size="small">
          <el-icon><Connection /></el-icon>
          自动布局
        </el-button>
      </div>
    </div>

    <!-- 画布区域 -->
    <div class="designer-canvas">
      <div class="canvas-content">
        <!-- 网格背景 -->
        <div class="grid-background"></div>
        
        <!-- 连接线 SVG -->
        <svg class="connections-layer" width="100%" height="100%">
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#409EFF" />
            </marker>
          </defs>
          
          <!-- 渲染所有连接线 -->
          <g v-for="transition in transitions" :key="transition.id">
            <!-- 不可见的交互热区 - 30px宽度便于点击 -->
            <path
              :d="getConnectionPath(transition)"
              stroke="rgba(0,0,0,0.01)"
              stroke-width="30"
              fill="none"
              class="connection-hotzone"
              @click="selectTransition(transition.id)"
              @contextmenu="showConnectionContextMenu(transition.id, $event)"
              @mouseenter="onTransitionHover(transition.id, true)"
              @mouseleave="onTransitionHover(transition.id, false)"
            />

            <!-- 可视连接线 -->
            <path
              :d="getConnectionPath(transition)"
              stroke="#409EFF"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
              :class="['connection-line', {
                'selected': selectedTransitions.includes(transition.id),
                'hovered': hoveredTransition === transition.id
              }]"
              pointer-events="none"
            />

            <!-- 连接线上的概率标签 -->
            <text
              :x="getConnectionLabelPosition(transition).x"
              :y="getConnectionLabelPosition(transition).y"
              text-anchor="middle"
              dominant-baseline="central"
              :class="['connection-label', {
                'selected': selectedTransitions.includes(transition.id),
                'hovered': hoveredTransition === transition.id
              }]"
              :fill="selectedTransitions.includes(transition.id) ? '#F56C6C' : '#409EFF'"
              font-size="12"
              :font-weight="selectedTransitions.includes(transition.id) ? 'bold' : '600'"
              @click="selectTransition(transition.id)"
            >
              {{ Math.round(transition.probability * 100) }}%
            </text>
          </g>

          <!-- 临时连接线（拖拽时显示） -->
          <g v-if="isConnecting">
            <path
              :d="tempConnectionPath"
              stroke="#409EFF"
              stroke-width="2"
              fill="none"
              stroke-dasharray="5,5"
              marker-end="url(#arrowhead)"
              class="temp-connection-line"
            />
          </g>
        </svg>
        
        <!-- 调试信息 -->
        <div v-if="nodes.length === 0" class="debug-info">
          <p>没有节点数据 - nodes.length: {{ nodes.length }}</p>
          <p>Props nodes: {{ JSON.stringify(nodes) }}</p>
        </div>

        <!-- 状态节点 -->
        <div
          v-for="node in nodes"
          :key="node.id"
          :class="['flow-node', `step-${node.step}`, {
            'selected': selectedNodes.includes(node.id),
            'current': node.id === currentNodeId,
            'dragging': draggedNode === node.id
          }]"
          :style="getNodeStyle(node)"
          @mousedown="startDrag(node.id, $event)"
          @contextmenu="showNodeContextMenu(node.id, $event)"
        >
          <!-- 节点头部 -->
          <div class="node-header">
            <div class="node-step" :style="{ backgroundColor: node.color }">
              {{ node.step }}
            </div>
            <div class="node-status" v-if="node.id === currentNodeId">
              <div class="status-dot running"></div>
            </div>
          </div>
          
          <!-- 节点内容 -->
          <div class="node-content">
            <div class="node-title">{{ node.name }}</div>
            <div class="node-description">{{ node.description }}</div>
          </div>
          
          <!-- 节点配置信息 -->
          <div class="node-config">
            <div class="config-item" v-if="node.config.heartbeatCount">
              <el-icon><Timer /></el-icon>
              <span>{{ node.config.heartbeatCount }}次心跳</span>
            </div>
          </div>

          <!-- 连接点 -->
          <div class="connection-points">
            <!-- 输出连接点（右侧） -->
            <div
              class="connection-point output-point"
              @mousedown="startConnection(node.id, $event)"
              @mouseenter="highlightConnectionPoint"
              @mouseleave="unhighlightConnectionPoint"
              title="拖拽到其他节点创建连接"
            >
              <div class="connection-dot"></div>
            </div>

            <!-- 输入连接点（左侧） -->
            <div
              class="connection-point input-point"
              @mouseup="endConnection(node.id, $event)"
              @mouseenter="highlightConnectionPoint"
              @mouseleave="unhighlightConnectionPoint"
              title="连接目标点"
            >
              <div class="connection-dot"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="properties-panel" v-if="selectedNodes.length === 1">
      <div class="panel-header">
        <h4>节点属性</h4>
        <el-button @click="closePropertiesPanel" size="small" text>
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="panel-content">
        <el-form :model="selectedNodeConfig" label-width="80px" size="small">
          <el-form-item label="步骤值">
            <el-input-number v-model="selectedNodeConfig.step" :min="0" :max="9" @change="updateSelectedNode" />
          </el-form-item>
          
          <el-form-item label="名称">
            <el-input v-model="selectedNodeConfig.name" @change="updateSelectedNode" />
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input v-model="selectedNodeConfig.description" type="textarea" :rows="2" @change="updateSelectedNode" />
          </el-form-item>
          
          <el-form-item label="颜色">
            <el-color-picker v-model="selectedNodeConfig.color" @change="updateSelectedNode" />
          </el-form-item>
          
          <el-divider>触发配置</el-divider>
          
          <el-form-item label="心跳次数">
            <el-input-number v-model="selectedNodeConfig.config.heartbeatCount" :min="1" :max="100" @change="updateSelectedNode" />
            <div class="form-help-text">指定在此状态停留的心跳次数后自动转换到下一状态</div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 连接属性面板 -->
    <div class="properties-panel" v-if="selectedTransitions.length === 1">
      <div class="panel-header">
        <h4>连接属性</h4>
        <el-button @click="closePropertiesPanel" size="small" text>
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="panel-content">
        <el-form :model="selectedTransitionConfig" label-width="80px" size="small">
          <el-form-item label="概率">
            <div v-if="isSingleOutgoingTransition(selectedTransitionConfig.id)" class="probability-readonly">
              <el-tag type="info" size="small">自动设置</el-tag>
              <span class="probability-value">100%</span>
              <div class="probability-hint">单一连接自动设为100%</div>
            </div>
            <div v-else class="probability-editable">
              <el-slider v-model="selectedTransitionConfig.probability" :min="0" :max="1" :step="0.01" @change="updateSelectedTransition" />
              <span class="probability-value">{{ Math.round(selectedTransitionConfig.probability * 100) }}%</span>
              <div class="probability-hint">多连接时可调整概率分配</div>
            </div>
          </el-form-item>
          
          <el-form-item label="延迟">
            <el-input-number v-model="selectedTransitionConfig.delay" :min="0" :step="100" @change="updateSelectedTransition">
              <template #append>ms</template>
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input v-model="selectedTransitionConfig.description" @change="updateSelectedTransition" />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="showContextMenu"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      @click.stop
    >
      <div v-if="contextMenuType === 'connection'" class="context-menu-items">
        <div class="context-menu-item" @click="deleteContextTarget">
          <el-icon><Delete /></el-icon>
          删除连接线
        </div>
        <div class="context-menu-item" @click="editContextTarget">
          <el-icon><Edit /></el-icon>
          编辑属性
        </div>
      </div>
      <div v-else-if="contextMenuType === 'node'" class="context-menu-items">
        <div class="context-menu-item" @click="deleteContextTarget">
          <el-icon><Delete /></el-icon>
          删除节点
        </div>
        <div class="context-menu-item" @click="editContextTarget">
          <el-icon><Edit /></el-icon>
          编辑属性
        </div>
      </div>
    </div>

    <!-- 点击遮罩层关闭右键菜单 -->
    <div
      v-if="showContextMenu"
      class="context-menu-overlay"
      @click="hideContextMenu"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import {
  Plus, Connection, Delete, Timer, Clock, Close, Edit
} from '@element-plus/icons-vue'
import messageManager from '../utils/message.js'

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  transitions: {
    type: Array,
    default: () => []
  },
  currentNodeId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'node-updated',
  'transition-updated', 
  'node-added',
  'transition-added',
  'node-deleted',
  'transition-deleted'
])

// 选择状态
const selectedNodes = ref([])
const selectedTransitions = ref([])
const selectedNodeConfig = ref({})
const selectedTransitionConfig = ref({})

// 拖拽状态
const isDragging = ref(false)
const draggedNode = ref(null)
const dragOffset = ref({ x: 0, y: 0 })
const canvasRect = ref(null)
const dragStartTime = ref(0)
const hasDragged = ref(false)

// 连线拖拽状态
const isConnecting = ref(false)
const connectionStart = ref(null)
const connectionEnd = ref({ x: 0, y: 0 })
const tempConnectionPath = ref('')

// 右键菜单状态
const showContextMenu = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const contextMenuTarget = ref(null)
const contextMenuType = ref('') // 'connection' | 'node' | 'canvas'

// 悬停状态
const hoveredTransition = ref('')

// 方法
const getNodeStyle = (node) => ({
  left: `${node.position.x}px`,
  top: `${node.position.y}px`,
  borderColor: node.color,
  backgroundColor: node.id === props.currentNodeId ? `${node.color}20` : 'var(--bg-primary)'
})

const getConnectionPath = (transition) => {
  const fromNode = props.nodes.find(n => n.id === transition.fromNodeId)
  const toNode = props.nodes.find(n => n.id === transition.toNodeId)

  if (!fromNode || !toNode) return ''

  // 节点尺寸 - 使用更准确的高度值
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px

  // 起始点（从节点中心开始）
  const fromCenter = {
    x: fromNode.position.x + nodeWidth / 2,
    y: fromNode.position.y + nodeHeight / 2
  }

  // 目标点（到节点中心）
  const toCenter = {
    x: toNode.position.x + nodeWidth / 2,
    y: toNode.position.y + nodeHeight / 2
  }

  // 计算方向向量
  const dx = toCenter.x - fromCenter.x
  const dy = toCenter.y - fromCenter.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance === 0) return ''

  // 单位方向向量
  const unitX = dx / distance
  const unitY = dy / distance

  // 计算起始点（从节点边缘开始）
  const start = getNodeEdgePoint(fromNode, unitX, unitY, nodeWidth, nodeHeight)

  // 计算结束点（到节点边缘结束）
  const end = getNodeEdgePoint(toNode, -unitX, -unitY, nodeWidth, nodeHeight)

  return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
}

// 计算节点边缘的交点
const getNodeEdgePoint = (node, dirX, dirY, nodeWidth, nodeHeight) => {
  const centerX = node.position.x + nodeWidth / 2
  const centerY = node.position.y + nodeHeight / 2

  // 计算与节点边界的交点
  const halfWidth = nodeWidth / 2
  const halfHeight = nodeHeight / 2

  // 计算射线与矩形边界的交点
  let t = Infinity

  // 检查与左右边界的交点
  if (dirX !== 0) {
    const tLeft = -halfWidth / dirX
    const tRight = halfWidth / dirX
    if (tLeft > 0) t = Math.min(t, tLeft)
    if (tRight > 0) t = Math.min(t, tRight)
  }

  // 检查与上下边界的交点
  if (dirY !== 0) {
    const tTop = -halfHeight / dirY
    const tBottom = halfHeight / dirY
    if (tTop > 0) t = Math.min(t, tTop)
    if (tBottom > 0) t = Math.min(t, tBottom)
  }

  // 如果没有找到有效交点，返回中心点
  if (t === Infinity) {
    return { x: centerX, y: centerY }
  }

  return {
    x: centerX + dirX * t,
    y: centerY + dirY * t
  }
}

const getConnectionMidpoint = (transition) => {
  const fromNode = props.nodes.find(n => n.id === transition.fromNodeId)
  const toNode = props.nodes.find(n => n.id === transition.toNodeId)

  if (!fromNode || !toNode) return { x: 0, y: 0 }

  // 节点尺寸 - 使用更准确的高度值
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px

  // 起始点（从节点中心开始）
  const fromCenter = {
    x: fromNode.position.x + nodeWidth / 2,
    y: fromNode.position.y + nodeHeight / 2
  }

  // 目标点（到节点中心）
  const toCenter = {
    x: toNode.position.x + nodeWidth / 2,
    y: toNode.position.y + nodeHeight / 2
  }

  // 计算方向向量
  const dx = toCenter.x - fromCenter.x
  const dy = toCenter.y - fromCenter.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance === 0) return { x: fromCenter.x, y: fromCenter.y }

  // 单位方向向量
  const unitX = dx / distance
  const unitY = dy / distance

  // 计算实际连接线的起始点和结束点（节点边缘）
  const start = getNodeEdgePoint(fromNode, unitX, unitY, nodeWidth, nodeHeight)
  const end = getNodeEdgePoint(toNode, -unitX, -unitY, nodeWidth, nodeHeight)

  // 返回实际连接线的中点
  return {
    x: (start.x + end.x) / 2,
    y: (start.y + end.y) / 2
  }
}

// 获取连接线标签的智能定位
const getConnectionLabelPosition = (transition) => {
  const fromNode = props.nodes.find(n => n.id === transition.fromNodeId)
  const toNode = props.nodes.find(n => n.id === transition.toNodeId)

  if (!fromNode || !toNode) return { x: 0, y: 0 }

  // 节点尺寸 - 使用更准确的高度值
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px

  // 起始点和目标点（节点中心）
  const fromCenter = {
    x: fromNode.position.x + nodeWidth / 2,
    y: fromNode.position.y + nodeHeight / 2
  }

  const toCenter = {
    x: toNode.position.x + nodeWidth / 2,
    y: toNode.position.y + nodeHeight / 2
  }

  // 计算方向向量
  const dx = toCenter.x - fromCenter.x
  const dy = toCenter.y - fromCenter.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance === 0) return { x: fromCenter.x, y: fromCenter.y }

  // 单位方向向量
  const unitX = dx / distance
  const unitY = dy / distance

  // 计算实际连接线的起始点和结束点（节点边缘）
  const start = getNodeEdgePoint(fromNode, unitX, unitY, nodeWidth, nodeHeight)
  const end = getNodeEdgePoint(toNode, -unitX, -unitY, nodeWidth, nodeHeight)

  // 连接线的中点
  const midpoint = {
    x: (start.x + end.x) / 2,
    y: (start.y + end.y) / 2
  }

  // 根据连接线的角度智能调整偏移
  const angle = Math.atan2(dy, dx)
  const offsetDistance = 20  // 增加偏移距离以确保标签清晰可见

  let offsetX = 0
  let offsetY = 0

  // 将角度转换为度数便于理解
  const angleDegrees = (angle * 180 / Math.PI + 360) % 360

  // 改进的角度区间划分，特别优化垂直连接线
  if (angleDegrees >= 315 || angleDegrees < 45) {
    // 水平向右 (315° - 45°): 向上偏移
    offsetY = -offsetDistance
  } else if (angleDegrees >= 45 && angleDegrees < 135) {
    // 垂直向下 (45° - 135°): 向右偏移，确保标签在连接线右侧
    offsetX = offsetDistance
  } else if (angleDegrees >= 135 && angleDegrees < 225) {
    // 水平向左 (135° - 225°): 向上偏移
    offsetY = -offsetDistance
  } else {
    // 垂直向上 (225° - 315°): 向右偏移，确保标签在连接线右侧
    offsetX = offsetDistance
  }

  // 对于接近垂直的连接线，进行特殊处理以确保标签居中
  const isNearVertical = Math.abs(Math.abs(angleDegrees - 90) % 180 - 90) < 10
  if (isNearVertical) {
    // 对于垂直连接线，确保标签在连接线的几何中心
    // 并且水平偏移以避免与连接线重叠
    if (angleDegrees >= 45 && angleDegrees < 135) {
      // 向下的垂直线：标签在右侧
      offsetX = offsetDistance
      offsetY = 0
    } else if (angleDegrees >= 225 && angleDegrees < 315) {
      // 向上的垂直线：标签在右侧
      offsetX = offsetDistance
      offsetY = 0
    }
  }

  return {
    x: midpoint.x + offsetX,
    y: midpoint.y + offsetY
  }
}



// 节点操作
const addNode = () => {
  console.log('addNode clicked - creating new node')
  const newNode = {
    id: `node_${Date.now()}`,
    step: getNextAvailableStep(),
    name: '新状态',
    description: '状态描述',
    color: '#409EFF',
    position: { x: 200 + Math.random() * 400, y: 100 + Math.random() * 300 },
    config: {
      heartbeatCount: 2,
      conditions: []
    }
  }

  console.log('Emitting node-added event with node:', newNode)
  emit('node-added', newNode)
}

const getNextAvailableStep = () => {
  const usedSteps = props.nodes.map(n => n.step)
  for (let i = 0; i <= 9; i++) {
    if (!usedSteps.includes(i)) {
      return i
    }
  }
  return 0
}

const connectNodes = () => {
  if (selectedNodes.value.length !== 2) return
  
  const newTransition = {
    id: `trans_${Date.now()}`,
    fromNodeId: selectedNodes.value[0],
    toNodeId: selectedNodes.value[1],
    probability: 1.0,
    conditions: [],
    delay: 0,
    description: '状态转换'
  }
  
  emit('transition-added', newTransition)
  selectedNodes.value = []
}

const deleteSelected = () => {
  const nodeCount = selectedNodes.value.length
  const transitionCount = selectedTransitions.value.length

  // 记录被删除连接的源节点，用于后续概率调整
  const affectedFromNodes = new Set()
  selectedTransitions.value.forEach(transitionId => {
    const transition = props.transitions.find(t => t.id === transitionId)
    if (transition) {
      affectedFromNodes.add(transition.fromNodeId)
    }
  })

  selectedNodes.value.forEach(nodeId => {
    emit('node-deleted', nodeId)
  })

  selectedTransitions.value.forEach(transitionId => {
    emit('transition-deleted', transitionId)
  })

  // 删除后调整受影响节点的概率
  setTimeout(() => {
    affectedFromNodes.forEach(nodeId => {
      adjustProbabilities(nodeId)
    })
  }, 100) // 延迟执行，确保删除操作完成

  // 显示删除成功消息
  if (nodeCount > 0 && transitionCount > 0) {
    messageManager.success(`已删除 ${nodeCount} 个节点和 ${transitionCount} 条连接线`)
  } else if (nodeCount > 0) {
    messageManager.success(`已删除 ${nodeCount} 个节点`)
  } else if (transitionCount > 0) {
    messageManager.success(`已删除 ${transitionCount} 条连接线`)
  }

  selectedNodes.value = []
  selectedTransitions.value = []
}

// 选择操作
const selectNode = (nodeId, event) => {
  if (event && (event.ctrlKey || event.metaKey)) {
    // 多选模式
    if (selectedNodes.value.includes(nodeId)) {
      selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId)
    } else {
      selectedNodes.value.push(nodeId)
    }
  } else {
    // 单选模式
    selectedNodes.value = [nodeId]
    selectedTransitions.value = []

    const node = props.nodes.find(n => n.id === nodeId)
    if (node) {
      selectedNodeConfig.value = { ...node }
    }
  }
}

// 拖拽操作
const startDrag = (nodeId, event) => {
  event.preventDefault()
  event.stopPropagation()

  const node = props.nodes.find(n => n.id === nodeId)
  if (!node) return

  dragStartTime.value = Date.now()
  hasDragged.value = false
  isDragging.value = true
  draggedNode.value = nodeId

  // 获取画布边界
  const canvas = event.target.closest('.canvas-content')
  if (canvas) {
    canvasRect.value = canvas.getBoundingClientRect()
  }

  // 计算鼠标相对于节点的偏移
  const nodeRect = event.target.closest('.flow-node').getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - nodeRect.left,
    y: event.clientY - nodeRect.top
  }

  // 添加全局事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', endDrag)
}

const handleDrag = (event) => {
  if (!isDragging.value || !draggedNode.value || !canvasRect.value) return

  event.preventDefault()
  hasDragged.value = true

  // 计算新位置
  const newX = event.clientX - canvasRect.value.left - dragOffset.value.x
  const newY = event.clientY - canvasRect.value.top - dragOffset.value.y

  // 限制在画布边界内
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px
  const canvasWidth = canvasRect.value.width
  const canvasHeight = canvasRect.value.height

  const constrainedX = Math.max(0, Math.min(newX, canvasWidth - nodeWidth))
  const constrainedY = Math.max(0, Math.min(newY, canvasHeight - nodeHeight))

  // 更新节点位置
  emit('node-updated', draggedNode.value, {
    position: { x: constrainedX, y: constrainedY }
  })
}

const endDrag = (event) => {
  const wasDragging = isDragging.value
  const nodeId = draggedNode.value

  isDragging.value = false
  draggedNode.value = null
  canvasRect.value = null

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)

  // 如果没有真正拖拽（只是点击），则触发选择
  if (wasDragging && !hasDragged.value && nodeId) {
    setTimeout(() => {
      selectNode(nodeId, event)
    }, 0)
  }
}

// 连线拖拽操作
const startConnection = (nodeId, event) => {
  event.preventDefault()
  event.stopPropagation()

  const node = props.nodes.find(n => n.id === nodeId)
  if (!node) return

  isConnecting.value = true
  connectionStart.value = nodeId

  // 获取画布边界
  const canvas = event.target.closest('.canvas-content')
  if (canvas) {
    canvasRect.value = canvas.getBoundingClientRect()
  }

  // 计算起始点位置（节点中心）
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px
  const startX = node.position.x + nodeWidth / 2
  const startY = node.position.y + nodeHeight / 2

  connectionEnd.value = { x: startX, y: startY }
  updateTempConnectionPath()

  // 添加全局事件监听
  document.addEventListener('mousemove', handleConnectionDrag)
  document.addEventListener('mouseup', cancelConnection)
}

const handleConnectionDrag = (event) => {
  if (!isConnecting.value || !canvasRect.value) return

  event.preventDefault()

  // 更新连接线终点位置
  connectionEnd.value = {
    x: event.clientX - canvasRect.value.left,
    y: event.clientY - canvasRect.value.top
  }

  updateTempConnectionPath()
}

const endConnection = (targetNodeId, event) => {
  if (!isConnecting.value || !connectionStart.value) return

  event.preventDefault()
  event.stopPropagation()

  // 不能连接到自己
  if (connectionStart.value === targetNodeId) {
    cancelConnection()
    return
  }

  // 创建新连接
  const newTransition = {
    id: `trans_${Date.now()}`,
    fromNodeId: connectionStart.value,
    toNodeId: targetNodeId,
    probability: 1.0,
    conditions: [],
    delay: 0,
    description: '状态转换'
  }

  emit('transition-added', newTransition)
  cancelConnection()
}

const cancelConnection = () => {
  isConnecting.value = false
  connectionStart.value = null
  connectionEnd.value = { x: 0, y: 0 }
  tempConnectionPath.value = ''

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleConnectionDrag)
  document.removeEventListener('mouseup', cancelConnection)
}

const updateTempConnectionPath = () => {
  if (!connectionStart.value) return

  const startNode = props.nodes.find(n => n.id === connectionStart.value)
  if (!startNode) return

  // 节点尺寸
  const nodeWidth = 160
  const nodeHeight = 110  // 修正：实际节点高度约为110px

  // 起始点（节点中心）
  const fromCenter = {
    x: startNode.position.x + nodeWidth / 2,
    y: startNode.position.y + nodeHeight / 2
  }

  // 计算方向向量
  const dx = connectionEnd.value.x - fromCenter.x
  const dy = connectionEnd.value.y - fromCenter.y
  const distance = Math.sqrt(dx * dx + dy * dy)

  if (distance === 0) return

  // 单位方向向量
  const unitX = dx / distance
  const unitY = dy / distance

  // 计算起始点（从节点边缘开始）
  const start = getNodeEdgePoint(startNode, unitX, unitY, nodeWidth, nodeHeight)

  tempConnectionPath.value = `M ${start.x} ${start.y} L ${connectionEnd.value.x} ${connectionEnd.value.y}`
}

const highlightConnectionPoint = (event) => {
  event.target.classList.add('highlighted')
}

const unhighlightConnectionPoint = (event) => {
  event.target.classList.remove('highlighted')
}

// 右键菜单功能
const showConnectionContextMenu = (transitionId, event) => {
  event.preventDefault()
  event.stopPropagation()

  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
  contextMenuTarget.value = transitionId
  contextMenuType.value = 'connection'
  showContextMenu.value = true

  // 选中这个连接线
  selectTransition(transitionId)
}

const showNodeContextMenu = (nodeId, event) => {
  event.preventDefault()
  event.stopPropagation()

  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
  contextMenuTarget.value = nodeId
  contextMenuType.value = 'node'
  showContextMenu.value = true

  // 选中这个节点
  selectNode(nodeId, event)
}

const hideContextMenu = () => {
  showContextMenu.value = false
  contextMenuTarget.value = null
  contextMenuType.value = ''
}

const deleteContextTarget = () => {
  if (contextMenuType.value === 'connection' && contextMenuTarget.value) {
    // 记录被删除连接的源节点
    const transition = props.transitions.find(t => t.id === contextMenuTarget.value)
    const fromNodeId = transition?.fromNodeId

    emit('transition-deleted', contextMenuTarget.value)
    selectedTransitions.value = []
    messageManager.success('已删除连接线')

    // 删除后调整概率
    if (fromNodeId) {
      setTimeout(() => {
        adjustProbabilities(fromNodeId)
      }, 100)
    }
  } else if (contextMenuType.value === 'node' && contextMenuTarget.value) {
    emit('node-deleted', contextMenuTarget.value)
    selectedNodes.value = []
    messageManager.success('已删除节点')
  }
  hideContextMenu()
}

const editContextTarget = () => {
  if (contextMenuType.value === 'connection' && contextMenuTarget.value) {
    selectTransition(contextMenuTarget.value)
  } else if (contextMenuType.value === 'node' && contextMenuTarget.value) {
    selectNode(contextMenuTarget.value, {})
  }
  hideContextMenu()
}

const selectTransition = (transitionId) => {
  selectedTransitions.value = [transitionId]
  selectedNodes.value = []

  const transition = props.transitions.find(t => t.id === transitionId)
  if (transition) {
    selectedTransitionConfig.value = { ...transition }
  }
}

// 悬停处理
const onTransitionHover = (transitionId, isHovering) => {
  hoveredTransition.value = isHovering ? transitionId : ''
}

// 计算节点的出口连接数量
const getOutgoingTransitions = (nodeId) => {
  return props.transitions.filter(t => t.fromNodeId === nodeId)
}

// 检查连接是否为单一出口（不可编辑概率）
const isSingleOutgoingTransition = (transitionId) => {
  const transition = props.transitions.find(t => t.id === transitionId)
  if (!transition) return false

  const outgoingTransitions = getOutgoingTransitions(transition.fromNodeId)
  return outgoingTransitions.length === 1
}

// 自动调整概率
const adjustProbabilities = (fromNodeId) => {
  console.log('adjustProbabilities called for nodeId:', fromNodeId)
  const outgoingTransitions = getOutgoingTransitions(fromNodeId)
  console.log('outgoingTransitions:', outgoingTransitions)

  if (outgoingTransitions.length === 1) {
    // 单一连接，设置为100%
    const transition = outgoingTransitions[0]
    console.log('Single transition found:', transition)
    if (transition.probability !== 1.0) {
      console.log('Updating probability to 1.0 for transition:', transition.id)
      emit('transition-updated', transition.id, { probability: 1.0 })
    } else {
      console.log('Transition already has probability 1.0')
    }
  } else if (outgoingTransitions.length > 1) {
    // 多连接时，平均分配概率
    const equalProbability = 1.0 / outgoingTransitions.length
    console.log(`Multiple transitions found, redistributing to ${equalProbability} each`)

    outgoingTransitions.forEach(transition => {
      if (Math.abs(transition.probability - equalProbability) > 0.001) {
        console.log(`Updating transition ${transition.id} probability from ${transition.probability} to ${equalProbability}`)
        emit('transition-updated', transition.id, { probability: equalProbability })
      }
    })
  } else {
    console.log('No outgoing transitions found')
  }
}

const closePropertiesPanel = () => {
  selectedNodes.value = []
  selectedTransitions.value = []
}

// 更新操作
const updateSelectedNode = () => {
  if (selectedNodes.value.length === 1) {
    emit('node-updated', selectedNodes.value[0], selectedNodeConfig.value)
  }
}

const updateSelectedTransition = () => {
  if (selectedTransitions.value.length === 1) {
    emit('transition-updated', selectedTransitions.value[0], {
      probability: selectedTransitionConfig.value.probability,
      delay: selectedTransitionConfig.value.delay,
      description: selectedTransitionConfig.value.description
    })
  }
}

// 自动布局
const autoLayout = () => {
  const layers = {}
  
  // 按step值分层
  props.nodes.forEach(node => {
    if (!layers[node.step]) {
      layers[node.step] = []
    }
    layers[node.step].push(node)
  })
  
  // 重新排列节点位置
  const layerKeys = Object.keys(layers).sort((a, b) => parseInt(a) - parseInt(b))
  const layerHeight = 120
  const nodeWidth = 180

  layerKeys.forEach((step, layerIndex) => {
    const nodesInLayer = layers[step]
    const startX = Math.max(20, (800 - nodesInLayer.length * nodeWidth) / 2)
    
    nodesInLayer.forEach((node, nodeIndex) => {
      const newPosition = {
        x: startX + nodeIndex * nodeWidth,
        y: 50 + layerIndex * layerHeight
      }
      
      emit('node-updated', node.id, { position: newPosition })
    })
  })
}

// 调试信息
onMounted(() => {
  console.log('TaskFlowDesignerSimple mounted')
  console.log('Props nodes:', props.nodes)
  console.log('Props transitions:', props.transitions)
  console.log('Props currentNodeId:', props.currentNodeId)
})

// 监听props变化
watch(() => props.nodes, (newNodes) => {
  console.log('Nodes changed:', newNodes)
}, { immediate: true, deep: true })

// 键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

const handleKeyDown = (event) => {
  // Delete键或Backspace键删除选中项
  if (event.key === 'Delete' || event.key === 'Backspace') {
    if (selectedNodes.value.length > 0 || selectedTransitions.value.length > 0) {
      event.preventDefault()
      deleteSelected()
    }
  }

  // Escape键取消选择
  if (event.key === 'Escape') {
    selectedNodes.value = []
    selectedTransitions.value = []
  }
}
</script>

<style scoped>
.task-flow-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #2c2c2c;
  border-radius: 8px;
  overflow: hidden;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1e1e1e;
  border-bottom: 1px solid #404040;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.operation-tips {
  font-size: 12px;
  color: #999;
  margin-right: 12px;
}

.tip-text {
  padding: 4px 8px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  color: #409EFF;
}

.designer-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #2c2c2c;
  min-height: 400px; /* 确保最小高度 */
}

.canvas-content {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px; /* 确保最小高度 */
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, #404040 1px, transparent 1px),
    linear-gradient(to bottom, #404040 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

/* 不可见的交互热区 */
.connection-hotzone {
  cursor: pointer;
  pointer-events: stroke;
}

.connection-line {
  pointer-events: none;
  transition: stroke-width 0.2s ease, stroke 0.2s ease;
}

.connection-line.hovered {
  stroke-width: 3;
  filter: drop-shadow(0 0 4px rgba(64, 158, 255, 0.4));
}

.connection-line.selected {
  stroke: #F56C6C;
  stroke-width: 3;
  filter: drop-shadow(0 0 4px rgba(245, 108, 108, 0.5));
}

.connection-line.selected + text {
  fill: #F56C6C;
  font-weight: bold;
}

.connection-label {
  pointer-events: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  text-shadow:
    -1px -1px 0 rgba(0, 0, 0, 0.8),
    1px -1px 0 rgba(0, 0, 0, 0.8),
    -1px 1px 0 rgba(0, 0, 0, 0.8),
    1px 1px 0 rgba(0, 0, 0, 0.8),
    0 0 4px rgba(0, 0, 0, 0.6);
}

.connection-label.hovered {
  font-size: 13px;
  font-weight: bold;
  text-shadow:
    -1px -1px 0 rgba(0, 0, 0, 0.9),
    1px -1px 0 rgba(0, 0, 0, 0.9),
    -1px 1px 0 rgba(0, 0, 0, 0.9),
    1px 1px 0 rgba(0, 0, 0, 0.9),
    0 0 6px rgba(0, 0, 0, 0.8),
    0 0 12px rgba(255, 255, 255, 0.4);
}

.flow-node {
  position: absolute;
  width: 160px;
  min-height: 80px;
  background: #1e1e1e;
  border: 2px solid #404040;
  border-radius: 8px;
  cursor: move;
  transition: all 0.2s ease;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  user-select: none;
}

.flow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.flow-node.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.flow-node.current {
  border-color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.flow-node.dragging {
  z-index: 1000;
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
  border-color: #409EFF;
  cursor: grabbing;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #404040;
}

.node-step {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.node-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409EFF;
}

.node-status .status-dot.running {
  animation: pulse 2s infinite;
}

.node-content {
  padding: 8px 12px;
}

.node-title {
  font-size: 13px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
  line-height: 1.2;
}

.node-description {
  font-size: 11px;
  color: #999;
  line-height: 1.4;
}

.node-config {
  padding: 6px 12px;
  border-top: 1px solid #404040;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #999;
}

.config-item .el-icon {
  font-size: 12px;
}

/* 连接点样式 */
.connection-points {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-point {
  position: absolute;
  width: 16px;
  height: 16px;
  pointer-events: all;
  cursor: crosshair;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.flow-node:hover .connection-point {
  opacity: 1;
}

.connection-point.highlighted {
  opacity: 1;
}

.output-point {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
}

.input-point {
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #409EFF;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.connection-point:hover .connection-dot {
  transform: scale(1.2);
  background: #67C23A;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.5);
}

.connection-point.highlighted .connection-dot {
  transform: scale(1.3);
  background: #67C23A;
  box-shadow: 0 0 12px rgba(103, 194, 58, 0.8);
}

/* 临时连接线样式 */
.temp-connection-line {
  pointer-events: none;
  opacity: 0.8;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-items {
  display: flex;
  flex-direction: column;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #fff;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.context-menu-item:hover {
  background: #404040;
}

.context-menu-item .el-icon {
  font-size: 14px;
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background: transparent;
}

.properties-panel {
  position: absolute;
  top: 60px;
  right: 16px;
  width: 300px;
  background: #1e1e1e;
  border: 1px solid #404040;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

/* 概率显示样式 */
.probability-readonly {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.probability-editable {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.probability-value {
  font-weight: bold;
  color: #409EFF;
  font-size: 14px;
}

.probability-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.probability-readonly .probability-value {
  color: #67C23A;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #404040;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #fff;
}

.panel-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* 状态节点颜色 */
.flow-node.step-0 .node-step { background: #909399; }
.flow-node.step-1 .node-step { background: #409EFF; }
.flow-node.step-2 .node-step { background: #67C23A; }
.flow-node.step-3 .node-step { background: #E6A23C; }
.flow-node.step-6 .node-step { background: #F56C6C; }
.flow-node.step-8 .node-step { background: #F56C6C; }
.flow-node.step-9 .node-step { background: #F56C6C; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-button) {
  background: #404040;
  border-color: #404040;
  color: #fff;
}

:deep(.el-button:hover) {
  background: #409EFF;
  border-color: #409EFF;
}

:deep(.el-button--primary) {
  background: #409EFF;
  border-color: #409EFF;
}

:deep(.el-button--success) {
  background: #67C23A;
  border-color: #67C23A;
}

:deep(.el-button--danger) {
  background: #F56C6C;
  border-color: #F56C6C;
}

:deep(.el-form-item__label) {
  color: #fff;
}

:deep(.el-input__wrapper) {
  background: #404040;
  border: 1px solid #606060;
}

:deep(.el-input__inner) {
  color: #fff;
}

:deep(.el-textarea__inner) {
  background: #404040;
  border: 1px solid #606060;
  color: #fff;
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  color: #fff;
  text-align: center;
  z-index: 10;
}

.form-help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}
</style>

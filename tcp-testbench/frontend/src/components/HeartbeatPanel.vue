<template>
  <el-card class="heartbeat-panel">
    <template #header>
      <div class="card-header heartbeat-header">
        <span class="header-title">
          <div class="title-icon heartbeat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <span class="title-text">心跳数据配置</span>
        </span>
        <div class="header-actions">
          <div class="heartbeat-status">
            <div :class="['status-indicator', heartbeatStore.isHeartbeatActive ? 'status-connected' : 'status-disconnected']">
              <div class="status-dot"></div>
              <span>{{ heartbeatStore.isHeartbeatActive ? '发送中' : '已停止' }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>

    <div class="heartbeat-config">
      <!-- 发送配置 -->
      <div class="section">
        <div class="section-title">发送配置</div>
        <el-form :model="heartbeatConfig" label-width="100px" size="default">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="发送间隔">
                <el-input-number
                  v-model="intervalSeconds"
                  :min="1"
                  :max="60"
                  @change="updateIntervalConfig"
                  :disabled="heartbeatStore.isHeartbeatActive"
                  style="width: 100%"
                >
                  <template #append>秒</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送模式">
                <el-radio-group 
                  v-model="heartbeatConfig.autoSend" 
                  @change="updateConfig"
                  :disabled="heartbeatStore.isHeartbeatActive"
                >
                  <el-radio :value="true">自动</el-radio>
                  <el-radio :value="false">手动</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 发送心跳按钮 -->
        <div class="heartbeat-send-action" style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #333;">
          <el-button
            type="primary"
            size="large"
            @click="handleHeartbeatSend"
            style="width: 100%; height: 44px; font-size: 14px; font-weight: 600;"
          >
            <el-icon><VideoPlay /></el-icon>
            发送心跳
          </el-button>
        </div>
      </div>

      <!-- 场景模板 -->
      <div class="section">
        <div class="section-title">场景模板</div>
        <el-radio-group v-model="selectedTemplate" @change="applyTemplate">
          <el-radio-button 
            v-for="(template, key) in heartbeatStore.heartbeatTemplates" 
            :key="key" 
            :value="key"
          >
            {{ template.name }}
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 车辆状态配置 -->
      <div class="section vehicle-status-section">
        <div class="section-title">车辆状态</div>
        <el-form :model="heartbeatData" label-width="80px" size="default" class="vehicle-status-form">
          <div class="form-row">
            <div class="form-group">
              <el-form-item label="速度">
                <el-input-number
                  v-model="heartbeatData.speed"
                  :min="0"
                  :max="50"
                  @change="updateHeartbeatData"
                  class="full-width-input"
                >
                  <template #append>km/h</template>
                </el-input-number>
              </el-form-item>
            </div>
            <div class="form-group">
              <el-form-item label="电量">
                <el-input-number
                  v-model="heartbeatData.battery"
                  :min="0"
                  :max="100"
                  @change="updateHeartbeatData"
                  class="full-width-input"
                >
                  <template #append>%</template>
                </el-input-number>
              </el-form-item>
            </div>
            <div class="form-group">
              <el-form-item label="档位">
                <el-select v-model="heartbeatData.gear" @change="updateHeartbeatData" class="full-width-input">
                  <el-option label="停车档" :value="0" />
                  <el-option label="前进档" :value="1" />
                  <el-option label="倒车档" :value="2" />
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div class="switch-row">
            <div class="switch-group">
              <el-form-item label="ACC">
                <el-switch
                  v-model="heartbeatData.acc"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateHeartbeatData"
                />
              </el-form-item>
            </div>
            <div class="switch-group">
              <el-form-item label="充电">
                <el-switch
                  v-model="heartbeatData.charging"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateHeartbeatData"
                />
              </el-form-item>
            </div>
            <div class="switch-group">
              <el-form-item label="锁定">
                <el-switch
                  v-model="heartbeatData.locked"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateHeartbeatData"
                />
              </el-form-item>
            </div>
            <div class="switch-group">
              <el-form-item label="大灯">
                <el-switch
                  v-model="heartbeatData.headLight"
                  :active-value="1"
                  :inactive-value="0"
                  @change="updateHeartbeatData"
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>





      <!-- 心跳统计 -->
      <div class="heartbeat-stats">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ heartbeatStore.heartbeatCount }}</div>
            <div class="stat-label">发送次数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ heartbeatStore.heartbeatRate }}</div>
            <div class="stat-label">发送频率(/分钟)</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ intervalSeconds }}s</div>
            <div class="stat-label">发送间隔</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatTime(heartbeatStore.lastHeartbeatTime) }}</div>
            <div class="stat-label">最后发送</div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { useHeartbeatStore } from '../stores/heartbeat'
import { useConnectionStore } from '../stores/connection'
import { useDeviceStore } from '../stores/device'
import { useLoggerStore } from '../stores/logger'
import {
  Timer, VideoPlay, VideoPause, Promotion
} from '@element-plus/icons-vue'
import messageManager from '../utils/message'
import backendLogger from '../utils/backendLogger'
import { SendHeartbeat } from 'wailsjs/go/main/App'
// import { ElMessage } from 'element-plus' // 已替换为统一消息管理器

const heartbeatStore = useHeartbeatStore()
const connectionStore = useConnectionStore()
const deviceStore = useDeviceStore()
const loggerStore = useLoggerStore()

// 定时器管理
let heartbeatTimer = null

// 本地状态
const heartbeatConfig = ref({ ...heartbeatStore.heartbeatConfig })
const heartbeatData = ref({ ...heartbeatStore.currentHeartbeatData })
const selectedTemplate = ref(heartbeatStore.heartbeatConfig.template)
const intervalSeconds = ref(Math.floor(heartbeatStore.heartbeatConfig.interval / 1000))

// 监听 store 变化
watch(() => heartbeatStore.heartbeatConfig, (newConfig) => {
  heartbeatConfig.value = { ...newConfig }
  intervalSeconds.value = Math.floor(newConfig.interval / 1000)
}, { deep: true })

watch(() => heartbeatStore.currentHeartbeatData, (newData) => {
  heartbeatData.value = { ...newData }
}, { deep: true })

// 监听连接状态变化，连接断开时停止心跳发送
watch(() => connectionStore.isConnected, (isConnected) => {
  if (!isConnected && heartbeatStore.isHeartbeatActive) {
    stopAutoHeartbeat()
    messageManager.warning('连接已断开，已停止心跳发送')
  }
})

// 方法
const updateConfig = () => {
  heartbeatStore.updateHeartbeatConfig(heartbeatConfig.value)
}

const updateIntervalConfig = () => {
  heartbeatStore.updateHeartbeatConfig({
    interval: intervalSeconds.value * 1000
  })
}

const updateHeartbeatData = () => {
  heartbeatStore.updateHeartbeatData(heartbeatData.value)
}

const applyTemplate = (templateKey) => {
  heartbeatStore.applyTemplate(templateKey)
  selectedTemplate.value = templateKey
  heartbeatData.value = { ...heartbeatStore.currentHeartbeatData }

  const template = heartbeatStore.heartbeatTemplates[templateKey]
  loggerStore.logSystemEvent(`应用心跳模板: ${template.name}`, template)
  messageManager.success(`已应用 ${template.name} 模板`)
}

// 获取按钮文字
const getHeartbeatButtonText = () => {
  if (!heartbeatConfig.value.autoSend) {
    return '发送心跳'
  }

  if (heartbeatStore.isHeartbeatActive) {
    return '停止自动发送'
  } else {
    return '开始自动发送'
  }
}

// 统一的心跳发送处理
const handleHeartbeatSend = () => {
  if (!connectionStore.isConnected) {
    messageManager.warning('请先连接服务器')
    return
  }

  if (heartbeatConfig.value.autoSend) {
    // 自动模式：切换定时发送状态
    if (heartbeatStore.isHeartbeatActive) {
      stopAutoHeartbeat()
    } else {
      startAutoHeartbeat()
    }
  } else {
    // 手动模式：发送单次心跳
    sendSingleHeartbeat()
  }
}

// 开始自动发送心跳
const startAutoHeartbeat = () => {
  heartbeatStore.startHeartbeat()

  // 启动定时器
  heartbeatTimer = setInterval(() => {
    sendHeartbeatToServer()
  }, heartbeatConfig.value.interval)

  // 立即发送一次
  sendHeartbeatToServer()

  loggerStore.logSystemEvent('开始自动发送心跳数据', {
    interval: heartbeatConfig.value.interval,
    template: selectedTemplate.value
  })
  messageManager.success(`开始自动发送心跳数据 (间隔: ${intervalSeconds.value}秒)`)
}

// 停止自动发送心跳
const stopAutoHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
    heartbeatTimer = null
  }

  heartbeatStore.stopHeartbeat()
  loggerStore.logSystemEvent('停止自动发送心跳数据')
  messageManager.info('已停止自动发送心跳数据')
}

// 发送单次心跳
const sendSingleHeartbeat = () => {
  sendHeartbeatToServer()
  messageManager.success('已发送单次心跳数据')
}

// 实际发送心跳到服务器
const sendHeartbeatToServer = async () => {
  try {
    const connectionId = connectionStore.activeConnectionId
    if (!connectionId) {
      messageManager.error('连接ID不存在，请重新连接')
      return
    }

    // 🔍 发送心跳前：打印当前心跳数据
    await backendLogger.separator('发送心跳前的数据')
    await backendLogger.heartbeat('心跳数据中的latestTask', heartbeatData.value.latestTask)

    // 构建心跳数据，包含真实的设备状态信息
    const heartbeatPayload = {
      // 设备基本信息
      imsi: deviceStore.deviceConfig.imsi,
      imei: deviceStore.deviceConfig.imei,

      // GPS位置信息（统一使用设备配置中的位置）
      lng: deviceStore.deviceConfig.lng,
      lat: deviceStore.deviceConfig.lat,

      // 车辆状态信息
      ...heartbeatData.value,

      // 时间戳
      timestamp: Date.now()
    }

    await backendLogger.heartbeat('发送心跳数据', {
      latestTask: heartbeatPayload.latestTask,
      imsi: heartbeatPayload.imsi
    })

    // 调用后端API发送心跳
    const result = await SendHeartbeat(connectionId, heartbeatPayload)

    // 更新统计信息
    heartbeatStore.incrementHeartbeatCount()
    heartbeatStore.updateLastHeartbeatTime()

    // 记录日志
    loggerStore.logMessageSent({ name: '心跳数据', cmd: 2 }, heartbeatPayload)

    await backendLogger.info('✅ 心跳发送成功: ' + result)
    await backendLogger.info('💡 任务流程状态转换由后端处理')

  } catch (error) {
    console.error('心跳发送失败:', error)
    loggerStore.logSystemEvent('心跳发送失败', { error: error.message || error })

    // 使用统一消息管理器显示错误消息
    messageManager.error(`心跳发送失败: ${error.message || error}`, 5000)

    // 如果是自动发送模式且发送失败，停止自动发送
    if (heartbeatConfig.value.autoSend && heartbeatStore.isHeartbeatActive) {
      stopAutoHeartbeat()
      messageManager.error(`心跳发送失败，已停止自动发送: ${error.message || error}`)
    } else {
      messageManager.error(`心跳发送失败: ${error.message || error}`)
    }
  }
}

const formatTime = (time) => {
  if (!time) return '--'
  return new Date(time).toLocaleTimeString('zh-CN')
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
    heartbeatTimer = null
  }

  // 如果心跳正在发送，停止它
  if (heartbeatStore.isHeartbeatActive) {
    heartbeatStore.stopHeartbeat()
  }
})
</script>

<style scoped>
.heartbeat-panel {
  height: auto;
}

.heartbeat-panel :deep(.el-card__body) {
  overflow: visible;
  padding: 12px;
  height: auto;
  margin-top: 0 !important;
  border-top: none !important;
}

.heartbeat-panel :deep(.el-card__header) {
  padding: 0 !important;
  border-bottom: none !important;
  margin-bottom: 0 !important;
}

/* 心跳面板主题色 - 绿色 */
.heartbeat-header {
  background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
  margin: 0;
  padding: 12px;
  border-radius: 8px 8px 0 0;
}

.heartbeat-header .header-title {
  color: #ffffff !important;
}

.heartbeat-header .heartbeat-status .status-indicator {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.heartbeat-header .heartbeat-status .status-indicator span {
  color: #ffffff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.15);
}

.heartbeat-icon {
  background: rgba(255, 255, 255, 0.15);
}

.heartbeat-status {
  display: flex;
  align-items: center;
}

.heartbeat-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section {
  padding: 12px;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.section-title {
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.heartbeat-send-action {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  width: 100%;
  display: block;
}

.heartbeat-send-action .el-button {
  font-size: 14px;
  font-weight: 600;
  height: 44px;
  width: 100% !important;
  display: block !important;
}

.control-actions {
  display: flex;
  gap: 12px;
}

.control-actions .el-button {
  flex: 1;
}

.heartbeat-stats {
  padding: 16px;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.el-radio-group {
  width: 100%;
}

.el-radio-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 车辆状态模块样式 */
.vehicle-status-section {
  position: relative;
}

.vehicle-status-form {
  width: 100%;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

.switch-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.switch-group {
  flex: 1;
  min-width: 120px;
  display: flex;
  align-items: center;
}

.switch-group .el-form-item {
  margin-bottom: 0;
}



/* 自动驾驶定位特殊样式 */
.auto-drive-switch {
  min-width: 160px;
}

.auto-drive-switch .el-form-item__label {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
  width: auto !important;
  min-width: 100px !important;
}

.full-width-input {
  width: 100%;
}

/* Element Plus 表单标签宽度控制 */

.auto-drive-switch .el-form-item__label {
  width: auto !important;
  min-width: 100px !important;
  margin-right: 12px !important;
  white-space: nowrap !important;
}

/* 全屏状态下的响应式优化 */
@media (min-width: 1600px) {
  .form-row {
    gap: 24px;
  }

  .switch-row {
    gap: 30px;
  }



  .form-group {
    min-width: 220px;
  }

  .coordinate-group {
    min-width: 300px;
  }

  .switch-group {
    min-width: 140px;
  }

  .gps-switch-item {
    min-width: 140px;
  }

  .auto-drive-switch {
    min-width: 180px;
  }

  .mileage-group {
    max-width: 350px;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1400px) {
  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .switch-row {
    flex-wrap: wrap;
    gap: 16px;
  }



  .form-group,
  .coordinate-group,
  .mileage-group {
    min-width: auto;
    width: 100%;
    max-width: none;
  }

  .switch-group,
  .gps-switch-item,
  .auto-drive-switch {
    min-width: auto;
    width: 100%;
    justify-content: flex-start;
  }
}

/* 小屏幕优化 */
@media (max-width: 768px) {
  .vehicle-status-form {
    font-size: 14px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .form-row,
  .switch-row {
    gap: 8px;
  }
}
</style>

export namespace main {
	
	export class ConnectionConfig {
	    id: string;
	    host: string;
	    port: number;
	    timeout: number;
	    keepalive: number;
	    encryptionKey: string;
	
	    static createFrom(source: any = {}) {
	        return new ConnectionConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.host = source["host"];
	        this.port = source["port"];
	        this.timeout = source["timeout"];
	        this.keepalive = source["keepalive"];
	        this.encryptionKey = source["encryptionKey"];
	    }
	}
	export class ConnectionStatusResponse {
	    id: string;
	    status: string;
	    error?: string;
	    exists: boolean;
	    isConnected: boolean;
	    isConnecting: boolean;
	    isError: boolean;
	    messagesSent: number;
	    messagesReceived: number;
	    errors: number;
	    timestamp: number;
	
	    static createFrom(source: any = {}) {
	        return new ConnectionStatusResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.status = source["status"];
	        this.error = source["error"];
	        this.exists = source["exists"];
	        this.isConnected = source["isConnected"];
	        this.isConnecting = source["isConnecting"];
	        this.isError = source["isError"];
	        this.messagesSent = source["messagesSent"];
	        this.messagesReceived = source["messagesReceived"];
	        this.errors = source["errors"];
	        this.timestamp = source["timestamp"];
	    }
	}
	export class DeviceConfigRequest {
	    imsi: string;
	    imei: string;
	    model: string;
	    softwareVer: string;
	    hardwareVer: string;
	    vendor: string;
	    ci: string;
	    pci: string;
	    manageIp: string;
	    position: string;
	    longitude: number;
	    latitude: number;
	
	    static createFrom(source: any = {}) {
	        return new DeviceConfigRequest(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.imsi = source["imsi"];
	        this.imei = source["imei"];
	        this.model = source["model"];
	        this.softwareVer = source["softwareVer"];
	        this.hardwareVer = source["hardwareVer"];
	        this.vendor = source["vendor"];
	        this.ci = source["ci"];
	        this.pci = source["pci"];
	        this.manageIp = source["manageIp"];
	        this.position = source["position"];
	        this.longitude = source["longitude"];
	        this.latitude = source["latitude"];
	    }
	}
	export class StatsResponse {
	    id: string;
	    // Go type: time
	    connectTime: any;
	    // Go type: time
	    lastMessageTime: any;
	    messagesSent: number;
	    messagesReceived: number;
	    bytesSent: number;
	    bytesReceived: number;
	    errors: number;
	
	    static createFrom(source: any = {}) {
	        return new StatsResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.connectTime = this.convertValues(source["connectTime"], null);
	        this.lastMessageTime = this.convertValues(source["lastMessageTime"], null);
	        this.messagesSent = source["messagesSent"];
	        this.messagesReceived = source["messagesReceived"];
	        this.bytesSent = source["bytesSent"];
	        this.bytesReceived = source["bytesReceived"];
	        this.errors = source["errors"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

export namespace taskflow {
	
	export class RuntimeState {
	    isRunning: boolean;
	    currentNodeId: string;
	    currentStep: number;
	    heartbeatCount: number;
	    totalHeartbeats: number;
	    lastTransitionTime: number;
	    startTime: number;
	
	    static createFrom(source: any = {}) {
	        return new RuntimeState(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.isRunning = source["isRunning"];
	        this.currentNodeId = source["currentNodeId"];
	        this.currentStep = source["currentStep"];
	        this.heartbeatCount = source["heartbeatCount"];
	        this.totalHeartbeats = source["totalHeartbeats"];
	        this.lastTransitionTime = source["lastTransitionTime"];
	        this.startTime = source["startTime"];
	    }
	}
	export class StateNode {
	    id: string;
	    step: number;
	    name: string;
	    // Go type: struct { HeartbeatCount int "json:\"heartbeatCount\"" }
	    config: any;
	    // Go type: struct { X float64 "json:\"x\""; Y float64 "json:\"y\"" }
	    position: any;
	
	    static createFrom(source: any = {}) {
	        return new StateNode(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.step = source["step"];
	        this.name = source["name"];
	        this.config = this.convertValues(source["config"], Object);
	        this.position = this.convertValues(source["position"], Object);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class StateTransition {
	    id: string;
	    fromNodeId: string;
	    toNodeId: string;
	    description: string;
	    probability: number;
	    triggerType: string;
	    triggerValue: number;
	
	    static createFrom(source: any = {}) {
	        return new StateTransition(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.fromNodeId = source["fromNodeId"];
	        this.toNodeId = source["toNodeId"];
	        this.description = source["description"];
	        this.probability = source["probability"];
	        this.triggerType = source["triggerType"];
	        this.triggerValue = source["triggerValue"];
	    }
	}
	export class TaskConfig {
	    taskId: number;
	    routeId: number;
	    taskName: string;
	    taskDescription: string;
	    taskDistance: number;
	    leftTime: number;
	    // Go type: struct { ID int "json:\"id\""; Name string "json:\"name\"" }
	    currentStation: any;
	    // Go type: struct { ID int "json:\"id\""; Name string "json:\"name\"" }
	    targetStation: any;
	
	    static createFrom(source: any = {}) {
	        return new TaskConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.taskId = source["taskId"];
	        this.routeId = source["routeId"];
	        this.taskName = source["taskName"];
	        this.taskDescription = source["taskDescription"];
	        this.taskDistance = source["taskDistance"];
	        this.leftTime = source["leftTime"];
	        this.currentStation = this.convertValues(source["currentStation"], Object);
	        this.targetStation = this.convertValues(source["targetStation"], Object);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class TaskFlowConfig {
	    id: string;
	    name: string;
	    description: string;
	    heartbeatInterval: number;
	    triggerMode: string;
	
	    static createFrom(source: any = {}) {
	        return new TaskFlowConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.description = source["description"];
	        this.heartbeatInterval = source["heartbeatInterval"];
	        this.triggerMode = source["triggerMode"];
	    }
	}
	export class TaskFlowData {
	    taskFlowConfig: TaskFlowConfig;
	    stateNodes: StateNode[];
	    stateTransitions: StateTransition[];
	    taskConfig: TaskConfig;
	
	    static createFrom(source: any = {}) {
	        return new TaskFlowData(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.taskFlowConfig = this.convertValues(source["taskFlowConfig"], TaskFlowConfig);
	        this.stateNodes = this.convertValues(source["stateNodes"], StateNode);
	        this.stateTransitions = this.convertValues(source["stateTransitions"], StateTransition);
	        this.taskConfig = this.convertValues(source["taskConfig"], TaskConfig);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class TaskInfo {
	    step: number;
	    taskId: number;
	    taskDistance: number;
	
	    static createFrom(source: any = {}) {
	        return new TaskInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.step = source["step"];
	        this.taskId = source["taskId"];
	        this.taskDistance = source["taskDistance"];
	    }
	}

}


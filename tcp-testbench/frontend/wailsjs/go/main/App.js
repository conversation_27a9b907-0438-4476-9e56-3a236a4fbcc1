// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function Connect(arg1) {
  return window['go']['main']['App']['Connect'](arg1);
}

export function CreateConnection(arg1, arg2) {
  return window['go']['main']['App']['CreateConnection'](arg1, arg2);
}

export function Disconnect(arg1) {
  return window['go']['main']['App']['Disconnect'](arg1);
}

export function GetAllConnections() {
  return window['go']['main']['App']['GetAllConnections']();
}

export function GetConnectionStatus(arg1) {
  return window['go']['main']['App']['GetConnectionStatus'](arg1);
}

export function GetCurrentTaskInfo() {
  return window['go']['main']['App']['GetCurrentTaskInfo']();
}

export function GetStats(arg1) {
  return window['go']['main']['App']['GetStats'](arg1);
}

export function GetTaskFlowData() {
  return window['go']['main']['App']['GetTaskFlowData']();
}

export function GetTaskFlowRuntimeState() {
  return window['go']['main']['App']['GetTaskFlowRuntimeState']();
}

export function LogToConsole(arg1, arg2, arg3) {
  return window['go']['main']['App']['LogToConsole'](arg1, arg2, arg3);
}

export function RemoveConnection(arg1) {
  return window['go']['main']['App']['RemoveConnection'](arg1);
}

export function SaveTaskFlowData(arg1) {
  return window['go']['main']['App']['SaveTaskFlowData'](arg1);
}

export function SendHeartbeat(arg1, arg2) {
  return window['go']['main']['App']['SendHeartbeat'](arg1, arg2);
}

export function SendRegister(arg1) {
  return window['go']['main']['App']['SendRegister'](arg1);
}

export function StartAutoHeartbeat(arg1, arg2, arg3) {
  return window['go']['main']['App']['StartAutoHeartbeat'](arg1, arg2, arg3);
}

export function StartTaskFlow() {
  return window['go']['main']['App']['StartTaskFlow']();
}

export function StopTaskFlow() {
  return window['go']['main']['App']['StopTaskFlow']();
}

export function TestConnection(arg1, arg2) {
  return window['go']['main']['App']['TestConnection'](arg1, arg2);
}

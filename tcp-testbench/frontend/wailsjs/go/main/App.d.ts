// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {main} from '../models';
import {taskflow} from '../models';

export function Connect(arg1:string):Promise<string>;

export function CreateConnection(arg1:main.ConnectionConfig,arg2:main.DeviceConfigRequest):Promise<string>;

export function Disconnect(arg1:string):Promise<string>;

export function GetAllConnections():Promise<Array<string>>;

export function GetConnectionStatus(arg1:string):Promise<main.ConnectionStatusResponse>;

export function GetCurrentTaskInfo():Promise<taskflow.TaskInfo>;

export function GetStats(arg1:string):Promise<main.StatsResponse>;

export function GetTaskFlowData():Promise<taskflow.TaskFlowData>;

export function GetTaskFlowRuntimeState():Promise<taskflow.RuntimeState>;

export function LogToConsole(arg1:string,arg2:string,arg3:any):Promise<void>;

export function RemoveConnection(arg1:string):Promise<string>;

export function SaveTaskFlowData(arg1:taskflow.TaskFlowData):Promise<string>;

export function SendHeartbeat(arg1:string,arg2:Record<string, any>):Promise<string>;

export function SendRegister(arg1:string):Promise<string>;

export function StartAutoHeartbeat(arg1:string,arg2:number,arg3:Record<string, any>):Promise<string>;

export function StartTaskFlow():Promise<string>;

export function StopTaskFlow():Promise<string>;

export function TestConnection(arg1:string,arg2:number):Promise<string>;

# TCP TestBench 前端开发规范

## 消息弹窗使用规范

### ⚠️ 重要：禁止直接使用 ElMessage

**绝对不要直接使用 `ElMessage`！** 这会导致多条消息同时显示，影响用户体验。

### ✅ 正确做法：使用统一的消息管理器

```javascript
// ❌ 错误 - 不要这样做
import { ElMessage } from 'element-plus'
ElMessage.success('操作成功')

// ✅ 正确 - 使用消息管理器
import messageManager from '../utils/message'
messageManager.success('操作成功')
```

### 📋 消息管理器 API

```javascript
// 成功消息
messageManager.success('操作成功', 3000)

// 错误消息
messageManager.error('操作失败', 3000)

// 警告消息
messageManager.warning('请注意', 3000)

// 信息消息
messageManager.info('提示信息', 3000)

// 手动关闭当前消息
messageManager.closeCurrentMessage()
```

### 🎯 消息管理器的优势

1. **单一消息显示**：确保同一时间只显示一条消息
2. **自动替换**：新消息会自动关闭旧消息
3. **无堆叠**：避免多条消息堆叠显示
4. **统一管理**：所有消息通过统一接口管理

### 🔧 技术实现原理

消息管理器通过以下机制确保单一消息显示：

1. **状态跟踪**：跟踪当前显示的消息实例
2. **自动关闭**：显示新消息前自动关闭当前消息
3. **回调清理**：消息关闭时清理状态引用

### 📝 代码审查检查点

在代码审查时，请检查：

- [ ] 没有直接导入或使用 `ElMessage`
- [ ] 所有消息弹窗都使用 `messageManager`
- [ ] 异步操作中的消息也使用统一管理器
- [ ] 组件卸载时正确清理消息状态

### 🚫 常见错误示例

```javascript
// ❌ 错误：直接使用 ElMessage
ElMessage.success('模板已应用')
ElMessage.error('操作失败')

// ❌ 错误：在循环中使用 ElMessage
templates.forEach(template => {
  ElMessage.success(`已应用 ${template.name}`)
})

// ❌ 错误：在异步操作中直接使用
setTimeout(() => {
  ElMessage.info('定时提醒')
}, 1000)
```

### ✅ 正确示例

```javascript
// ✅ 正确：使用消息管理器
messageManager.success('模板已应用')
messageManager.error('操作失败')

// ✅ 正确：在循环中使用（会自动替换）
templates.forEach(template => {
  messageManager.success(`已应用 ${template.name}`)
})

// ✅ 正确：在异步操作中使用
setTimeout(() => {
  messageManager.info('定时提醒')
}, 1000)
```

### 🔄 迁移现有代码

如果发现现有代码使用了 `ElMessage`，请按以下步骤迁移：

1. 移除 `ElMessage` 的导入
2. 添加 `messageManager` 的导入
3. 替换所有 `ElMessage.xxx()` 为 `messageManager.xxx()`
4. 测试确保消息显示正常

### 📞 联系方式

如有疑问，请联系项目维护者。

---

**记住：始终使用 `messageManager`，永远不要直接使用 `ElMessage`！**

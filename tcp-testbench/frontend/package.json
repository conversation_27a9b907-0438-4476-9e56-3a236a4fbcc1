{"name": "tcp-testbench-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.2.37"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.3", "vite": "^3.0.7"}}
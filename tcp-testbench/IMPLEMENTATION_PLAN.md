# TCP客户端测试工具功能实现计划

## 项目概述

基于现有的Wails + Vue 3架构，实现完整的TCP客户端功能来模拟设备与ccserver的真实交互。

## 1. 功能分析

### 1.1 现有UI组件分析
- ✅ **ConnectionPanel**: 服务器连接配置面板
- ✅ **DevicePanel**: 设备信息配置面板  
- ✅ **HeartbeatPanel**: 心跳数据配置面板
- ✅ **CommandPanel**: 命令发送面板
- ✅ **LoggerPanel**: 日志显示面板
- ✅ **StatusPanel**: 状态监控面板
- ✅ **PerformanceTest**: 性能测试面板

### 1.2 现有Store状态管理
- ✅ **connection.js**: 连接状态和服务器配置管理
- ✅ **device.js**: 设备配置和模板管理
- ✅ **heartbeat.js**: 心跳数据管理
- ✅ **command.js**: 命令管理
- ✅ **logger.js**: 日志管理

### 1.3 已实现的后端功能
- ✅ **YHL协议客户端**: 完整的协议封装、解析、CRC校验
- ✅ **TCP客户端**: 连接管理、消息发送接收、状态监控
- ✅ **消息构建器**: 注册消息、心跳消息的构建和序列化
- ✅ **后端API**: 连接管理、消息发送、状态查询等接口

## 2. 协议实现状态

### 2.1 YHL协议客户端 ✅
- **文件**: `protocol/yhl_client.go`
- **功能**:
  - 消息封装和解析
  - CRC16校验计算
  - SM4-GCM加密支持
  - V1/V2协议版本支持
  - 响应消息解析

### 2.2 消息定义 ✅
- **文件**: `protocol/messages.go`
- **功能**:
  - 设备注册消息结构
  - 心跳消息完整结构（包含所有字段）
  - 消息构建器和默认选项
  - 任务信息、告警信息等子结构

### 2.3 TCP客户端 ✅
- **文件**: `client/tcp_client.go`
- **功能**:
  - TCP连接建立和维护
  - 连接状态监控和自动重连
  - 消息发送和接收队列
  - 统计信息收集
  - 事件回调机制

## 3. 后端API实现状态

### 3.1 已实现的API ✅
- `CreateConnection`: 创建TCP连接配置
- `Connect`: 连接到服务器
- `Disconnect`: 断开连接
- `SendRegister`: 发送注册消息
- `SendHeartbeat`: 发送心跳消息
- `StartAutoHeartbeat`: 启动自动心跳
- `GetConnectionStatus`: 获取连接状态
- `GetStats`: 获取统计信息
- `GetAllConnections`: 获取所有连接列表
- `RemoveConnection`: 删除连接
- `TestConnection`: 简单连接测试

### 3.2 心跳选项解析 ✅
- 支持所有心跳字段的动态配置
- 任务状态模拟
- 设备状态模拟
- 位置信息模拟

## 4. 待实现功能

### 4.1 前端集成 🔄
**优先级**: 高
**预计时间**: 2-3天

#### 4.1.1 更新Store状态管理
- [ ] 更新 `connection.js` 以使用新的后端API
- [ ] 实现多连接管理
- [ ] 添加连接状态实时更新
- [ ] 集成统计信息显示

#### 4.1.2 更新UI组件
- [ ] 修改 `ConnectionPanel.vue` 支持多连接
- [ ] 更新 `DevicePanel.vue` 集成设备配置
- [ ] 修改 `HeartbeatPanel.vue` 支持完整心跳选项
- [ ] 更新 `StatusPanel.vue` 显示实时状态
- [ ] 修改 `LoggerPanel.vue` 显示消息日志

### 4.2 消息日志和调试 🔄
**优先级**: 高
**预计时间**: 1-2天

#### 4.2.1 消息日志系统
- [ ] 实现消息发送/接收日志记录
- [ ] 添加消息内容格式化显示
- [ ] 支持消息过滤和搜索
- [ ] 实现日志导出功能

#### 4.2.2 协议调试功能
- [ ] 十六进制消息查看
- [ ] 协议字段解析显示
- [ ] 消息重放功能
- [ ] 错误消息分析

### 4.3 高级测试功能 🔄
**优先级**: 中
**预计时间**: 2-3天

#### 4.3.1 批量测试
- [ ] 多设备并发连接测试
- [ ] 压力测试功能
- [ ] 性能基准测试
- [ ] 测试报告生成

#### 4.3.2 场景模拟
- [ ] 设备移动轨迹模拟
- [ ] 任务执行流程模拟
- [ ] 异常情况模拟
- [ ] 网络中断恢复测试

### 4.4 配置管理 🔄
**优先级**: 中
**预计时间**: 1天

#### 4.4.1 配置模板
- [ ] 设备配置模板管理
- [ ] 心跳配置预设
- [ ] 测试场景配置
- [ ] 配置导入导出

#### 4.4.2 环境配置
- [ ] 多环境服务器配置
- [ ] 加密密钥管理
- [ ] 连接参数优化
- [ ] 调试选项配置

## 5. 技术实现方案

### 5.1 架构设计
```
Frontend (Vue 3)
├── Components (UI组件)
├── Stores (状态管理)
├── Utils (工具函数)
└── Wails Bridge (前后端通信)

Backend (Go)
├── Protocol (YHL协议实现)
├── Client (TCP客户端)
├── App (Wails应用接口)
└── Utils (工具函数)
```

### 5.2 数据流设计
```
UI操作 → Store状态 → Wails API → Go后端 → TCP客户端 → ccserver
                                                    ↓
UI更新 ← Store更新 ← 事件回调 ← Go后端 ← TCP响应 ← ccserver
```

### 5.3 关键技术点
- **并发安全**: 使用sync.RWMutex保护共享状态
- **事件驱动**: 基于回调函数的事件通知机制
- **状态管理**: 前端Pinia + 后端内存状态
- **协议兼容**: 完全兼容ccserver的YHL协议
- **错误处理**: 完善的错误处理和恢复机制

## 6. 测试方案

### 6.1 单元测试
- [ ] YHL协议编解码测试
- [ ] TCP客户端连接测试
- [ ] 消息构建器测试
- [ ] 状态管理测试

### 6.2 集成测试
- [ ] 与ccserver的完整交互测试
- [ ] 多设备并发测试
- [ ] 长时间稳定性测试
- [ ] 异常情况恢复测试

### 6.3 性能测试
- [ ] 消息吞吐量测试
- [ ] 内存使用测试
- [ ] CPU使用测试
- [ ] 网络延迟测试

## 7. 部署和使用

### 7.1 构建流程
```bash
# 安装依赖
go mod tidy
cd frontend && npm install

# 开发模式
wails dev

# 构建发布版本
wails build
```

### 7.2 使用流程
1. 配置服务器连接参数
2. 设置设备信息
3. 创建TCP连接
4. 发送注册消息
5. 启动自动心跳
6. 监控连接状态和消息日志
7. 进行各种测试场景

## 8. 风险评估

### 8.1 技术风险
- **协议兼容性**: 需要确保与ccserver协议完全兼容
- **并发处理**: 多连接并发可能导致资源竞争
- **内存管理**: 长时间运行可能导致内存泄漏

### 8.2 缓解措施
- 基于ccserver源码实现协议，确保兼容性
- 使用Go的并发安全机制
- 实现资源清理和连接池管理
- 添加内存监控和自动清理

## 9. 后续优化

### 9.1 功能扩展
- 支持更多协议版本
- 添加图形化数据展示
- 实现自动化测试脚本
- 支持插件扩展机制

### 9.2 性能优化
- 消息池化减少GC压力
- 连接复用提高效率
- 异步处理提升响应速度
- 缓存机制减少重复计算

## 10. 时间计划

| 阶段 | 功能 | 预计时间 | 状态 |
|------|------|----------|------|
| 阶段1 | 协议和后端实现 | 3天 | ✅ 已完成 |
| 阶段2 | 前端集成 | 3天 | 🔄 进行中 |
| 阶段3 | 调试功能 | 2天 | ⏳ 待开始 |
| 阶段4 | 高级功能 | 3天 | ⏳ 待开始 |
| 阶段5 | 测试优化 | 2天 | ⏳ 待开始 |

**总计**: 约13天完成全部功能

## 11. 成功标准

- ✅ 能够成功连接到ccserver
- ✅ 能够发送有效的注册和心跳消息
- ✅ 能够接收和解析服务器响应
- [ ] 支持多设备并发连接
- [ ] 提供完整的调试和监控功能
- [ ] 具备良好的用户体验和稳定性

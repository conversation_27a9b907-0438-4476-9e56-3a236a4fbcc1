package client

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"
	"tcp-testbench/protocol"
)

// TCP客户端状态
type ClientStatus int

const (
	StatusDisconnected ClientStatus = iota
	StatusConnecting
	StatusConnected
	StatusError
)

// TCP客户端
type TcpClient struct {
	// 连接配置
	serverAddr string
	timeout    time.Duration
	keepalive  time.Duration
	
	// 协议处理
	yhlClient     *protocol.YhlClient
	msgBuilder    *protocol.MessageBuilder
	deviceConfig  *protocol.DeviceConfig
	
	// 连接管理
	conn          net.Conn
	status        ClientStatus
	statusMutex   sync.RWMutex
	
	// 消息处理
	sendChan      chan []byte
	receiveChan   chan []byte
	errorChan     chan error
	
	// 控制信号
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	
	// 统计信息
	stats         *ClientStats
	
	// 事件回调
	onStatusChange    func(status ClientStatus, err error)
	onMessageReceived func(data []byte)
	onMessageSent     func(data []byte)
}

// 客户端统计信息
type ClientStats struct {
	ConnectTime       time.Time
	LastMessageTime   time.Time
	MessagesSent      int64
	MessagesReceived  int64
	BytesSent         int64
	BytesReceived     int64
	Errors            int64
	mutex             sync.RWMutex
}

// 客户端配置
type ClientConfig struct {
	ServerAddr   string
	Timeout      time.Duration
	Keepalive    time.Duration
	DeviceConfig *protocol.DeviceConfig
	EncryptionKey string
}

// 创建新的TCP客户端
func NewTcpClient(config *ClientConfig) *TcpClient {
	ctx, cancel := context.WithCancel(context.Background())
	
	yhlClient := protocol.NewYhlClient()
	if config.EncryptionKey != "" {
		yhlClient.SetEncryptionKey(config.EncryptionKey)
	}
	
	client := &TcpClient{
		serverAddr:   config.ServerAddr,
		timeout:      config.Timeout,
		keepalive:    config.Keepalive,
		yhlClient:    yhlClient,
		msgBuilder:   protocol.NewMessageBuilder(config.DeviceConfig),
		deviceConfig: config.DeviceConfig,
		status:       StatusDisconnected,
		sendChan:     make(chan []byte, 100),
		receiveChan:  make(chan []byte, 100),
		errorChan:    make(chan error, 10),
		ctx:          ctx,
		cancel:       cancel,
		stats:        &ClientStats{},
	}
	
	return client
}

// 设置状态变化回调
func (c *TcpClient) SetStatusChangeCallback(callback func(status ClientStatus, err error)) {
	c.onStatusChange = callback
}

// 设置消息接收回调
func (c *TcpClient) SetMessageReceivedCallback(callback func(data []byte)) {
	c.onMessageReceived = callback
}

// 设置消息发送回调
func (c *TcpClient) SetMessageSentCallback(callback func(data []byte)) {
	c.onMessageSent = callback
}

// 连接到服务器
func (c *TcpClient) Connect() error {
	c.setStatus(StatusConnecting, nil)
	
	// 建立TCP连接
	dialer := &net.Dialer{
		Timeout: c.timeout,
	}
	
	conn, err := dialer.DialContext(c.ctx, "tcp", c.serverAddr)
	if err != nil {
		c.setStatus(StatusError, err)
		return fmt.Errorf("failed to connect to server: %v", err)
	}
	
	c.conn = conn
	c.setStatus(StatusConnected, nil)
	
	// 更新统计信息
	c.stats.mutex.Lock()
	c.stats.ConnectTime = time.Now()
	c.stats.mutex.Unlock()
	
	// 启动消息处理协程
	c.wg.Add(3)
	go c.sendLoop()
	go c.receiveLoop()
	go c.errorHandler()
	
	return nil
}

// 断开连接
func (c *TcpClient) Disconnect() error {
	c.cancel()
	
	if c.conn != nil {
		c.conn.Close()
	}
	
	c.wg.Wait()
	c.setStatus(StatusDisconnected, nil)
	
	return nil
}

// 发送注册消息
func (c *TcpClient) SendRegister() error {
	if c.status != StatusConnected {
		return fmt.Errorf("client not connected")
	}
	
	// 构建注册消息
	registerMsg := c.msgBuilder.BuildRegisterMessage()
	
	// 序列化消息
	data, err := c.yhlClient.BuildMessage(protocol.CMD_REGISTER, registerMsg, false)
	if err != nil {
		return fmt.Errorf("failed to build register message: %v", err)
	}
	
	// 发送消息
	select {
	case c.sendChan <- data:
		return nil
	case <-c.ctx.Done():
		return fmt.Errorf("client is shutting down")
	default:
		return fmt.Errorf("send channel is full")
	}
}

// 发送心跳消息
func (c *TcpClient) SendHeartbeat(options *protocol.HeartbeatOptions) error {
	if c.status != StatusConnected {
		return fmt.Errorf("client not connected")
	}
	
	if options == nil {
		options = protocol.GetDefaultHeartbeatOptions()
	}
	
	// 构建心跳消息
	heartbeatMsg := c.msgBuilder.BuildHeartbeatMessage(options)
	
	// 序列化消息
	data, err := c.yhlClient.BuildMessage(protocol.CMD_HEARTBEAT, heartbeatMsg, false)
	if err != nil {
		return fmt.Errorf("failed to build heartbeat message: %v", err)
	}
	
	// 发送消息
	select {
	case c.sendChan <- data:
		return nil
	case <-c.ctx.Done():
		return fmt.Errorf("client is shutting down")
	default:
		return fmt.Errorf("send channel is full")
	}
}

// 启动自动心跳
func (c *TcpClient) StartAutoHeartbeat(interval time.Duration, options *protocol.HeartbeatOptions) {
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()
		
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				if c.status == StatusConnected {
					if err := c.SendHeartbeat(options); err != nil {
						c.errorChan <- fmt.Errorf("auto heartbeat failed: %v", err)
					}
				}
			case <-c.ctx.Done():
				return
			}
		}
	}()
}

// 获取客户端状态
func (c *TcpClient) GetStatus() ClientStatus {
	c.statusMutex.RLock()
	defer c.statusMutex.RUnlock()
	return c.status
}

// 获取统计信息
func (c *TcpClient) GetStats() ClientStats {
	c.stats.mutex.RLock()
	defer c.stats.mutex.RUnlock()
	return *c.stats
}

// 设置状态
func (c *TcpClient) setStatus(status ClientStatus, err error) {
	c.statusMutex.Lock()
	c.status = status
	c.statusMutex.Unlock()
	
	if c.onStatusChange != nil {
		c.onStatusChange(status, err)
	}
}

// 发送循环
func (c *TcpClient) sendLoop() {
	defer c.wg.Done()
	
	for {
		select {
		case data := <-c.sendChan:
			if c.conn != nil {
				// 设置写超时
				c.conn.SetWriteDeadline(time.Now().Add(c.timeout))
				
				n, err := c.conn.Write(data)
				if err != nil {
					c.errorChan <- fmt.Errorf("send failed: %v", err)
					continue
				}
				
				// 更新统计信息
				c.stats.mutex.Lock()
				c.stats.MessagesSent++
				c.stats.BytesSent += int64(n)
				c.stats.LastMessageTime = time.Now()
				c.stats.mutex.Unlock()
				
				// 触发发送回调
				if c.onMessageSent != nil {
					c.onMessageSent(data)
				}
			}
			
		case <-c.ctx.Done():
			return
		}
	}
}

// 接收循环
func (c *TcpClient) receiveLoop() {
	defer c.wg.Done()
	
	buffer := make([]byte, 4096)
	
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			if c.conn != nil {
				// 设置读超时
				c.conn.SetReadDeadline(time.Now().Add(c.keepalive))
				
				n, err := c.conn.Read(buffer)
				if err != nil {
					if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
						// 超时不算错误，继续循环
						continue
					}
					c.errorChan <- fmt.Errorf("receive failed: %v", err)
					return
				}
				
				if n > 0 {
					data := make([]byte, n)
					copy(data, buffer[:n])
					
					// 更新统计信息
					c.stats.mutex.Lock()
					c.stats.MessagesReceived++
					c.stats.BytesReceived += int64(n)
					c.stats.LastMessageTime = time.Now()
					c.stats.mutex.Unlock()
					
					// 发送到接收通道
					select {
					case c.receiveChan <- data:
					default:
						// 接收通道满了，丢弃消息
					}
					
					// 触发接收回调
					if c.onMessageReceived != nil {
						c.onMessageReceived(data)
					}
				}
			}
		}
	}
}

// 错误处理
func (c *TcpClient) errorHandler() {
	defer c.wg.Done()
	
	for {
		select {
		case err := <-c.errorChan:
			// 更新统计信息
			c.stats.mutex.Lock()
			c.stats.Errors++
			c.stats.mutex.Unlock()
			
			// 设置错误状态
			c.setStatus(StatusError, err)
			
			// 如果是连接错误，尝试重连
			if c.conn != nil {
				c.conn.Close()
				c.conn = nil
			}
			
		case <-c.ctx.Done():
			return
		}
	}
}

// 状态字符串
func (s ClientStatus) String() string {
	switch s {
	case StatusDisconnected:
		return "disconnected"
	case StatusConnecting:
		return "connecting"
	case StatusConnected:
		return "connected"
	case StatusError:
		return "error"
	default:
		return "unknown"
	}
}

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"path/filepath"
	"sync"
	"time"
	"tcp-testbench/client"
	"tcp-testbench/protocol"
	"tcp-testbench/taskflow"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx         context.Context
	clients     map[string]*client.TcpClient
	mutex       sync.RWMutex
	taskManager *taskflow.TaskFlowManager
}

// NewApp creates a new App application struct
func NewApp() *App {
	// 创建配置目录路径
	configPath := filepath.Join(".", "config", "taskflow.json")

	app := &App{
		clients:     make(map[string]*client.TcpClient),
		taskManager: taskflow.NewTaskFlowManager(configPath),
	}

	return app
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// 连接配置
type ConnectionConfig struct {
	ID            string `json:"id"`
	Host          string `json:"host"`
	Port          int    `json:"port"`
	Timeout       int    `json:"timeout"`       // 秒
	Keepalive     int    `json:"keepalive"`     // 秒
	EncryptionKey string `json:"encryptionKey"`
}

// 设备配置
type DeviceConfigRequest struct {
	IMSI        string  `json:"imsi"`
	IMEI        string  `json:"imei"`
	Model       string  `json:"model"`
	SoftwareVer string  `json:"softwareVer"`
	HardwareVer string  `json:"hardwareVer"`
	Vendor      string  `json:"vendor"`
	CI          string  `json:"ci"`
	PCI         string  `json:"pci"`
	ManageIP    string  `json:"manageIp"`
	Position    string  `json:"position"`
	Longitude   float64 `json:"longitude"`
	Latitude    float64 `json:"latitude"`
}

// 连接状态响应
type ConnectionStatusResponse struct {
	ID            string `json:"id"`
	Status        string `json:"status"`
	Error         string `json:"error,omitempty"`
	Exists        bool   `json:"exists"`
	IsConnected   bool   `json:"isConnected"`
	IsConnecting  bool   `json:"isConnecting"`
	IsError       bool   `json:"isError"`
	MessagesSent  int64  `json:"messagesSent"`
	MessagesReceived int64 `json:"messagesReceived"`
	Errors        int64  `json:"errors"`
	Timestamp     int64  `json:"timestamp"`
}

// 统计信息响应
type StatsResponse struct {
	ID               string    `json:"id"`
	ConnectTime      time.Time `json:"connectTime"`
	LastMessageTime  time.Time `json:"lastMessageTime"`
	MessagesSent     int64     `json:"messagesSent"`
	MessagesReceived int64     `json:"messagesReceived"`
	BytesSent        int64     `json:"bytesSent"`
	BytesReceived    int64     `json:"bytesReceived"`
	Errors           int64     `json:"errors"`
}

// 创建TCP连接
func (a *App) CreateConnection(connConfig ConnectionConfig, deviceConfig DeviceConfigRequest) string {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// 如果连接已存在，先关闭
	if existingClient, exists := a.clients[connConfig.ID]; exists {
		existingClient.Disconnect()
		delete(a.clients, connConfig.ID)
	}

	// 创建设备配置
	devConfig := &protocol.DeviceConfig{
		IMSI:        deviceConfig.IMSI,
		IMEI:        deviceConfig.IMEI,
		Model:       deviceConfig.Model,
		SoftwareVer: deviceConfig.SoftwareVer,
		HardwareVer: deviceConfig.HardwareVer,
		Vendor:      deviceConfig.Vendor,
		CI:          deviceConfig.CI,
		PCI:         deviceConfig.PCI,
		ManageIP:    deviceConfig.ManageIP,
		Position:    deviceConfig.Position,
		Longitude:   deviceConfig.Longitude,
		Latitude:    deviceConfig.Latitude,
	}

	// 创建客户端配置
	clientConfig := &client.ClientConfig{
		ServerAddr:    fmt.Sprintf("%s:%d", connConfig.Host, connConfig.Port),
		Timeout:       time.Duration(connConfig.Timeout) * time.Second,
		Keepalive:     time.Duration(connConfig.Keepalive) * time.Second,
		DeviceConfig:  devConfig,
		EncryptionKey: connConfig.EncryptionKey,
	}

	// 创建TCP客户端
	tcpClient := client.NewTcpClient(clientConfig)

	// 设置回调函数
	tcpClient.SetStatusChangeCallback(func(status client.ClientStatus, err error) {
		fmt.Printf("Connection %s status changed to %s\n", connConfig.ID, status.String())
		if err != nil {
			fmt.Printf("Connection %s error: %v\n", connConfig.ID, err)
		}

		// 🆕 通知前端状态变化
		a.notifyStatusChange(connConfig.ID, status, err)
	})

	tcpClient.SetMessageReceivedCallback(func(data []byte) {
		fmt.Printf("Connection %s received %d bytes\n", connConfig.ID, len(data))
	})

	tcpClient.SetMessageSentCallback(func(data []byte) {
		fmt.Printf("Connection %s sent %d bytes\n", connConfig.ID, len(data))
	})

	// 保存客户端
	a.clients[connConfig.ID] = tcpClient

	return "连接创建成功"
}

// 连接到服务器
func (a *App) Connect(connectionID string) string {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return "连接不存在"
	}

	if err := tcpClient.Connect(); err != nil {
		return fmt.Sprintf("连接失败: %v", err)
	}

	return "连接成功"
}

// 断开连接
func (a *App) Disconnect(connectionID string) string {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return "连接不存在"
	}

	if err := tcpClient.Disconnect(); err != nil {
		return fmt.Sprintf("断开连接失败: %v", err)
	}

	return "断开连接成功"
}

// 发送注册消息
func (a *App) SendRegister(connectionID string) string {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return "连接不存在"
	}

	if err := tcpClient.SendRegister(); err != nil {
		return fmt.Sprintf("发送注册消息失败: %v", err)
	}

	return "注册消息发送成功"
}

// 发送心跳消息
func (a *App) SendHeartbeat(connectionID string, options map[string]interface{}) string {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return "连接不存在"
	}

	// 🔄 从任务流程管理器获取当前任务信息
	currentTaskInfo := a.taskManager.GetCurrentTaskInfo()
	fmt.Printf("💓 发送心跳 - 当前任务信息: step=%d, taskId=%d, distance=%d\n",
		currentTaskInfo.Step, currentTaskInfo.TaskID, currentTaskInfo.TaskDistance)

	// 解析心跳选项并注入任务信息
	heartbeatOptions := a.parseHeartbeatOptions(options)

	// 🆕 强制使用任务流程管理器的任务信息
	heartbeatOptions.TaskStep = int8(currentTaskInfo.Step)
	heartbeatOptions.TaskID = uint64(currentTaskInfo.TaskID)
	heartbeatOptions.TaskDistance = currentTaskInfo.TaskDistance

	fmt.Printf("💓 实际发送的任务信息: step=%d, taskId=%d, distance=%d\n",
		heartbeatOptions.TaskStep, heartbeatOptions.TaskID, heartbeatOptions.TaskDistance)

	if err := tcpClient.SendHeartbeat(heartbeatOptions); err != nil {
		return fmt.Sprintf("发送心跳消息失败: %v", err)
	}

	// 🔄 通知任务流程管理器心跳已发送
	if err := a.taskManager.OnHeartbeatSent(); err != nil {
		fmt.Printf("⚠️ 任务流程处理心跳失败: %v\n", err)
	}

	return "心跳消息发送成功"
}

// 启动自动心跳
func (a *App) StartAutoHeartbeat(connectionID string, intervalSeconds int, options map[string]interface{}) string {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return "连接不存在"
	}

	// 解析心跳选项
	heartbeatOptions := a.parseHeartbeatOptions(options)

	// 启动自动心跳
	interval := time.Duration(intervalSeconds) * time.Second
	tcpClient.StartAutoHeartbeat(interval, heartbeatOptions)

	return "自动心跳启动成功"
}

// 获取连接状态
func (a *App) GetConnectionStatus(connectionID string) ConnectionStatusResponse {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return ConnectionStatusResponse{
			ID:        connectionID,
			Status:    "not_found",
			Error:     "连接不存在",
			Exists:    false,
			Timestamp: time.Now().Unix(),
		}
	}

	status := tcpClient.GetStatus()
	stats := tcpClient.GetStats()

	return ConnectionStatusResponse{
		ID:               connectionID,
		Status:           status.String(),
		Exists:           true,
		IsConnected:      status == client.StatusConnected,
		IsConnecting:     status == client.StatusConnecting,
		IsError:          status == client.StatusError,
		MessagesSent:     stats.MessagesSent,
		MessagesReceived: stats.MessagesReceived,
		Errors:           stats.Errors,
		Timestamp:        time.Now().Unix(),
	}
}

// 获取统计信息
func (a *App) GetStats(connectionID string) StatsResponse {
	a.mutex.RLock()
	tcpClient, exists := a.clients[connectionID]
	a.mutex.RUnlock()

	if !exists {
		return StatsResponse{
			ID: connectionID,
		}
	}

	stats := tcpClient.GetStats()
	return StatsResponse{
		ID:               connectionID,
		ConnectTime:      stats.ConnectTime,
		LastMessageTime:  stats.LastMessageTime,
		MessagesSent:     stats.MessagesSent,
		MessagesReceived: stats.MessagesReceived,
		BytesSent:        stats.BytesSent,
		BytesReceived:    stats.BytesReceived,
		Errors:           stats.Errors,
	}
}

// 获取所有连接列表
func (a *App) GetAllConnections() []string {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	connections := make([]string, 0, len(a.clients))
	for id := range a.clients {
		connections = append(connections, id)
	}

	return connections
}

// 删除连接
func (a *App) RemoveConnection(connectionID string) string {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if tcpClient, exists := a.clients[connectionID]; exists {
		tcpClient.Disconnect()
		delete(a.clients, connectionID)
		return "连接删除成功"
	}

	return "连接不存在"
}

// 解析心跳选项
func (a *App) parseHeartbeatOptions(options map[string]interface{}) *protocol.HeartbeatOptions {
	// 获取默认选项
	heartbeatOptions := protocol.GetDefaultHeartbeatOptions()

	if options == nil {
		return heartbeatOptions
	}

	// 解析各种选项
	if val, ok := options["simulateMovement"].(bool); ok {
		heartbeatOptions.SimulateMovement = val
	}
	if val, ok := options["vehicleType"].(float64); ok {
		heartbeatOptions.VehicleType = int8(val)
	}
	if val, ok := options["acc"].(float64); ok {
		heartbeatOptions.ACC = int8(val)
	}
	if val, ok := options["gear"].(float64); ok {
		heartbeatOptions.Gear = int8(val)
	}
	if val, ok := options["deviceStatus"].(float64); ok {
		heartbeatOptions.DeviceStatus = int8(val)
	}
	if val, ok := options["workMode"].(float64); ok {
		heartbeatOptions.WorkMode = int8(val)
	}
	if val, ok := options["totalMileage"].(float64); ok {
		heartbeatOptions.TotalMileage = int32(val)
	}
	if val, ok := options["remainMileage"].(float64); ok {
		heartbeatOptions.RemainMileage = int16(val)
	}
	if val, ok := options["speed"].(float64); ok {
		heartbeatOptions.Speed = int16(val)
	}
	if val, ok := options["speedLimit"].(float64); ok {
		heartbeatOptions.SpeedLimit = int16(val)
	}
	if val, ok := options["powerLevel"].(float64); ok {
		heartbeatOptions.PowerLevel = int8(val)
	}
	if val, ok := options["powerVoltage"].(float64); ok {
		heartbeatOptions.PowerVoltage = int16(val)
	}
	if val, ok := options["powerCurrent"].(float64); ok {
		heartbeatOptions.PowerCurrent = int16(val)
	}
	if val, ok := options["powerCharging"].(float64); ok {
		heartbeatOptions.PowerCharging = int8(val)
	}
	if val, ok := options["batteryStatus"].(float64); ok {
		heartbeatOptions.BatteryStatus = int16(val)
	}

	// 任务相关选项 - 支持两种格式
	// 1. 平铺格式（向后兼容）
	if val, ok := options["taskStep"].(float64); ok {
		heartbeatOptions.TaskStep = int8(val)
	}
	if val, ok := options["taskId"].(float64); ok {
		heartbeatOptions.TaskID = uint64(val)
	}
	if val, ok := options["routeId"].(float64); ok {
		heartbeatOptions.RouteID = uint64(val)
	}
	if val, ok := options["executionId"].(float64); ok {
		heartbeatOptions.ExecutionId = int64(val)
	}
	if val, ok := options["progress"].(float64); ok {
		heartbeatOptions.Progress = int8(val)
	}
	if val, ok := options["startTime"].(float64); ok {
		heartbeatOptions.StartTime = int64(val)
	}
	if val, ok := options["taskDistance"].(float64); ok {
		heartbeatOptions.TaskDistance = int(val)
	}
	if val, ok := options["targetDistance"].(float64); ok {
		heartbeatOptions.TargetDistance = int(val)
	}
	if val, ok := options["leftTime"].(float64); ok {
		heartbeatOptions.LeftTime = int(val)
	}
	if val, ok := options["taskName"].(string); ok {
		heartbeatOptions.TaskName = val
	}
	if val, ok := options["taskDesp"].(string); ok {
		heartbeatOptions.TaskDesp = val
	}
	if val, ok := options["currentStationId"].(float64); ok {
		heartbeatOptions.CurrentStationID = int(val)
	}
	if val, ok := options["currentStationName"].(string); ok {
		heartbeatOptions.CurrentStationName = val
	}
	if val, ok := options["targetStationId"].(float64); ok {
		heartbeatOptions.TargetStationID = int(val)
	}
	if val, ok := options["targetStationName"].(string); ok {
		heartbeatOptions.TargetStationName = val
	}

	// 2. 嵌套格式（latestTask对象）
	if latestTask, ok := options["latestTask"].(map[string]interface{}); ok {
		if step, exists := latestTask["step"].(float64); exists {
			heartbeatOptions.TaskStep = int8(step)
		}
		if taskId, exists := latestTask["taskId"].(float64); exists {
			heartbeatOptions.TaskID = uint64(taskId)
		}
		if taskDistance, exists := latestTask["taskDistance"].(float64); exists {
			heartbeatOptions.TaskDistance = int(taskDistance)
		}
		// 可以根据需要添加更多latestTask字段的解析
	}

	return heartbeatOptions
}

// 测试连接（简单的TCP连接测试）
func (a *App) TestConnection(host string, port int) string {
	address := fmt.Sprintf("%s:%d", host, port)

	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return fmt.Sprintf("连接失败: %v", err)
	}
	defer conn.Close()

	return "连接成功"
}

// 前端日志打印到后端控制台
func (a *App) LogToConsole(level string, message string, data interface{}) {
	switch level {
	case "info":
		fmt.Printf("🔍 [前端] %s\n", message)
	case "warn":
		fmt.Printf("⚠️ [前端] %s\n", message)
	case "error":
		fmt.Printf("❌ [前端] %s\n", message)
	case "debug":
		fmt.Printf("🐛 [前端] %s\n", message)
	default:
		fmt.Printf("📝 [前端] %s\n", message)
	}

	// 如果有数据，也打印出来
	if data != nil {
		dataJSON, err := json.Marshal(data)
		if err == nil {
			fmt.Printf("    数据: %s\n", string(dataJSON))
		}
	}
}

// ==================== 任务流程管理API ====================

// StartTaskFlow 启动任务流程
func (a *App) StartTaskFlow() string {
	if err := a.taskManager.StartFlow(); err != nil {
		return fmt.Sprintf("启动任务流程失败: %v", err)
	}
	return "任务流程启动成功"
}

// StopTaskFlow 停止任务流程
func (a *App) StopTaskFlow() string {
	a.taskManager.StopFlow()
	return "任务流程停止成功"
}

// SaveTaskFlowData 保存前端传来的任务流程数据
func (a *App) SaveTaskFlowData(data taskflow.TaskFlowData) string {
	if err := a.taskManager.SaveTaskFlowData(data); err != nil {
		return fmt.Sprintf("保存任务流程数据失败: %v", err)
	}
	return "任务流程数据保存成功"
}

// GetTaskFlowData 获取任务流程数据（供前端使用）
func (a *App) GetTaskFlowData() taskflow.TaskFlowData {
	return a.taskManager.GetTaskFlowData()
}

// GetTaskFlowRuntimeState 获取任务流程运行时状态
func (a *App) GetTaskFlowRuntimeState() taskflow.RuntimeState {
	return a.taskManager.GetRuntimeState()
}

// GetCurrentTaskInfo 获取当前任务信息
func (a *App) GetCurrentTaskInfo() taskflow.TaskInfo {
	return a.taskManager.GetCurrentTaskInfo()
}

// notifyStatusChange 通知前端状态变化
func (a *App) notifyStatusChange(connectionID string, status client.ClientStatus, err error) {
	// 使用Wails的事件系统通知前端
	eventData := map[string]interface{}{
		"connectionID": connectionID,
		"status":       status.String(),
		"error":        nil,
	}

	if err != nil {
		eventData["error"] = err.Error()
	}

	fmt.Printf("🔔 Sending event 'connection-status-changed': %+v\n", eventData)

	// 发送事件到前端
	if a.ctx != nil {
		runtime.EventsEmit(a.ctx, "connection-status-changed", eventData)
		fmt.Printf("✅ Event sent successfully\n")
	} else {
		fmt.Printf("❌ Context is nil, cannot send event\n")
	}
}



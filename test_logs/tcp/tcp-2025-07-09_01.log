[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:N/A] Kafka 连接失败: brokers=[**************:9092 localhost:9092], 开始连接[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:N/A] Kafka 连接成功: brokers=[**************:9092 localhost:9092], 连接建立成功[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:N/A] Kafka 分区消费者启动成功: partition=0, 消费者启动成功[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:N/A] Kafka 分区消费者启动失败: partition=1, 启动失败: 分区不存在[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:N/A] 获取 Kafka 分区列表失败: topic=device-cmd, error=连接超时[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 消息接收: topic=device-cmd, partition=0, offset=12345, cmd=0x0101, timestamp=2025-07-09 23:30:00, key=device_001[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460987654321098] Kafka 消息接收: topic=device-cmd, partition=0, offset=12346, cmd=0x0610, timestamp=2025-07-09 23:30:01, key=device_002[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:N/A] Kafka 消息解析失败: topic=device-cmd, partition=0, offset=12347, error=JSON 解析失败, raw={"cmd":invalid,"imsi":"460123456789012"}[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令处理成功: cmd=0x0101, GoPoint 命令处理: lat=39.908823, lng=116.397470[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 命令处理失败: cmd=0x0102, SetSpeed 消息解析失败: 缺少 speed 字段[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460987654321098] Kafka 命令处理成功: cmd=0x0610, AddOrder 命令处理: billNo=B20250709001, productName=拿铁咖啡[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 设备指令下发成功: cmd=0x0101, 指令下发成功[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 设备指令下发失败: cmd=0x0102, 发送命令到终端失败: 设备离线[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460987654321098] Kafka 设备指令下发成功: cmd=0x0610, 指令下发成功[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令下发内容: cmd=0x0101, data={"cmd":257,"lat":39.908823,"lng":116.397470}[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 结果 MQTT 发布成功: topic=/pix/cmd/device, code=0[0m
[33m[2025-07-09 23:30:46.198] [WARN] [TCP] [IMSI:460987654321098] Kafka 结果发布跳过: MQTT 未连接, topic=/pix/cmd/device[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 结果 MQTT 发布失败: topic=/pix/cmd/device, error=连接超时[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令处理成功: cmd=0x0101, GoPoint 命令处理: lat=39.908823, lng=116.397470[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令处理成功: cmd=0x0102, SetSpeed 命令处理: speed=15.50[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460987654321098] Kafka 命令处理成功: cmd=0x0610, AddOrder 命令处理: billNo=B20250709002, productName=美式咖啡[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令处理成功: cmd=0x0201, CloudSync 命令处理: name=map_v2.0.zip, url=https://example.com/maps/map_v2.0.zip[0m
[32m[2025-07-09 23:30:46.198] [INFO] [TCP] [IMSI:460123456789012] Kafka 命令处理成功: cmd=0x0301, SelectAlarm 命令处理: level=2[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 命令处理失败: cmd=0x0101, GoPoint 消息解析失败: 缺少 lat 字段[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460987654321098] Kafka 命令处理失败: cmd=0x0610, AddOrder 消息解析失败: JSON 格式错误[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 命令处理失败: cmd=0x0201, CloudSync 消息解析失败: 缺少 downloadUrl 字段[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460123456789012] Kafka 设备指令下发失败: cmd=0x0101, 发送命令到终端失败: 终端与服务器的连接已断开[0m
[31m[2025-07-09 23:30:46.198] [ERROR] [TCP] [IMSI:460987654321098] Kafka 设备指令下发失败: cmd=0x0610, 发送命令到终端失败: 设备忙碌中[0m

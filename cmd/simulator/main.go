package main

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
)

// 协议常量
const (
	MSG_START_CHAR1      = '@'
	MSG_START_CHAR2      = '<'  // ccserver期望的是'<'
	PROTOCOL_VERSION     = 0x01
	ENCRYPTION_NONE      = 0x00
	MSG_FORMAT_JSON      = 0x0000
	
	// 协议命令
	CMD_REGISTER    = 0x0001
	CMD_HEARTBEAT   = 0x0002
)

// 站点信息
type SimpleStation struct {
	ID   int    `json:"id"`   // 站点ID
	Name string `json:"name"` // 站点名称
}

// 任务信息
type TaskInfo struct {
	Step           int           `json:"step"`
	TaskId         int           `json:"taskId"`
	ExecutionId    int64         `json:"executionId"`    // 🆕 执行实例ID
	RouteId        int           `json:"routeId"`
	Progress       int           `json:"progress"`
	StartTime      int64         `json:"startTime"`
	LeftTime       int           `json:"leftTime"`
	TaskDistance   int           `json:"taskDistance"`
	Name           string        `json:"name"`
	Desp           string        `json:"desp"`
	// 🆕 添加站点信息字段
	CurrentStation SimpleStation `json:"currentStation"` // 当前站点
	TargetStation  SimpleStation `json:"targetStation"`  // 目标站点
	Point          string        `json:"point"`          // 当前位置点
	NextPoint      string        `json:"nextPoint"`      // 下一个位置点
	TargetDistance int           `json:"targetDistance"` // 目标距离
}

// 心跳数据结构
type HeartbeatData struct {
	Cmd        int      `json:"cmd"`
	IMSI       string   `json:"imsi"`
	IMEI       string   `json:"imei"`
	Lat        float64  `json:"lat"`
	Lng        float64  `json:"lng"`
	Located    int      `json:"located"`
	TM         int      `json:"tm"`         // 总里程(km) - 🔧 修改为int类型以匹配ccserver
	Speed      int      `json:"spd"`
	ACC        int      `json:"acc"`
	Gear       int      `json:"gear"`
	LatestTask TaskInfo `json:"latestTask"` // 🆕 包含executionId的任务信息
}

// 测试配置
type TestConfig struct {
	TestMode         bool    // 是否启用测试模式
	ExecutionId      int64   // 测试用的executionId
	TestSteps        []int   // 测试步骤序列
	StepInterval     int     // 步骤间隔（秒）
	Interactive      bool    // 是否启用交互模式
	LongExecution    bool    // 🆕 是否启用长时间执行测试
	ExecutionDuration int    // 🆕 Step=1执行持续时间（秒）
	HeartbeatInterval int    // 🆕 心跳发送间隔（秒）
	MileagePerBeat   float64 // 🆕 每次心跳增加的里程（km）
}

// TCP客户端模拟器
type TCPSimulator struct {
	conn         net.Conn
	imsi         string
	currentTask  TaskInfo
	billNo       uint32
	totalMileage float64
	testConfig   TestConfig  // 🆕 测试配置
	// 🆕 GPS轨迹模拟
	startLat     float64     // 起始纬度
	startLng     float64     // 起始经度
	endLat       float64     // 目标纬度
	endLng       float64     // 目标经度
	currentLat   float64     // 当前纬度
	currentLng   float64     // 当前经度
}

// 创建新的模拟器
func NewTCPSimulator(serverAddr string, imsi string, testConfig TestConfig) (*TCPSimulator, error) {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("连接服务器失败: %v", err)
	}

	simulator := &TCPSimulator{
		conn:         conn,
		imsi:         imsi,
		billNo:       1,
		totalMileage: 100.0, // 初始里程100km
		testConfig:   testConfig, // 🆕 保存测试配置
	}

	fmt.Printf("✅ 成功连接到ccserver: %s\n", serverAddr)
	if testConfig.TestMode {
		fmt.Printf("🧪 测试模式已启用，ExecutionId=%d\n", testConfig.ExecutionId)
	}
	return simulator, nil
}

// CRC16校验
func crc16CheckSum(data []byte) uint16 {
	var crc uint16 = 0xFFFF
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&1 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// 构建协议包
func (s *TCPSimulator) makeProtocolPacket(msgBody []byte) []byte {
	var buffer bytes.Buffer
	
	// 1. 消息头 '@<'
	buffer.WriteByte(MSG_START_CHAR1)
	buffer.WriteByte(MSG_START_CHAR2)
	
	// 2. 长度占位符(稍后填充)
	lengthPos := buffer.Len()
	buffer.Write([]byte{0, 0})
	
	// 3. 流水号
	binary.Write(&buffer, binary.BigEndian, s.billNo)
	s.billNo++
	
	// 4. 消息体格式
	binary.Write(&buffer, binary.BigEndian, uint16(MSG_FORMAT_JSON))
	
	// 5. 协议版本号
	buffer.WriteByte(PROTOCOL_VERSION)
	
	// 6. 加密方式
	buffer.WriteByte(ENCRYPTION_NONE)
	
	// 7. 随机数 (4字节)
	binary.Write(&buffer, binary.BigEndian, uint32(0))

	// 8. 时间戳 (8字节，与ccserver保持一致)
	ts := time.Now().Unix()
	binary.Write(&buffer, binary.BigEndian, ts)
	
	// 9. 预留24字节
	buffer.Write(make([]byte, 24))
	
	// 10. 消息体
	if msgBody != nil {
		buffer.Write(msgBody)
	}
	
	// 11. 填充长度(不包括头部@<，包括CRC16)
	data := buffer.Bytes()
	length := uint16(len(data) - 4 + 2) // -4(@<和长度字段) +2(CRC16)
	binary.BigEndian.PutUint16(data[lengthPos:], length)
	
	// 12. 计算并添加CRC16
	crc := crc16CheckSum(data[2:]) // 从长度字段开始计算
	binary.Write(&buffer, binary.BigEndian, crc)
	
	return buffer.Bytes()
}

// 发送注册消息
func (s *TCPSimulator) sendRegister() error {
	registerData := map[string]interface{}{
		"cmd":           CMD_REGISTER,
		"imsi":          s.imsi,
		"imei":          "860123456789012",
		"model":         "TestDevice",
		"software_ver":  "1.0.0",
		"hardware_ver":  "1.0.0",
		"vendor":        "TestVendor",
		"latitude":      22.487064,
		"longitude":     113.910408,
		"position":      "深圳市南山区",
		"manage_ip":     "127.0.0.1",
		"ci":            "12345",
		"pci":           "67890",
	}
	
	jsonData, err := json.Marshal(registerData)
	if err != nil {
		return fmt.Errorf("序列化注册数据失败: %v", err)
	}
	
	packet := s.makeProtocolPacket(jsonData)
	_, err = s.conn.Write(packet)
	if err != nil {
		return fmt.Errorf("发送注册消息失败: %v", err)
	}
	
	fmt.Printf("📤 发送注册消息: IMSI=%s\n", s.imsi)
	return nil
}

// 发送心跳消息
func (s *TCPSimulator) sendHeartbeat() error {
	heartbeatData := HeartbeatData{
		Cmd:        CMD_HEARTBEAT,
		IMSI:       s.imsi,
		IMEI:       "860123456789012",
		Lat:        22.487064 + float64(time.Now().Unix()%100)*0.0001, // 模拟位置变化
		Lng:        113.910408 + float64(time.Now().Unix()%100)*0.0001,
		Located:    1,
		TM:         int(s.totalMileage), // 🔧 转换为整数以匹配ccserver的int32类型
		Speed:      30,
		ACC:        1,
		Gear:       1,
		LatestTask: s.currentTask,
	}
	
	jsonData, err := json.Marshal(heartbeatData)
	if err != nil {
		return fmt.Errorf("序列化心跳数据失败: %v", err)
	}
	
	packet := s.makeProtocolPacket(jsonData)
	_, err = s.conn.Write(packet)
	if err != nil {
		return fmt.Errorf("发送心跳消息失败: %v", err)
	}
	
	fmt.Printf("💓 发送心跳: Step=%d, TaskId=%d, ExecutionId=%d, TM=%.1fkm\n", 
		s.currentTask.Step, s.currentTask.TaskId, s.currentTask.ExecutionId, s.totalMileage)
	return nil
}

// 启动消息接收协程
func (s *TCPSimulator) startMessageReceiver() {
	go func() {
		for {
			err := s.receiveMessage()
			if err != nil {
				fmt.Printf("❌ 接收消息失败: %v\n", err)
				time.Sleep(1 * time.Second)
			}
		}
	}()
}

// 启动定时心跳
func (s *TCPSimulator) startHeartbeatTimer() {
	go func() {
		ticker := time.NewTicker(10 * time.Second) // 每10秒发送一次心跳
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := s.sendHeartbeat(); err != nil {
					fmt.Printf("❌ 定时心跳发送失败: %v\n", err)
				}
			}
		}
	}()
}

// 接收并解析服务器消息
func (s *TCPSimulator) receiveMessage() error {
	// 设置读取超时
	s.conn.SetReadDeadline(time.Now().Add(30 * time.Second))

	// 读取消息头
	header := make([]byte, 4)
	_, err := s.conn.Read(header)
	if err != nil {
		return fmt.Errorf("读取消息头失败: %v", err)
	}

	// 检查消息头 - ccserver发送的响应使用@>
	if header[0] != MSG_START_CHAR1 || header[1] != '>' {
		return fmt.Errorf("无效的消息头: %x %x", header[0], header[1])
	}

	// 读取消息长度
	length := binary.BigEndian.Uint16(header[2:4])

	// 读取剩余数据
	remaining := make([]byte, length)
	_, err = s.conn.Read(remaining)
	if err != nil {
		return fmt.Errorf("读取消息体失败: %v", err)
	}

	// 解析JSON消息体(跳过协议头部分)
	// 协议头结构：流水号(4) + 格式(2) + 版本(1) + 加密(1) + 随机数(4) + 时间戳(8) + 预留(24) = 44字节
	if length > 44 {
		jsonStart := 42 // 跳过协议头字段：4+2+1+1+4+8+24=44，但从remaining开始算是42
		jsonEnd := int(length) - 2 // 减去CRC16

		if jsonEnd > jsonStart {
			jsonData := remaining[jsonStart:jsonEnd]

			// 🔧 去除JSON数据前面的空字符
			jsonStr := string(jsonData)
			jsonStr = strings.TrimLeft(jsonStr, "\x00\t\n\r ")
			cleanJsonData := []byte(jsonStr)

			fmt.Printf("🔍 接收到服务器消息，长度=%d, JSON起始=%d, 结束=%d\n", length, jsonStart, jsonEnd)
			fmt.Printf("🔍 清理后JSON数据: %s\n", string(cleanJsonData))

			// 尝试解析JSON
			var message map[string]interface{}
			if err := json.Unmarshal(cleanJsonData, &message); err == nil {
				s.handleServerMessage(message, cleanJsonData)
			} else {
				fmt.Printf("❌ JSON解析失败: %v\n", err)
				fmt.Printf("❌ 原始数据: %s\n", string(cleanJsonData))
			}
		}
	}

	return nil
}

// 处理服务器消息
func (s *TCPSimulator) handleServerMessage(message map[string]interface{}, rawJSON []byte) {
	cmd, ok := message["cmd"].(float64)
	if !ok {
		return
	}

	switch int(cmd) {
	case 0x0018: // 任务下发
		fmt.Printf("🎉 收到任务下发指令!\n")
		fmt.Printf("   Raw JSON: %s\n", string(rawJSON))

		// 解析任务信息
		if taskId, ok := message["taskId"].(float64); ok {
			s.currentTask.TaskId = int(taskId)
		}
		if routeId, ok := message["routeId"].(float64); ok {
			s.currentTask.RouteId = int(routeId)
		}
		if name, ok := message["name"].(string); ok {
			s.currentTask.Name = name
		}
		if desp, ok := message["desp"].(string); ok {
			s.currentTask.Desp = desp
		}

		// 🆕 解析executionId
		if executionId, ok := message["executionId"].(float64); ok {
			s.currentTask.ExecutionId = int64(executionId)
			fmt.Printf("   ✅ 解析到ExecutionId: %d\n", s.currentTask.ExecutionId)
		}

		// 设置任务初始状态
		s.currentTask.Step = 0 // 收到任务但未开始
		s.currentTask.StartTime = time.Now().Unix()
		s.currentTask.LeftTime = 5 // 预计5分钟完成
		s.currentTask.TaskDistance = 360 // 360米

		// 🆕 设置站点信息
		s.currentTask.CurrentStation = SimpleStation{
			ID:   1,
			Name: "深圳湾科技生态园",
		}
		s.currentTask.TargetStation = SimpleStation{
			ID:   2,
			Name: "南山科技园",
		}
		s.currentTask.Point = "22.487064,113.910408"      // 起始GPS坐标
		s.currentTask.NextPoint = "22.497064,113.920408"  // 目标GPS坐标
		s.currentTask.TargetDistance = 360                // 到目标点距离（米）

		fmt.Printf("   任务ID: %d, 路线ID: %d\n", s.currentTask.TaskId, s.currentTask.RouteId)
		fmt.Printf("   任务名称: %s\n", s.currentTask.Name)
		fmt.Printf("   任务描述: %s\n", s.currentTask.Desp)
		fmt.Printf("   起始站点: %s (ID: %d)\n", s.currentTask.CurrentStation.Name, s.currentTask.CurrentStation.ID)
		fmt.Printf("   目标站点: %s (ID: %d)\n", s.currentTask.TargetStation.Name, s.currentTask.TargetStation.ID)
		fmt.Printf("   🔄 任务信息已保存到LatestTask，后续心跳将包含ExecutionId和站点信息\n")

		// 🆕 自动开始任务执行
		fmt.Printf("   🚀 2秒后自动开始任务执行...\n")
		time.Sleep(2 * time.Second)
		s.currentTask.Step = 1
		s.sendHeartbeat()

		// 🆕 5秒后自动完成任务
		go func() {
			time.Sleep(5 * time.Second)
			fmt.Printf("   ✅ 自动完成任务...\n")
			s.currentTask.Step = 2
			s.totalMileage += 1.0 // 🔧 修改为增加1km里程（整数）
			s.sendHeartbeat()
		}()

	default:
		fmt.Printf("📥 收到其他指令: cmd=%d\n", int(cmd))
	}
}

// 🆕 发送测试心跳（指定executionId和step）
func (s *TCPSimulator) sendTestHeartbeat(executionId int64, step int, taskId int) error {
	// 构建测试任务信息
	testTask := TaskInfo{
		Step:        step,
		TaskId:      taskId,
		ExecutionId: executionId,
		RouteId:     1,
		Progress:    0,
		StartTime:   time.Now().Unix(),
		LeftTime:    5,
		TaskDistance: 360,
		Name:        "ExecutionId测试任务",
		Desp:        "测试task_execution_report表更新",
		// 🆕 添加站点信息模拟
		CurrentStation: SimpleStation{
			ID:   1,
			Name: "深圳湾科技生态园",
		},
		TargetStation: SimpleStation{
			ID:   2,
			Name: "南山科技园",
		},
		Point:          "22.487064,113.910408", // 起始GPS坐标
		NextPoint:      "22.497064,113.920408", // 目标GPS坐标
		TargetDistance: 360,                    // 到目标点距离（米）
	}

	heartbeatData := HeartbeatData{
		Cmd:        CMD_HEARTBEAT,
		IMSI:       s.imsi,
		IMEI:       "860123456789012",
		Lat:        22.487064 + float64(time.Now().Unix()%100)*0.0001,
		Lng:        113.910408 + float64(time.Now().Unix()%100)*0.0001,
		Located:    1,
		TM:         int(s.totalMileage), // 🔧 转换为整数以匹配ccserver的int32类型
		Speed:      30,
		ACC:        1,
		Gear:       1,
		LatestTask: testTask,
	}

	jsonData, err := json.Marshal(heartbeatData)
	if err != nil {
		return fmt.Errorf("序列化测试心跳数据失败: %v", err)
	}

	packet := s.makeProtocolPacket(jsonData)
	_, err = s.conn.Write(packet)
	if err != nil {
		return fmt.Errorf("发送测试心跳失败: %v", err)
	}

	stepDesc := map[int]string{
		0: "收到任务但未开始",
		1: "任务执行中",
		2: "任务完成",
		6: "任务暂停",
		8: "任务终止",
		9: "任务失败",
	}

	fmt.Printf("🧪 发送测试心跳: ExecutionId=%d, Step=%d (%s), TaskId=%d, TM=%.1fkm\n",
		executionId, step, stepDesc[step], taskId, s.totalMileage)
	return nil
}

// 🆕 启动自动测试模式
func (s *TCPSimulator) startAutoTestMode() {
	if !s.testConfig.TestMode {
		return
	}

	go func() {
		if s.testConfig.LongExecution {
			s.startLongExecutionTest()
		} else if len(s.testConfig.TestSteps) > 0 {
			s.startQuickStepTest()
		}
	}()
}

// 🆕 启动长时间执行测试
func (s *TCPSimulator) startLongExecutionTest() {
	fmt.Printf("🚀 启动长时间执行测试，ExecutionId=%d, 执行时长=%d秒, 心跳间隔=%d秒\n",
		s.testConfig.ExecutionId, s.testConfig.ExecutionDuration, s.testConfig.HeartbeatInterval)

	// 等待连接稳定
	time.Sleep(3 * time.Second)

	// Step 0: 收到任务但未开始
	fmt.Printf("📋 Step 0: 收到任务但未开始\n")
	err := s.sendTestHeartbeat(s.testConfig.ExecutionId, 0, 9513)
	if err != nil {
		fmt.Printf("❌ 发送Step=0心跳失败: %v\n", err)
		return
	}

	time.Sleep(time.Duration(s.testConfig.StepInterval) * time.Second)

	// Step 1: 任务执行中 - 长时间执行
	fmt.Printf("🏃 Step 1: 开始任务执行，预计执行 %d 秒...\n", s.testConfig.ExecutionDuration)
	startTime := time.Now()
	startMileage := s.totalMileage

	// 发送初始的Step=1心跳
	err = s.sendTestHeartbeat(s.testConfig.ExecutionId, 1, 9513)
	if err != nil {
		fmt.Printf("❌ 发送Step=1心跳失败: %v\n", err)
		return
	}

	// 在执行期间定期发送心跳
	heartbeatCount := 0
	for elapsed := time.Since(startTime); elapsed < time.Duration(s.testConfig.ExecutionDuration)*time.Second; elapsed = time.Since(startTime) {
		time.Sleep(time.Duration(s.testConfig.HeartbeatInterval) * time.Second)
		heartbeatCount++

		// 增加里程，模拟车辆行驶
		s.totalMileage += s.testConfig.MileagePerBeat

		// 发送Step=1心跳
		err = s.sendTestHeartbeat(s.testConfig.ExecutionId, 1, 9513)
		if err != nil {
			fmt.Printf("❌ 发送Step=1心跳失败: %v\n", err)
			continue
		}

		remainingTime := s.testConfig.ExecutionDuration - int(elapsed.Seconds())
		fmt.Printf("   ⏱️  执行中... 已用时 %d 秒，剩余 %d 秒，当前里程 %.1f km\n",
			int(elapsed.Seconds()), remainingTime, s.totalMileage)
	}

	// Step 2: 任务完成
	endTime := time.Now()
	actualDuration := endTime.Sub(startTime)
	actualMileage := s.totalMileage - startMileage

	fmt.Printf("✅ Step 2: 任务完成\n")
	fmt.Printf("   📊 执行统计: 实际用时 %.1f 秒，行驶里程 %.1f km，平均速度 %.2f km/h\n",
		actualDuration.Seconds(), actualMileage, actualMileage*3600/actualDuration.Seconds())

	err = s.sendTestHeartbeat(s.testConfig.ExecutionId, 2, 9513)
	if err != nil {
		fmt.Printf("❌ 发送Step=2心跳失败: %v\n", err)
		return
	}

	fmt.Printf("🎉 长时间执行测试完成！\n")
}

// 🆕 启动快速步骤测试（原有逻辑）
func (s *TCPSimulator) startQuickStepTest() {
	fmt.Printf("🚀 启动快速步骤测试，ExecutionId=%d, 步骤序列=%v\n",
		s.testConfig.ExecutionId, s.testConfig.TestSteps)

	// 等待连接稳定
	time.Sleep(3 * time.Second)

	for i, step := range s.testConfig.TestSteps {
		if i > 0 {
			fmt.Printf("⏳ 等待 %d 秒后执行下一步...\n", s.testConfig.StepInterval)
			time.Sleep(time.Duration(s.testConfig.StepInterval) * time.Second)
		}

		err := s.sendTestHeartbeat(s.testConfig.ExecutionId, step, 9513)
		if err != nil {
			fmt.Printf("❌ 发送测试心跳失败: %v\n", err)
			continue
		}

		// 🔧 修改里程增加逻辑，确保发送整数里程
		// 如果是step=1，增加1km里程
		if step == 1 {
			s.totalMileage += 1.0
		}
		// 如果是step=2，再增加1km里程表示任务完成
		if step == 2 {
			s.totalMileage += 1.0
		}
	}

	fmt.Printf("✅ 快速步骤测试完成\n")
}

// 🆕 启动交互式测试模式
func (s *TCPSimulator) startInteractiveMode() {
	if !s.testConfig.Interactive {
		return
	}

	go func() {
		fmt.Printf("\n🎮 交互式测试模式已启用\n")
		fmt.Printf("可用命令:\n")
		fmt.Printf("  test <executionId> <step> [taskId] - 发送测试心跳\n")
		fmt.Printf("  例如: test 38 1 9513\n")
		fmt.Printf("  quit - 退出程序\n")
		fmt.Printf("  help - 显示帮助\n\n")

		scanner := bufio.NewScanner(os.Stdin)
		for {
			fmt.Printf("simulator> ")
			if !scanner.Scan() {
				break
			}

			line := strings.TrimSpace(scanner.Text())
			if line == "" {
				continue
			}

			parts := strings.Fields(line)
			if len(parts) == 0 {
				continue
			}

			switch parts[0] {
			case "test":
				if len(parts) < 3 {
					fmt.Printf("❌ 用法: test <executionId> <step> [taskId]\n")
					continue
				}

				executionId, err := strconv.ParseInt(parts[1], 10, 64)
				if err != nil {
					fmt.Printf("❌ 无效的executionId: %s\n", parts[1])
					continue
				}

				step, err := strconv.Atoi(parts[2])
				if err != nil {
					fmt.Printf("❌ 无效的step: %s\n", parts[2])
					continue
				}

				taskId := 9513 // 默认taskId
				if len(parts) > 3 {
					taskId, err = strconv.Atoi(parts[3])
					if err != nil {
						fmt.Printf("❌ 无效的taskId: %s\n", parts[3])
						continue
					}
				}

				err = s.sendTestHeartbeat(executionId, step, taskId)
				if err != nil {
					fmt.Printf("❌ 发送测试心跳失败: %v\n", err)
				}

			case "quit", "exit":
				fmt.Printf("👋 退出程序\n")
				os.Exit(0)

			case "help":
				fmt.Printf("可用命令:\n")
				fmt.Printf("  test <executionId> <step> [taskId] - 发送测试心跳\n")
				fmt.Printf("  quit - 退出程序\n")
				fmt.Printf("  help - 显示帮助\n")

			default:
				fmt.Printf("❌ 未知命令: %s，输入 help 查看帮助\n", parts[0])
			}
		}
	}()
}

// 关闭连接
func (s *TCPSimulator) Close() {
	if s.conn != nil {
		s.conn.Close()
		fmt.Printf("🔌 连接已关闭\n")
	}
}

// 解析命令行参数
func parseArgs() (string, string, TestConfig) {
	if len(os.Args) < 3 {
		fmt.Printf("使用方法: %s <服务器地址> <设备IMSI> [选项]\n", os.Args[0])
		fmt.Printf("示例: %s localhost:2020 TEST_EXEC_ID_001\n", os.Args[0])
		fmt.Printf("\n测试选项:\n")
		fmt.Printf("  --test-execution-id <id>     指定测试用的executionId\n")
		fmt.Printf("  --test-steps <steps>         指定测试步骤序列，用逗号分隔 (如: 0,1,2)\n")
		fmt.Printf("  --step-interval <seconds>    步骤间隔时间，默认5秒\n")
		fmt.Printf("  --interactive               启用交互式测试模式\n")
		fmt.Printf("  --long-execution-test       启用长时间执行测试模式\n")
		fmt.Printf("  --execution-duration <sec>   Step=1执行持续时间，默认60秒\n")
		fmt.Printf("  --heartbeat-interval <sec>   心跳发送间隔，默认10秒\n")
		fmt.Printf("  --mileage-per-beat <km>      每次心跳增加的里程，默认0.5km\n")
		fmt.Printf("\n测试示例:\n")
		fmt.Printf("  %s localhost:2020 TEST_EXEC_ID_001 --test-execution-id 38 --test-steps 0,1,2\n", os.Args[0])
		fmt.Printf("  %s localhost:2020 TEST_EXEC_ID_001 --long-execution-test --test-execution-id 38\n", os.Args[0])
		fmt.Printf("  %s localhost:2020 TEST_EXEC_ID_001 --interactive\n", os.Args[0])
		os.Exit(1)
	}

	serverAddr := os.Args[1]
	imsi := os.Args[2]

	testConfig := TestConfig{
		StepInterval:      5,    // 默认5秒间隔
		ExecutionDuration: 60,   // 默认60秒执行时间
		HeartbeatInterval: 10,   // 默认10秒心跳间隔
		MileagePerBeat:    0.5,  // 默认每次心跳增加0.5km
	}

	// 解析可选参数
	for i := 3; i < len(os.Args); i++ {
		switch os.Args[i] {
		case "--test-execution-id":
			if i+1 < len(os.Args) {
				if id, err := strconv.ParseInt(os.Args[i+1], 10, 64); err == nil {
					testConfig.TestMode = true
					testConfig.ExecutionId = id
					i++ // 跳过下一个参数
				}
			}
		case "--test-steps":
			if i+1 < len(os.Args) {
				stepStrs := strings.Split(os.Args[i+1], ",")
				for _, stepStr := range stepStrs {
					if step, err := strconv.Atoi(strings.TrimSpace(stepStr)); err == nil {
						testConfig.TestSteps = append(testConfig.TestSteps, step)
					}
				}
				i++ // 跳过下一个参数
			}
		case "--step-interval":
			if i+1 < len(os.Args) {
				if interval, err := strconv.Atoi(os.Args[i+1]); err == nil {
					testConfig.StepInterval = interval
				}
				i++ // 跳过下一个参数
			}
		case "--interactive":
			testConfig.Interactive = true
		case "--long-execution-test":
			testConfig.LongExecution = true
		case "--execution-duration":
			if i+1 < len(os.Args) {
				if duration, err := strconv.Atoi(os.Args[i+1]); err == nil {
					testConfig.ExecutionDuration = duration
				}
				i++ // 跳过下一个参数
			}
		case "--heartbeat-interval":
			if i+1 < len(os.Args) {
				if interval, err := strconv.Atoi(os.Args[i+1]); err == nil {
					testConfig.HeartbeatInterval = interval
				}
				i++ // 跳过下一个参数
			}
		case "--mileage-per-beat":
			if i+1 < len(os.Args) {
				if mileage, err := strconv.ParseFloat(os.Args[i+1], 64); err == nil {
					testConfig.MileagePerBeat = mileage
				}
				i++ // 跳过下一个参数
			}
		}
	}

	return serverAddr, imsi, testConfig
}

// 主函数
func main() {
	serverAddr, imsi, testConfig := parseArgs()

	// 创建模拟器
	simulator, err := NewTCPSimulator(serverAddr, imsi, testConfig)
	if err != nil {
		fmt.Printf("❌ 创建模拟器失败: %v\n", err)
		os.Exit(1)
	}
	defer simulator.Close()

	// 🆕 启动消息接收协程
	simulator.startMessageReceiver()

	// 🆕 启动定时心跳（仅在非测试模式下）
	if !testConfig.TestMode {
		simulator.startHeartbeatTimer()
	}

	// 发送注册消息
	if err := simulator.sendRegister(); err != nil {
		fmt.Printf("❌ 注册失败: %v\n", err)
		os.Exit(1)
	}

	// 等待注册响应
	time.Sleep(2 * time.Second)

	// 发送初始心跳
	if err := simulator.sendHeartbeat(); err != nil {
		fmt.Printf("❌ 发送心跳失败: %v\n", err)
		os.Exit(1)
	}

	// 🆕 根据配置启动不同模式
	if testConfig.TestMode && len(testConfig.TestSteps) > 0 {
		fmt.Printf("🧪 启动自动测试模式...\n")
		simulator.startAutoTestMode()
	}

	if testConfig.Interactive {
		fmt.Printf("🎮 启动交互式测试模式...\n")
		simulator.startInteractiveMode()
	}

	if !testConfig.TestMode && !testConfig.Interactive {
		fmt.Printf("🎯 TCP模拟器已启动，等待任务下发...\n")
		fmt.Printf("💡 模拟器将自动处理任务下发并模拟执行过程\n")
	}

	fmt.Printf("📊 按Ctrl+C退出\n\n")

	// 保持程序运行
	select {}
}
